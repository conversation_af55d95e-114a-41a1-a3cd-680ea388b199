{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, Injectable, Inject, Optional, NgModule } from '@angular/core';\nimport { asyncScheduler, Observable, from, of } from 'rxjs';\nimport * as i1 from '@angular/fire';\nimport { keepUnstableUntilFirst, VERSION } from '@angular/fire';\nimport { startWith, pairwise, map, scan, distinctUntilChanged, filter } from 'rxjs/operators';\nimport { ɵfirebaseAppFactory, ɵcacheInstance, FIREBASE_OPTIONS, FIREBASE_APP_NAME } from '@angular/fire/compat';\nimport { isPlatformServer } from '@angular/common';\nimport 'firebase/compat/auth';\nimport 'firebase/compat/firestore';\nimport * as i2 from '@angular/fire/compat/auth';\nimport { ɵauthFactory, USE_EMULATOR as USE_EMULATOR$1, SETTINGS as SETTINGS$1, TENANT_ID, LANGUAGE_CODE, USE_DEVICE_LANGUAGE, PERSISTENCE } from '@angular/fire/compat/auth';\nimport * as i3 from '@angular/fire/app-check';\nimport firebase from 'firebase/compat/app';\n\nfunction _fromRef(ref, scheduler = asyncScheduler) {\n  return new Observable(subscriber => {\n    let unsubscribe;\n\n    if (scheduler != null) {\n      scheduler.schedule(() => {\n        unsubscribe = ref.onSnapshot({\n          includeMetadataChanges: true\n        }, subscriber);\n      });\n    } else {\n      unsubscribe = ref.onSnapshot({\n        includeMetadataChanges: true\n      }, subscriber);\n    }\n\n    return () => {\n      if (unsubscribe != null) {\n        unsubscribe();\n      }\n    };\n  });\n}\n\nfunction fromRef(ref, scheduler) {\n  return _fromRef(ref, scheduler);\n}\n\nfunction fromDocRef(ref, scheduler) {\n  return fromRef(ref, scheduler).pipe(startWith(undefined), pairwise(), map(([priorPayload, payload]) => {\n    if (!payload.exists) {\n      return {\n        payload,\n        type: 'removed'\n      };\n    }\n\n    if (!(priorPayload === null || priorPayload === void 0 ? void 0 : priorPayload.exists)) {\n      return {\n        payload,\n        type: 'added'\n      };\n    }\n\n    return {\n      payload,\n      type: 'modified'\n    };\n  }));\n}\n\nfunction fromCollectionRef(ref, scheduler) {\n  return fromRef(ref, scheduler).pipe(map(payload => ({\n    payload,\n    type: 'query'\n  })));\n}\n/**\n * Return a stream of document changes on a query. These results are not in sort order but in\n * order of occurence.\n */\n\n\nfunction docChanges(query, scheduler) {\n  return fromCollectionRef(query, scheduler).pipe(startWith(undefined), pairwise(), map(([priorAction, action]) => {\n    const docChanges = action.payload.docChanges();\n    const actions = docChanges.map(change => ({\n      type: change.type,\n      payload: change\n    })); // the metadata has changed from the prior emission\n\n    if (priorAction && JSON.stringify(priorAction.payload.metadata) !== JSON.stringify(action.payload.metadata)) {\n      // go through all the docs in payload and figure out which ones changed\n      action.payload.docs.forEach((currentDoc, currentIndex) => {\n        const docChange = docChanges.find(d => d.doc.ref.isEqual(currentDoc.ref));\n        const priorDoc = priorAction === null || priorAction === void 0 ? void 0 : priorAction.payload.docs.find(d => d.ref.isEqual(currentDoc.ref));\n\n        if (docChange && JSON.stringify(docChange.doc.metadata) === JSON.stringify(currentDoc.metadata) || !docChange && priorDoc && JSON.stringify(priorDoc.metadata) === JSON.stringify(currentDoc.metadata)) {// document doesn't appear to have changed, don't log another action\n        } else {\n          // since the actions are processed in order just push onto the array\n          actions.push({\n            type: 'modified',\n            payload: {\n              oldIndex: currentIndex,\n              newIndex: currentIndex,\n              type: 'modified',\n              doc: currentDoc\n            }\n          });\n        }\n      });\n    }\n\n    return actions;\n  }));\n}\n/**\n * Return a stream of document changes on a query. These results are in sort order.\n */\n\n\nfunction sortedChanges(query, events, scheduler) {\n  return docChanges(query, scheduler).pipe(scan((current, changes) => combineChanges(current, changes.map(it => it.payload), events), []), distinctUntilChanged(), // cut down on unneed change cycles\n  map(changes => changes.map(c => ({\n    type: c.type,\n    payload: c\n  }))));\n}\n/**\n * Combines the total result set from the current set of changes from an incoming set\n * of changes.\n */\n\n\nfunction combineChanges(current, changes, events) {\n  changes.forEach(change => {\n    // skip unwanted change types\n    if (events.indexOf(change.type) > -1) {\n      current = combineChange(current, change);\n    }\n  });\n  return current;\n}\n/**\n * Splice arguments on top of a sliced array, to break top-level ===\n * this is useful for change-detection\n */\n\n\nfunction sliceAndSplice(original, start, deleteCount, ...args) {\n  const returnArray = original.slice();\n  returnArray.splice(start, deleteCount, ...args);\n  return returnArray;\n}\n/**\n * Creates a new sorted array from a new change.\n * Build our own because we allow filtering of action types ('added', 'removed', 'modified') before scanning\n * and so we have greater control over change detection (by breaking ===)\n */\n\n\nfunction combineChange(combined, change) {\n  switch (change.type) {\n    case 'added':\n      if (combined[change.newIndex] && combined[change.newIndex].doc.ref.isEqual(change.doc.ref)) {// Not sure why the duplicates are getting fired\n      } else {\n        return sliceAndSplice(combined, change.newIndex, 0, change);\n      }\n\n      break;\n\n    case 'modified':\n      if (combined[change.oldIndex] == null || combined[change.oldIndex].doc.ref.isEqual(change.doc.ref)) {\n        // When an item changes position we first remove it\n        // and then add it's new position\n        if (change.oldIndex !== change.newIndex) {\n          const copiedArray = combined.slice();\n          copiedArray.splice(change.oldIndex, 1);\n          copiedArray.splice(change.newIndex, 0, change);\n          return copiedArray;\n        } else {\n          return sliceAndSplice(combined, change.newIndex, 1, change);\n        }\n      }\n\n      break;\n\n    case 'removed':\n      if (combined[change.oldIndex] && combined[change.oldIndex].doc.ref.isEqual(change.doc.ref)) {\n        return sliceAndSplice(combined, change.oldIndex, 1);\n      }\n\n      break;\n  }\n\n  return combined;\n}\n\nfunction validateEventsArray(events) {\n  if (!events || events.length === 0) {\n    events = ['added', 'removed', 'modified'];\n  }\n\n  return events;\n}\n/**\n * AngularFirestoreCollection service\n *\n * This class creates a reference to a Firestore Collection. A reference and a query are provided in\n * in the constructor. The query can be the unqueried reference if no query is desired.The class\n * is generic which gives you type safety for data update methods and data streaming.\n *\n * This class uses Symbol.observable to transform into Observable using Observable.from().\n *\n * This class is rarely used directly and should be created from the AngularFirestore service.\n *\n * Example:\n *\n * const collectionRef = firebase.firestore.collection('stocks');\n * const query = collectionRef.where('price', '>', '0.01');\n * const fakeStock = new AngularFirestoreCollection<Stock>(collectionRef, query);\n *\n * // NOTE!: the updates are performed on the reference not the query\n * await fakeStock.add({ name: 'FAKE', price: 0.01 });\n *\n * // Subscribe to changes as snapshots. This provides you data updates as well as delta updates.\n * fakeStock.valueChanges().subscribe(value => console.log(value));\n */\n\n\nclass AngularFirestoreCollection {\n  /**\n   * The constructor takes in a CollectionReference and Query to provide wrapper methods\n   * for data operations and data streaming.\n   *\n   * Note: Data operation methods are done on the reference not the query. This means\n   * when you update data it is not updating data to the window of your query unless\n   * the data fits the criteria of the query. See the AssociatedRefence type for details\n   * on this implication.\n   */\n  constructor(ref, query, afs) {\n    this.ref = ref;\n    this.query = query;\n    this.afs = afs;\n  }\n  /**\n   * Listen to the latest change in the stream. This method returns changes\n   * as they occur and they are not sorted by query order. This allows you to construct\n   * your own data structure.\n   */\n\n\n  stateChanges(events) {\n    let source = docChanges(this.query, this.afs.schedulers.outsideAngular);\n\n    if (events && events.length > 0) {\n      source = source.pipe(map(actions => actions.filter(change => events.indexOf(change.type) > -1)));\n    }\n\n    return source.pipe( // We want to filter out empty arrays, but always emit at first, so the developer knows\n    // that the collection has been resolve; even if it's empty\n    startWith(undefined), pairwise(), filter(([prior, current]) => current.length > 0 || !prior), map(([prior, current]) => current), keepUnstableUntilFirst);\n  }\n  /**\n   * Create a stream of changes as they occur it time. This method is similar to stateChanges()\n   * but it collects each event in an array over time.\n   */\n\n\n  auditTrail(events) {\n    return this.stateChanges(events).pipe(scan((current, action) => [...current, ...action], []));\n  }\n  /**\n   * Create a stream of synchronized changes. This method keeps the local array in sorted\n   * query order.\n   */\n\n\n  snapshotChanges(events) {\n    const validatedEvents = validateEventsArray(events);\n    const scheduledSortedChanges$ = sortedChanges(this.query, validatedEvents, this.afs.schedulers.outsideAngular);\n    return scheduledSortedChanges$.pipe(keepUnstableUntilFirst);\n  }\n\n  valueChanges(options = {}) {\n    return fromCollectionRef(this.query, this.afs.schedulers.outsideAngular).pipe(map(actions => actions.payload.docs.map(a => {\n      if (options.idField) {\n        return Object.assign(Object.assign({}, a.data()), {\n          [options.idField]: a.id\n        });\n      } else {\n        return a.data();\n      }\n    })), keepUnstableUntilFirst);\n  }\n  /**\n   * Retrieve the results of the query once.\n   */\n\n\n  get(options) {\n    return from(this.query.get(options)).pipe(keepUnstableUntilFirst);\n  }\n  /**\n   * Add data to a collection reference.\n   *\n   * Note: Data operation methods are done on the reference not the query. This means\n   * when you update data it is not updating data to the window of your query unless\n   * the data fits the criteria of the query.\n   */\n\n\n  add(data) {\n    return this.ref.add(data);\n  }\n  /**\n   * Create a reference to a single document in a collection.\n   */\n\n\n  doc(path) {\n    // TODO is there a better way to solve this type issue\n    return new AngularFirestoreDocument(this.ref.doc(path), this.afs);\n  }\n\n}\n/**\n * AngularFirestoreDocument service\n *\n * This class creates a reference to a Firestore Document. A reference is provided in\n * in the constructor. The class is generic which gives you type safety for data update\n * methods and data streaming.\n *\n * This class uses Symbol.observable to transform into Observable using Observable.from().\n *\n * This class is rarely used directly and should be created from the AngularFirestore service.\n *\n * Example:\n *\n * const fakeStock = new AngularFirestoreDocument<Stock>(doc('stocks/FAKE'));\n * await fakeStock.set({ name: 'FAKE', price: 0.01 });\n * fakeStock.valueChanges().map(snap => {\n *   if(snap.exists) return snap.data();\n *   return null;\n * }).subscribe(value => console.log(value));\n * // OR! Transform using Observable.from() and the data is unwrapped for you\n * Observable.from(fakeStock).subscribe(value => console.log(value));\n */\n\n\nclass AngularFirestoreDocument {\n  /**\n   * The constructor takes in a DocumentReference to provide wrapper methods\n   * for data operations, data streaming, and Symbol.observable.\n   */\n  constructor(ref, afs) {\n    this.ref = ref;\n    this.afs = afs;\n  }\n  /**\n   * Create or overwrite a single document.\n   */\n\n\n  set(data, options) {\n    return this.ref.set(data, options);\n  }\n  /**\n   * Update some fields of a document without overwriting the entire document.\n   */\n\n\n  update(data) {\n    return this.ref.update(data);\n  }\n  /**\n   * Delete a document.\n   */\n\n\n  delete() {\n    return this.ref.delete();\n  }\n  /**\n   * Create a reference to a sub-collection given a path and an optional query\n   * function.\n   */\n\n\n  collection(path, queryFn) {\n    const collectionRef = this.ref.collection(path);\n    const {\n      ref,\n      query\n    } = associateQuery(collectionRef, queryFn);\n    return new AngularFirestoreCollection(ref, query, this.afs);\n  }\n  /**\n   * Listen to snapshot updates from the document.\n   */\n\n\n  snapshotChanges() {\n    const scheduledFromDocRef$ = fromDocRef(this.ref, this.afs.schedulers.outsideAngular);\n    return scheduledFromDocRef$.pipe(keepUnstableUntilFirst);\n  }\n\n  valueChanges(options = {}) {\n    return this.snapshotChanges().pipe(map(({\n      payload\n    }) => options.idField ? Object.assign(Object.assign({}, payload.data()), {\n      [options.idField]: payload.id\n    }) : payload.data()));\n  }\n  /**\n   * Retrieve the document once.\n   */\n\n\n  get(options) {\n    return from(this.ref.get(options)).pipe(keepUnstableUntilFirst);\n  }\n\n}\n/**\n * AngularFirestoreCollectionGroup service\n *\n * This class holds a reference to a Firestore Collection Group Query.\n *\n * This class uses Symbol.observable to transform into Observable using Observable.from().\n *\n * This class is rarely used directly and should be created from the AngularFirestore service.\n *\n * Example:\n *\n * const collectionGroup = firebase.firestore.collectionGroup('stocks');\n * const query = collectionRef.where('price', '>', '0.01');\n * const fakeStock = new AngularFirestoreCollectionGroup<Stock>(query, afs);\n *\n * // Subscribe to changes as snapshots. This provides you data updates as well as delta updates.\n * fakeStock.valueChanges().subscribe(value => console.log(value));\n */\n\n\nclass AngularFirestoreCollectionGroup {\n  /**\n   * The constructor takes in a CollectionGroupQuery to provide wrapper methods\n   * for data operations and data streaming.\n   */\n  constructor(query, afs) {\n    this.query = query;\n    this.afs = afs;\n  }\n  /**\n   * Listen to the latest change in the stream. This method returns changes\n   * as they occur and they are not sorted by query order. This allows you to construct\n   * your own data structure.\n   */\n\n\n  stateChanges(events) {\n    if (!events || events.length === 0) {\n      return docChanges(this.query, this.afs.schedulers.outsideAngular).pipe(keepUnstableUntilFirst);\n    }\n\n    return docChanges(this.query, this.afs.schedulers.outsideAngular).pipe(map(actions => actions.filter(change => events.indexOf(change.type) > -1)), filter(changes => changes.length > 0), keepUnstableUntilFirst);\n  }\n  /**\n   * Create a stream of changes as they occur it time. This method is similar to stateChanges()\n   * but it collects each event in an array over time.\n   */\n\n\n  auditTrail(events) {\n    return this.stateChanges(events).pipe(scan((current, action) => [...current, ...action], []));\n  }\n  /**\n   * Create a stream of synchronized changes. This method keeps the local array in sorted\n   * query order.\n   */\n\n\n  snapshotChanges(events) {\n    const validatedEvents = validateEventsArray(events);\n    const scheduledSortedChanges$ = sortedChanges(this.query, validatedEvents, this.afs.schedulers.outsideAngular);\n    return scheduledSortedChanges$.pipe(keepUnstableUntilFirst);\n  }\n\n  valueChanges(options = {}) {\n    const fromCollectionRefScheduled$ = fromCollectionRef(this.query, this.afs.schedulers.outsideAngular);\n    return fromCollectionRefScheduled$.pipe(map(actions => actions.payload.docs.map(a => {\n      if (options.idField) {\n        return Object.assign({\n          [options.idField]: a.id\n        }, a.data());\n      } else {\n        return a.data();\n      }\n    })), keepUnstableUntilFirst);\n  }\n  /**\n   * Retrieve the results of the query once.\n   */\n\n\n  get(options) {\n    return from(this.query.get(options)).pipe(keepUnstableUntilFirst);\n  }\n\n}\n/**\n * The value of this token determines whether or not the firestore will have persistance enabled\n */\n\n\nconst ENABLE_PERSISTENCE = new InjectionToken('angularfire2.enableFirestorePersistence');\nconst PERSISTENCE_SETTINGS = new InjectionToken('angularfire2.firestore.persistenceSettings');\nconst SETTINGS = new InjectionToken('angularfire2.firestore.settings');\nconst USE_EMULATOR = new InjectionToken('angularfire2.firestore.use-emulator');\n/**\n * A utility methods for associating a collection reference with\n * a query.\n *\n * @param collectionRef - A collection reference to query\n * @param queryFn - The callback to create a query\n *\n * Example:\n * const { query, ref } = associateQuery(docRef.collection('items'), ref => {\n *  return ref.where('age', '<', 200);\n * });\n */\n\nfunction associateQuery(collectionRef, queryFn = ref => ref) {\n  const query = queryFn(collectionRef);\n  const ref = collectionRef;\n  return {\n    query,\n    ref\n  };\n}\n/**\n * AngularFirestore Service\n *\n * This service is the main entry point for this feature module. It provides\n * an API for creating Collection and Reference services. These services can\n * then be used to do data updates and observable streams of the data.\n *\n * Example:\n *\n * import { Component } from '@angular/core';\n * import { AngularFirestore, AngularFirestoreCollection, AngularFirestoreDocument } from '@angular/fire/firestore';\n * import { Observable } from 'rxjs/Observable';\n * import { from } from 'rxjs/observable';\n *\n * @Component({\n *   selector: 'app-my-component',\n *   template: `\n *    <h2>Items for {{ (profile | async)?.name }}\n *    <ul>\n *       <li *ngFor=\"let item of items | async\">{{ item.name }}</li>\n *    </ul>\n *    <div class=\"control-input\">\n *       <input type=\"text\" #itemname />\n *       <button (click)=\"addItem(itemname.value)\">Add Item</button>\n *    </div>\n *   `\n * })\n * export class MyComponent implements OnInit {\n *\n *   // services for data operations and data streaming\n *   private readonly itemsRef: AngularFirestoreCollection<Item>;\n *   private readonly profileRef: AngularFirestoreDocument<Profile>;\n *\n *   // observables for template\n *   items: Observable<Item[]>;\n *   profile: Observable<Profile>;\n *\n *   // inject main service\n *   constructor(private readonly afs: AngularFirestore) {}\n *\n *   ngOnInit() {\n *     this.itemsRef = afs.collection('items', ref => ref.where('user', '==', 'davideast').limit(10));\n *     this.items = this.itemsRef.valueChanges().map(snap => snap.docs.map(data => doc.data()));\n *     // this.items = from(this.itemsRef); // you can also do this with no mapping\n *\n *     this.profileRef = afs.doc('users/davideast');\n *     this.profile = this.profileRef.valueChanges();\n *   }\n *\n *   addItem(name: string) {\n *     const user = 'davideast';\n *     this.itemsRef.add({ name, user });\n *   }\n * }\n */\n\n\nclass AngularFirestore {\n  /**\n   * Each Feature of AngularFire has a FirebaseApp injected. This way we\n   * don't rely on the main Firebase App instance and we can create named\n   * apps and use multiple apps.\n   */\n  constructor(options, name, shouldEnablePersistence, settings, // tslint:disable-next-line:ban-types\n  platformId, zone, schedulers, persistenceSettings, _useEmulator, auth, useAuthEmulator, authSettings, // can't use firebase.auth.AuthSettings here\n  tenantId, languageCode, useDeviceLanguage, persistence, _appCheckInstances) {\n    this.schedulers = schedulers;\n    const app = ɵfirebaseAppFactory(options, zone, name);\n    const useEmulator = _useEmulator;\n\n    if (auth) {\n      ɵauthFactory(app, zone, useAuthEmulator, tenantId, languageCode, useDeviceLanguage, authSettings, persistence);\n    }\n\n    [this.firestore, this.persistenceEnabled$] = ɵcacheInstance(`${app.name}.firestore`, 'AngularFirestore', app.name, () => {\n      const firestore = zone.runOutsideAngular(() => app.firestore());\n\n      if (settings) {\n        firestore.settings(settings);\n      }\n\n      if (useEmulator) {\n        firestore.useEmulator(...useEmulator);\n      }\n\n      if (shouldEnablePersistence && !isPlatformServer(platformId)) {\n        // We need to try/catch here because not all enablePersistence() failures are caught\n        // https://github.com/firebase/firebase-js-sdk/issues/608\n        const enablePersistence = () => {\n          try {\n            return from(firestore.enablePersistence(persistenceSettings || undefined).then(() => true, () => false));\n          } catch (e) {\n            if (typeof console !== 'undefined') {\n              console.warn(e);\n            }\n\n            return of(false);\n          }\n        };\n\n        return [firestore, zone.runOutsideAngular(enablePersistence)];\n      } else {\n        return [firestore, of(false)];\n      }\n    }, [settings, useEmulator, shouldEnablePersistence]);\n  }\n\n  collection(pathOrRef, queryFn) {\n    let collectionRef;\n\n    if (typeof pathOrRef === 'string') {\n      collectionRef = this.firestore.collection(pathOrRef);\n    } else {\n      collectionRef = pathOrRef;\n    }\n\n    const {\n      ref,\n      query\n    } = associateQuery(collectionRef, queryFn);\n    const refInZone = this.schedulers.ngZone.run(() => ref);\n    return new AngularFirestoreCollection(refInZone, query, this);\n  }\n  /**\n   * Create a reference to a Firestore Collection Group based on a collectionId\n   * and an optional query function to narrow the result\n   * set.\n   */\n\n\n  collectionGroup(collectionId, queryGroupFn) {\n    const queryFn = queryGroupFn || (ref => ref);\n\n    const collectionGroup = this.firestore.collectionGroup(collectionId);\n    return new AngularFirestoreCollectionGroup(queryFn(collectionGroup), this);\n  }\n\n  doc(pathOrRef) {\n    let ref;\n\n    if (typeof pathOrRef === 'string') {\n      ref = this.firestore.doc(pathOrRef);\n    } else {\n      ref = pathOrRef;\n    }\n\n    const refInZone = this.schedulers.ngZone.run(() => ref);\n    return new AngularFirestoreDocument(refInZone, this);\n  }\n  /**\n   * Returns a generated Firestore Document Id.\n   */\n\n\n  createId() {\n    return this.firestore.collection('_').doc().id;\n  }\n\n}\n\nAngularFirestore.ɵfac = function AngularFirestore_Factory(t) {\n  return new (t || AngularFirestore)(i0.ɵɵinject(FIREBASE_OPTIONS), i0.ɵɵinject(FIREBASE_APP_NAME, 8), i0.ɵɵinject(ENABLE_PERSISTENCE, 8), i0.ɵɵinject(SETTINGS, 8), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.ɵAngularFireSchedulers), i0.ɵɵinject(PERSISTENCE_SETTINGS, 8), i0.ɵɵinject(USE_EMULATOR, 8), i0.ɵɵinject(i2.AngularFireAuth, 8), i0.ɵɵinject(USE_EMULATOR$1, 8), i0.ɵɵinject(SETTINGS$1, 8), i0.ɵɵinject(TENANT_ID, 8), i0.ɵɵinject(LANGUAGE_CODE, 8), i0.ɵɵinject(USE_DEVICE_LANGUAGE, 8), i0.ɵɵinject(PERSISTENCE, 8), i0.ɵɵinject(i3.AppCheckInstances, 8));\n};\n\nAngularFirestore.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: AngularFirestore,\n  factory: AngularFirestore.ɵfac,\n  providedIn: 'any'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularFirestore, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'any'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [FIREBASE_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [FIREBASE_APP_NAME]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ENABLE_PERSISTENCE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [SETTINGS]\n      }]\n    }, {\n      type: Object,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1.ɵAngularFireSchedulers\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [PERSISTENCE_SETTINGS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [USE_EMULATOR]\n      }]\n    }, {\n      type: i2.AngularFireAuth,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [USE_EMULATOR$1]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [SETTINGS$1]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [TENANT_ID]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [LANGUAGE_CODE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [USE_DEVICE_LANGUAGE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [PERSISTENCE]\n      }]\n    }, {\n      type: i3.AppCheckInstances,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\nclass AngularFirestoreModule {\n  constructor() {\n    firebase.registerVersion('angularfire', VERSION.full, 'fst-compat');\n  }\n  /**\n   * Attempt to enable persistent storage, if possible\n   */\n\n\n  static enablePersistence(persistenceSettings) {\n    return {\n      ngModule: AngularFirestoreModule,\n      providers: [{\n        provide: ENABLE_PERSISTENCE,\n        useValue: true\n      }, {\n        provide: PERSISTENCE_SETTINGS,\n        useValue: persistenceSettings\n      }]\n    };\n  }\n\n}\n\nAngularFirestoreModule.ɵfac = function AngularFirestoreModule_Factory(t) {\n  return new (t || AngularFirestoreModule)();\n};\n\nAngularFirestoreModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: AngularFirestoreModule\n});\nAngularFirestoreModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [AngularFirestore]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularFirestoreModule, [{\n    type: NgModule,\n    args: [{\n      providers: [AngularFirestore]\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { AngularFirestore, AngularFirestoreCollection, AngularFirestoreCollectionGroup, AngularFirestoreDocument, AngularFirestoreModule, ENABLE_PERSISTENCE, PERSISTENCE_SETTINGS, SETTINGS, USE_EMULATOR, associateQuery, combineChange, combineChanges, docChanges, fromCollectionRef, fromDocRef, fromRef, sortedChanges, validateEventsArray };", "map": {"version": 3, "names": ["i0", "InjectionToken", "PLATFORM_ID", "Injectable", "Inject", "Optional", "NgModule", "asyncScheduler", "Observable", "from", "of", "i1", "keepUnstableUntilFirst", "VERSION", "startWith", "pairwise", "map", "scan", "distinctUntilChanged", "filter", "ɵfirebaseAppFactory", "ɵcacheInstance", "FIREBASE_OPTIONS", "FIREBASE_APP_NAME", "isPlatformServer", "i2", "ɵauthFactory", "USE_EMULATOR", "USE_EMULATOR$1", "SETTINGS", "SETTINGS$1", "TENANT_ID", "LANGUAGE_CODE", "USE_DEVICE_LANGUAGE", "PERSISTENCE", "i3", "firebase", "_fromRef", "ref", "scheduler", "subscriber", "unsubscribe", "schedule", "onSnapshot", "includeMetadataChanges", "fromRef", "fromDocRef", "pipe", "undefined", "priorPayload", "payload", "exists", "type", "fromCollectionRef", "do<PERSON><PERSON><PERSON><PERSON>", "query", "priorAction", "action", "actions", "change", "JSON", "stringify", "metadata", "docs", "for<PERSON>ach", "currentDoc", "currentIndex", "doc<PERSON><PERSON><PERSON>", "find", "d", "doc", "isEqual", "priorDoc", "push", "oldIndex", "newIndex", "sortedChanges", "events", "current", "changes", "combineChanges", "it", "c", "indexOf", "combineChange", "sliceAndSplice", "original", "start", "deleteCount", "args", "returnArray", "slice", "splice", "combined", "copiedArray", "validateEventsArray", "length", "AngularFirestoreCollection", "constructor", "afs", "stateChanges", "source", "schedulers", "outsideAngular", "prior", "auditTrail", "snapshotChanges", "validatedEvents", "scheduledSortedChanges$", "valueChanges", "options", "a", "idField", "Object", "assign", "data", "id", "get", "add", "path", "AngularFirestoreDocument", "set", "update", "delete", "collection", "queryFn", "collectionRef", "<PERSON><PERSON><PERSON><PERSON>", "scheduledFromDocRef$", "AngularFirestoreCollectionGroup", "fromCollectionRefScheduled$", "ENABLE_PERSISTENCE", "PERSISTENCE_SETTINGS", "AngularFirestore", "name", "shouldEnablePersistence", "settings", "platformId", "zone", "persistenceSettings", "_useEmulator", "auth", "useAuthEmulator", "authSettings", "tenantId", "languageCode", "useDeviceLanguage", "persistence", "_appCheckInstances", "app", "useEmulator", "firestore", "persistenceEnabled$", "runOutsideAngular", "enablePersistence", "then", "e", "console", "warn", "pathOrRef", "refInZone", "ngZone", "run", "collectionGroup", "collectionId", "queryGroupFn", "createId", "ɵfac", "NgZone", "ɵAngularFireSchedulers", "AngularFireAuth", "AppCheckInstances", "ɵprov", "providedIn", "decorators", "AngularFirestoreModule", "registerVersion", "full", "ngModule", "providers", "provide", "useValue", "ɵmod", "ɵinj"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/@angular/fire/fesm2015/angular-fire-compat-firestore.js"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, Injectable, Inject, Optional, NgModule } from '@angular/core';\nimport { asyncScheduler, Observable, from, of } from 'rxjs';\nimport * as i1 from '@angular/fire';\nimport { keepUnstableUntilFirst, VERSION } from '@angular/fire';\nimport { startWith, pairwise, map, scan, distinctUntilChanged, filter } from 'rxjs/operators';\nimport { ɵfirebaseAppFactory, ɵcacheInstance, FIREBASE_OPTIONS, FIREBASE_APP_NAME } from '@angular/fire/compat';\nimport { isPlatformServer } from '@angular/common';\nimport 'firebase/compat/auth';\nimport 'firebase/compat/firestore';\nimport * as i2 from '@angular/fire/compat/auth';\nimport { ɵauthFactory, USE_EMULATOR as USE_EMULATOR$1, SETTINGS as SETTINGS$1, TENANT_ID, LANGUAGE_CODE, USE_DEVICE_LANGUAGE, PERSISTENCE } from '@angular/fire/compat/auth';\nimport * as i3 from '@angular/fire/app-check';\nimport firebase from 'firebase/compat/app';\n\nfunction _fromRef(ref, scheduler = asyncScheduler) {\n    return new Observable(subscriber => {\n        let unsubscribe;\n        if (scheduler != null) {\n            scheduler.schedule(() => {\n                unsubscribe = ref.onSnapshot({ includeMetadataChanges: true }, subscriber);\n            });\n        }\n        else {\n            unsubscribe = ref.onSnapshot({ includeMetadataChanges: true }, subscriber);\n        }\n        return () => {\n            if (unsubscribe != null) {\n                unsubscribe();\n            }\n        };\n    });\n}\nfunction fromRef(ref, scheduler) {\n    return _fromRef(ref, scheduler);\n}\nfunction fromDocRef(ref, scheduler) {\n    return fromRef(ref, scheduler)\n        .pipe(startWith(undefined), pairwise(), map(([priorPayload, payload]) => {\n        if (!payload.exists) {\n            return { payload, type: 'removed' };\n        }\n        if (!(priorPayload === null || priorPayload === void 0 ? void 0 : priorPayload.exists)) {\n            return { payload, type: 'added' };\n        }\n        return { payload, type: 'modified' };\n    }));\n}\nfunction fromCollectionRef(ref, scheduler) {\n    return fromRef(ref, scheduler).pipe(map(payload => ({ payload, type: 'query' })));\n}\n\n/**\n * Return a stream of document changes on a query. These results are not in sort order but in\n * order of occurence.\n */\nfunction docChanges(query, scheduler) {\n    return fromCollectionRef(query, scheduler)\n        .pipe(startWith(undefined), pairwise(), map(([priorAction, action]) => {\n        const docChanges = action.payload.docChanges();\n        const actions = docChanges.map(change => ({ type: change.type, payload: change }));\n        // the metadata has changed from the prior emission\n        if (priorAction && JSON.stringify(priorAction.payload.metadata) !== JSON.stringify(action.payload.metadata)) {\n            // go through all the docs in payload and figure out which ones changed\n            action.payload.docs.forEach((currentDoc, currentIndex) => {\n                const docChange = docChanges.find(d => d.doc.ref.isEqual(currentDoc.ref));\n                const priorDoc = priorAction === null || priorAction === void 0 ? void 0 : priorAction.payload.docs.find(d => d.ref.isEqual(currentDoc.ref));\n                if (docChange && JSON.stringify(docChange.doc.metadata) === JSON.stringify(currentDoc.metadata) ||\n                    !docChange && priorDoc && JSON.stringify(priorDoc.metadata) === JSON.stringify(currentDoc.metadata)) {\n                    // document doesn't appear to have changed, don't log another action\n                }\n                else {\n                    // since the actions are processed in order just push onto the array\n                    actions.push({\n                        type: 'modified',\n                        payload: {\n                            oldIndex: currentIndex,\n                            newIndex: currentIndex,\n                            type: 'modified',\n                            doc: currentDoc\n                        }\n                    });\n                }\n            });\n        }\n        return actions;\n    }));\n}\n/**\n * Return a stream of document changes on a query. These results are in sort order.\n */\nfunction sortedChanges(query, events, scheduler) {\n    return docChanges(query, scheduler)\n        .pipe(scan((current, changes) => combineChanges(current, changes.map(it => it.payload), events), []), distinctUntilChanged(), // cut down on unneed change cycles\n    map(changes => changes.map(c => ({ type: c.type, payload: c }))));\n}\n/**\n * Combines the total result set from the current set of changes from an incoming set\n * of changes.\n */\nfunction combineChanges(current, changes, events) {\n    changes.forEach(change => {\n        // skip unwanted change types\n        if (events.indexOf(change.type) > -1) {\n            current = combineChange(current, change);\n        }\n    });\n    return current;\n}\n/**\n * Splice arguments on top of a sliced array, to break top-level ===\n * this is useful for change-detection\n */\nfunction sliceAndSplice(original, start, deleteCount, ...args) {\n    const returnArray = original.slice();\n    returnArray.splice(start, deleteCount, ...args);\n    return returnArray;\n}\n/**\n * Creates a new sorted array from a new change.\n * Build our own because we allow filtering of action types ('added', 'removed', 'modified') before scanning\n * and so we have greater control over change detection (by breaking ===)\n */\nfunction combineChange(combined, change) {\n    switch (change.type) {\n        case 'added':\n            if (combined[change.newIndex] && combined[change.newIndex].doc.ref.isEqual(change.doc.ref)) {\n                // Not sure why the duplicates are getting fired\n            }\n            else {\n                return sliceAndSplice(combined, change.newIndex, 0, change);\n            }\n            break;\n        case 'modified':\n            if (combined[change.oldIndex] == null || combined[change.oldIndex].doc.ref.isEqual(change.doc.ref)) {\n                // When an item changes position we first remove it\n                // and then add it's new position\n                if (change.oldIndex !== change.newIndex) {\n                    const copiedArray = combined.slice();\n                    copiedArray.splice(change.oldIndex, 1);\n                    copiedArray.splice(change.newIndex, 0, change);\n                    return copiedArray;\n                }\n                else {\n                    return sliceAndSplice(combined, change.newIndex, 1, change);\n                }\n            }\n            break;\n        case 'removed':\n            if (combined[change.oldIndex] && combined[change.oldIndex].doc.ref.isEqual(change.doc.ref)) {\n                return sliceAndSplice(combined, change.oldIndex, 1);\n            }\n            break;\n    }\n    return combined;\n}\n\nfunction validateEventsArray(events) {\n    if (!events || events.length === 0) {\n        events = ['added', 'removed', 'modified'];\n    }\n    return events;\n}\n/**\n * AngularFirestoreCollection service\n *\n * This class creates a reference to a Firestore Collection. A reference and a query are provided in\n * in the constructor. The query can be the unqueried reference if no query is desired.The class\n * is generic which gives you type safety for data update methods and data streaming.\n *\n * This class uses Symbol.observable to transform into Observable using Observable.from().\n *\n * This class is rarely used directly and should be created from the AngularFirestore service.\n *\n * Example:\n *\n * const collectionRef = firebase.firestore.collection('stocks');\n * const query = collectionRef.where('price', '>', '0.01');\n * const fakeStock = new AngularFirestoreCollection<Stock>(collectionRef, query);\n *\n * // NOTE!: the updates are performed on the reference not the query\n * await fakeStock.add({ name: 'FAKE', price: 0.01 });\n *\n * // Subscribe to changes as snapshots. This provides you data updates as well as delta updates.\n * fakeStock.valueChanges().subscribe(value => console.log(value));\n */\nclass AngularFirestoreCollection {\n    /**\n     * The constructor takes in a CollectionReference and Query to provide wrapper methods\n     * for data operations and data streaming.\n     *\n     * Note: Data operation methods are done on the reference not the query. This means\n     * when you update data it is not updating data to the window of your query unless\n     * the data fits the criteria of the query. See the AssociatedRefence type for details\n     * on this implication.\n     */\n    constructor(ref, query, afs) {\n        this.ref = ref;\n        this.query = query;\n        this.afs = afs;\n    }\n    /**\n     * Listen to the latest change in the stream. This method returns changes\n     * as they occur and they are not sorted by query order. This allows you to construct\n     * your own data structure.\n     */\n    stateChanges(events) {\n        let source = docChanges(this.query, this.afs.schedulers.outsideAngular);\n        if (events && events.length > 0) {\n            source = source.pipe(map(actions => actions.filter(change => events.indexOf(change.type) > -1)));\n        }\n        return source.pipe(\n        // We want to filter out empty arrays, but always emit at first, so the developer knows\n        // that the collection has been resolve; even if it's empty\n        startWith(undefined), pairwise(), filter(([prior, current]) => current.length > 0 || !prior), map(([prior, current]) => current), keepUnstableUntilFirst);\n    }\n    /**\n     * Create a stream of changes as they occur it time. This method is similar to stateChanges()\n     * but it collects each event in an array over time.\n     */\n    auditTrail(events) {\n        return this.stateChanges(events).pipe(scan((current, action) => [...current, ...action], []));\n    }\n    /**\n     * Create a stream of synchronized changes. This method keeps the local array in sorted\n     * query order.\n     */\n    snapshotChanges(events) {\n        const validatedEvents = validateEventsArray(events);\n        const scheduledSortedChanges$ = sortedChanges(this.query, validatedEvents, this.afs.schedulers.outsideAngular);\n        return scheduledSortedChanges$.pipe(keepUnstableUntilFirst);\n    }\n    valueChanges(options = {}) {\n        return fromCollectionRef(this.query, this.afs.schedulers.outsideAngular)\n            .pipe(map(actions => actions.payload.docs.map(a => {\n            if (options.idField) {\n                return Object.assign(Object.assign({}, a.data()), { [options.idField]: a.id });\n            }\n            else {\n                return a.data();\n            }\n        })), keepUnstableUntilFirst);\n    }\n    /**\n     * Retrieve the results of the query once.\n     */\n    get(options) {\n        return from(this.query.get(options)).pipe(keepUnstableUntilFirst);\n    }\n    /**\n     * Add data to a collection reference.\n     *\n     * Note: Data operation methods are done on the reference not the query. This means\n     * when you update data it is not updating data to the window of your query unless\n     * the data fits the criteria of the query.\n     */\n    add(data) {\n        return this.ref.add(data);\n    }\n    /**\n     * Create a reference to a single document in a collection.\n     */\n    doc(path) {\n        // TODO is there a better way to solve this type issue\n        return new AngularFirestoreDocument(this.ref.doc(path), this.afs);\n    }\n}\n\n/**\n * AngularFirestoreDocument service\n *\n * This class creates a reference to a Firestore Document. A reference is provided in\n * in the constructor. The class is generic which gives you type safety for data update\n * methods and data streaming.\n *\n * This class uses Symbol.observable to transform into Observable using Observable.from().\n *\n * This class is rarely used directly and should be created from the AngularFirestore service.\n *\n * Example:\n *\n * const fakeStock = new AngularFirestoreDocument<Stock>(doc('stocks/FAKE'));\n * await fakeStock.set({ name: 'FAKE', price: 0.01 });\n * fakeStock.valueChanges().map(snap => {\n *   if(snap.exists) return snap.data();\n *   return null;\n * }).subscribe(value => console.log(value));\n * // OR! Transform using Observable.from() and the data is unwrapped for you\n * Observable.from(fakeStock).subscribe(value => console.log(value));\n */\nclass AngularFirestoreDocument {\n    /**\n     * The constructor takes in a DocumentReference to provide wrapper methods\n     * for data operations, data streaming, and Symbol.observable.\n     */\n    constructor(ref, afs) {\n        this.ref = ref;\n        this.afs = afs;\n    }\n    /**\n     * Create or overwrite a single document.\n     */\n    set(data, options) {\n        return this.ref.set(data, options);\n    }\n    /**\n     * Update some fields of a document without overwriting the entire document.\n     */\n    update(data) {\n        return this.ref.update(data);\n    }\n    /**\n     * Delete a document.\n     */\n    delete() {\n        return this.ref.delete();\n    }\n    /**\n     * Create a reference to a sub-collection given a path and an optional query\n     * function.\n     */\n    collection(path, queryFn) {\n        const collectionRef = this.ref.collection(path);\n        const { ref, query } = associateQuery(collectionRef, queryFn);\n        return new AngularFirestoreCollection(ref, query, this.afs);\n    }\n    /**\n     * Listen to snapshot updates from the document.\n     */\n    snapshotChanges() {\n        const scheduledFromDocRef$ = fromDocRef(this.ref, this.afs.schedulers.outsideAngular);\n        return scheduledFromDocRef$.pipe(keepUnstableUntilFirst);\n    }\n    valueChanges(options = {}) {\n        return this.snapshotChanges().pipe(map(({ payload }) => options.idField ? Object.assign(Object.assign({}, payload.data()), { [options.idField]: payload.id }) : payload.data()));\n    }\n    /**\n     * Retrieve the document once.\n     */\n    get(options) {\n        return from(this.ref.get(options)).pipe(keepUnstableUntilFirst);\n    }\n}\n\n/**\n * AngularFirestoreCollectionGroup service\n *\n * This class holds a reference to a Firestore Collection Group Query.\n *\n * This class uses Symbol.observable to transform into Observable using Observable.from().\n *\n * This class is rarely used directly and should be created from the AngularFirestore service.\n *\n * Example:\n *\n * const collectionGroup = firebase.firestore.collectionGroup('stocks');\n * const query = collectionRef.where('price', '>', '0.01');\n * const fakeStock = new AngularFirestoreCollectionGroup<Stock>(query, afs);\n *\n * // Subscribe to changes as snapshots. This provides you data updates as well as delta updates.\n * fakeStock.valueChanges().subscribe(value => console.log(value));\n */\nclass AngularFirestoreCollectionGroup {\n    /**\n     * The constructor takes in a CollectionGroupQuery to provide wrapper methods\n     * for data operations and data streaming.\n     */\n    constructor(query, afs) {\n        this.query = query;\n        this.afs = afs;\n    }\n    /**\n     * Listen to the latest change in the stream. This method returns changes\n     * as they occur and they are not sorted by query order. This allows you to construct\n     * your own data structure.\n     */\n    stateChanges(events) {\n        if (!events || events.length === 0) {\n            return docChanges(this.query, this.afs.schedulers.outsideAngular).pipe(keepUnstableUntilFirst);\n        }\n        return docChanges(this.query, this.afs.schedulers.outsideAngular)\n            .pipe(map(actions => actions.filter(change => events.indexOf(change.type) > -1)), filter(changes => changes.length > 0), keepUnstableUntilFirst);\n    }\n    /**\n     * Create a stream of changes as they occur it time. This method is similar to stateChanges()\n     * but it collects each event in an array over time.\n     */\n    auditTrail(events) {\n        return this.stateChanges(events).pipe(scan((current, action) => [...current, ...action], []));\n    }\n    /**\n     * Create a stream of synchronized changes. This method keeps the local array in sorted\n     * query order.\n     */\n    snapshotChanges(events) {\n        const validatedEvents = validateEventsArray(events);\n        const scheduledSortedChanges$ = sortedChanges(this.query, validatedEvents, this.afs.schedulers.outsideAngular);\n        return scheduledSortedChanges$.pipe(keepUnstableUntilFirst);\n    }\n    valueChanges(options = {}) {\n        const fromCollectionRefScheduled$ = fromCollectionRef(this.query, this.afs.schedulers.outsideAngular);\n        return fromCollectionRefScheduled$\n            .pipe(map(actions => actions.payload.docs.map(a => {\n            if (options.idField) {\n                return Object.assign({ [options.idField]: a.id }, a.data());\n            }\n            else {\n                return a.data();\n            }\n        })), keepUnstableUntilFirst);\n    }\n    /**\n     * Retrieve the results of the query once.\n     */\n    get(options) {\n        return from(this.query.get(options)).pipe(keepUnstableUntilFirst);\n    }\n}\n\n/**\n * The value of this token determines whether or not the firestore will have persistance enabled\n */\nconst ENABLE_PERSISTENCE = new InjectionToken('angularfire2.enableFirestorePersistence');\nconst PERSISTENCE_SETTINGS = new InjectionToken('angularfire2.firestore.persistenceSettings');\nconst SETTINGS = new InjectionToken('angularfire2.firestore.settings');\nconst USE_EMULATOR = new InjectionToken('angularfire2.firestore.use-emulator');\n/**\n * A utility methods for associating a collection reference with\n * a query.\n *\n * @param collectionRef - A collection reference to query\n * @param queryFn - The callback to create a query\n *\n * Example:\n * const { query, ref } = associateQuery(docRef.collection('items'), ref => {\n *  return ref.where('age', '<', 200);\n * });\n */\nfunction associateQuery(collectionRef, queryFn = ref => ref) {\n    const query = queryFn(collectionRef);\n    const ref = collectionRef;\n    return { query, ref };\n}\n/**\n * AngularFirestore Service\n *\n * This service is the main entry point for this feature module. It provides\n * an API for creating Collection and Reference services. These services can\n * then be used to do data updates and observable streams of the data.\n *\n * Example:\n *\n * import { Component } from '@angular/core';\n * import { AngularFirestore, AngularFirestoreCollection, AngularFirestoreDocument } from '@angular/fire/firestore';\n * import { Observable } from 'rxjs/Observable';\n * import { from } from 'rxjs/observable';\n *\n * @Component({\n *   selector: 'app-my-component',\n *   template: `\n *    <h2>Items for {{ (profile | async)?.name }}\n *    <ul>\n *       <li *ngFor=\"let item of items | async\">{{ item.name }}</li>\n *    </ul>\n *    <div class=\"control-input\">\n *       <input type=\"text\" #itemname />\n *       <button (click)=\"addItem(itemname.value)\">Add Item</button>\n *    </div>\n *   `\n * })\n * export class MyComponent implements OnInit {\n *\n *   // services for data operations and data streaming\n *   private readonly itemsRef: AngularFirestoreCollection<Item>;\n *   private readonly profileRef: AngularFirestoreDocument<Profile>;\n *\n *   // observables for template\n *   items: Observable<Item[]>;\n *   profile: Observable<Profile>;\n *\n *   // inject main service\n *   constructor(private readonly afs: AngularFirestore) {}\n *\n *   ngOnInit() {\n *     this.itemsRef = afs.collection('items', ref => ref.where('user', '==', 'davideast').limit(10));\n *     this.items = this.itemsRef.valueChanges().map(snap => snap.docs.map(data => doc.data()));\n *     // this.items = from(this.itemsRef); // you can also do this with no mapping\n *\n *     this.profileRef = afs.doc('users/davideast');\n *     this.profile = this.profileRef.valueChanges();\n *   }\n *\n *   addItem(name: string) {\n *     const user = 'davideast';\n *     this.itemsRef.add({ name, user });\n *   }\n * }\n */\nclass AngularFirestore {\n    /**\n     * Each Feature of AngularFire has a FirebaseApp injected. This way we\n     * don't rely on the main Firebase App instance and we can create named\n     * apps and use multiple apps.\n     */\n    constructor(options, name, shouldEnablePersistence, settings, \n    // tslint:disable-next-line:ban-types\n    platformId, zone, schedulers, persistenceSettings, _useEmulator, auth, useAuthEmulator, authSettings, // can't use firebase.auth.AuthSettings here\n    tenantId, languageCode, useDeviceLanguage, persistence, _appCheckInstances) {\n        this.schedulers = schedulers;\n        const app = ɵfirebaseAppFactory(options, zone, name);\n        const useEmulator = _useEmulator;\n        if (auth) {\n            ɵauthFactory(app, zone, useAuthEmulator, tenantId, languageCode, useDeviceLanguage, authSettings, persistence);\n        }\n        [this.firestore, this.persistenceEnabled$] = ɵcacheInstance(`${app.name}.firestore`, 'AngularFirestore', app.name, () => {\n            const firestore = zone.runOutsideAngular(() => app.firestore());\n            if (settings) {\n                firestore.settings(settings);\n            }\n            if (useEmulator) {\n                firestore.useEmulator(...useEmulator);\n            }\n            if (shouldEnablePersistence && !isPlatformServer(platformId)) {\n                // We need to try/catch here because not all enablePersistence() failures are caught\n                // https://github.com/firebase/firebase-js-sdk/issues/608\n                const enablePersistence = () => {\n                    try {\n                        return from(firestore.enablePersistence(persistenceSettings || undefined).then(() => true, () => false));\n                    }\n                    catch (e) {\n                        if (typeof console !== 'undefined') {\n                            console.warn(e);\n                        }\n                        return of(false);\n                    }\n                };\n                return [firestore, zone.runOutsideAngular(enablePersistence)];\n            }\n            else {\n                return [firestore, of(false)];\n            }\n        }, [settings, useEmulator, shouldEnablePersistence]);\n    }\n    collection(pathOrRef, queryFn) {\n        let collectionRef;\n        if (typeof pathOrRef === 'string') {\n            collectionRef = this.firestore.collection(pathOrRef);\n        }\n        else {\n            collectionRef = pathOrRef;\n        }\n        const { ref, query } = associateQuery(collectionRef, queryFn);\n        const refInZone = this.schedulers.ngZone.run(() => ref);\n        return new AngularFirestoreCollection(refInZone, query, this);\n    }\n    /**\n     * Create a reference to a Firestore Collection Group based on a collectionId\n     * and an optional query function to narrow the result\n     * set.\n     */\n    collectionGroup(collectionId, queryGroupFn) {\n        const queryFn = queryGroupFn || (ref => ref);\n        const collectionGroup = this.firestore.collectionGroup(collectionId);\n        return new AngularFirestoreCollectionGroup(queryFn(collectionGroup), this);\n    }\n    doc(pathOrRef) {\n        let ref;\n        if (typeof pathOrRef === 'string') {\n            ref = this.firestore.doc(pathOrRef);\n        }\n        else {\n            ref = pathOrRef;\n        }\n        const refInZone = this.schedulers.ngZone.run(() => ref);\n        return new AngularFirestoreDocument(refInZone, this);\n    }\n    /**\n     * Returns a generated Firestore Document Id.\n     */\n    createId() {\n        return this.firestore.collection('_').doc().id;\n    }\n}\nAngularFirestore.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFirestore, deps: [{ token: FIREBASE_OPTIONS }, { token: FIREBASE_APP_NAME, optional: true }, { token: ENABLE_PERSISTENCE, optional: true }, { token: SETTINGS, optional: true }, { token: PLATFORM_ID }, { token: i0.NgZone }, { token: i1.ɵAngularFireSchedulers }, { token: PERSISTENCE_SETTINGS, optional: true }, { token: USE_EMULATOR, optional: true }, { token: i2.AngularFireAuth, optional: true }, { token: USE_EMULATOR$1, optional: true }, { token: SETTINGS$1, optional: true }, { token: TENANT_ID, optional: true }, { token: LANGUAGE_CODE, optional: true }, { token: USE_DEVICE_LANGUAGE, optional: true }, { token: PERSISTENCE, optional: true }, { token: i3.AppCheckInstances, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nAngularFirestore.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFirestore, providedIn: 'any' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFirestore, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'any'\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [FIREBASE_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [FIREBASE_APP_NAME]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ENABLE_PERSISTENCE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [SETTINGS]\n                }] }, { type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.NgZone }, { type: i1.ɵAngularFireSchedulers }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [PERSISTENCE_SETTINGS]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [USE_EMULATOR]\n                }] }, { type: i2.AngularFireAuth, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [USE_EMULATOR$1]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [SETTINGS$1]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [TENANT_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [LANGUAGE_CODE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [USE_DEVICE_LANGUAGE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [PERSISTENCE]\n                }] }, { type: i3.AppCheckInstances, decorators: [{\n                    type: Optional\n                }] }]; } });\n\nclass AngularFirestoreModule {\n    constructor() {\n        firebase.registerVersion('angularfire', VERSION.full, 'fst-compat');\n    }\n    /**\n     * Attempt to enable persistent storage, if possible\n     */\n    static enablePersistence(persistenceSettings) {\n        return {\n            ngModule: AngularFirestoreModule,\n            providers: [\n                { provide: ENABLE_PERSISTENCE, useValue: true },\n                { provide: PERSISTENCE_SETTINGS, useValue: persistenceSettings },\n            ]\n        };\n    }\n}\nAngularFirestoreModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFirestoreModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nAngularFirestoreModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFirestoreModule });\nAngularFirestoreModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFirestoreModule, providers: [AngularFirestore] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFirestoreModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [AngularFirestore]\n                }]\n        }], ctorParameters: function () { return []; } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularFirestore, AngularFirestoreCollection, AngularFirestoreCollectionGroup, AngularFirestoreDocument, AngularFirestoreModule, ENABLE_PERSISTENCE, PERSISTENCE_SETTINGS, SETTINGS, USE_EMULATOR, associateQuery, combineChange, combineChanges, docChanges, fromCollectionRef, fromDocRef, fromRef, sortedChanges, validateEventsArray };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,WAAzB,EAAsCC,UAAtC,EAAkDC,MAAlD,EAA0DC,QAA1D,EAAoEC,QAApE,QAAoF,eAApF;AACA,SAASC,cAAT,EAAyBC,UAAzB,EAAqCC,IAArC,EAA2CC,EAA3C,QAAqD,MAArD;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,sBAAT,EAAiCC,OAAjC,QAAgD,eAAhD;AACA,SAASC,SAAT,EAAoBC,QAApB,EAA8BC,GAA9B,EAAmCC,IAAnC,EAAyCC,oBAAzC,EAA+DC,MAA/D,QAA6E,gBAA7E;AACA,SAASC,mBAAT,EAA8BC,cAA9B,EAA8CC,gBAA9C,EAAgEC,iBAAhE,QAAyF,sBAAzF;AACA,SAASC,gBAAT,QAAiC,iBAAjC;AACA,OAAO,sBAAP;AACA,OAAO,2BAAP;AACA,OAAO,KAAKC,EAAZ,MAAoB,2BAApB;AACA,SAASC,YAAT,EAAuBC,YAAY,IAAIC,cAAvC,EAAuDC,QAAQ,IAAIC,UAAnE,EAA+EC,SAA/E,EAA0FC,aAA1F,EAAyGC,mBAAzG,EAA8HC,WAA9H,QAAiJ,2BAAjJ;AACA,OAAO,KAAKC,EAAZ,MAAoB,yBAApB;AACA,OAAOC,QAAP,MAAqB,qBAArB;;AAEA,SAASC,QAAT,CAAkBC,GAAlB,EAAuBC,SAAS,GAAGhC,cAAnC,EAAmD;EAC/C,OAAO,IAAIC,UAAJ,CAAegC,UAAU,IAAI;IAChC,IAAIC,WAAJ;;IACA,IAAIF,SAAS,IAAI,IAAjB,EAAuB;MACnBA,SAAS,CAACG,QAAV,CAAmB,MAAM;QACrBD,WAAW,GAAGH,GAAG,CAACK,UAAJ,CAAe;UAAEC,sBAAsB,EAAE;QAA1B,CAAf,EAAiDJ,UAAjD,CAAd;MACH,CAFD;IAGH,CAJD,MAKK;MACDC,WAAW,GAAGH,GAAG,CAACK,UAAJ,CAAe;QAAEC,sBAAsB,EAAE;MAA1B,CAAf,EAAiDJ,UAAjD,CAAd;IACH;;IACD,OAAO,MAAM;MACT,IAAIC,WAAW,IAAI,IAAnB,EAAyB;QACrBA,WAAW;MACd;IACJ,CAJD;EAKH,CAfM,CAAP;AAgBH;;AACD,SAASI,OAAT,CAAiBP,GAAjB,EAAsBC,SAAtB,EAAiC;EAC7B,OAAOF,QAAQ,CAACC,GAAD,EAAMC,SAAN,CAAf;AACH;;AACD,SAASO,UAAT,CAAoBR,GAApB,EAAyBC,SAAzB,EAAoC;EAChC,OAAOM,OAAO,CAACP,GAAD,EAAMC,SAAN,CAAP,CACFQ,IADE,CACGjC,SAAS,CAACkC,SAAD,CADZ,EACyBjC,QAAQ,EADjC,EACqCC,GAAG,CAAC,CAAC,CAACiC,YAAD,EAAeC,OAAf,CAAD,KAA6B;IACzE,IAAI,CAACA,OAAO,CAACC,MAAb,EAAqB;MACjB,OAAO;QAAED,OAAF;QAAWE,IAAI,EAAE;MAAjB,CAAP;IACH;;IACD,IAAI,EAAEH,YAAY,KAAK,IAAjB,IAAyBA,YAAY,KAAK,KAAK,CAA/C,GAAmD,KAAK,CAAxD,GAA4DA,YAAY,CAACE,MAA3E,CAAJ,EAAwF;MACpF,OAAO;QAAED,OAAF;QAAWE,IAAI,EAAE;MAAjB,CAAP;IACH;;IACD,OAAO;MAAEF,OAAF;MAAWE,IAAI,EAAE;IAAjB,CAAP;EACH,CAR8C,CADxC,CAAP;AAUH;;AACD,SAASC,iBAAT,CAA2Bf,GAA3B,EAAgCC,SAAhC,EAA2C;EACvC,OAAOM,OAAO,CAACP,GAAD,EAAMC,SAAN,CAAP,CAAwBQ,IAAxB,CAA6B/B,GAAG,CAACkC,OAAO,KAAK;IAAEA,OAAF;IAAWE,IAAI,EAAE;EAAjB,CAAL,CAAR,CAAhC,CAAP;AACH;AAED;AACA;AACA;AACA;;;AACA,SAASE,UAAT,CAAoBC,KAApB,EAA2BhB,SAA3B,EAAsC;EAClC,OAAOc,iBAAiB,CAACE,KAAD,EAAQhB,SAAR,CAAjB,CACFQ,IADE,CACGjC,SAAS,CAACkC,SAAD,CADZ,EACyBjC,QAAQ,EADjC,EACqCC,GAAG,CAAC,CAAC,CAACwC,WAAD,EAAcC,MAAd,CAAD,KAA2B;IACvE,MAAMH,UAAU,GAAGG,MAAM,CAACP,OAAP,CAAeI,UAAf,EAAnB;IACA,MAAMI,OAAO,GAAGJ,UAAU,CAACtC,GAAX,CAAe2C,MAAM,KAAK;MAAEP,IAAI,EAAEO,MAAM,CAACP,IAAf;MAAqBF,OAAO,EAAES;IAA9B,CAAL,CAArB,CAAhB,CAFuE,CAGvE;;IACA,IAAIH,WAAW,IAAII,IAAI,CAACC,SAAL,CAAeL,WAAW,CAACN,OAAZ,CAAoBY,QAAnC,MAAiDF,IAAI,CAACC,SAAL,CAAeJ,MAAM,CAACP,OAAP,CAAeY,QAA9B,CAApE,EAA6G;MACzG;MACAL,MAAM,CAACP,OAAP,CAAea,IAAf,CAAoBC,OAApB,CAA4B,CAACC,UAAD,EAAaC,YAAb,KAA8B;QACtD,MAAMC,SAAS,GAAGb,UAAU,CAACc,IAAX,CAAgBC,CAAC,IAAIA,CAAC,CAACC,GAAF,CAAMhC,GAAN,CAAUiC,OAAV,CAAkBN,UAAU,CAAC3B,GAA7B,CAArB,CAAlB;QACA,MAAMkC,QAAQ,GAAGhB,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACN,OAAZ,CAAoBa,IAApB,CAAyBK,IAAzB,CAA8BC,CAAC,IAAIA,CAAC,CAAC/B,GAAF,CAAMiC,OAAN,CAAcN,UAAU,CAAC3B,GAAzB,CAAnC,CAA3E;;QACA,IAAI6B,SAAS,IAAIP,IAAI,CAACC,SAAL,CAAeM,SAAS,CAACG,GAAV,CAAcR,QAA7B,MAA2CF,IAAI,CAACC,SAAL,CAAeI,UAAU,CAACH,QAA1B,CAAxD,IACA,CAACK,SAAD,IAAcK,QAAd,IAA0BZ,IAAI,CAACC,SAAL,CAAeW,QAAQ,CAACV,QAAxB,MAAsCF,IAAI,CAACC,SAAL,CAAeI,UAAU,CAACH,QAA1B,CADpE,EACyG,CACrG;QACH,CAHD,MAIK;UACD;UACAJ,OAAO,CAACe,IAAR,CAAa;YACTrB,IAAI,EAAE,UADG;YAETF,OAAO,EAAE;cACLwB,QAAQ,EAAER,YADL;cAELS,QAAQ,EAAET,YAFL;cAGLd,IAAI,EAAE,UAHD;cAILkB,GAAG,EAAEL;YAJA;UAFA,CAAb;QASH;MACJ,CAnBD;IAoBH;;IACD,OAAOP,OAAP;EACH,CA5B8C,CADxC,CAAP;AA8BH;AACD;AACA;AACA;;;AACA,SAASkB,aAAT,CAAuBrB,KAAvB,EAA8BsB,MAA9B,EAAsCtC,SAAtC,EAAiD;EAC7C,OAAOe,UAAU,CAACC,KAAD,EAAQhB,SAAR,CAAV,CACFQ,IADE,CACG9B,IAAI,CAAC,CAAC6D,OAAD,EAAUC,OAAV,KAAsBC,cAAc,CAACF,OAAD,EAAUC,OAAO,CAAC/D,GAAR,CAAYiE,EAAE,IAAIA,EAAE,CAAC/B,OAArB,CAAV,EAAyC2B,MAAzC,CAArC,EAAuF,EAAvF,CADP,EACmG3D,oBAAoB,EADvH,EAC2H;EAClIF,GAAG,CAAC+D,OAAO,IAAIA,OAAO,CAAC/D,GAAR,CAAYkE,CAAC,KAAK;IAAE9B,IAAI,EAAE8B,CAAC,CAAC9B,IAAV;IAAgBF,OAAO,EAAEgC;EAAzB,CAAL,CAAb,CAAZ,CAFI,CAAP;AAGH;AACD;AACA;AACA;AACA;;;AACA,SAASF,cAAT,CAAwBF,OAAxB,EAAiCC,OAAjC,EAA0CF,MAA1C,EAAkD;EAC9CE,OAAO,CAACf,OAAR,CAAgBL,MAAM,IAAI;IACtB;IACA,IAAIkB,MAAM,CAACM,OAAP,CAAexB,MAAM,CAACP,IAAtB,IAA8B,CAAC,CAAnC,EAAsC;MAClC0B,OAAO,GAAGM,aAAa,CAACN,OAAD,EAAUnB,MAAV,CAAvB;IACH;EACJ,CALD;EAMA,OAAOmB,OAAP;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASO,cAAT,CAAwBC,QAAxB,EAAkCC,KAAlC,EAAyCC,WAAzC,EAAsD,GAAGC,IAAzD,EAA+D;EAC3D,MAAMC,WAAW,GAAGJ,QAAQ,CAACK,KAAT,EAApB;EACAD,WAAW,CAACE,MAAZ,CAAmBL,KAAnB,EAA0BC,WAA1B,EAAuC,GAAGC,IAA1C;EACA,OAAOC,WAAP;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASN,aAAT,CAAuBS,QAAvB,EAAiClC,MAAjC,EAAyC;EACrC,QAAQA,MAAM,CAACP,IAAf;IACI,KAAK,OAAL;MACI,IAAIyC,QAAQ,CAAClC,MAAM,CAACgB,QAAR,CAAR,IAA6BkB,QAAQ,CAAClC,MAAM,CAACgB,QAAR,CAAR,CAA0BL,GAA1B,CAA8BhC,GAA9B,CAAkCiC,OAAlC,CAA0CZ,MAAM,CAACW,GAAP,CAAWhC,GAArD,CAAjC,EAA4F,CACxF;MACH,CAFD,MAGK;QACD,OAAO+C,cAAc,CAACQ,QAAD,EAAWlC,MAAM,CAACgB,QAAlB,EAA4B,CAA5B,EAA+BhB,MAA/B,CAArB;MACH;;MACD;;IACJ,KAAK,UAAL;MACI,IAAIkC,QAAQ,CAAClC,MAAM,CAACe,QAAR,CAAR,IAA6B,IAA7B,IAAqCmB,QAAQ,CAAClC,MAAM,CAACe,QAAR,CAAR,CAA0BJ,GAA1B,CAA8BhC,GAA9B,CAAkCiC,OAAlC,CAA0CZ,MAAM,CAACW,GAAP,CAAWhC,GAArD,CAAzC,EAAoG;QAChG;QACA;QACA,IAAIqB,MAAM,CAACe,QAAP,KAAoBf,MAAM,CAACgB,QAA/B,EAAyC;UACrC,MAAMmB,WAAW,GAAGD,QAAQ,CAACF,KAAT,EAApB;UACAG,WAAW,CAACF,MAAZ,CAAmBjC,MAAM,CAACe,QAA1B,EAAoC,CAApC;UACAoB,WAAW,CAACF,MAAZ,CAAmBjC,MAAM,CAACgB,QAA1B,EAAoC,CAApC,EAAuChB,MAAvC;UACA,OAAOmC,WAAP;QACH,CALD,MAMK;UACD,OAAOT,cAAc,CAACQ,QAAD,EAAWlC,MAAM,CAACgB,QAAlB,EAA4B,CAA5B,EAA+BhB,MAA/B,CAArB;QACH;MACJ;;MACD;;IACJ,KAAK,SAAL;MACI,IAAIkC,QAAQ,CAAClC,MAAM,CAACe,QAAR,CAAR,IAA6BmB,QAAQ,CAAClC,MAAM,CAACe,QAAR,CAAR,CAA0BJ,GAA1B,CAA8BhC,GAA9B,CAAkCiC,OAAlC,CAA0CZ,MAAM,CAACW,GAAP,CAAWhC,GAArD,CAAjC,EAA4F;QACxF,OAAO+C,cAAc,CAACQ,QAAD,EAAWlC,MAAM,CAACe,QAAlB,EAA4B,CAA5B,CAArB;MACH;;MACD;EA5BR;;EA8BA,OAAOmB,QAAP;AACH;;AAED,SAASE,mBAAT,CAA6BlB,MAA7B,EAAqC;EACjC,IAAI,CAACA,MAAD,IAAWA,MAAM,CAACmB,MAAP,KAAkB,CAAjC,EAAoC;IAChCnB,MAAM,GAAG,CAAC,OAAD,EAAU,SAAV,EAAqB,UAArB,CAAT;EACH;;EACD,OAAOA,MAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMoB,0BAAN,CAAiC;EAC7B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,WAAW,CAAC5D,GAAD,EAAMiB,KAAN,EAAa4C,GAAb,EAAkB;IACzB,KAAK7D,GAAL,GAAWA,GAAX;IACA,KAAKiB,KAAL,GAAaA,KAAb;IACA,KAAK4C,GAAL,GAAWA,GAAX;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIC,YAAY,CAACvB,MAAD,EAAS;IACjB,IAAIwB,MAAM,GAAG/C,UAAU,CAAC,KAAKC,KAAN,EAAa,KAAK4C,GAAL,CAASG,UAAT,CAAoBC,cAAjC,CAAvB;;IACA,IAAI1B,MAAM,IAAIA,MAAM,CAACmB,MAAP,GAAgB,CAA9B,EAAiC;MAC7BK,MAAM,GAAGA,MAAM,CAACtD,IAAP,CAAY/B,GAAG,CAAC0C,OAAO,IAAIA,OAAO,CAACvC,MAAR,CAAewC,MAAM,IAAIkB,MAAM,CAACM,OAAP,CAAexB,MAAM,CAACP,IAAtB,IAA8B,CAAC,CAAxD,CAAZ,CAAf,CAAT;IACH;;IACD,OAAOiD,MAAM,CAACtD,IAAP,EACP;IACA;IACAjC,SAAS,CAACkC,SAAD,CAHF,EAGejC,QAAQ,EAHvB,EAG2BI,MAAM,CAAC,CAAC,CAACqF,KAAD,EAAQ1B,OAAR,CAAD,KAAsBA,OAAO,CAACkB,MAAR,GAAiB,CAAjB,IAAsB,CAACQ,KAA9C,CAHjC,EAGuFxF,GAAG,CAAC,CAAC,CAACwF,KAAD,EAAQ1B,OAAR,CAAD,KAAsBA,OAAvB,CAH1F,EAG2HlE,sBAH3H,CAAP;EAIH;EACD;AACJ;AACA;AACA;;;EACI6F,UAAU,CAAC5B,MAAD,EAAS;IACf,OAAO,KAAKuB,YAAL,CAAkBvB,MAAlB,EAA0B9B,IAA1B,CAA+B9B,IAAI,CAAC,CAAC6D,OAAD,EAAUrB,MAAV,KAAqB,CAAC,GAAGqB,OAAJ,EAAa,GAAGrB,MAAhB,CAAtB,EAA+C,EAA/C,CAAnC,CAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIiD,eAAe,CAAC7B,MAAD,EAAS;IACpB,MAAM8B,eAAe,GAAGZ,mBAAmB,CAAClB,MAAD,CAA3C;IACA,MAAM+B,uBAAuB,GAAGhC,aAAa,CAAC,KAAKrB,KAAN,EAAaoD,eAAb,EAA8B,KAAKR,GAAL,CAASG,UAAT,CAAoBC,cAAlD,CAA7C;IACA,OAAOK,uBAAuB,CAAC7D,IAAxB,CAA6BnC,sBAA7B,CAAP;EACH;;EACDiG,YAAY,CAACC,OAAO,GAAG,EAAX,EAAe;IACvB,OAAOzD,iBAAiB,CAAC,KAAKE,KAAN,EAAa,KAAK4C,GAAL,CAASG,UAAT,CAAoBC,cAAjC,CAAjB,CACFxD,IADE,CACG/B,GAAG,CAAC0C,OAAO,IAAIA,OAAO,CAACR,OAAR,CAAgBa,IAAhB,CAAqB/C,GAArB,CAAyB+F,CAAC,IAAI;MACnD,IAAID,OAAO,CAACE,OAAZ,EAAqB;QACjB,OAAOC,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBH,CAAC,CAACI,IAAF,EAAlB,CAAd,EAA2C;UAAE,CAACL,OAAO,CAACE,OAAT,GAAmBD,CAAC,CAACK;QAAvB,CAA3C,CAAP;MACH,CAFD,MAGK;QACD,OAAOL,CAAC,CAACI,IAAF,EAAP;MACH;IACJ,CAPwB,CAAZ,CADN,EAQFvG,sBARE,CAAP;EASH;EACD;AACJ;AACA;;;EACIyG,GAAG,CAACP,OAAD,EAAU;IACT,OAAOrG,IAAI,CAAC,KAAK8C,KAAL,CAAW8D,GAAX,CAAeP,OAAf,CAAD,CAAJ,CAA8B/D,IAA9B,CAAmCnC,sBAAnC,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI0G,GAAG,CAACH,IAAD,EAAO;IACN,OAAO,KAAK7E,GAAL,CAASgF,GAAT,CAAaH,IAAb,CAAP;EACH;EACD;AACJ;AACA;;;EACI7C,GAAG,CAACiD,IAAD,EAAO;IACN;IACA,OAAO,IAAIC,wBAAJ,CAA6B,KAAKlF,GAAL,CAASgC,GAAT,CAAaiD,IAAb,CAA7B,EAAiD,KAAKpB,GAAtD,CAAP;EACH;;AA/E4B;AAkFjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMqB,wBAAN,CAA+B;EAC3B;AACJ;AACA;AACA;EACItB,WAAW,CAAC5D,GAAD,EAAM6D,GAAN,EAAW;IAClB,KAAK7D,GAAL,GAAWA,GAAX;IACA,KAAK6D,GAAL,GAAWA,GAAX;EACH;EACD;AACJ;AACA;;;EACIsB,GAAG,CAACN,IAAD,EAAOL,OAAP,EAAgB;IACf,OAAO,KAAKxE,GAAL,CAASmF,GAAT,CAAaN,IAAb,EAAmBL,OAAnB,CAAP;EACH;EACD;AACJ;AACA;;;EACIY,MAAM,CAACP,IAAD,EAAO;IACT,OAAO,KAAK7E,GAAL,CAASoF,MAAT,CAAgBP,IAAhB,CAAP;EACH;EACD;AACJ;AACA;;;EACIQ,MAAM,GAAG;IACL,OAAO,KAAKrF,GAAL,CAASqF,MAAT,EAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIC,UAAU,CAACL,IAAD,EAAOM,OAAP,EAAgB;IACtB,MAAMC,aAAa,GAAG,KAAKxF,GAAL,CAASsF,UAAT,CAAoBL,IAApB,CAAtB;IACA,MAAM;MAAEjF,GAAF;MAAOiB;IAAP,IAAiBwE,cAAc,CAACD,aAAD,EAAgBD,OAAhB,CAArC;IACA,OAAO,IAAI5B,0BAAJ,CAA+B3D,GAA/B,EAAoCiB,KAApC,EAA2C,KAAK4C,GAAhD,CAAP;EACH;EACD;AACJ;AACA;;;EACIO,eAAe,GAAG;IACd,MAAMsB,oBAAoB,GAAGlF,UAAU,CAAC,KAAKR,GAAN,EAAW,KAAK6D,GAAL,CAASG,UAAT,CAAoBC,cAA/B,CAAvC;IACA,OAAOyB,oBAAoB,CAACjF,IAArB,CAA0BnC,sBAA1B,CAAP;EACH;;EACDiG,YAAY,CAACC,OAAO,GAAG,EAAX,EAAe;IACvB,OAAO,KAAKJ,eAAL,GAAuB3D,IAAvB,CAA4B/B,GAAG,CAAC,CAAC;MAAEkC;IAAF,CAAD,KAAiB4D,OAAO,CAACE,OAAR,GAAkBC,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBhE,OAAO,CAACiE,IAAR,EAAlB,CAAd,EAAiD;MAAE,CAACL,OAAO,CAACE,OAAT,GAAmB9D,OAAO,CAACkE;IAA7B,CAAjD,CAAlB,GAAwGlE,OAAO,CAACiE,IAAR,EAA1H,CAA/B,CAAP;EACH;EACD;AACJ;AACA;;;EACIE,GAAG,CAACP,OAAD,EAAU;IACT,OAAOrG,IAAI,CAAC,KAAK6B,GAAL,CAAS+E,GAAT,CAAaP,OAAb,CAAD,CAAJ,CAA4B/D,IAA5B,CAAiCnC,sBAAjC,CAAP;EACH;;AAnD0B;AAsD/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMqH,+BAAN,CAAsC;EAClC;AACJ;AACA;AACA;EACI/B,WAAW,CAAC3C,KAAD,EAAQ4C,GAAR,EAAa;IACpB,KAAK5C,KAAL,GAAaA,KAAb;IACA,KAAK4C,GAAL,GAAWA,GAAX;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIC,YAAY,CAACvB,MAAD,EAAS;IACjB,IAAI,CAACA,MAAD,IAAWA,MAAM,CAACmB,MAAP,KAAkB,CAAjC,EAAoC;MAChC,OAAO1C,UAAU,CAAC,KAAKC,KAAN,EAAa,KAAK4C,GAAL,CAASG,UAAT,CAAoBC,cAAjC,CAAV,CAA2DxD,IAA3D,CAAgEnC,sBAAhE,CAAP;IACH;;IACD,OAAO0C,UAAU,CAAC,KAAKC,KAAN,EAAa,KAAK4C,GAAL,CAASG,UAAT,CAAoBC,cAAjC,CAAV,CACFxD,IADE,CACG/B,GAAG,CAAC0C,OAAO,IAAIA,OAAO,CAACvC,MAAR,CAAewC,MAAM,IAAIkB,MAAM,CAACM,OAAP,CAAexB,MAAM,CAACP,IAAtB,IAA8B,CAAC,CAAxD,CAAZ,CADN,EAC+EjC,MAAM,CAAC4D,OAAO,IAAIA,OAAO,CAACiB,MAAR,GAAiB,CAA7B,CADrF,EACsHpF,sBADtH,CAAP;EAEH;EACD;AACJ;AACA;AACA;;;EACI6F,UAAU,CAAC5B,MAAD,EAAS;IACf,OAAO,KAAKuB,YAAL,CAAkBvB,MAAlB,EAA0B9B,IAA1B,CAA+B9B,IAAI,CAAC,CAAC6D,OAAD,EAAUrB,MAAV,KAAqB,CAAC,GAAGqB,OAAJ,EAAa,GAAGrB,MAAhB,CAAtB,EAA+C,EAA/C,CAAnC,CAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIiD,eAAe,CAAC7B,MAAD,EAAS;IACpB,MAAM8B,eAAe,GAAGZ,mBAAmB,CAAClB,MAAD,CAA3C;IACA,MAAM+B,uBAAuB,GAAGhC,aAAa,CAAC,KAAKrB,KAAN,EAAaoD,eAAb,EAA8B,KAAKR,GAAL,CAASG,UAAT,CAAoBC,cAAlD,CAA7C;IACA,OAAOK,uBAAuB,CAAC7D,IAAxB,CAA6BnC,sBAA7B,CAAP;EACH;;EACDiG,YAAY,CAACC,OAAO,GAAG,EAAX,EAAe;IACvB,MAAMoB,2BAA2B,GAAG7E,iBAAiB,CAAC,KAAKE,KAAN,EAAa,KAAK4C,GAAL,CAASG,UAAT,CAAoBC,cAAjC,CAArD;IACA,OAAO2B,2BAA2B,CAC7BnF,IADE,CACG/B,GAAG,CAAC0C,OAAO,IAAIA,OAAO,CAACR,OAAR,CAAgBa,IAAhB,CAAqB/C,GAArB,CAAyB+F,CAAC,IAAI;MACnD,IAAID,OAAO,CAACE,OAAZ,EAAqB;QACjB,OAAOC,MAAM,CAACC,MAAP,CAAc;UAAE,CAACJ,OAAO,CAACE,OAAT,GAAmBD,CAAC,CAACK;QAAvB,CAAd,EAA2CL,CAAC,CAACI,IAAF,EAA3C,CAAP;MACH,CAFD,MAGK;QACD,OAAOJ,CAAC,CAACI,IAAF,EAAP;MACH;IACJ,CAPwB,CAAZ,CADN,EAQFvG,sBARE,CAAP;EASH;EACD;AACJ;AACA;;;EACIyG,GAAG,CAACP,OAAD,EAAU;IACT,OAAOrG,IAAI,CAAC,KAAK8C,KAAL,CAAW8D,GAAX,CAAeP,OAAf,CAAD,CAAJ,CAA8B/D,IAA9B,CAAmCnC,sBAAnC,CAAP;EACH;;AAtDiC;AAyDtC;AACA;AACA;;;AACA,MAAMuH,kBAAkB,GAAG,IAAIlI,cAAJ,CAAmB,yCAAnB,CAA3B;AACA,MAAMmI,oBAAoB,GAAG,IAAInI,cAAJ,CAAmB,4CAAnB,CAA7B;AACA,MAAM4B,QAAQ,GAAG,IAAI5B,cAAJ,CAAmB,iCAAnB,CAAjB;AACA,MAAM0B,YAAY,GAAG,IAAI1B,cAAJ,CAAmB,qCAAnB,CAArB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAAS8H,cAAT,CAAwBD,aAAxB,EAAuCD,OAAO,GAAGvF,GAAG,IAAIA,GAAxD,EAA6D;EACzD,MAAMiB,KAAK,GAAGsE,OAAO,CAACC,aAAD,CAArB;EACA,MAAMxF,GAAG,GAAGwF,aAAZ;EACA,OAAO;IAAEvE,KAAF;IAASjB;EAAT,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM+F,gBAAN,CAAuB;EACnB;AACJ;AACA;AACA;AACA;EACInC,WAAW,CAACY,OAAD,EAAUwB,IAAV,EAAgBC,uBAAhB,EAAyCC,QAAzC,EACX;EACAC,UAFW,EAECC,IAFD,EAEOpC,UAFP,EAEmBqC,mBAFnB,EAEwCC,YAFxC,EAEsDC,IAFtD,EAE4DC,eAF5D,EAE6EC,YAF7E,EAE2F;EACtGC,QAHW,EAGDC,YAHC,EAGaC,iBAHb,EAGgCC,WAHhC,EAG6CC,kBAH7C,EAGiE;IACxE,KAAK9C,UAAL,GAAkBA,UAAlB;IACA,MAAM+C,GAAG,GAAGjI,mBAAmB,CAAC0F,OAAD,EAAU4B,IAAV,EAAgBJ,IAAhB,CAA/B;IACA,MAAMgB,WAAW,GAAGV,YAApB;;IACA,IAAIC,IAAJ,EAAU;MACNnH,YAAY,CAAC2H,GAAD,EAAMX,IAAN,EAAYI,eAAZ,EAA6BE,QAA7B,EAAuCC,YAAvC,EAAqDC,iBAArD,EAAwEH,YAAxE,EAAsFI,WAAtF,CAAZ;IACH;;IACD,CAAC,KAAKI,SAAN,EAAiB,KAAKC,mBAAtB,IAA6CnI,cAAc,CAAE,GAAEgI,GAAG,CAACf,IAAK,YAAb,EAA0B,kBAA1B,EAA8Ce,GAAG,CAACf,IAAlD,EAAwD,MAAM;MACrH,MAAMiB,SAAS,GAAGb,IAAI,CAACe,iBAAL,CAAuB,MAAMJ,GAAG,CAACE,SAAJ,EAA7B,CAAlB;;MACA,IAAIf,QAAJ,EAAc;QACVe,SAAS,CAACf,QAAV,CAAmBA,QAAnB;MACH;;MACD,IAAIc,WAAJ,EAAiB;QACbC,SAAS,CAACD,WAAV,CAAsB,GAAGA,WAAzB;MACH;;MACD,IAAIf,uBAAuB,IAAI,CAAC/G,gBAAgB,CAACiH,UAAD,CAAhD,EAA8D;QAC1D;QACA;QACA,MAAMiB,iBAAiB,GAAG,MAAM;UAC5B,IAAI;YACA,OAAOjJ,IAAI,CAAC8I,SAAS,CAACG,iBAAV,CAA4Bf,mBAAmB,IAAI3F,SAAnD,EAA8D2G,IAA9D,CAAmE,MAAM,IAAzE,EAA+E,MAAM,KAArF,CAAD,CAAX;UACH,CAFD,CAGA,OAAOC,CAAP,EAAU;YACN,IAAI,OAAOC,OAAP,KAAmB,WAAvB,EAAoC;cAChCA,OAAO,CAACC,IAAR,CAAaF,CAAb;YACH;;YACD,OAAOlJ,EAAE,CAAC,KAAD,CAAT;UACH;QACJ,CAVD;;QAWA,OAAO,CAAC6I,SAAD,EAAYb,IAAI,CAACe,iBAAL,CAAuBC,iBAAvB,CAAZ,CAAP;MACH,CAfD,MAgBK;QACD,OAAO,CAACH,SAAD,EAAY7I,EAAE,CAAC,KAAD,CAAd,CAAP;MACH;IACJ,CA3B0D,EA2BxD,CAAC8H,QAAD,EAAWc,WAAX,EAAwBf,uBAAxB,CA3BwD,CAA3D;EA4BH;;EACDX,UAAU,CAACmC,SAAD,EAAYlC,OAAZ,EAAqB;IAC3B,IAAIC,aAAJ;;IACA,IAAI,OAAOiC,SAAP,KAAqB,QAAzB,EAAmC;MAC/BjC,aAAa,GAAG,KAAKyB,SAAL,CAAe3B,UAAf,CAA0BmC,SAA1B,CAAhB;IACH,CAFD,MAGK;MACDjC,aAAa,GAAGiC,SAAhB;IACH;;IACD,MAAM;MAAEzH,GAAF;MAAOiB;IAAP,IAAiBwE,cAAc,CAACD,aAAD,EAAgBD,OAAhB,CAArC;IACA,MAAMmC,SAAS,GAAG,KAAK1D,UAAL,CAAgB2D,MAAhB,CAAuBC,GAAvB,CAA2B,MAAM5H,GAAjC,CAAlB;IACA,OAAO,IAAI2D,0BAAJ,CAA+B+D,SAA/B,EAA0CzG,KAA1C,EAAiD,IAAjD,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI4G,eAAe,CAACC,YAAD,EAAeC,YAAf,EAA6B;IACxC,MAAMxC,OAAO,GAAGwC,YAAY,KAAK/H,GAAG,IAAIA,GAAZ,CAA5B;;IACA,MAAM6H,eAAe,GAAG,KAAKZ,SAAL,CAAeY,eAAf,CAA+BC,YAA/B,CAAxB;IACA,OAAO,IAAInC,+BAAJ,CAAoCJ,OAAO,CAACsC,eAAD,CAA3C,EAA8D,IAA9D,CAAP;EACH;;EACD7F,GAAG,CAACyF,SAAD,EAAY;IACX,IAAIzH,GAAJ;;IACA,IAAI,OAAOyH,SAAP,KAAqB,QAAzB,EAAmC;MAC/BzH,GAAG,GAAG,KAAKiH,SAAL,CAAejF,GAAf,CAAmByF,SAAnB,CAAN;IACH,CAFD,MAGK;MACDzH,GAAG,GAAGyH,SAAN;IACH;;IACD,MAAMC,SAAS,GAAG,KAAK1D,UAAL,CAAgB2D,MAAhB,CAAuBC,GAAvB,CAA2B,MAAM5H,GAAjC,CAAlB;IACA,OAAO,IAAIkF,wBAAJ,CAA6BwC,SAA7B,EAAwC,IAAxC,CAAP;EACH;EACD;AACJ;AACA;;;EACIM,QAAQ,GAAG;IACP,OAAO,KAAKf,SAAL,CAAe3B,UAAf,CAA0B,GAA1B,EAA+BtD,GAA/B,GAAqC8C,EAA5C;EACH;;AAnFkB;;AAqFvBiB,gBAAgB,CAACkC,IAAjB;EAAA,iBAA6GlC,gBAA7G,EAAmGrI,EAAnG,UAA+IsB,gBAA/I,GAAmGtB,EAAnG,UAA4KuB,iBAA5K,MAAmGvB,EAAnG,UAA0NmI,kBAA1N,MAAmGnI,EAAnG,UAAyQ6B,QAAzQ,MAAmG7B,EAAnG,UAA8SE,WAA9S,GAAmGF,EAAnG,UAAsUA,EAAE,CAACwK,MAAzU,GAAmGxK,EAAnG,UAA4VW,EAAE,CAAC8J,sBAA/V,GAAmGzK,EAAnG,UAAkYoI,oBAAlY,MAAmGpI,EAAnG,UAAmb2B,YAAnb,MAAmG3B,EAAnG,UAA4dyB,EAAE,CAACiJ,eAA/d,MAAmG1K,EAAnG,UAA2gB4B,cAA3gB,MAAmG5B,EAAnG,UAAsjB8B,UAAtjB,MAAmG9B,EAAnG,UAA6lB+B,SAA7lB,MAAmG/B,EAAnG,UAAmoBgC,aAAnoB,MAAmGhC,EAAnG,UAA6qBiC,mBAA7qB,MAAmGjC,EAAnG,UAA6tBkC,WAA7tB,MAAmGlC,EAAnG,UAAqwBmC,EAAE,CAACwI,iBAAxwB;AAAA;;AACAtC,gBAAgB,CAACuC,KAAjB,kBADmG5K,EACnG;EAAA,OAAiHqI,gBAAjH;EAAA,SAAiHA,gBAAjH;EAAA,YAA+I;AAA/I;;AACA;EAAA,mDAFmGrI,EAEnG,mBAA2FqI,gBAA3F,EAAyH,CAAC;IAC9GjF,IAAI,EAAEjD,UADwG;IAE9GsF,IAAI,EAAE,CAAC;MACCoF,UAAU,EAAE;IADb,CAAD;EAFwG,CAAD,CAAzH,EAK4B,YAAY;IAAE,OAAO,CAAC;MAAEzH,IAAI,EAAEJ,SAAR;MAAmB8H,UAAU,EAAE,CAAC;QAC9D1H,IAAI,EAAEhD,MADwD;QAE9DqF,IAAI,EAAE,CAACnE,gBAAD;MAFwD,CAAD;IAA/B,CAAD,EAG3B;MAAE8B,IAAI,EAAEJ,SAAR;MAAmB8H,UAAU,EAAE,CAAC;QAClC1H,IAAI,EAAE/C;MAD4B,CAAD,EAElC;QACC+C,IAAI,EAAEhD,MADP;QAECqF,IAAI,EAAE,CAAClE,iBAAD;MAFP,CAFkC;IAA/B,CAH2B,EAQ3B;MAAE6B,IAAI,EAAEJ,SAAR;MAAmB8H,UAAU,EAAE,CAAC;QAClC1H,IAAI,EAAE/C;MAD4B,CAAD,EAElC;QACC+C,IAAI,EAAEhD,MADP;QAECqF,IAAI,EAAE,CAAC0C,kBAAD;MAFP,CAFkC;IAA/B,CAR2B,EAa3B;MAAE/E,IAAI,EAAEJ,SAAR;MAAmB8H,UAAU,EAAE,CAAC;QAClC1H,IAAI,EAAE/C;MAD4B,CAAD,EAElC;QACC+C,IAAI,EAAEhD,MADP;QAECqF,IAAI,EAAE,CAAC5D,QAAD;MAFP,CAFkC;IAA/B,CAb2B,EAkB3B;MAAEuB,IAAI,EAAE6D,MAAR;MAAgB6D,UAAU,EAAE,CAAC;QAC/B1H,IAAI,EAAEhD,MADyB;QAE/BqF,IAAI,EAAE,CAACvF,WAAD;MAFyB,CAAD;IAA5B,CAlB2B,EAqB3B;MAAEkD,IAAI,EAAEpD,EAAE,CAACwK;IAAX,CArB2B,EAqBN;MAAEpH,IAAI,EAAEzC,EAAE,CAAC8J;IAAX,CArBM,EAqB+B;MAAErH,IAAI,EAAEJ,SAAR;MAAmB8H,UAAU,EAAE,CAAC;QAC5F1H,IAAI,EAAE/C;MADsF,CAAD,EAE5F;QACC+C,IAAI,EAAEhD,MADP;QAECqF,IAAI,EAAE,CAAC2C,oBAAD;MAFP,CAF4F;IAA/B,CArB/B,EA0B3B;MAAEhF,IAAI,EAAEJ,SAAR;MAAmB8H,UAAU,EAAE,CAAC;QAClC1H,IAAI,EAAE/C;MAD4B,CAAD,EAElC;QACC+C,IAAI,EAAEhD,MADP;QAECqF,IAAI,EAAE,CAAC9D,YAAD;MAFP,CAFkC;IAA/B,CA1B2B,EA+B3B;MAAEyB,IAAI,EAAE3B,EAAE,CAACiJ,eAAX;MAA4BI,UAAU,EAAE,CAAC;QAC3C1H,IAAI,EAAE/C;MADqC,CAAD;IAAxC,CA/B2B,EAiC3B;MAAE+C,IAAI,EAAEJ,SAAR;MAAmB8H,UAAU,EAAE,CAAC;QAClC1H,IAAI,EAAE/C;MAD4B,CAAD,EAElC;QACC+C,IAAI,EAAEhD,MADP;QAECqF,IAAI,EAAE,CAAC7D,cAAD;MAFP,CAFkC;IAA/B,CAjC2B,EAsC3B;MAAEwB,IAAI,EAAEJ,SAAR;MAAmB8H,UAAU,EAAE,CAAC;QAClC1H,IAAI,EAAE/C;MAD4B,CAAD,EAElC;QACC+C,IAAI,EAAEhD,MADP;QAECqF,IAAI,EAAE,CAAC3D,UAAD;MAFP,CAFkC;IAA/B,CAtC2B,EA2C3B;MAAEsB,IAAI,EAAEJ,SAAR;MAAmB8H,UAAU,EAAE,CAAC;QAClC1H,IAAI,EAAE/C;MAD4B,CAAD,EAElC;QACC+C,IAAI,EAAEhD,MADP;QAECqF,IAAI,EAAE,CAAC1D,SAAD;MAFP,CAFkC;IAA/B,CA3C2B,EAgD3B;MAAEqB,IAAI,EAAEJ,SAAR;MAAmB8H,UAAU,EAAE,CAAC;QAClC1H,IAAI,EAAE/C;MAD4B,CAAD,EAElC;QACC+C,IAAI,EAAEhD,MADP;QAECqF,IAAI,EAAE,CAACzD,aAAD;MAFP,CAFkC;IAA/B,CAhD2B,EAqD3B;MAAEoB,IAAI,EAAEJ,SAAR;MAAmB8H,UAAU,EAAE,CAAC;QAClC1H,IAAI,EAAE/C;MAD4B,CAAD,EAElC;QACC+C,IAAI,EAAEhD,MADP;QAECqF,IAAI,EAAE,CAACxD,mBAAD;MAFP,CAFkC;IAA/B,CArD2B,EA0D3B;MAAEmB,IAAI,EAAEJ,SAAR;MAAmB8H,UAAU,EAAE,CAAC;QAClC1H,IAAI,EAAE/C;MAD4B,CAAD,EAElC;QACC+C,IAAI,EAAEhD,MADP;QAECqF,IAAI,EAAE,CAACvD,WAAD;MAFP,CAFkC;IAA/B,CA1D2B,EA+D3B;MAAEkB,IAAI,EAAEjB,EAAE,CAACwI,iBAAX;MAA8BG,UAAU,EAAE,CAAC;QAC7C1H,IAAI,EAAE/C;MADuC,CAAD;IAA1C,CA/D2B,CAAP;EAiElB,CAtExB;AAAA;;AAwEA,MAAM0K,sBAAN,CAA6B;EACzB7E,WAAW,GAAG;IACV9D,QAAQ,CAAC4I,eAAT,CAAyB,aAAzB,EAAwCnK,OAAO,CAACoK,IAAhD,EAAsD,YAAtD;EACH;EACD;AACJ;AACA;;;EAC4B,OAAjBvB,iBAAiB,CAACf,mBAAD,EAAsB;IAC1C,OAAO;MACHuC,QAAQ,EAAEH,sBADP;MAEHI,SAAS,EAAE,CACP;QAAEC,OAAO,EAAEjD,kBAAX;QAA+BkD,QAAQ,EAAE;MAAzC,CADO,EAEP;QAAED,OAAO,EAAEhD,oBAAX;QAAiCiD,QAAQ,EAAE1C;MAA3C,CAFO;IAFR,CAAP;EAOH;;AAfwB;;AAiB7BoC,sBAAsB,CAACR,IAAvB;EAAA,iBAAmHQ,sBAAnH;AAAA;;AACAA,sBAAsB,CAACO,IAAvB,kBA5FmGtL,EA4FnG;EAAA,MAAoH+K;AAApH;AACAA,sBAAsB,CAACQ,IAAvB,kBA7FmGvL,EA6FnG;EAAA,WAAuJ,CAACqI,gBAAD;AAAvJ;;AACA;EAAA,mDA9FmGrI,EA8FnG,mBAA2F+K,sBAA3F,EAA+H,CAAC;IACpH3H,IAAI,EAAE9C,QAD8G;IAEpHmF,IAAI,EAAE,CAAC;MACC0F,SAAS,EAAE,CAAC9C,gBAAD;IADZ,CAAD;EAF8G,CAAD,CAA/H,EAK4B,YAAY;IAAE,OAAO,EAAP;EAAY,CALtD;AAAA;AAOA;AACA;AACA;;;AAEA,SAASA,gBAAT,EAA2BpC,0BAA3B,EAAuDgC,+BAAvD,EAAwFT,wBAAxF,EAAkHuD,sBAAlH,EAA0I5C,kBAA1I,EAA8JC,oBAA9J,EAAoLvG,QAApL,EAA8LF,YAA9L,EAA4MoG,cAA5M,EAA4N3C,aAA5N,EAA2OJ,cAA3O,EAA2P1B,UAA3P,EAAuQD,iBAAvQ,EAA0RP,UAA1R,EAAsSD,OAAtS,EAA+S+B,aAA/S,EAA8TmB,mBAA9T", "ignoreList": []}, "metadata": {}, "sourceType": "module"}