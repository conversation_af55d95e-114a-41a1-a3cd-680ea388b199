<div class="flex-col h-100-182 overflow-auto">
    <div class="d-flex w-100" *ngIf="rowData?.length">
        <div class="px-10 w-60pr tb-w-10">
            <div class="my-20 flex-end w-100">
                <div class="btn-coal" (click)="openCenterModal(BlockForm)">
                    <span class="ic-add icon ic-xxs mr-8"></span>Add New {{ projectSubType == 'Plot' ? 'Phase' : 'Block' }}
                </div>
            </div>
            <ag-grid-angular class="ag-theme-alpine manage-block w-100" [gridOptions]="gridOptions"
                (gridReady)="onGridReady($event)" [rowData]="rowData" [alwaysShowHorizontalScroll]="true"
                [alwaysShowVerticalScroll]="true" [paginationPageSize]="10">
            </ag-grid-angular>
            <div class="my-20 w-100 flex-end">
                <div class="mr-10" *ngIf="totalCount">{{ 'GLOBAL.showing' | translate }}
                    {{totalCount ? currOffset*pageSize + 1 : 0}}
                    {{ 'GLOBAL.to-small' | translate }}
                    {{currOffset*pageSize + rowData?.length}}
                    {{ 'GLOBAL.of-small' | translate }} {{totalCount}} {{ 'GLOBAL.entries-small' | translate}}
                </div>
                <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]='getPages(totalCount,pageSize)'
                    (pageChange)="onPageChange($event)">
                </pagination>
            </div>
        </div>
        <div class="w-40pr flex-center flex-wrap tb-d-none">
            <img src="../../../../assets/images/projects/building-1.svg" *ngIf="rowData?.length==1" alt="block" />
            <img src="../../../../assets/images/projects/building-2.svg" *ngIf="rowData?.length==2" alt="block" />
            <img src="../../../../assets/images/projects/building-3.svg" *ngIf="rowData?.length==3" alt="block" />
            <img src="../../../../assets/images/projects/building-4.svg" *ngIf="rowData?.length>=4" alt="block" />
        </div>
    </div>

    <div *ngIf="!rowData?.length" class="h-100 w-100 px-20">
        <div class="flex-center-col h-100">
            <img src="../../../../assets/images/projects/no-blocks.svg" alt="No {{ projectSubType == 'Plot' ? 'Phase' : 'Block' }} found" class="mt-20">
            <h4 class="text-dark-gray">No {{ (projectSubType == 'Plot' ? 'Phase' : 'Block') }} available</h4>
            <div class="btn-coal my-10" (click)="openCenterModal()">
                <span class="ic-add icon ic-xxs mr-8"></span>Add New {{ projectSubType == 'Plot' ? 'Phase' : 'Block' }}
            </div>
        </div>
    </div>
</div>

<ng-template #BulkDeleteModal>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>{{ 'LEADS.bulk' | translate }} {{ 'BUTTONS.delete' | translate }}</h3>
            <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()">
            </div>
        </div>
        <div class="px-12">
            <div class="field-label mb-10">Select Blocks</div>
            <div class="flex-column scrollbar max-h-100-150">
                <ng-container *ngFor="let block of selectedNodes">
                    <div class="p-12 fw-600 text-sm border-bottom text-secondary bg-white">
                        <span class="text-truncate-1 break-all"> {{ block?.name }}</span>
                    </div>
                </ng-container>
            </div>
            <div class="flex-center" (click)="bulkDelete()">
                <button class="btn-coal mt-20">Delete</button>
            </div>
        </div>
    </div>
</ng-template>

<div class="justify-center">
    <div class="position-absolute bg-white bottom-12 br-12 flex-between box-shadow-10 p-16 z-index-2"
        [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}">
        <div class="align-center tb-mb-10">
            <div class="fw-600 text-coal mr-20 text-xl">{{gridApi?.getSelectedNodes()?.length}}
                {{gridApi?.getSelectedNodes()?.length > 1 ? 'Items' : 'Item'}} {{ 'LEADS.selected' | translate}}
            </div>
        </div>
        <div class="flex-center flex-wrap">
            <button class="btn-bulk-red"
                (click)="openBulkDeleteModal(BulkDeleteModal)">Bulk Delete</button>
        </div>
    </div>
</div>

<div *ngIf="rowData?.length" class="flex-end px-20 py-16 bg-white box-shadow-10">
    <u class="mr-20 text-black-200 text-large fw-600 cursor-pointer" (click)="manageProject()">Cancel</u>
    <button class="btn-coal" (click)="saveAndNext()">Go To Next</button>
</div>