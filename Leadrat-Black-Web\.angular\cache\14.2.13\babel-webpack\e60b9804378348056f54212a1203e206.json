{"ast": null, "code": "import baseEach from './_baseEach.js';\n/**\n * Aggregates elements of `collection` on `accumulator` with keys transformed\n * by `iteratee` and values set by `setter`.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform keys.\n * @param {Object} accumulator The initial aggregated object.\n * @returns {Function} Returns `accumulator`.\n */\n\nfunction baseAggregator(collection, setter, iteratee, accumulator) {\n  baseEach(collection, function (value, key, collection) {\n    setter(accumulator, value, iteratee(value), collection);\n  });\n  return accumulator;\n}\n\nexport default baseAggregator;", "map": {"version": 3, "names": ["baseEach", "baseAggregator", "collection", "setter", "iteratee", "accumulator", "value", "key"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/lodash-es/_baseAggregator.js"], "sourcesContent": ["import baseEach from './_baseEach.js';\n\n/**\n * Aggregates elements of `collection` on `accumulator` with keys transformed\n * by `iteratee` and values set by `setter`.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} setter The function to set `accumulator` values.\n * @param {Function} iteratee The iteratee to transform keys.\n * @param {Object} accumulator The initial aggregated object.\n * @returns {Function} Returns `accumulator`.\n */\nfunction baseAggregator(collection, setter, iteratee, accumulator) {\n  baseEach(collection, function(value, key, collection) {\n    setter(accumulator, value, iteratee(value), collection);\n  });\n  return accumulator;\n}\n\nexport default baseAggregator;\n"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,gBAArB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,cAAT,CAAwBC,UAAxB,EAAoCC,MAApC,EAA4CC,QAA5C,EAAsDC,WAAtD,EAAmE;EACjEL,QAAQ,CAACE,UAAD,EAAa,UAASI,KAAT,EAAgBC,GAAhB,EAAqBL,UAArB,EAAiC;IACpDC,MAAM,CAACE,WAAD,EAAcC,KAAd,EAAqBF,QAAQ,CAACE,KAAD,CAA7B,EAAsCJ,UAAtC,CAAN;EACD,CAFO,CAAR;EAGA,OAAOG,WAAP;AACD;;AAED,eAAeJ,cAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}