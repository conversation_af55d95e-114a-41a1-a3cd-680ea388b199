{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ChangeDetectorRef, ElementRef, EventEmitter, SimpleChanges, TemplateRef } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { NavigationEnd } from '@angular/router';\nimport * as moment from 'moment';\nimport { BehaviorSubject, combineLatest } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, filter, skipWhile, take, takeUntil } from 'rxjs/operators';\nimport { EMPTY_GUID, LEAD_STATUS_REASONS, UPDATE_STATUS, UPDATE_STATUS_PAST_TENSE, VALIDATION_CLEAR, VALIDATION_SET } from 'src/app/app.constants';\nimport { FolderNamesS3, LeadSource } from 'src/app/app.enum';\nimport { assignToSort, changeCalendar, formatBudget, getTimeZoneDate, onlyNumbers, onPickerOpened, patchFormControlValue, patchTimeZoneWithTime, setTimeZoneDateWithTime, toggleValidation, validateAllFormFields } from 'src/app/core/utils/common.util';\nimport { LeadAppointmentComponent } from 'src/app/features/leads/lead-appointment/lead-appointment.component';\nimport { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';\nimport { FetchInvoiceById } from 'src/app/reducers/invoice/invoice.actions';\nimport { getBookingData } from 'src/app/reducers/invoice/invoice.reducer';\nimport { FetchBulkOperation, UpdateLeadStatus, UpdateMultipleLead } from 'src/app/reducers/lead/lead.actions';\nimport { getLeadStatusIsLoading, getMultipleLeadStatusIsLoading } from 'src/app/reducers/lead/lead.reducer';\nimport { LeadPreviewChanged } from 'src/app/reducers/loader/loader.actions';\nimport { getPermissions, getPermissionsIsLoading } from 'src/app/reducers/permissions/permissions.reducers';\nimport { FetchProjectById } from 'src/app/reducers/project/project.action';\nimport { getIsProjectByIdLoading, getProjectsIDWithName, getProjectsIDWithNameIsLoading, getSelectedProjectById } from 'src/app/reducers/project/project.reducer';\nimport { FetchPropertyById, FetchPropertyWithIdNameList } from 'src/app/reducers/property/property.actions';\nimport { getPropertyListDetails, getPropertyListDetailsIsLoading, getPropertyWithIdNameList } from 'src/app/reducers/property/property.reducer';\nimport { FetchLocationsWithGoogle } from 'src/app/reducers/site/site.actions';\nimport { getLocationsWithGoogleApi } from 'src/app/reducers/site/site.reducer';\nimport { getCustomStatusList, getCustomStatusListIsLoading } from 'src/app/reducers/status/status.reducer';\nimport { FetchReportingManagerDetails } from 'src/app/reducers/teams/teams.actions';\nimport { getAdminsAndReportees, getAdminsAndReporteesIsLoading, getManagerDetails, getUserBasicDetails, getUsersListForReassignment, getUsersListForReassignmentIsLoading } from 'src/app/reducers/teams/teams.reducer';\nimport { BulkOperationTrackerComponent } from 'src/app/shared/components/bulk-operation-tracker/bulk-operation-tracker.component';\nimport { BookingFormComponent } from '../booking-form/booking-form.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-bootstrap/modal\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@ngrx/store\";\nimport * as i4 from \"src/app/shared/components/lead-preview/lead-preview.component\";\nimport * as i5 from \"angular2-notifications\";\nimport * as i6 from \"src/app/services/shared/share-data.service\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"src/app/services/controllers/blob-storage.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@ng-select/ng-select\";\nimport * as i11 from \"../../../shared/components/form-error-wrapper/form-error-wrapper.component\";\nimport * as i12 from \"@danielmoncada/angular-datetime-picker\";\nimport * as i13 from \"src/app/shared/components/browse-drop-upload/browse-drop-upload.component\";\nimport * as i14 from \"src/app/shared/directives/dropdownPanel-Resize.directive\";\nimport * as i15 from \"@ngx-translate/core\";\nconst _c0 = [\"statusForm\"];\nconst _c1 = [\"noUnitFound\"];\nconst _c2 = [\"trackerInfoModal\"];\n\nfunction CustomStatusChangeComponent_div_2_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r19.leadInfo.status.childType.displayName, \"\");\n  }\n}\n\nfunction CustomStatusChangeComponent_div_2_div_13_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r23.userBasicDetails == null ? null : ctx_r23.userBasicDetails.timeZoneInfo == null ? null : ctx_r23.userBasicDetails.timeZoneInfo.timeZoneName, \") \");\n  }\n}\n\nfunction CustomStatusChangeComponent_div_2_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"span\", 23);\n    i0.ɵɵelementStart(2, \"div\")(3, \"h5\", 19);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CustomStatusChangeComponent_div_2_div_13_div_5_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r20.leadInfo == null ? null : ctx_r20.leadInfo.scheduledDate) ? ctx_r20.getTimeZoneDate(ctx_r20.leadInfo == null ? null : ctx_r20.leadInfo.scheduledDate, ctx_r20.userBasicDetails == null ? null : ctx_r20.userBasicDetails.timeZoneInfo == null ? null : ctx_r20.userBasicDetails.timeZoneInfo.baseUTcOffset, \"dateWithTime\") : \"---\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r20.userBasicDetails == null ? null : ctx_r20.userBasicDetails.timeZoneInfo == null ? null : ctx_r20.userBasicDetails.timeZoneInfo.timeZoneName) && (ctx_r20.leadInfo == null ? null : ctx_r20.leadInfo.scheduledDate) && (ctx_r20.userBasicDetails == null ? null : ctx_r20.userBasicDetails.shouldShowTimeZone));\n  }\n}\n\nfunction CustomStatusChangeComponent_div_2_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"span\", 27);\n    i0.ɵɵelementStart(2, \"p\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", (ctx_r21.leadInfo == null ? null : ctx_r21.leadInfo.notes == null ? null : ctx_r21.leadInfo.notes.length) > 170 && !ctx_r21.isReadMore ? \"text-truncate-2\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r21.leadInfo == null ? null : ctx_r21.leadInfo.notes) ? ctx_r21.leadInfo == null ? null : ctx_r21.leadInfo.notes : \"---\", \" \");\n  }\n}\n\nconst _c3 = function (a0) {\n  return {\n    \"rotate-180\": a0\n  };\n};\n\nfunction CustomStatusChangeComponent_div_2_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_div_2_div_15_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.isReadMore = !ctx_r24.isReadMore);\n    });\n    i0.ɵɵelementStart(1, \"span\", 30);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 31);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"read \", ctx_r22.isReadMore ? \"less\" : \"more\", \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c3, ctx_r22.isReadMore));\n  }\n}\n\nfunction CustomStatusChangeComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 15)(7, \"div\", 16)(8, \"div\", 17);\n    i0.ɵɵelement(9, \"span\", 18);\n    i0.ɵɵelementStart(10, \"h5\", 19);\n    i0.ɵɵtext(11);\n    i0.ɵɵtemplate(12, CustomStatusChangeComponent_div_2_span_12_Template, 2, 1, \"span\", 2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, CustomStatusChangeComponent_div_2_div_13_Template, 6, 2, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, CustomStatusChangeComponent_div_2_div_14_Template, 4, 2, \"div\", 21);\n    i0.ɵɵtemplate(15, CustomStatusChangeComponent_div_2_div_15_Template, 4, 4, \"div\", 22);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", i0.ɵɵpipeBind1(3, 8, \"GLOBAL.current\"), \" \", i0.ɵɵpipeBind1(4, 10, \"GLOBAL.lead\"), \" \", i0.ɵɵpipeBind1(5, 12, \"GLOBAL.status\"), \"\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.leadInfo == null ? null : ctx_r1.leadInfo.status == null ? null : ctx_r1.leadInfo.status.displayName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.leadInfo == null ? null : ctx_r1.leadInfo.status == null ? null : ctx_r1.leadInfo.status.childType == null ? null : ctx_r1.leadInfo.status.childType.displayName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.leadInfo == null ? null : ctx_r1.leadInfo.scheduledDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.leadInfo == null ? null : ctx_r1.leadInfo.notes);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.leadInfo == null ? null : ctx_r1.leadInfo.notes == null ? null : ctx_r1.leadInfo.notes.length) > 170);\n  }\n}\n\nfunction CustomStatusChangeComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"LEADS.lead-unassigned-status\"));\n  }\n}\n\nfunction CustomStatusChangeComponent_div_7_ng_container_1_ng_container_1_div_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r33.bookedAndInvoiceStatus.join(\", \"), \" \");\n  }\n}\n\nconst _c4 = function (a0) {\n  return {\n    \"d-none\": a0\n  };\n};\n\nfunction CustomStatusChangeComponent_div_7_ng_container_1_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtext(1, \" Update the leads, except for \");\n    i0.ɵɵtemplate(2, CustomStatusChangeComponent_div_7_ng_container_1_ng_container_1_div_1_span_2_Template, 2, 1, \"span\", 2);\n    i0.ɵɵtext(3, \" as these leads cannot be updated further. \");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c4, !ctx_r30.isSelectedOnlySomeBooked()));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r30.bookedAndInvoiceStatus == null ? null : ctx_r30.bookedAndInvoiceStatus.length) > 0);\n  }\n}\n\nfunction CustomStatusChangeComponent_div_7_ng_container_1_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0)(1);\n    i0.ɵɵelement(2, \"input\", 38);\n    i0.ɵɵelementStart(3, \"label\", 39);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_div_7_ng_container_1_ng_container_1_ng_container_2_Template_label_click_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r37);\n      const customStatus_r34 = restoredCtx.$implicit;\n      const ctx_r36 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r36.showReasons(customStatus_r34));\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd()();\n  }\n\n  if (rf & 2) {\n    const customStatus_r34 = ctx.$implicit;\n    const i_r35 = ctx.index;\n    const ctx_r31 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"leadOption\", i_r35, \"\")(\"automate-id\", \"leadOption\", i_r35, \"\");\n    i0.ɵɵproperty(\"value\", customStatus_r34.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", (ctx_r31.updateForm == null ? null : ctx_r31.updateForm.controls[\"leadStatus\"] == null ? null : ctx_r31.updateForm.controls[\"leadStatus\"].value) === customStatus_r34.id);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"leadOption\", i_r35, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 7, customStatus_r34.actionName), \" \");\n  }\n}\n\nfunction CustomStatusChangeComponent_div_7_ng_container_1_ng_container_1_ng_container_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtext(1, \" Status is a required field. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CustomStatusChangeComponent_div_7_ng_container_1_ng_container_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CustomStatusChangeComponent_div_7_ng_container_1_ng_container_1_ng_container_3_div_1_Template, 2, 0, \"div\", 40);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.updateForm == null ? null : ctx_r32.updateForm.controls[\"leadStatus\"] == null ? null : ctx_r32.updateForm.controls[\"leadStatus\"].errors == null ? null : ctx_r32.updateForm.controls[\"leadStatus\"].errors.required);\n  }\n}\n\nfunction CustomStatusChangeComponent_div_7_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CustomStatusChangeComponent_div_7_ng_container_1_ng_container_1_div_1_Template, 4, 4, \"div\", 35);\n    i0.ɵɵtemplate(2, CustomStatusChangeComponent_div_7_ng_container_1_ng_container_1_ng_container_2_Template, 6, 9, \"ng-container\", 36);\n    i0.ɵɵtemplate(3, CustomStatusChangeComponent_div_7_ng_container_1_ng_container_1_ng_container_3_Template, 2, 1, \"ng-container\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.currentPath !== \"/invoice\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r28.customStatusListFiltered);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r28.updateForm == null ? null : ctx_r28.updateForm.controls[\"leadStatus\"] == null ? null : ctx_r28.updateForm.controls[\"leadStatus\"].dirty) || (ctx_r28.updateForm == null ? null : ctx_r28.updateForm.controls[\"leadStatus\"] == null ? null : ctx_r28.updateForm.controls[\"leadStatus\"].touched));\n  }\n}\n\nfunction CustomStatusChangeComponent_div_7_ng_container_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"img\", 43);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CustomStatusChangeComponent_div_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CustomStatusChangeComponent_div_7_ng_container_1_ng_container_1_Template, 4, 3, \"ng-container\", 2);\n    i0.ɵɵtemplate(2, CustomStatusChangeComponent_div_7_ng_container_1_div_2_Template, 2, 0, \"div\", 34);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r26.canShowStatusPopupInPreview);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r26.canShowStatusPopupInPreview);\n  }\n}\n\nfunction CustomStatusChangeComponent_div_7_div_2_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"input\", 48);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_div_7_div_2_ng_container_7_Template_input_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const reason_r41 = restoredCtx.$implicit;\n      const ctx_r43 = i0.ɵɵnextContext(3);\n      ctx_r43.updateSelectedReason(reason_r41);\n      return i0.ɵɵresetView(ctx_r43.reasonChanged(reason_r41));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 49);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const reason_r41 = ctx.$implicit;\n    const i_r42 = ctx.index;\n    const ctx_r39 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"option\", i_r42, \"\")(\"automate-id\", \"option\", i_r42, \"\");\n    i0.ɵɵproperty(\"value\", reason_r41.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", (ctx_r39.updateForm == null ? null : ctx_r39.updateForm.controls[\"reason\"] == null ? null : ctx_r39.updateForm.controls[\"reason\"].value) === reason_r41.id);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"option\", i_r42, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", reason_r41.displayName, \" \");\n  }\n}\n\nfunction CustomStatusChangeComponent_div_7_div_2_ng_container_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1, \" Sub-Status is a required field. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CustomStatusChangeComponent_div_7_div_2_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CustomStatusChangeComponent_div_7_div_2_ng_container_8_div_1_Template, 2, 0, \"div\", 50);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r40.updateForm == null ? null : ctx_r40.updateForm.controls[\"reason\"] == null ? null : ctx_r40.updateForm.controls[\"reason\"].errors == null ? null : ctx_r40.updateForm.controls[\"reason\"].errors.required);\n  }\n}\n\nfunction CustomStatusChangeComponent_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 44)(2, \"div\", 45);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"a\", 46);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_div_7_div_2_Template_a_click_5_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r46 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r46.deselectStatuses());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 47);\n    i0.ɵɵtemplate(7, CustomStatusChangeComponent_div_7_div_2_ng_container_7_Template, 4, 7, \"ng-container\", 36);\n    i0.ɵɵtemplate(8, CustomStatusChangeComponent_div_7_div_2_ng_container_8_Template, 2, 1, \"ng-container\", 2);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(4, 3, ctx_r27.selectedStatus == null ? null : ctx_r27.selectedStatus.displayName), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r27.callBackReason);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r27.updateForm == null ? null : ctx_r27.updateForm.controls[\"reason\"] == null ? null : ctx_r27.updateForm.controls[\"reason\"].dirty) || (ctx_r27.updateForm == null ? null : ctx_r27.updateForm.controls[\"reason\"] == null ? null : ctx_r27.updateForm.controls[\"reason\"].touched));\n  }\n}\n\nfunction CustomStatusChangeComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtemplate(1, CustomStatusChangeComponent_div_7_ng_container_1_Template, 3, 2, \"ng-container\", 2);\n    i0.ɵɵtemplate(2, CustomStatusChangeComponent_div_7_div_2_Template, 9, 5, \"div\", 2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.hideStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hideStatus);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_8_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"label\", 52);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementStart(5, \"input\", 53);\n    i0.ɵɵlistener(\"change\", function CustomStatusChangeComponent_ng_container_8_ng_container_1_Template_input_change_5_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r49 = i0.ɵɵnextContext(2);\n      ctx_r49.isMeetingDone ? ctx_r49.isMeetingDone = false : ctx_r49.isMeetingDone = true;\n      return i0.ɵɵresetView(ctx_r49.patchFormControlValue(ctx_r49.updateForm, \"leadStatus\", ctx_r49.currentLeadStatus[\"meeting-done\"]));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"span\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"label\", 52);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementStart(11, \"input\", 55);\n    i0.ɵɵlistener(\"change\", function CustomStatusChangeComponent_ng_container_8_ng_container_1_Template_input_change_11_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      ctx_r51.isVisitDone ? ctx_r51.isVisitDone = false : ctx_r51.isVisitDone = true;\n      return i0.ɵɵresetView(ctx_r51.patchFormControlValue(ctx_r51.updateForm, \"leadStatus\", ctx_r51.currentLeadStatus[\"visit-done\"]));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"span\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r48 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(3, 6, \"LEAD_FORM.meeting\"), \" \", i0.ɵɵpipeBind1(4, 8, \"LEAD_FORM.done\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r48.isMeetingDone);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(9, 10, \"LEAD_FORM.visit\"), \" \", i0.ɵɵpipeBind1(10, 12, \"LEAD_FORM.done\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r48.isVisitDone);\n  }\n}\n\nconst _c5 = function () {\n  return [\"Referral\", \"Walk In\", \"Direct\"];\n};\n\nfunction CustomStatusChangeComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CustomStatusChangeComponent_ng_container_8_ng_container_1_Template, 13, 14, \"ng-container\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpureFunction0(1, _c5).includes(ctx_r4.leadSource) && (ctx_r4.leadInfo == null ? null : ctx_r4.leadInfo.leadStatus == null ? null : ctx_r4.leadInfo.leadStatus.baseStatus) == \"New\");\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_2_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r59 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r59.soldPriceInWords);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 58)(2, \"div\", 59);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"form-errors-wrapper\", 60);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementStart(7, \"input\", 61);\n    i0.ɵɵlistener(\"keydown\", function CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_2_Template_input_keydown_7_listener($event) {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r60.onlyNumbers($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_2_div_8_Template, 3, 1, \"div\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const field_r53 = i0.ɵɵnextContext().$implicit;\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", (field_r53 == null ? null : field_r53.validators == null ? null : field_r53.validators.includes(\"required\")) ? \"field-label-req\" : \"field-label\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 5, \"LEAD_FORM.sold-price\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", i0.ɵɵpipeBind1(6, 7, \"LEAD_FORM.sold-price\"));\n    i0.ɵɵproperty(\"control\", ctx_r54.updateForm == null ? null : ctx_r54.updateForm.controls[\"soldPrice\"]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r54.updateForm == null ? null : ctx_r54.updateForm.controls[\"soldPrice\"] == null ? null : ctx_r54.updateForm.controls[\"soldPrice\"].value);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 58)(2, \"div\", 59);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"form-errors-wrapper\", 60);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelement(7, \"input\", 65);\n    i0.ɵɵelementStart(8, \"owl-date-time\", 66, 67);\n    i0.ɵɵlistener(\"afterPickerOpen\", function CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_3_Template_owl_date_time_afterPickerOpen_8_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r64 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r64.onPickerOpened(ctx_r64.currentDate));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const _r63 = i0.ɵɵreference(9);\n\n    const field_r53 = i0.ɵɵnextContext().$implicit;\n    const ctx_r55 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", (field_r53 == null ? null : field_r53.validators == null ? null : field_r53.validators.includes(\"required\")) ? \"field-label-req\" : \"field-label\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 9, \"LEAD_FORM.schedule-date\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", i0.ɵɵpipeBind1(6, 11, \"LEAD_FORM.schedule-date\"));\n    i0.ɵɵproperty(\"control\", ctx_r55.updateForm == null ? null : ctx_r55.updateForm.controls[\"ScheduledDate\"]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"owlDateTime\", _r63)(\"owlDateTimeTrigger\", _r63)(\"min\", !ctx_r55.isPastDateSelectionEnabled ? ctx_r55.minDate : null);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"hour12Timer\", \"true\")(\"startAt\", ctx_r55.currentDate);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_4_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"input\", 72)(2, \"span\", 54);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r68 = ctx.item;\n    const item$_r69 = ctx.item$;\n    const index_r70 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"item-\", index_r70, \"\")(\"automate-id\", \"item-\", index_r70, \"\");\n    i0.ɵɵproperty(\"checked\", item$_r69.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", item_r68.name, \" \");\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 58)(2, \"div\", 59);\n    i0.ɵɵtext(3, \" Project(s)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"form-errors-wrapper\", 68)(5, \"ng-select\", 69);\n    i0.ɵɵtemplate(6, CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_4_ng_template_6_Template, 4, 4, \"ng-template\", 70);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const field_r53 = i0.ɵɵnextContext().$implicit;\n    const ctx_r56 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", (field_r53 == null ? null : field_r53.validators == null ? null : field_r53.validators.includes(\"required\")) ? \"field-label-req\" : \"field-label\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"control\", ctx_r56.updateForm == null ? null : ctx_r56.updateForm.controls[\"Projects\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r56.projectList)(\"multiple\", true)(\"closeOnSelect\", false)(\"addTag\", true);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_5_ng_select_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"input\", 72)(2, \"span\", 54);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r74 = ctx.item;\n    const item$_r75 = ctx.item$;\n    const index_r76 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"item-\", index_r76, \"\")(\"automate-id\", \"item-\", index_r76, \"\");\n    i0.ɵɵproperty(\"checked\", item$_r75.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", item_r74, \" \");\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_5_ng_select_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-select\", 75);\n    i0.ɵɵtemplate(1, CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_5_ng_select_4_ng_template_1_Template, 4, 4, \"ng-template\", 70);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r72 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r72.propertyList)(\"multiple\", true)(\"closeOnSelect\", false)(\"addTag\", true);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"form-errors-wrapper\", 73)(2, \"div\", 59);\n    i0.ɵɵtext(3, \" Property(s)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_5_ng_select_4_Template, 2, 5, \"ng-select\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const field_r53 = i0.ɵɵnextContext().$implicit;\n    const ctx_r57 = i0.ɵɵnextContext(2);\n\n    const _r13 = i0.ɵɵreference(18);\n\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"control\", ctx_r57.updateForm == null ? null : ctx_r57.updateForm.controls[\"Properties\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", (field_r53 == null ? null : field_r53.validators == null ? null : field_r53.validators.includes(\"required\")) ? \"field-label-req\" : \"field-label\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r57.propertyListIsLoading)(\"ngIfElse\", _r13);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r82 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_div_5_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r82);\n      const ctx_r81 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r81.isUpload = true);\n    });\n    i0.ɵɵelement(1, \"span\", 80);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r78 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, (ctx_r78.uploadedFiles == null ? null : ctx_r78.uploadedFiles.length) ? \"LEADS.upload-another-document\" : \"LEADS.upload-new-document\"), \" \");\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r84 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"form\", 81)(2, \"div\", 82)(3, \"div\", 83);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 84);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 85)(8, \"browse-drop-upload\", 86);\n    i0.ɵɵlistener(\"uploadedFile\", function CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_div_6_Template_browse_drop_upload_uploadedFile_8_listener($event) {\n      i0.ɵɵrestoreView(_r84);\n      const ctx_r83 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r83.onFileSelection($event));\n    })(\"uploadedFileName\", function CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_div_6_Template_browse_drop_upload_uploadedFileName_8_listener($event) {\n      i0.ɵɵrestoreView(_r84);\n      const ctx_r85 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r85.selectedFileName = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 87)(10, \"div\", 88);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_div_6_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r84);\n      const ctx_r86 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r86.isUpload = false);\n    });\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 89);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_div_6_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r84);\n      const ctx_r87 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r87.addDocument());\n    });\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r79 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r79.documentsForm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 6, \"LEADS.document-title\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"allowedFileType\", \"imgPdfDoc\")(\"allowedFileFormat\", ctx_r79.fileFormatToBeUploaded);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 8, \"BUTTONS.cancel\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(15, 10, \"SIDEBAR.add\"), \" Document\");\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_ng_container_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r92 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 93)(2, \"div\", 94);\n    i0.ɵɵelement(3, \"span\", 95);\n    i0.ɵɵelementStart(4, \"div\", 96);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 94)(7, \"a\", 97);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_ng_container_7_div_2_Template_a_click_7_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r92);\n      const i_r90 = restoredCtx.index;\n      const ctx_r91 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r91.onClickRemoveDocument(i_r90));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const i_r90 = ctx.index;\n    const ctx_r88 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r88.uploadedFilesName[i_r90]);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 90);\n    i0.ɵɵtemplate(2, CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_ng_container_7_div_2_Template, 8, 1, \"div\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r80 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r80.uploadedFiles);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 76)(2, \"div\", 77)(3, \"div\", 59);\n    i0.ɵɵtext(4, \" Add Documents\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_div_5_Template, 4, 3, \"div\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_div_6_Template, 16, 12, \"div\", 2);\n    i0.ɵɵtemplate(7, CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_ng_container_7_Template, 3, 1, \"ng-container\", 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const field_r53 = i0.ɵɵnextContext().$implicit;\n    const ctx_r58 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", (field_r53 == null ? null : field_r53.validators == null ? null : field_r53.validators.includes(\"required\")) ? \"field-label-req mt-0\" : \"field-label mt-0\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r58.isUpload);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r58.isUpload);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r58.uploadedFiles == null ? null : ctx_r58.uploadedFiles.length);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_9_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0)(1, 56);\n    i0.ɵɵtemplate(2, CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_2_Template, 9, 9, \"ng-container\", 57);\n    i0.ɵɵtemplate(3, CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_3_Template, 10, 13, \"ng-container\", 57);\n    i0.ɵɵtemplate(4, CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_4_Template, 7, 7, \"ng-container\", 57);\n    i0.ɵɵtemplate(5, CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_5_Template, 5, 4, \"ng-container\", 57);\n    i0.ɵɵtemplate(6, CustomStatusChangeComponent_ng_template_9_ng_container_0_ng_container_6_Template, 8, 4, \"ng-container\", 57);\n    i0.ɵɵelementContainerEnd()();\n  }\n\n  if (rf & 2) {\n    const field_r53 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitch\", field_r53 == null ? null : field_r53.field == null ? null : field_r53.field.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"TotalSoldPrice\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"ScheduledDate\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Projects\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Property\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Document\");\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CustomStatusChangeComponent_ng_template_9_ng_container_0_Template, 7, 6, \"ng-container\", 36);\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngForOf\", (ctx_r6.selectedReason == null ? null : ctx_r6.selectedReason.customFields == null ? null : ctx_r6.selectedReason.customFields.length) ? ctx_r6.selectedReason == null ? null : ctx_r6.selectedReason.customFields : ctx_r6.selectedStatus == null ? null : ctx_r6.selectedStatus.customFields);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_ng_container_22_ng_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 120)(1, \"span\", 121);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const curr_r105 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", curr_r105.currency);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"title\", curr_r105.currency);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", curr_r105.currency, \" \");\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ng-select\", 118);\n    i0.ɵɵtemplate(2, CustomStatusChangeComponent_ng_container_11_ng_container_22_ng_option_2_Template, 3, 3, \"ng-option\", 119);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r95 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r95.currencyList);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h5\", 122);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r97 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate((ctx_r97.leadInfo == null ? null : ctx_r97.leadInfo.enquiry == null ? null : ctx_r97.leadInfo.enquiry.currency) || ctx_r97.defaultCurrency);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 123);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r98 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r98.miniagreementValueInWords, \"\");\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r109 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"div\", 124)(2, \"input\", 125);\n    i0.ɵɵlistener(\"change\", function CustomStatusChangeComponent_ng_container_11_div_30_Template_input_change_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r109);\n      const data_r106 = restoredCtx.$implicit;\n      const ctx_r108 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r108.switchTabProjectProperty(data_r106));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 126);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const data_r106 = ctx.$implicit;\n    const i_r107 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"projectProperty\", i_r107, \"\");\n    i0.ɵɵproperty(\"value\", data_r106);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"projectProperty\", i_r107, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(data_r106);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_div_31_ng_select_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r112 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 129);\n    i0.ɵɵlistener(\"change\", function CustomStatusChangeComponent_ng_container_11_div_31_ng_select_4_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r112);\n      const ctx_r111 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r111.onPropertyChange($event == null ? null : $event.id));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r110 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r110.propertyList);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"div\", 101);\n    i0.ɵɵtext(2, \"Choose Property\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form-errors-wrapper\", 127);\n    i0.ɵɵtemplate(4, CustomStatusChangeComponent_ng_container_11_div_31_ng_select_4_Template, 1, 2, \"ng-select\", 128);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r100 = i0.ɵɵnextContext(2);\n\n    const _r13 = i0.ɵɵreference(18);\n\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"control\", ctx_r100.updateForm.controls[\"chosenProperty\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r100.propertyListIsLoading)(\"ngIfElse\", _r13);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_div_32_ng_select_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r115 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 132);\n    i0.ɵɵlistener(\"change\", function CustomStatusChangeComponent_ng_container_11_div_32_ng_select_5_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r115);\n      const ctx_r114 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r114.onProjectChange($event == null ? null : $event.id));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r113 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r113.projectList);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"div\", 100)(2, \"div\", 59);\n    i0.ɵɵtext(3, \"Choose Project\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"form-errors-wrapper\", 130);\n    i0.ɵɵtemplate(5, CustomStatusChangeComponent_ng_container_11_div_32_ng_select_5_Template, 1, 2, \"ng-select\", 131);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r101 = i0.ɵɵnextContext(2);\n\n    const _r13 = i0.ɵɵreference(18);\n\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r101.isProjectMandatory ? \"field-label-req\" : \"field-label\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"control\", ctx_r101.updateForm.controls[\"chosenProject\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r101.projectListIsLoading)(\"ngIfElse\", _r13);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r117 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 133)(1, \"div\", 59);\n    i0.ɵɵtext(2, \"Choose Unit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 134)(4, \"form-errors-wrapper\", 135)(5, \"ng-select\", 136);\n    i0.ɵɵlistener(\"change\", function CustomStatusChangeComponent_ng_container_11_div_33_Template_ng_select_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r117);\n      const ctx_r116 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r116.onChoosenUnitChange($event));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r102 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r102.isProjectMandatory ? \"field-label-req\" : \"field-label\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"control\", ctx_r102.updateForm.controls[\"chosenUnit\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r102.unitInfo)(\"readonly\", !(ctx_r102.updateForm.controls[\"chosenProject\"] == null ? null : ctx_r102.updateForm.controls[\"chosenProject\"].value));\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r124 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_div_5_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r124);\n      const ctx_r123 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r123.isUpload = true);\n    });\n    i0.ɵɵelement(1, \"span\", 80);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r120 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 1, (ctx_r120.uploadedFiles == null ? null : ctx_r120.uploadedFiles.length) ? \"LEADS.upload-another-document\" : \"LEADS.upload-new-document\"), \" \");\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r126 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"form\", 81)(2, \"div\", 82)(3, \"div\", 83);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 84);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 85)(8, \"browse-drop-upload\", 86);\n    i0.ɵɵlistener(\"uploadedFile\", function CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_div_6_Template_browse_drop_upload_uploadedFile_8_listener($event) {\n      i0.ɵɵrestoreView(_r126);\n      const ctx_r125 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r125.onFileSelection($event));\n    })(\"uploadedFileName\", function CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_div_6_Template_browse_drop_upload_uploadedFileName_8_listener($event) {\n      i0.ɵɵrestoreView(_r126);\n      const ctx_r127 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r127.selectedFileName = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 87)(10, \"div\", 88);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_div_6_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r126);\n      const ctx_r128 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r128.isUpload = false);\n    });\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 89);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_div_6_Template_div_click_13_listener() {\n      i0.ɵɵrestoreView(_r126);\n      const ctx_r129 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r129.addDocument());\n    });\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r121 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r121.documentsForm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 6, \"LEADS.document-title\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"allowedFileType\", \"imgPdfDoc\")(\"allowedFileFormat\", ctx_r121.fileFormatToBeUploaded);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 8, \"BUTTONS.cancel\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(15, 10, \"SIDEBAR.add\"), \" Document\");\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_ng_container_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r134 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 93)(2, \"div\", 94);\n    i0.ɵɵelement(3, \"span\", 95);\n    i0.ɵɵelementStart(4, \"div\", 96);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 94)(7, \"a\", 97);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_ng_container_7_div_2_Template_a_click_7_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r134);\n      const i_r132 = restoredCtx.index;\n      const ctx_r133 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r133.onClickRemoveDocument(i_r132));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const i_r132 = ctx.index;\n    const ctx_r130 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r130.uploadedFilesName[i_r132]);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 90);\n    i0.ɵɵtemplate(2, CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_ng_container_7_div_2_Template, 8, 1, \"div\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r122 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r122.uploadedFiles);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 76)(2, \"div\", 77)(3, \"div\", 59);\n    i0.ɵɵtext(4, \" Add Documents\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_div_5_Template, 4, 3, \"div\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_div_6_Template, 16, 12, \"div\", 2);\n    i0.ɵɵtemplate(7, CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_ng_container_7_Template, 3, 1, \"ng-container\", 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const field_r118 = i0.ɵɵnextContext().$implicit;\n    const ctx_r119 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", (field_r118 == null ? null : field_r118.validators == null ? null : field_r118.validators.includes(\"required\")) ? \"field-label-req mt-0\" : \"field-label mt-0\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r119.isUpload);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r119.isUpload);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r119.uploadedFiles == null ? null : ctx_r119.uploadedFiles.length);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_11_ng_container_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0)(1, 56);\n    i0.ɵɵtemplate(2, CustomStatusChangeComponent_ng_container_11_ng_container_34_ng_container_2_Template, 8, 4, \"ng-container\", 57);\n    i0.ɵɵelementContainerEnd()();\n  }\n\n  if (rf & 2) {\n    const field_r118 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitch\", field_r118 == null ? null : field_r118.field == null ? null : field_r118.field.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"Document\");\n  }\n}\n\nconst _c6 = function () {\n  return [\"Property\", \"Project\"];\n};\n\nfunction CustomStatusChangeComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r137 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 98)(2, \"div\", 99)(3, \"div\", 100)(4, \"div\", 101);\n    i0.ɵɵtext(5, \"Booking under name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"form-errors-wrapper\", 102)(7, \"input\", 103);\n    i0.ɵɵlistener(\"blur\", function CustomStatusChangeComponent_ng_container_11_Template_input_blur_7_listener($event) {\n      i0.ɵɵrestoreView(_r137);\n      const ctx_r136 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r136.checkBookedUnderName($event));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(8, \"div\", 99)(9, \"div\", 101);\n    i0.ɵɵtext(10, \"Booked Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"form-errors-wrapper\", 104);\n    i0.ɵɵelement(12, \"input\", 105);\n    i0.ɵɵelementStart(13, \"owl-date-time\", 66, 67);\n    i0.ɵɵlistener(\"afterPickerOpen\", function CustomStatusChangeComponent_ng_container_11_Template_owl_date_time_afterPickerOpen_13_listener() {\n      i0.ɵɵrestoreView(_r137);\n      const ctx_r138 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r138.onPickerOpened(ctx_r138.currentDate));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 106)(16, \"div\", 83);\n    i0.ɵɵtext(17, \"Agreement Value\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 107)(19, \"form-errors-wrapper\", 108)(20, \"input\", 109);\n    i0.ɵɵlistener(\"wheel\", function CustomStatusChangeComponent_ng_container_11_Template_input_wheel_20_listener($event) {\n      return $event.preventDefault();\n    })(\"input\", function CustomStatusChangeComponent_ng_container_11_Template_input_input_20_listener($event) {\n      i0.ɵɵrestoreView(_r137);\n      const ctx_r140 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r140.onInputAgreementValue($event.target.value));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 110);\n    i0.ɵɵtemplate(22, CustomStatusChangeComponent_ng_container_11_ng_container_22_Template, 3, 1, \"ng-container\", 8);\n    i0.ɵɵtemplate(23, CustomStatusChangeComponent_ng_container_11_ng_template_23_Template, 2, 1, \"ng-template\", null, 111, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(25, CustomStatusChangeComponent_ng_container_11_div_25_Template, 2, 1, \"div\", 112);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 113)(27, \"div\", 83);\n    i0.ɵɵtext(28, \"Choose Property/ Project\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 114);\n    i0.ɵɵtemplate(30, CustomStatusChangeComponent_ng_container_11_div_30_Template, 5, 4, \"div\", 115);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(31, CustomStatusChangeComponent_ng_container_11_div_31_Template, 5, 3, \"div\", 116);\n    i0.ɵɵtemplate(32, CustomStatusChangeComponent_ng_container_11_div_32_Template, 6, 4, \"div\", 116);\n    i0.ɵɵtemplate(33, CustomStatusChangeComponent_ng_container_11_div_33_Template, 6, 5, \"div\", 117);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, CustomStatusChangeComponent_ng_container_11_ng_container_34_Template, 3, 2, \"ng-container\", 36);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const _r94 = i0.ɵɵreference(14);\n\n    const _r96 = i0.ɵɵreference(24);\n\n    const ctx_r7 = i0.ɵɵnextContext();\n    let tmp_12_0;\n    let tmp_13_0;\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"control\", ctx_r7.updateForm.controls[\"bookedUnderName\"]);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"control\", ctx_r7.updateForm.controls[\"bookedDate\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"owlDateTime\", _r94)(\"max\", ctx_r7.currentDate)(\"owlDateTimeTrigger\", _r94);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"hour12Timer\", \"true\")(\"startAt\", ctx_r7.updateForm.controls[\"bookedDate\"].value ? null : ctx_r7.currentDate);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"control\", ctx_r7.updateForm.controls[\"agreementValue\"]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r7.currencyList == null ? null : ctx_r7.currencyList.length) > 1)(\"ngIfElse\", _r96);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.updateForm.controls[\"agreementValue\"] == null ? null : ctx_r7.updateForm.controls[\"agreementValue\"].value);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(16, _c6));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx_r7.updateForm.get(\"projectProperty\")) == null ? null : tmp_12_0.value) === \"Property\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx_r7.updateForm.get(\"projectProperty\")) == null ? null : tmp_13_0.value) === \"Project\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.isShowUnitInfoField && (ctx_r7.updateForm.controls[\"chosenProject\"] == null ? null : ctx_r7.updateForm.controls[\"chosenProject\"].value));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", (ctx_r7.selectedReason == null ? null : ctx_r7.selectedReason.customFields == null ? null : ctx_r7.selectedReason.customFields.length) ? ctx_r7.selectedReason == null ? null : ctx_r7.selectedReason.customFields : ctx_r7.selectedStatus == null ? null : ctx_r7.selectedStatus.customFields);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_12_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 144);\n    i0.ɵɵtext(1, \"primary\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_12_ng_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 120);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementStart(3, \"span\", 145);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r142 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r142.EMPTY_GUID);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 3, \"LEADS.unassign-lead\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" (\", i0.ɵɵpipeBind1(5, 5, \"LEADS.mark-unassigned\"), \")\");\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_12_ng_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 120);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const user_r145 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r145.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", user_r145.firstName, \" \", user_r145.lastName, \"\");\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_12_div_12_ng_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 120);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementStart(3, \"span\", 145);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r146 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"value\", ctx_r146.EMPTY_GUID);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 3, \"LEADS.unassign-lead\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" (\", i0.ɵɵpipeBind1(5, 5, \"LEADS.mark-unassigned\"), \")\");\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_12_div_12_ng_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 120);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const user_r148 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r148.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", user_r148.firstName, \" \", user_r148.lastName, \"\");\n  }\n}\n\nconst _c7 = function (a0, a1) {\n  return {\n    \"blinking\": a0,\n    \"pe-none\": a1\n  };\n};\n\nfunction CustomStatusChangeComponent_ng_container_12_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 146)(1, \"div\", 144);\n    i0.ɵɵtext(2, \"secondary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ng-select\", 147);\n    i0.ɵɵtemplate(4, CustomStatusChangeComponent_ng_container_12_div_12_ng_option_4_Template, 6, 7, \"ng-option\", 142);\n    i0.ɵɵtemplate(5, CustomStatusChangeComponent_ng_container_12_div_12_ng_option_5_Template, 2, 3, \"ng-option\", 119);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r144 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction2(4, _c7, ctx_r144.isUserListLoading, !(ctx_r144.updateForm == null ? null : ctx_r144.updateForm.controls[\"assignedToUserId\"] == null ? null : ctx_r144.updateForm.controls[\"assignedToUserId\"].value)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r144.leadInfo.secondaryUserId != ctx_r144.EMPTY_GUID);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r144.secondaryAgentList);\n  }\n}\n\nconst _c8 = function (a0) {\n  return {\n    \"blinking\": a0\n  };\n};\n\nfunction CustomStatusChangeComponent_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 83);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 137)(6, \"div\", 138);\n    i0.ɵɵtemplate(7, CustomStatusChangeComponent_ng_container_12_div_7_Template, 2, 0, \"div\", 139);\n    i0.ɵɵelementStart(8, \"form-errors-wrapper\", 140)(9, \"ng-select\", 141);\n    i0.ɵɵtemplate(10, CustomStatusChangeComponent_ng_container_12_ng_option_10_Template, 6, 7, \"ng-option\", 142);\n    i0.ɵɵtemplate(11, CustomStatusChangeComponent_ng_container_12_ng_option_11_Template, 2, 3, \"ng-option\", 119);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, CustomStatusChangeComponent_ng_container_12_div_12_Template, 6, 7, \"div\", 143);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(3, 10, \"BUTTONS.assign\"), \" \", i0.ɵɵpipeBind1(4, 12, \"GLOBAL.to\"), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r8.isDualOwnershipEnabled ? \"w-50\" : \"w-100\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.isDualOwnershipEnabled);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"control\", ctx_r8.updateForm == null ? null : ctx_r8.updateForm.controls[\"assignedToUserId\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(14, _c8, ctx_r8.isUserListLoading));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.leadInfo.assignTo != ctx_r8.EMPTY_GUID);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.primaryAgentList);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.isDualOwnershipEnabled);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 59);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"form-errors-wrapper\", 60);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelement(6, \"textarea\", 148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r9.isNotesMandatory ? \"field-label-req\" : \"field-label\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 4, \"TASK.notes\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", i0.ɵɵpipeBind1(5, 6, \"TASK.notes\"));\n    i0.ɵɵproperty(\"control\", ctx_r9.updateForm == null ? null : ctx_r9.updateForm.controls[\"notes\"]);\n  }\n}\n\nfunction CustomStatusChangeComponent_div_14_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r154 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_div_14_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r154);\n      const ctx_r153 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r153.openAppointmentPopup());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"BUTTONS.update-lead-status\"));\n  }\n}\n\nfunction CustomStatusChangeComponent_div_14_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r156 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_div_14_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r156);\n      const ctx_r155 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r155.updateStatus());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r150 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, ctx_r150.isLeadPreview && !ctx_r150.whatsAppComp ? \"BUTTONS.save-and-close\" : \"BUTTONS.save\"));\n  }\n}\n\nfunction CustomStatusChangeComponent_div_14_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r158 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 157);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_div_14_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r158);\n      const ctx_r157 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r157.updateStatus(true));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, \"BUTTONS.save-and-next\"));\n  }\n}\n\nfunction CustomStatusChangeComponent_div_14_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r160 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 158);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_div_14_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r160);\n      const ctx_r159 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r159.fullBookingFormModal(ctx_r159.fullBookingForm));\n    });\n    i0.ɵɵtext(1, \"Save & fill booking form\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction CustomStatusChangeComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r162 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 149)(1, \"h5\", 150);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_div_14_Template_h5_click_1_listener() {\n      i0.ɵɵrestoreView(_r162);\n      const ctx_r161 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r161.modalService.hide());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 151);\n    i0.ɵɵtemplate(5, CustomStatusChangeComponent_div_14_button_5_Template, 3, 3, \"button\", 152);\n    i0.ɵɵtemplate(6, CustomStatusChangeComponent_div_14_button_6_Template, 3, 3, \"button\", 153);\n    i0.ɵɵtemplate(7, CustomStatusChangeComponent_div_14_button_7_Template, 3, 3, \"button\", 154);\n    i0.ɵɵtemplate(8, CustomStatusChangeComponent_div_14_button_8_Template, 2, 0, \"button\", 155);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c4, ctx_r10.isCustomStatusListLoading));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 6, \"BUTTONS.cancel\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.canShowStatusPopupInPreview && ctx_r10.canUpdateStatus && !ctx_r10.isUnassignedLead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.canShowStatusPopupInPreview && ctx_r10.canUpdateStatus && !ctx_r10.isUnassignedLead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isLastLead && !ctx_r10.canShowStatusPopupInPreview && !ctx_r10.isBulkUpdate && ctx_r10.canUpdateStatus && !ctx_r10.isUnassignedLead && !ctx_r10.whatsAppComp);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isShowBookingFormBtn && ctx_r10.canUpdateBookedLead && !ctx_r10.isBulkUpdate);\n  }\n}\n\nconst _c9 = function (a0, a1) {\n  return {\n    \"justify-end\": a0,\n    \"justify-center\": a1\n  };\n};\n\nfunction CustomStatusChangeComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 159);\n    i0.ɵɵelement(1, \"img\", 160);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c9, ctx_r12.leadStatusIsLoading || ctx_r12.multipleLeadsIsLoading, ctx_r12.isCustomStatusListLoading));\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ng-select\", 161);\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"virtualScroll\", true);\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r164 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 162)(1, \"h3\", 163);\n    i0.ɵɵtext(2, \"There is no unit information provided for this project. Please check with the admin.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 164)(4, \"button\", 165);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_ng_template_19_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r164);\n      const ctx_r163 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r163.modalRef.hide());\n    });\n    i0.ɵɵtext(5, \"OK\");\n    i0.ɵɵelementEnd()()();\n  }\n}\n\nfunction CustomStatusChangeComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r166 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"h5\", 166);\n    i0.ɵɵtext(1, \"Bulk Update Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 167)(3, \"h4\", 168);\n    i0.ɵɵtext(4, \"Bulk Update Status In progress. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h5\", 169);\n    i0.ɵɵtext(6, \"You can check \");\n    i0.ɵɵelementStart(7, \"span\", 170);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_ng_template_21_Template_span_click_7_listener() {\n      i0.ɵɵrestoreView(_r166);\n      const ctx_r165 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r165.openBulkUpdatedStatus());\n    });\n    i0.ɵɵtext(8, \"\\u201CBulk Operation Tracker\\u201D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" to view updated status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 171);\n    i0.ɵɵlistener(\"click\", function CustomStatusChangeComponent_ng_template_21_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r166);\n      const ctx_r167 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r167.modalService.hide());\n    });\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 1, \"BULK_LEAD.got-it\"), \"\");\n  }\n}\n\nconst _c10 = function (a0, a1, a2) {\n  return {\n    \"pe-none blinking\": a0,\n    \"h-100-333 ph-h-100-358\": a1,\n    \"h-100-460\": a2\n  };\n};\n\nconst _c11 = function (a0, a1) {\n  return {\n    \"pe-none\": a0,\n    \"grid-blur\": a1\n  };\n};\n\nexport class CustomStatusChangeComponent {\n  constructor(modalRef, modalService, formBuilder, _store, cdr, _leadPreviewComponent, _notificationsService, shareDataService, router, s3UploadService) {\n    this.modalRef = modalRef;\n    this.modalService = modalService;\n    this.formBuilder = formBuilder;\n    this._store = _store;\n    this.cdr = cdr;\n    this._leadPreviewComponent = _leadPreviewComponent;\n    this._notificationsService = _notificationsService;\n    this.shareDataService = shareDataService;\n    this.router = router;\n    this.s3UploadService = s3UploadService;\n    this.stopper = new EventEmitter();\n    this.searchPlaceTerm$ = new BehaviorSubject('');\n    this.canShowStatusPopupInPreview = false;\n    this.canUpdateStatus = false;\n    this.isLeadPreview = false;\n    this.isLastLead = false;\n    this.whatsAppComp = false;\n    this.dispSubStatus = LEAD_STATUS_REASONS;\n    this.leadStatus = UPDATE_STATUS;\n    this.currentLeadStatus = UPDATE_STATUS_PAST_TENSE;\n    this.updateLeadStates = [];\n    this.callBackReason = [];\n    this.notInterestedReason = [];\n    this.placesList = [];\n    this.navigateLink = '';\n    this.whatsAppShare = '';\n    this.defaultCurrency = '';\n    this.soldPriceInWords = '';\n    this.budgetInWords = '';\n    this.hideStatus = false;\n    this.isVisitDone = false;\n    this.didCustDenyOtp = false;\n    this.isBulkUpdate = false;\n    this.showLocationSearch = false;\n    this.projectList = [];\n    this.propertyList = [];\n    this.currencyList = [];\n    this.leadStatusIsLoading = false;\n    this.multipleLeadsIsLoading = false;\n    this.propertyListIsLoading = true;\n    this.projectListIsLoading = true;\n    this.canAssignLead = false;\n    this.primaryUserList = [];\n    this.secondaryUserList = [];\n    this.primaryAgentList = [];\n    this.secondaryAgentList = [];\n    this.isUserListLoading = true;\n    this.deactiveUsers = [];\n    this.isLeadStatusLoading = false;\n    this.canUpdateBookedLead = false;\n    this.isReadMore = false;\n    this.isCustomStatusListLoading = true;\n    this.manualLocationsList = [];\n    this.customStatusListFiltered = [];\n    this.EMPTY_GUID = EMPTY_GUID;\n    this.moment = moment;\n    this.patchFormControlValue = patchFormControlValue;\n    this.formatBudget = formatBudget;\n    this.onlyNumbers = onlyNumbers;\n    this.getTimeZoneDate = getTimeZoneDate;\n    this.isShowUnitInfoField = false;\n    this.selectedProject = {};\n    this.isShowBookingFormBtn = false;\n    this.isSaveAndNext = false;\n    this.bookedAndInvoiceStatus = [];\n    this.onPickerOpened = onPickerOpened;\n    this.currentDate = new Date();\n    this.trackerModalOpen = false;\n    this.isPastDateSelectionEnabled = true;\n    this.uploadedFiles = [];\n    this.uploadedFilesName = [];\n    this.fileFormatToBeUploaded = 'application/pdf,image/x-png,image/gif,image/jpeg,image/tiff';\n    this.selectedFile = '';\n    this.selectedFileName = null;\n    this.isUpload = false;\n    this.maxDate = new Date(); // this.minDate = new Date();\n    // this.minDate.setDate(this.minDate.getDate());\n\n    this._store.dispatch(new FetchPropertyWithIdNameList());\n\n    this._store.dispatch(new FetchLocationsWithGoogle());\n  }\n\n  get canShowStatus() {\n    return !this.hideStatus && !this.canShowStatusPopupInPreview;\n  }\n\n  get isUnassignedLead() {\n    var _a;\n\n    return ((_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.assignTo) == EMPTY_GUID;\n  }\n\n  isBookedLead() {\n    var _a;\n\n    return !this.canUpdateBookedLead && ((_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.some(lead => {\n      var _a;\n\n      return (_a = lead === null || lead === void 0 ? void 0 : lead.status) === null || _a === void 0 ? void 0 : _a.shouldUseForBooking;\n    }));\n  }\n\n  isSelectedOnlySomeBooked() {\n    var _a;\n\n    if (!((_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.length)) {\n      return false;\n    }\n\n    const allBookedOrInvoiced = this.leadInfo.every(lead => {\n      var _a;\n\n      return ((_a = lead.status) === null || _a === void 0 ? void 0 : _a.shouldUseForBooking) || lead.status.shouldUseForInvoice;\n    });\n    const someBookedOrInvoiced = this.leadInfo.some(lead => {\n      var _a;\n\n      return ((_a = lead.status) === null || _a === void 0 ? void 0 : _a.shouldUseForBooking) || lead.status.shouldUseForInvoice;\n    });\n    return allBookedOrInvoiced || someBookedOrInvoiced && !allBookedOrInvoiced;\n  }\n\n  isSelectedAllBookedLead() {\n    const leadInfo = this.leadInfo.length ? this.leadInfo : [];\n    return leadInfo === null || leadInfo === void 0 ? void 0 : leadInfo.every(lead => {\n      var _a;\n\n      return (_a = lead === null || lead === void 0 ? void 0 : lead.status) === null || _a === void 0 ? void 0 : _a.shouldUseForBooking;\n    });\n  }\n\n  getMaxDate() {\n    return new Date();\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      var _a, _b, _c, _d, _e;\n\n      _this.isProjectSubscription = _this.shareDataService.isProjectUnit$.subscribe(() => {\n        _this.modalRef = _this.modalService.show(_this.noUnitFound, {\n          class: 'modal-500 top-modal ip-modal-unset',\n          ignoreBackdropClick: true\n        });\n      });\n\n      _this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {\n        _this.currentPath = _this.router.url;\n      });\n\n      _this.currentPath = _this.router.url;\n\n      _this._store.select(getCustomStatusList).pipe(takeUntil(_this.stopper)).subscribe(customStatus => {\n        var _a, _b, _c, _d, _e, _f;\n\n        customStatus === null || customStatus === void 0 ? void 0 : customStatus.forEach(status => {\n          if ((status === null || status === void 0 ? void 0 : status.shouldUseForBooking) || (status === null || status === void 0 ? void 0 : status.shouldUseForInvoice)) {\n            if (!_this.bookedAndInvoiceStatus.includes(status === null || status === void 0 ? void 0 : status.displayName)) {\n              _this.bookedAndInvoiceStatus.push(status === null || status === void 0 ? void 0 : status.displayName);\n            }\n          }\n        });\n\n        _this.bookedAndInvoiceStatus.sort((a, b) => a.localeCompare(b));\n\n        _this.customStatusList = customStatus;\n        let shouldUseForBooking = _this.isBulkUpdate ? _this.leadInfo.every(lead => {\n          var _a;\n\n          return (_a = lead === null || lead === void 0 ? void 0 : lead.status) === null || _a === void 0 ? void 0 : _a.shouldUseForBooking;\n        }) : (_b = (_a = _this.leadInfo) === null || _a === void 0 ? void 0 : _a.status) === null || _b === void 0 ? void 0 : _b.shouldUseForBooking;\n\n        if (_this.isSelectedAllBookedOrInvoicedLead()) {\n          _this.customStatusListFiltered = (_c = _this.customStatusList) === null || _c === void 0 ? void 0 : _c.filter(status => status.shouldUseForBookingCancel).sort((a, b) => a.orderRank - b.orderRank);\n        } else if (shouldUseForBooking) {\n          _this.customStatusListFiltered = (_d = _this.customStatusList) === null || _d === void 0 ? void 0 : _d.filter(status => {\n            var _a;\n\n            return (status === null || status === void 0 ? void 0 : status.shouldUseForBookingCancel) && !(status === null || status === void 0 ? void 0 : status.shouldBeHidden) || ((_a = _this.leadInfo) === null || _a === void 0 ? void 0 : _a.status.id) === status.id;\n          }).sort((a, b) => a.orderRank - b.orderRank);\n        } else if (_this.currentPath === '/invoice') {\n          _this.customStatusListFiltered = (_e = _this.customStatusList) === null || _e === void 0 ? void 0 : _e.filter(status => (status === null || status === void 0 ? void 0 : status.shouldUseForBookingCancel) || status.shouldUseForInvoice).sort((a, b) => a.orderRank - b.orderRank);\n        } else {\n          _this.customStatusListFiltered = (_f = _this.customStatusList) === null || _f === void 0 ? void 0 : _f.filter(status => !(status === null || status === void 0 ? void 0 : status.shouldUseForBookingCancel) && !(status === null || status === void 0 ? void 0 : status.shouldUseForInvoice) && !(status === null || status === void 0 ? void 0 : status.shouldBeHidden)).sort((a, b) => a.orderRank - b.orderRank);\n        }\n      });\n\n      _this._store.select(getCustomStatusListIsLoading).pipe(takeUntil(_this.stopper)).subscribe(isLoading => {\n        _this.isCustomStatusListLoading = isLoading;\n      });\n\n      _this._store.select(getPermissions).pipe(takeUntil(_this.stopper)).subscribe(permissions => {\n        if (!(permissions === null || permissions === void 0 ? void 0 : permissions.length)) return;\n\n        if (permissions === null || permissions === void 0 ? void 0 : permissions.includes('Permissions.Leads.UpdateBookedLead')) {\n          _this.canUpdateBookedLead = true;\n        }\n      });\n\n      _this._store.select(getGlobalSettingsAnonymous).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        _this.isDualOwnershipEnabled = data === null || data === void 0 ? void 0 : data.isDualOwnershipEnabled;\n        _this.isPastDateSelectionEnabled = data === null || data === void 0 ? void 0 : data.isPastDateSelectionEnabled;\n      });\n\n      _this._store.select(getUserBasicDetails).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        var _a, _b;\n\n        _this.userBasicDetails = data;\n        _this.currentDate = changeCalendar((_b = (_a = _this.userBasicDetails) === null || _a === void 0 ? void 0 : _a.timeZoneInfo) === null || _b === void 0 ? void 0 : _b.baseUTcOffset);\n        _this.minDate = _this.currentDate;\n      });\n\n      _this.getMiniBookingFormDatas = _this.shareDataService.getMiniBookingformData().subscribe(data => {\n        var _a, _b, _c, _d, _e, _f, _g, _h;\n\n        _this.makeRequired(data === null || data === void 0 ? void 0 : data.status);\n\n        _this.patchMiniBookDataIfBooked(data === null || data === void 0 ? void 0 : data.id);\n\n        if ((_a = data === null || data === void 0 ? void 0 : data.status) === null || _a === void 0 ? void 0 : _a.shouldUseForBooking) {\n          _this.customStatusListFiltered = (_b = _this.customStatusList) === null || _b === void 0 ? void 0 : _b.filter(status => (status === null || status === void 0 ? void 0 : status.shouldUseForBookingCancel) || (status === null || status === void 0 ? void 0 : status.shouldUseForBooking)).sort((a, b) => a.orderRank - b.orderRank);\n          const status = (_d = (_c = _this.customStatusListFiltered) === null || _c === void 0 ? void 0 : _c.filter(status => status === null || status === void 0 ? void 0 : status.shouldUseForBooking)) === null || _d === void 0 ? void 0 : _d[0];\n\n          _this.makeRequired(status);\n\n          _this.showReasons(status, data === null || data === void 0 ? void 0 : data.id);\n        }\n\n        if ((_e = data === null || data === void 0 ? void 0 : data.status) === null || _e === void 0 ? void 0 : _e.shouldUseForInvoice) {\n          _this.customStatusListFiltered = (_f = _this.customStatusList) === null || _f === void 0 ? void 0 : _f.filter(status => (status === null || status === void 0 ? void 0 : status.shouldUseForBookingCancel) || status.shouldUseForInvoice).sort((a, b) => a.orderRank - b.orderRank);\n          const status = (_h = (_g = _this.customStatusListFiltered) === null || _g === void 0 ? void 0 : _g.filter(status => status === null || status === void 0 ? void 0 : status.shouldUseForInvoice)) === null || _h === void 0 ? void 0 : _h[0];\n\n          _this.makeRequired(status);\n\n          _this.showReasons(status, data === null || data === void 0 ? void 0 : data.id);\n        }\n      });\n\n      if (location.href.includes('/invoice') || ((_b = (_a = _this.leadInfo) === null || _a === void 0 ? void 0 : _a.status) === null || _b === void 0 ? void 0 : _b.shouldUseForBooking)) {\n        const status = (_d = (_c = _this.customStatusListFiltered) === null || _c === void 0 ? void 0 : _c.filter(status => {\n          var _a, _b;\n\n          return ((_b = (_a = _this.leadInfo) === null || _a === void 0 ? void 0 : _a.status) === null || _b === void 0 ? void 0 : _b.shouldUseForBooking) ? status === null || status === void 0 ? void 0 : status.shouldUseForBooking : status === null || status === void 0 ? void 0 : status.shouldUseForInvoice;\n        })) === null || _d === void 0 ? void 0 : _d[0];\n\n        _this.makeRequired(status);\n\n        _this.showReasons(status, (_e = _this.leadInfo) === null || _e === void 0 ? void 0 : _e.id);\n\n        return;\n      }\n    })();\n  }\n\n  ngOnChanges(changes) {\n    var _a, _b, _c, _d, _e, _f;\n\n    if (changes === null || changes === void 0 ? void 0 : changes.leadInfo) {\n      if ((_b = (_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.status) === null || _b === void 0 ? void 0 : _b.shouldUseForBooking) this.customStatusListFiltered = (_c = this.customStatusList) === null || _c === void 0 ? void 0 : _c.filter(status => {\n        return status === null || status === void 0 ? void 0 : status.shouldUseForBookingCancel;\n      });else if ((_e = (_d = this.leadInfo) === null || _d === void 0 ? void 0 : _d.status) === null || _e === void 0 ? void 0 : _e.shouldUseForBookingCancel) {\n        this.customStatusListFiltered = (_f = this.customStatusList) === null || _f === void 0 ? void 0 : _f.filter(status => {\n          return !(status === null || status === void 0 ? void 0 : status.shouldUseForBookingCancel);\n        });\n      }\n      this.initialize();\n    }\n  }\n\n  initializeUpdateForm() {\n    var _a, _b, _c, _d, _e, _f;\n\n    let isPrimaryUserActive = (_b = (_a = this.primaryAgentList) === null || _a === void 0 ? void 0 : _a.filter(user => {\n      var _a;\n\n      return (user === null || user === void 0 ? void 0 : user.id) === ((_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.assignTo);\n    })) === null || _b === void 0 ? void 0 : _b.length;\n    let isSecondaryUserActive = (_d = (_c = this.secondaryAgentList) === null || _c === void 0 ? void 0 : _c.filter(user => {\n      var _a;\n\n      return (user === null || user === void 0 ? void 0 : user.id) === ((_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.secondaryUserId);\n    })) === null || _d === void 0 ? void 0 : _d.length;\n    this.defaultControls = {\n      bookedUnderName: null,\n      ScheduledDate: null,\n      Projects: null,\n      bookedDate: null,\n      agreementValue: null,\n      projectProperty: null,\n      chosenProperty: null,\n      chosenProject: null,\n      chosenUnit: null,\n      currency: null,\n      notes: null,\n      leadStatus: null,\n      reason: '',\n      assignedToUserId: [((_e = this.leadInfo) === null || _e === void 0 ? void 0 : _e.assignTo) !== EMPTY_GUID && isPrimaryUserActive ? (_f = this.leadInfo) === null || _f === void 0 ? void 0 : _f.assignTo : null],\n      secondaryAssignTo: this.leadInfo.secondaryUserId !== EMPTY_GUID && isSecondaryUserActive ? this.leadInfo.secondaryUserId : null\n    };\n    this.updateForm = this.formBuilder.group(this.defaultControls);\n    this.documentsForm = this.formBuilder.group({\n      docTitle: ['']\n    });\n  }\n\n  clearManualLocation(location) {\n    this.manualLocationsList = this.manualLocationsList.filter(manualLocation => JSON.stringify(manualLocation) != JSON.stringify(location));\n  }\n\n  addMoreLocation() {\n    const {\n      locality,\n      city,\n      state\n    } = this.updateForm.value;\n\n    if (!(city === null || city === void 0 ? void 0 : city.trim()) && !(state === null || state === void 0 ? void 0 : state.trim()) && !(locality === null || locality === void 0 ? void 0 : locality.trim())) {\n      return;\n    }\n\n    const filteredLocation = this.manualLocationsList.filter(location => {\n      return (city === null || city === void 0 ? void 0 : city.trim()) == (location === null || location === void 0 ? void 0 : location.city) && (locality === null || locality === void 0 ? void 0 : locality.trim()) == (location === null || location === void 0 ? void 0 : location.locality) && (state === null || state === void 0 ? void 0 : state.trim()) == (location === null || location === void 0 ? void 0 : location.state);\n    });\n    if (filteredLocation === null || filteredLocation === void 0 ? void 0 : filteredLocation.length) return;\n    this.manualLocationsList.push({\n      city: city === null || city === void 0 ? void 0 : city.trim(),\n      locality: locality === null || locality === void 0 ? void 0 : locality.trim(),\n      state: state === null || state === void 0 ? void 0 : state.trim()\n    });\n    this.removeLocation('changeLocation');\n  }\n\n  removeLocation(type) {\n    switch (type) {\n      case 'changeLocation':\n        this.updateForm.patchValue({\n          locality: null,\n          city: null,\n          state: null\n        });\n        break;\n    }\n  }\n\n  initialize() {\n    var _a, _b, _c, _d, _e;\n\n    this.isBulkUpdate = Array.isArray(this.leadInfo);\n    this.initializeUpdateForm();\n    this.allLeadStatus = JSON.parse(localStorage.getItem('masterleadstatus'));\n    this.updateLeadStates = ((_a = this.allLeadStatus) === null || _a === void 0 ? void 0 : _a.filter(status => {\n      return !['Meeting Done', 'Site Visit Done', 'New', 'Pending', 'Site Visit Not Done', 'Meeting Not Done'].includes(status.displayName);\n    })) || [];\n\n    this._store.select(getGlobalSettingsAnonymous).pipe(takeUntil(this.stopper)).subscribe(data => {\n      var _a, _b, _c, _d;\n\n      this.currencyList = data.countries && data.countries.length > 0 ? data.countries[0].currencies : null;\n      this.defaultCurrency = data.countries && data.countries.length > 0 ? data.countries[0].defaultCurrency : null;\n      this.isProjectMandatory = (_a = data === null || data === void 0 ? void 0 : data.leadProjectSetting) === null || _a === void 0 ? void 0 : _a.isProjectMandatoryOnBooking;\n      this.isNotesMandatory = (_b = data === null || data === void 0 ? void 0 : data.leadNotesSetting) === null || _b === void 0 ? void 0 : _b.isNotesMandatoryOnUpdateLead;\n\n      if (this.isNotesMandatory) {\n        (_c = this.updateForm.controls['notes']) === null || _c === void 0 ? void 0 : _c.setValidators([Validators.required]);\n      } else {\n        (_d = this.updateForm.controls['notes']) === null || _d === void 0 ? void 0 : _d.clearValidators();\n      }\n    });\n\n    (_c = (_b = this.updateForm) === null || _b === void 0 ? void 0 : _b.get('leadExpectedBudget')) === null || _c === void 0 ? void 0 : _c.valueChanges.subscribe(val => {\n      var _a, _b;\n\n      this.budgetInWords = formatBudget(val, ((_b = (_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.enquiry) === null || _b === void 0 ? void 0 : _b.currency) || this.defaultCurrency);\n    });\n\n    this._store.select(getLocationsWithGoogleApi).pipe(takeUntil(this.stopper)).subscribe(data => {\n      this.placesList = data === null || data === void 0 ? void 0 : data.slice().sort((a, b) => a.location.localeCompare(b.location));\n    });\n\n    this.searchPlaceTerm$.pipe(debounceTime(500), distinctUntilChanged(), filter(searchStr => searchStr.length > 2)).subscribe(searchStr => {\n      this._store.dispatch(new FetchLocationsWithGoogle(searchStr));\n    });\n\n    this._store.select(getProjectsIDWithName).pipe(takeUntil(this.stopper)).subscribe(data => {\n      var _a;\n\n      this.projectList = (_a = data === null || data === void 0 ? void 0 : data.slice()) === null || _a === void 0 ? void 0 : _a.sort((a, b) => a.name.localeCompare(b.name));\n    });\n\n    this._store.select(getProjectsIDWithNameIsLoading).pipe(takeUntil(this.stopper)).subscribe(data => {\n      this.projectListIsLoading = data;\n    });\n\n    this._store.select(getPropertyWithIdNameList).pipe(takeUntil(this.stopper)).subscribe(data => {\n      this.propertyList = data.slice().sort((a, b) => {\n        const nameA = a.name || '';\n        const nameB = b.name || '';\n        return nameA.localeCompare(nameB);\n      });\n    });\n\n    this._store.select(getPropertyListDetailsIsLoading).pipe(takeUntil(this.stopper)).subscribe(data => {\n      this.propertyListIsLoading = data;\n    });\n\n    const adminsWithReportees$ = this._store.select(getAdminsAndReportees).pipe(takeUntil(this.stopper));\n\n    const adminsWithReporteesIsLoading$ = this._store.select(getAdminsAndReporteesIsLoading).pipe(takeUntil(this.stopper));\n\n    const allUsers$ = this._store.select(getUsersListForReassignment).pipe(takeUntil(this.stopper));\n\n    const allUsersIsLoading$ = this._store.select(getUsersListForReassignmentIsLoading).pipe(takeUntil(this.stopper));\n\n    const permissions$ = this._store.select(getPermissions).pipe(takeUntil(this.stopper));\n\n    const permissionsIsLoading$ = this._store.select(getPermissionsIsLoading).pipe(takeUntil(this.stopper));\n\n    combineLatest({\n      adminsWithReportees: adminsWithReportees$,\n      adminsWithReporteesIsLoading: adminsWithReporteesIsLoading$,\n      allUsers: allUsers$,\n      allUsersIsLoading: allUsersIsLoading$,\n      permissions: permissions$,\n      permissionsIsLoading: permissionsIsLoading$\n    }).subscribe(({\n      adminsWithReportees,\n      adminsWithReporteesIsLoading,\n      allUsers,\n      allUsersIsLoading,\n      permissions,\n      permissionsIsLoading\n    }) => {\n      var _a, _b, _c, _d, _e, _f;\n\n      let userList;\n\n      if (permissions === null || permissions === void 0 ? void 0 : permissions.includes('Permissions.Leads.Assign')) {\n        this.canAssignLead = true;\n      }\n\n      if (permissions === null || permissions === void 0 ? void 0 : permissions.includes('Permissions.Users.AssignToAny')) {\n        userList = allUsers;\n      } else {\n        userList = adminsWithReportees;\n      }\n\n      let activeUsers = userList === null || userList === void 0 ? void 0 : userList.filter(user => user.isActive);\n      this.deactiveUsers = userList === null || userList === void 0 ? void 0 : userList.filter(user => !user.isActive);\n      this.primaryUserList = assignToSort(activeUsers, (_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.assignTo);\n      this.primaryAgentList = assignToSort(activeUsers, (_b = this.leadInfo) === null || _b === void 0 ? void 0 : _b.assignTo);\n      this.secondaryUserList = assignToSort(activeUsers, (_c = this.leadInfo) === null || _c === void 0 ? void 0 : _c.secondaryUserId);\n      this.secondaryUserList = assignToSort(activeUsers, (_d = this.leadInfo) === null || _d === void 0 ? void 0 : _d.secondaryUserId);\n      this.primaryAgentList = (_e = this.primaryUserList) === null || _e === void 0 ? void 0 : _e.filter(el => {\n        var _a, _b;\n\n        return !((_b = (_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.secondaryUserId) === null || _b === void 0 ? void 0 : _b.includes(el === null || el === void 0 ? void 0 : el.id));\n      });\n      this.secondaryAgentList = (_f = this.secondaryUserList) === null || _f === void 0 ? void 0 : _f.filter(el => {\n        var _a, _b;\n\n        return !((_b = (_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.assignTo) === null || _b === void 0 ? void 0 : _b.includes(el === null || el === void 0 ? void 0 : el.id));\n      });\n      this.updateForm.get('assignedToUserId').valueChanges.subscribe(val => {\n        this.secondaryAgentList = this.secondaryUserList.filter(el => !(val === null || val === void 0 ? void 0 : val.includes(el === null || el === void 0 ? void 0 : el.id)));\n      });\n      this.updateForm.get('secondaryAssignTo').valueChanges.subscribe(val => {\n        this.primaryAgentList = this.primaryUserList.filter(el => !(val === null || val === void 0 ? void 0 : val.includes(el === null || el === void 0 ? void 0 : el.id)));\n      });\n      this.isUserListLoading = adminsWithReporteesIsLoading && allUsersIsLoading && permissionsIsLoading;\n    });\n\n    if (!this.isBulkUpdate) {\n      this.leadSource = LeadSource[(_e = (_d = this.leadInfo) === null || _d === void 0 ? void 0 : _d.enquiry) === null || _e === void 0 ? void 0 : _e.leadSource];\n    } // this.conditionalStatusRendering();\n\n  }\n\n  ngAfterViewInit() {\n    const element = document.getElementById('bulk-status-table');\n\n    if (element) {\n      this.elementHeight = 'unset';\n      const calculatedHeight = `${this.elementHeight}`;\n      this.statusForm.nativeElement.style.height = calculatedHeight;\n    }\n\n    this.cdr.detectChanges();\n  }\n\n  dateValidator(control) {\n    const selectedDate = control.value;\n\n    if (selectedDate) {\n      const currentDate = new Date();\n      const selectedDateTime = new Date(selectedDate);\n      currentDate.setSeconds(0, 0);\n      selectedDateTime.setSeconds(0, 0);\n\n      if (selectedDateTime > currentDate) {\n        return {\n          invalidDate: true\n        };\n      }\n    }\n\n    return null;\n  }\n\n  openAppointmentPopup() {\n    let initialState = {\n      data: this.leadInfo,\n      closeModal: () => {\n        appointmentModalRef.hide();\n      },\n      closeLeadPreviewModal: this.closeLeadPreviewModal\n    };\n    const appointmentModalRef = this.modalService.show(LeadAppointmentComponent, {\n      initialState,\n      class: 'right-modal modal-550 ip-modal-unset'\n    });\n  }\n\n  showReasons(status, id) {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n\n    const invoiceId = id !== undefined && id !== null && id !== '' ? id : '';\n\n    if ((((_b = (_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.status) === null || _b === void 0 ? void 0 : _b.shouldUseForInvoice) || ((_d = (_c = this.leadInfo) === null || _c === void 0 ? void 0 : _c.status) === null || _d === void 0 ? void 0 : _d.shouldUseForBooking) || (status === null || status === void 0 ? void 0 : status.shouldUseForInvoice)) && (status === null || status === void 0 ? void 0 : status.actionName) !== 'Booking Cancel') {\n      this.patchMiniBookDataIfBooked(invoiceId);\n    }\n\n    if (this.currentPath === '/invoice') {\n      if (status === null || status === void 0 ? void 0 : status.shouldUseForInvoice) {\n        this.isShowBookingFormBtn = true;\n      } else if (status === null || status === void 0 ? void 0 : status.shouldUseForBookingCancel) {\n        this.isShowBookingFormBtn = false;\n        this.clearBookingFormValidations();\n      }\n    } else {\n      if (status === null || status === void 0 ? void 0 : status.shouldUseForBooking) {\n        this.isShowBookingFormBtn = true;\n      } else {\n        this.isShowBookingFormBtn = false;\n        this.clearBookingFormValidations();\n      }\n    }\n\n    patchFormControlValue(this.updateForm, 'leadStatus', status === null || status === void 0 ? void 0 : status.id);\n\n    if ((_e = status === null || status === void 0 ? void 0 : status.childTypes) === null || _e === void 0 ? void 0 : _e.length) {\n      this.callBackReason = status.childTypes;\n      this.hideStatus = true;\n    }\n\n    this.makeRequired(status);\n    this.selectedStatus = status;\n    let childCount = (_h = (_g = (_f = this.customStatusList.filter(statusObj => (statusObj === null || statusObj === void 0 ? void 0 : statusObj.id) === (status === null || status === void 0 ? void 0 : status.id))) === null || _f === void 0 ? void 0 : _f[0]) === null || _g === void 0 ? void 0 : _g.childTypes) === null || _h === void 0 ? void 0 : _h.length;\n\n    if (childCount) {\n      toggleValidation(VALIDATION_SET, this.updateForm, 'reason', [Validators.required]);\n    } else {\n      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'reason');\n    }\n  }\n\n  clearBookingFormValidations() {\n    const fieldsToClear = ['bookedUnderName', 'bookedDate', 'agreementValue', 'chosenProperty', 'chosenProject', 'chosenUnit'];\n    fieldsToClear.forEach(field => toggleValidation(VALIDATION_CLEAR, this.updateForm, field));\n  }\n\n  addFormControls(formValues, validators = {}) {\n    var _a;\n\n    Object.keys(this.updateForm.controls).forEach(controlName => {\n      if (!this.defaultControls.hasOwnProperty(controlName)) {\n        this.updateForm.removeControl(controlName);\n      }\n    });\n\n    for (const controlName in formValues) {\n      if (formValues.hasOwnProperty(controlName)) {\n        this.updateForm.addControl(controlName, this.formBuilder.control(formValues[controlName], []));\n      }\n    }\n\n    for (const controlName in validators) {\n      if (validators.hasOwnProperty(controlName)) {\n        const validatorsArray = this.getValidators(validators[controlName]);\n        (_a = this.updateForm.get(controlName)) === null || _a === void 0 ? void 0 : _a.setValidators(validatorsArray);\n      }\n    }\n  }\n\n  reasonChanged(reason) {\n    (reason === null || reason === void 0 ? void 0 : reason.shouldUseForBooking) ? '' : this.makeRequired(reason, true);\n  }\n\n  getValidators(validators) {\n    const validatorsArray = [];\n    validators === null || validators === void 0 ? void 0 : validators.forEach(validator => {\n      switch (validator) {\n        case 'required':\n          validatorsArray.push(Validators.required);\n          break;\n\n        case 'futureDate':\n          validatorsArray.push(this.validateScheduleTime(this.currentDate));\n          break;\n\n        default:\n          break;\n      }\n    });\n    return validatorsArray;\n  }\n\n  validateScheduleTime(Date) {\n    return control => {\n      const currentTime = new Date(Date);\n      const selectedTime = new Date(control === null || control === void 0 ? void 0 : control.value);\n      const selectedHours = selectedTime === null || selectedTime === void 0 ? void 0 : selectedTime.getHours();\n      const selectedMinutes = selectedTime === null || selectedTime === void 0 ? void 0 : selectedTime.getMinutes();\n      const currentDate = new Date(Date);\n      const scheduleDate = new Date(control === null || control === void 0 ? void 0 : control.value);\n      currentDate.setHours(0, 0, 0, 0);\n      scheduleDate.setHours(0, 0, 0, 0);\n\n      if (currentDate.getTime() == scheduleDate.getTime() && (selectedHours < currentTime.getHours() || selectedHours === currentTime.getHours() && selectedMinutes <= currentTime.getMinutes())) {\n        return {\n          invalidTime: true\n        };\n      }\n\n      return null;\n    };\n  }\n\n  makeRequired(status, isSubStatus = false) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t;\n\n    if (!((_a = status === null || status === void 0 ? void 0 : status.childTypes) === null || _a === void 0 ? void 0 : _a.length) && !isSubStatus) toggleValidation(VALIDATION_CLEAR, this.updateForm, 'reason');else toggleValidation(VALIDATION_SET, this.updateForm, 'reason');\n    const formValues = {};\n    const validators = {}; // this should be refactored\n\n    if ((status === null || status === void 0 ? void 0 : status.shouldUseForBooking) || (status === null || status === void 0 ? void 0 : status.shouldUseForInvoice)) {\n      const customFields = [];\n      [['Projects', null, []], ['bookedUnderName', null, ['required']], ['bookedDate', null, ['required']], ['agreementValue', null, []], ['projectProperty', 'Property', []], ['chosenProperty', null, ['required']], ['chosenProject', null, [this.isProjectMandatory ? 'required' : '']], ['chosenUnit', null, ['required']], ['currency', ((_c = (_b = this.leadInfo) === null || _b === void 0 ? void 0 : _b.enquiry) === null || _c === void 0 ? void 0 : _c.currency) || this.defaultCurrency, []]].forEach(field => {\n        customFields.push({\n          isRequired: false,\n          value: field === null || field === void 0 ? void 0 : field[1],\n          field: {\n            lastModifiedOn: 'string',\n            createdOn: 'string',\n            createdBy: 'string',\n            lastModifiedBy: 'string',\n            id: 'string',\n            name: field === null || field === void 0 ? void 0 : field[0],\n            orderRank: 0,\n            module: 'string',\n            notes: 'any'\n          },\n          validators: field[2]\n        });\n      });\n      status = Object.assign(Object.assign({}, status), {\n        customFields\n      });\n    }\n\n    (_d = status === null || status === void 0 ? void 0 : status.customFields) === null || _d === void 0 ? void 0 : _d.forEach(field => {\n      formValues[field.field.name] = null;\n      validators[field.field.name] = field === null || field === void 0 ? void 0 : field.validators;\n    });\n    this.addFormControls(formValues, validators);\n\n    if ((status === null || status === void 0 ? void 0 : status.shouldUseForBooking) || (status === null || status === void 0 ? void 0 : status.shouldUseForInvoice)) {\n      this.updateForm.get('currency').setValue(((_f = (_e = this.leadInfo) === null || _e === void 0 ? void 0 : _e.enquiry) === null || _f === void 0 ? void 0 : _f.currency) || this.defaultCurrency);\n      this.updateForm.get('projectProperty').setValue('Property');\n      this.updateForm.get('bookedDate').setValue(this.currentDate);\n      this.updateForm.get('currency').valueChanges.subscribe(val => {\n        var _a;\n\n        this.miniagreementValueInWords = formatBudget(this.updateForm.value.agreementValue || ((_a = this.updateForm) === null || _a === void 0 ? void 0 : _a.get('agreementValue').value), val);\n      });\n      this.updateForm.get('agreementValue').valueChanges.subscribe(val => {\n        this.miniagreementValueInWords = formatBudget(val, this.updateForm.value.currency);\n      });\n      this.updateForm.get('assignedToUserId').valueChanges.subscribe(val => {\n        if (val) {\n          this.selectedProject.assignedToUserId = val;\n\n          this._store.dispatch(new FetchReportingManagerDetails(val));\n\n          this._store.select(getManagerDetails).pipe().subscribe(data => {\n            this.userDetails = data;\n          });\n        }\n      });\n      this.updateForm.get('secondaryAssignTo').valueChanges.subscribe(val => {\n        if (val) {\n          this.selectedProject.secondaryAssignTo = val;\n        }\n      });\n    } else {\n      let project = (_h = (_g = this.leadInfo) === null || _g === void 0 ? void 0 : _g.projects) === null || _h === void 0 ? void 0 : _h.map(data => data.name);\n      (_j = this.updateForm.get('Projects')) === null || _j === void 0 ? void 0 : _j.setValue(project);\n    }\n\n    (_l = (_k = this.updateForm) === null || _k === void 0 ? void 0 : _k.get('soldPriceCurrency')) === null || _l === void 0 ? void 0 : _l.valueChanges.subscribe(val => {\n      var _a;\n\n      this.soldPriceInWords = formatBudget(((_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.soldPrice) || this.updateForm.value.soldPrice, val);\n    });\n    (_o = (_m = this.updateForm) === null || _m === void 0 ? void 0 : _m.get('soldPrice')) === null || _o === void 0 ? void 0 : _o.valueChanges.subscribe(val => {\n      var _a;\n\n      this.soldPriceInWords = formatBudget(val, this.updateForm.value.soldPriceCurrency || ((_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.soldPriceCurrency));\n    });\n    (_q = (_p = this.updateForm) === null || _p === void 0 ? void 0 : _p.get('leadExpectedBudget')) === null || _q === void 0 ? void 0 : _q.valueChanges.subscribe(val => {\n      var _a, _b;\n\n      this.budgetInWords = formatBudget(val, ((_b = (_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.enquiry) === null || _b === void 0 ? void 0 : _b.currency) || this.defaultCurrency);\n    });\n\n    if (status === null || status === void 0 ? void 0 : status.shouldUseForBooking) {\n      (_r = this.updateForm.get('soldPriceCurrency')) === null || _r === void 0 ? void 0 : _r.setValue(((_t = (_s = this.leadInfo) === null || _s === void 0 ? void 0 : _s.enquiry) === null || _t === void 0 ? void 0 : _t.currency) || this.defaultCurrency);\n    }\n  }\n\n  deselectStatuses() {\n    this.updateForm.get('ScheduledDate').setErrors(null);\n    this.updateForm.controls['reason'].setValue('');\n    this.updateForm.controls['leadStatus'].setValue('');\n    this.selectedStatus = null;\n    this.hideStatus = false;\n    this.selectedReason = null;\n    this.isShowBookingFormBtn = false;\n    this.clearBookingFormValidations();\n  }\n\n  updateStatus(isSaveAndNext = false) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\n\n    this.isSaveAndNext = isSaveAndNext;\n    Object.keys(this.updateForm.controls).forEach(field => {\n      const control = this.updateForm.get(field);\n\n      if (control && control.invalid) {\n        console.log(`Invalid field: ${field}`);\n      }\n    });\n    this.selectedProject.agreementValue = (_b = (_a = this.updateForm) === null || _a === void 0 ? void 0 : _a.value) === null || _b === void 0 ? void 0 : _b.agreementValue;\n    const invalidLeadNameMessage = 'Lead Name is invalid, Please Rename to continue';\n\n    if (!this.isBulkUpdate) {\n      if (!((_d = (_c = this.leadInfo) === null || _c === void 0 ? void 0 : _c.name) === null || _d === void 0 ? void 0 : _d.trim())) {\n        (_e = this._notificationsService) === null || _e === void 0 ? void 0 : _e.warn(invalidLeadNameMessage);\n        return;\n      }\n    } else {\n      for (const lead of this.leadInfo) {\n        if (!((_f = lead === null || lead === void 0 ? void 0 : lead.name) === null || _f === void 0 ? void 0 : _f.trim())) {\n          (_g = this._notificationsService) === null || _g === void 0 ? void 0 : _g.warn(invalidLeadNameMessage);\n          return;\n        }\n      }\n    }\n\n    if (this.updateForm.invalid) {\n      validateAllFormFields(this.updateForm);\n      return;\n    }\n\n    if (this.isUpload) {\n      this.addDocument();\n    }\n\n    if (this.navigateLink && this.updateForm.value.shareContent !== 'Properties') {\n      window.location.href = this.navigateLink;\n    } else if (this.whatsAppShare) {\n      window.open(this.whatsAppShare, '_blank');\n    }\n\n    if (((_h = this.uploadedFiles) === null || _h === void 0 ? void 0 : _h.length) && ((_k = (_j = this.uploadedFiles) === null || _j === void 0 ? void 0 : _j[0]) === null || _k === void 0 ? void 0 : _k.includes('data:'))) {\n      this.uploadDocumentsToS3(isSaveAndNext);\n      return;\n    }\n\n    this.proceedWithStatusUpdate(isSaveAndNext);\n  } // Helper methods to keep the main method clean and readable\n\n\n  prepareAddressPayload(userData) {\n    var _a, _b;\n\n    return (_b = (this.showLocationSearch ? userData === null || userData === void 0 ? void 0 : userData.locationId : this.manualLocationsList) || ((_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.locationId)) === null || _b === void 0 ? void 0 : _b.map(location => {\n      var _a, _b, _c, _d, _e, _f, _g, _h;\n\n      return {\n        locationId: (_a = location === null || location === void 0 ? void 0 : location.id) !== null && _a !== void 0 ? _a : location === null || location === void 0 ? void 0 : location.id,\n        placeId: (_b = location === null || location === void 0 ? void 0 : location.placeId) !== null && _b !== void 0 ? _b : location === null || location === void 0 ? void 0 : location.placeId,\n        subLocality: ((_c = location === null || location === void 0 ? void 0 : location.enquiredLocality) !== null && _c !== void 0 ? _c : location === null || location === void 0 ? void 0 : location.enquiredLocality) || ((_d = location === null || location === void 0 ? void 0 : location.locality) !== null && _d !== void 0 ? _d : location === null || location === void 0 ? void 0 : location.locality),\n        city: ((_e = location === null || location === void 0 ? void 0 : location.enquiredCity) !== null && _e !== void 0 ? _e : location === null || location === void 0 ? void 0 : location.enquiredCity) || ((_f = location === null || location === void 0 ? void 0 : location.city) !== null && _f !== void 0 ? _f : location === null || location === void 0 ? void 0 : location.city),\n        state: ((_g = location === null || location === void 0 ? void 0 : location.enquiredState) !== null && _g !== void 0 ? _g : location === null || location === void 0 ? void 0 : location.enquiredState) || ((_h = location === null || location === void 0 ? void 0 : location.state) !== null && _h !== void 0 ? _h : location === null || location === void 0 ? void 0 : location.state)\n      };\n    });\n  }\n\n  createPayload(userData, addressPayload, isAddressPayloadEmpty, postPonedStatusId) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;\n\n    return {\n      id: this.leadInfo.id,\n      leadStatusId: userData.reason || userData.leadStatus || this.leadInfo.leadStatusId,\n      scheduledDate: setTimeZoneDateWithTime(userData.ScheduledDate, (_b = (_a = this.userBasicDetails) === null || _a === void 0 ? void 0 : _a.timeZoneInfo) === null || _b === void 0 ? void 0 : _b.baseUTcOffset) || undefined,\n      bookedDate: setTimeZoneDateWithTime(userData.bookedDate, (_d = (_c = this.userBasicDetails) === null || _c === void 0 ? void 0 : _c.timeZoneInfo) === null || _d === void 0 ? void 0 : _d.baseUTcOffset) || undefined,\n      rating: userData.rating || this.leadInfo.rating,\n      notes: userData.notes ? userData.notes : this.leadInfo.notes,\n      IsNotesUpdated: userData.notes ? true : false,\n      bookedUnderName: userData === null || userData === void 0 ? void 0 : userData.bookedUnderName,\n      agreementValue: userData === null || userData === void 0 ? void 0 : userData.agreementValue,\n      soldPrice: userData.soldPrice,\n      soldPriceCurrency: userData.soldPriceCurrency,\n      unmatchedBudget: userData.leadExpectedBudget || this.leadInfo.unmatchedBudget || undefined,\n      purchasedFrom: userData.purchasedFromWhom || this.leadInfo.purchasedFrom,\n      addresses: isAddressPayloadEmpty ? null : addressPayload,\n      postponedDate: userData.reason === postPonedStatusId ? setTimeZoneDateWithTime(userData.ScheduledDate, (_f = (_e = this.userBasicDetails) === null || _e === void 0 ? void 0 : _e.timeZoneInfo) === null || _f === void 0 ? void 0 : _f.baseUTcOffset) : setTimeZoneDateWithTime(userData === null || userData === void 0 ? void 0 : userData.revertDate, (_h = (_g = this.userBasicDetails) === null || _g === void 0 ? void 0 : _g.timeZoneInfo) === null || _h === void 0 ? void 0 : _h.baseUTcOffset) || undefined,\n      propertiesList: (userData === null || userData === void 0 ? void 0 : userData.Properties) ? userData === null || userData === void 0 ? void 0 : userData.Properties : ((_k = (_j = this.leadInfo) === null || _j === void 0 ? void 0 : _j.properties) === null || _k === void 0 ? void 0 : _k.map(proper => proper.title)) || [],\n      projectsList: (userData === null || userData === void 0 ? void 0 : userData.Projects) ? userData === null || userData === void 0 ? void 0 : userData.Projects : ((_m = (_l = this.leadInfo) === null || _l === void 0 ? void 0 : _l.projects) === null || _m === void 0 ? void 0 : _m.map(proje => proje.name)) || [],\n      projectIds: (userData === null || userData === void 0 ? void 0 : userData.chosenProject) ? [userData.chosenProject] : null,\n      unitTypeId: (_o = userData === null || userData === void 0 ? void 0 : userData.chosenUnit) !== null && _o !== void 0 ? _o : null,\n      propertyIds: (userData === null || userData === void 0 ? void 0 : userData.chosenProperty) ? [userData.chosenProperty] : null,\n      currency: userData.currency\n    };\n  }\n\n  handleProjectPropertySelection(payload) {\n    if (!payload.propertiesList) payload.propertiesList = [];\n    if (!payload.projectsList) payload.projectsList = [];\n\n    if (this.selectedProject.isprojectproperty === 'Property') {\n      payload.propertiesList = [...new Set([...payload.propertiesList, this.selectedProject.selectedpropertyname])];\n    } else if (this.selectedProject.isprojectproperty === 'Project') {\n      payload.projectsList = [...new Set([...payload.projectsList, this.selectedProject.selectedpropertyname])];\n    }\n  }\n\n  assignUsers(userData, payload) {\n    var _a;\n\n    payload.assignTo = userData.assignedToUserId || ((_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.assignTo) || EMPTY_GUID;\n    payload.secondaryUserId = userData.secondaryAssignTo || this.leadInfo.secondaryUserId || EMPTY_GUID;\n  }\n\n  handleLeadStatus(payload) {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n\n    if (((_b = (_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.status) === null || _b === void 0 ? void 0 : _b.status) === 'meeting_scheduled' || ((_d = (_c = this.leadInfo) === null || _c === void 0 ? void 0 : _c.status) === null || _d === void 0 ? void 0 : _d.status) === 'site_visit_scheduled') {\n      payload.isFullyCompleted = true;\n      payload.projects = (_f = (_e = this.leadInfo) === null || _e === void 0 ? void 0 : _e.projects) === null || _f === void 0 ? void 0 : _f.map(project => typeof project === 'string' ? project : project === null || project === void 0 ? void 0 : project.name);\n      payload.properties = (_h = (_g = this.leadInfo) === null || _g === void 0 ? void 0 : _g.properties) === null || _h === void 0 ? void 0 : _h.map(property => property === null || property === void 0 ? void 0 : property.title);\n    }\n  }\n\n  handleSingleUpdate(payload) {\n    this.leadStatusIsLoading = true;\n\n    this._store.dispatch(new UpdateLeadStatus(payload, this.leadInfo.id, false, false));\n\n    if (!this.canShowStatusPopupInPreview) {\n      this._store.dispatch(new LeadPreviewChanged());\n    } // Close modal when response is received\n\n\n    this._store.select(getLeadStatusIsLoading).pipe(skipWhile(isLoading => isLoading), take(1)).subscribe(isLoading => {\n      this.leadStatusIsLoading = false;\n\n      if (!isLoading) {\n        if (!this.isSaveAndNext) {\n          this.modalRef.hide();\n        }\n      }\n    });\n  }\n\n  handleBulkUpdate(userData, addressPayload, isAddressPayloadEmpty, postPonedStatusId, documents) {\n    this.multipleLeadsIsLoading = true;\n\n    if (!userData.leadStatus) {\n      this.modalService.hide();\n      return;\n    }\n\n    const notBookedLeads = this.leadInfo.filter(lead => {\n      var _a;\n\n      return !((_a = lead.status) === null || _a === void 0 ? void 0 : _a.shouldUseForBooking);\n    });\n    const leadItems = this.isSelectedOnlySomeBooked() ? notBookedLeads : this.leadInfo;\n    const bulkPayload = Object.assign({}, this.createBulkPayload(leadItems, userData, addressPayload, isAddressPayloadEmpty, postPonedStatusId, documents));\n\n    this._store.dispatch(new UpdateMultipleLead(bulkPayload, true));\n\n    this._store.select(getMultipleLeadStatusIsLoading).pipe(skipWhile(isLoading => isLoading), take(1)).subscribe(isLoading => {\n      this.multipleLeadsIsLoading = isLoading;\n      this.modalService.hide();\n    });\n  }\n\n  createBulkPayload(lead, userData, addressPayload, isAddressPayloadEmpty, postPonedStatusId, documents) {\n    var _a;\n\n    const ids = lead.map(leadData => leadData.id);\n    const ArrayLeadIds = ids.flat();\n    const payloadObj = {\n      LeadIds: ArrayLeadIds,\n      leadStatusId: (userData === null || userData === void 0 ? void 0 : userData.reason) || (userData === null || userData === void 0 ? void 0 : userData.leadStatus),\n      rating: userData === null || userData === void 0 ? void 0 : userData.rating,\n      notes: userData === null || userData === void 0 ? void 0 : userData.notes,\n      scheduledDate: userData.ScheduledDate || undefined,\n      bookedDate: (userData === null || userData === void 0 ? void 0 : userData.bookedDate) || null,\n      revertDate: (userData === null || userData === void 0 ? void 0 : userData.revertDate) || null,\n      unmatchedBudget: (userData === null || userData === void 0 ? void 0 : userData.leadExpectedBudget) || null,\n      purchasedFrom: userData === null || userData === void 0 ? void 0 : userData.purchasedFromWhom,\n      bookedUnderName: userData.bookedUnderName,\n      agreementValue: userData.agreementValue || null,\n      soldPrice: userData.soldPrice,\n      soldPriceCurrency: userData.soldPriceCurrency,\n      postponedDate: (userData === null || userData === void 0 ? void 0 : userData.reason) === postPonedStatusId ? userData.ScheduledDate : (userData === null || userData === void 0 ? void 0 : userData.revertDate) || undefined,\n      addresses: isAddressPayloadEmpty ? null : addressPayload,\n      propertiesList: (userData === null || userData === void 0 ? void 0 : userData.Properties) ? userData === null || userData === void 0 ? void 0 : userData.Properties : [],\n      projectsList: (userData === null || userData === void 0 ? void 0 : userData.Projects) ? userData === null || userData === void 0 ? void 0 : userData.Projects : [],\n      projectIds: (userData === null || userData === void 0 ? void 0 : userData.chosenProject) ? [userData.chosenProject] : null,\n      propertyIds: (userData === null || userData === void 0 ? void 0 : userData.chosenProperty) ? [userData.chosenProperty] : null,\n      unitTypeId: (_a = userData === null || userData === void 0 ? void 0 : userData.chosenUnit) !== null && _a !== void 0 ? _a : null\n    };\n\n    if (documents === null || documents === void 0 ? void 0 : documents.length) {\n      payloadObj.documents = documents;\n    }\n\n    if (!payloadObj.propertiesList) payloadObj.propertiesList = [];\n    if (!payloadObj.projectsList) payloadObj.projectsList = [];\n\n    if (this.selectedProject) {\n      if (this.selectedProject.isprojectproperty === 'Property') {\n        payloadObj.propertiesList = [...new Set([...payloadObj.propertiesList, this.selectedProject.selectedpropertyname])];\n      } else if (this.selectedProject.isprojectproperty === 'Project') {\n        payloadObj.projectsList = [...new Set([...payloadObj.projectsList, this.selectedProject.selectedpropertyname])];\n      }\n    }\n\n    lead.forEach(leadData => {\n      var _a, _b;\n\n      if (((_a = leadData === null || leadData === void 0 ? void 0 : leadData.status) === null || _a === void 0 ? void 0 : _a.status) === 'meeting_scheduled' || ((_b = leadData === null || leadData === void 0 ? void 0 : leadData.status) === null || _b === void 0 ? void 0 : _b.status) === 'site_visit_scheduled') {\n        payloadObj.isFullyCompleted = true; // payloadObj.projects = leadData?.projects?.map((project: any) => project?.name);\n        // payloadObj.properties = leadData?.properties?.map((property: any) => property?.title);\n      }\n\n      payloadObj.assignTo = userData.assignedToUserId || null;\n      payloadObj.secondaryUserId = userData.secondaryAssignTo || null;\n    });\n    const intervalId = setInterval(() => {\n      var _a;\n\n      if (this.modalService.getModalsCount() === 0) {\n        const numberOfLeads = (_a = payloadObj === null || payloadObj === void 0 ? void 0 : payloadObj.LeadIds) === null || _a === void 0 ? void 0 : _a.length;\n\n        if (numberOfLeads >= 50) {\n          this.modalRef = this.modalService.show(this.trackerInfoModal, Object.assign({}, {\n            class: 'modal-400 top-modal ph-modal-unset',\n            ignoreBackdropClick: true,\n            keyboard: false\n          }));\n        }\n      }\n\n      clearInterval(intervalId);\n    }, 2000);\n    return payloadObj;\n  }\n\n  updateLeadInfo(userData, payload) {\n    this._store.select(getLeadStatusIsLoading).pipe(skipWhile(isLoading => isLoading), take(1)).subscribe(isLoading => {\n      var _a, _b, _c, _d, _e;\n\n      let masterLeadStatus = JSON.parse(localStorage.getItem('masterleadstatus'));\n      masterLeadStatus = this.customStatusList;\n      const id = userData.reason || userData.leadStatus || this.leadInfo.leadStatusId;\n      const status = masterLeadStatus.find(item => item.id === id || item.childTypes.some(child => child.id === id));\n      this.leadInfo = Object.assign(Object.assign({}, this.leadInfo), {\n        status: Object.assign(Object.assign({}, this.leadInfo.status), {\n          displayName: status.displayName || this.leadInfo.status.displayName\n        }),\n        rating: userData.rating,\n        projects: userData.projectsList,\n        properties: userData.propertiesList,\n        notes: userData.notes,\n        scheduledDate: userData.ScheduledDate,\n        assignTo: userData.assignedToUserId || this.leadInfo.assignTo || EMPTY_GUID,\n        secondaryUserId: userData.secondaryAssignTo || this.leadInfo.secondaryUserId || EMPTY_GUID\n      }); // Handle sub-status display name\n\n      let hasSubStatus = false;\n\n      for (let reason of this.callBackReason) {\n        if (((_c = (_b = (_a = this.updateForm) === null || _a === void 0 ? void 0 : _a.controls) === null || _b === void 0 ? void 0 : _b['reason']) === null || _c === void 0 ? void 0 : _c.value) === (reason === null || reason === void 0 ? void 0 : reason.id) && ((_e = (_d = this.leadInfo) === null || _d === void 0 ? void 0 : _d.status) === null || _e === void 0 ? void 0 : _e.childType)) {\n          this.leadInfo.status.childType = Object.assign(Object.assign({}, this.leadInfo.status.childType), {\n            displayName: reason.displayName\n          });\n          hasSubStatus = true;\n          break;\n        }\n      }\n\n      if (!hasSubStatus) {\n        this.leadInfo.status.childType = Object.assign(Object.assign({}, this.leadInfo.status.childType), {\n          displayName: ''\n        });\n      }\n    });\n  }\n\n  openBulkUpdatedStatus() {\n    this._store.dispatch(new FetchBulkOperation(1, 10, 'lead'));\n\n    this.modalRef = this.modalService.show(BulkOperationTrackerComponent, {\n      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',\n      initialState: {\n        moduleType: 'lead'\n      }\n    });\n  }\n\n  cleanStatusForm() {\n    this.deselectStatuses();\n    this.resetDocumentState(); // this.updateForm.controls['scheduledDate'].setValue('');\n    // this.updateForm.controls['notes'].setValue('');\n    // this.updateForm.controls['rating'].setValue('');\n    // this.updateForm.controls['projectsList'].setValue([]);\n    // this.updateForm.controls['propertiesList'].setValue([]);\n  }\n\n  updateSelectedReason(reason) {\n    var _a;\n\n    let customFields = (_a = [...(reason === null || reason === void 0 ? void 0 : reason.customFields)]) === null || _a === void 0 ? void 0 : _a.sort((a, b) => {\n      var _a, _b;\n\n      return ((_a = a === null || a === void 0 ? void 0 : a.field) === null || _a === void 0 ? void 0 : _a.orderRank) - ((_b = b === null || b === void 0 ? void 0 : b.field) === null || _b === void 0 ? void 0 : _b.orderRank);\n    });\n    reason = Object.assign(Object.assign({}, reason), {\n      customFields\n    });\n    this.selectedReason = reason;\n  }\n\n  closeModal() {\n    this.isVisitDone = false;\n    this.modalRef.hide();\n    this.updateForm.reset();\n    this.hideStatus = false;\n    this.selectedReason = null;\n  }\n\n  fullBookingFormModal() {\n    if (this.updateForm.invalid) {\n      validateAllFormFields(this.updateForm);\n      return;\n    }\n\n    if (this.updateForm.value.notes) {\n      this.selectedProject.notes = this.updateForm.value.notes;\n    }\n\n    this.isShowBookingFormBtn = false;\n    this.updateStatus(true);\n\n    this._store.select(getManagerDetails).pipe(take(1)).subscribe(data => {\n      var _a;\n\n      this.userDetails = data;\n      let initialState = {\n        selectedProject: this.selectedProject,\n        leadInfo: this.leadInfo,\n        userDetails: this.userDetails,\n        miniBookDate: (_a = this.updateForm.get('bookedDate')) === null || _a === void 0 ? void 0 : _a.value\n      };\n      this.modalRef = this.modalService.show(BookingFormComponent, {\n        class: 'right-modal modal-550 ip-modal-unset',\n        initialState\n      });\n      this.modalRef.onHide.subscribe(() => {\n        this.isShowBookingFormBtn = true;\n      });\n    });\n  }\n\n  switchTabProjectProperty(value) {\n    var _a, _b, _c;\n\n    if (value === 'Property') {\n      this.isShowUnitInfoField = false;\n      (_a = this.updateForm.controls['projectsList']) === null || _a === void 0 ? void 0 : _a.setValue(null);\n      (_b = this.updateForm.controls['chosenProject']) === null || _b === void 0 ? void 0 : _b.setValue(null);\n      toggleValidation(VALIDATION_SET, this.updateForm, 'chosenProperty', [Validators.required]);\n      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProject');\n      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenUnit');\n    } else {\n      this.isShowUnitInfoField = true;\n      (_c = this.updateForm.controls['chosenProperty']) === null || _c === void 0 ? void 0 : _c.setValue(null);\n\n      if (this.isProjectMandatory) {\n        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenProject', [Validators.required]);\n        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenUnit', [Validators.required]);\n      }\n\n      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProperty');\n    }\n  }\n\n  onInputAgreementValue(value) {\n    this.selectedProject.agreementValue = value;\n  }\n\n  onPropertyChange(id) {\n    toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProject');\n    toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenUnit');\n    this.isSelectedPropertyOrProject = 'Property';\n    this.isShowUnitInfoField = false;\n\n    if (id) {\n      this._store.dispatch(new FetchPropertyById(id));\n    }\n\n    this._store.select(getPropertyListDetails).pipe(takeUntil(this.stopper)).subscribe(data => {\n      var _a, _b, _c, _d, _e;\n\n      if (data) {\n        this.selectedProject.selectedpropertyname = data.title;\n        this.selectedProject.buildername = (_a = data.ownerDetails) === null || _a === void 0 ? void 0 : _a.name;\n        this.selectedProject.sealablearea = (_b = data.dimension) === null || _b === void 0 ? void 0 : _b.saleableArea;\n        this.selectedProject.carpetarea = (_c = data.dimension) === null || _c === void 0 ? void 0 : _c.carpetArea;\n        this.selectedProject.brokerageCurrency = (_d = data.monetaryInfo) === null || _d === void 0 ? void 0 : _d.brokerageCurrency;\n        this.selectedProject.brockragecharge = (_e = data.monetaryInfo) === null || _e === void 0 ? void 0 : _e.brokerage;\n        this.selectedProject.isprojectproperty = this.isSelectedPropertyOrProject;\n      }\n    });\n  }\n\n  onProjectChange(id) {\n    var _a;\n\n    if (id) {\n      this._store.dispatch(new FetchProjectById(id));\n\n      if (this.isProjectMandatory) {\n        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenUnit', [Validators.required]);\n        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenProject', [Validators.required]);\n      } else {\n        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenUnit');\n        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProject');\n      }\n    }\n\n    toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProperty');\n    this.isSelectedPropertyOrProject = 'Project';\n    this.isShowUnitInfoField = true;\n    (_a = this.updateForm.get('chosenUnit')) === null || _a === void 0 ? void 0 : _a.reset();\n    combineLatest([this._store.select(getIsProjectByIdLoading), this._store.select(getSelectedProjectById)]).pipe(takeUntil(this.stopper)).subscribe(([isLoading, projectData]) => {\n      var _a, _b, _c;\n\n      this.isUnitInfoDataLoading = isLoading;\n\n      if (projectData) {\n        this.unitInfo = projectData.unitTypes;\n        this.selectedProject = {\n          selectedpropertyname: projectData.name,\n          buildername: (_a = projectData.builderDetail) === null || _a === void 0 ? void 0 : _a.name,\n          brockragecharge: (_b = projectData.monetaryInfo) === null || _b === void 0 ? void 0 : _b.brokerage,\n          brokerageCurrency: (_c = projectData.monetaryInfo) === null || _c === void 0 ? void 0 : _c.brokerageCurrency,\n          isprojectproperty: this.isSelectedPropertyOrProject,\n          allprojectdata: projectData\n        };\n      }\n    });\n  }\n\n  onChoosenUnitChange(event) {\n    var _a;\n\n    (_a = this.unitInfo) === null || _a === void 0 ? void 0 : _a.map(data => {\n      if (data.id === (event === null || event === void 0 ? void 0 : event.id)) {\n        this.selectedProject.carpetarea = data === null || data === void 0 ? void 0 : data.carpetArea;\n        this.selectedProject.sealablearea = data === null || data === void 0 ? void 0 : data.superBuildUpArea;\n        this.selectedProject.unitname = data === null || data === void 0 ? void 0 : data.name;\n      }\n    });\n  }\n\n  patchMiniBookDataIfBooked(id) {\n    var _a;\n\n    id && this._store.dispatch(new FetchInvoiceById(id));\n    (_a = this._store.select(getBookingData)) === null || _a === void 0 ? void 0 : _a.subscribe(bookingDetails => {\n      var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;\n\n      if (!bookingDetails) return;\n      const {\n        projects,\n        properties,\n        unitType,\n        bookedUnderName,\n        agreementValue,\n        notes,\n        currency\n      } = bookingDetails;\n      const hasProjects = Array.isArray(projects) && projects.length > 0;\n      const hasProperties = Array.isArray(properties) && properties.length > 0;\n      if (hasProjects) this.onProjectChange(projects[0].id);\n      if (hasProperties) this.onPropertyChange(properties[0].id);\n      const validAgreementValue = agreementValue > 0 ? agreementValue : null;\n      this.selectedProject.agreementValue = validAgreementValue;\n      this.updateForm.patchValue({\n        reason: (_c = (_b = (_a = this.leadInfo) === null || _a === void 0 ? void 0 : _a.status) === null || _b === void 0 ? void 0 : _b.childType) === null || _c === void 0 ? void 0 : _c.id,\n        leadStatus: (_e = (_d = this.leadInfo) === null || _d === void 0 ? void 0 : _d.status) === null || _e === void 0 ? void 0 : _e.id,\n        bookedDate: patchTimeZoneWithTime(bookingDetails.bookedDate, (_g = (_f = this.userBasicDetails) === null || _f === void 0 ? void 0 : _f.timeZoneInfo) === null || _g === void 0 ? void 0 : _g.baseUTcOffset),\n        bookedUnderName,\n        agreementValue: validAgreementValue,\n        chosenProperty: hasProperties ? (_h = properties[0]) === null || _h === void 0 ? void 0 : _h.id : null,\n        chosenProject: hasProjects ? (_j = projects[0]) === null || _j === void 0 ? void 0 : _j.id : null,\n        chosenUnit: unitType === null || unitType === void 0 ? void 0 : unitType.id,\n        notes,\n        currency,\n        assignedToUserId: (_k = this.leadInfo) === null || _k === void 0 ? void 0 : _k.assignTo,\n        secondaryAssignTo: ((_l = this.leadInfo) === null || _l === void 0 ? void 0 : _l.secondaryUserId) !== EMPTY_GUID ? (_m = this.leadInfo) === null || _m === void 0 ? void 0 : _m.secondaryUserId : null\n      });\n      this.selectedReason = (_q = (_p = (_o = this.leadInfo) === null || _o === void 0 ? void 0 : _o.status) === null || _p === void 0 ? void 0 : _p.childType) === null || _q === void 0 ? void 0 : _q.displayName;\n      const projectProperty = hasProjects ? 'Project' : hasProperties ? 'Property' : 'Property';\n      (_s = (_r = this.updateForm) === null || _r === void 0 ? void 0 : _r.get('projectProperty')) === null || _s === void 0 ? void 0 : _s.setValue(projectProperty);\n\n      if (projectProperty === 'Project') {\n        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProperty');\n      } else if (projectProperty === 'Property') {\n        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProject');\n        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenUnit');\n      }\n\n      if (unitType === null || unitType === void 0 ? void 0 : unitType.id) this.onChoosenUnitChange(unitType.id);\n      this.miniagreementValueInWords = formatBudget(agreementValue, currency || this.defaultCurrency);\n    });\n  }\n\n  isSelectedAllBookedOrInvoicedLead() {\n    if (!Array.isArray(this.leadInfo)) {\n      return false;\n    }\n\n    if (this.leadInfo.length === 0) {\n      return false;\n    }\n\n    const allBookedOrInvoiced = this.leadInfo.every(lead => {\n      var _a, _b;\n\n      return ((_a = lead === null || lead === void 0 ? void 0 : lead.status) === null || _a === void 0 ? void 0 : _a.shouldUseForBooking) || ((_b = lead === null || lead === void 0 ? void 0 : lead.status) === null || _b === void 0 ? void 0 : _b.shouldUseForInvoice);\n    });\n    return allBookedOrInvoiced;\n  }\n\n  checkBookedUnderName(event) {\n    const inputValue = event.target.value.trim();\n\n    if (inputValue) {\n      this.updateForm.controls['bookedUnderName'].setValue(inputValue);\n      this.updateForm.controls['bookedUnderName'].setErrors(null);\n    } else {\n      this.updateForm.controls['bookedUnderName'].setErrors({\n        whitespace: true\n      });\n    }\n  }\n\n  onFileSelection(e) {\n    this.selectedFile = e[0];\n  }\n\n  addDocument() {\n    if (!this.documentsForm.valid || !this.selectedFile) {\n      validateAllFormFields(this.documentsForm);\n      return;\n    }\n\n    this.uploadedFiles.push(this.selectedFile);\n    this.uploadedFilesName.push(this.documentsForm.value.docTitle || this.selectedFileName);\n    this.isUpload = false;\n    this.documentsForm.reset();\n    this.selectedFile = null;\n  }\n\n  onClickRemoveDocument(docIndex) {\n    this.uploadedFiles = this.uploadedFiles.filter((_, index) => index !== docIndex);\n    this.uploadedFilesName = this.uploadedFilesName.filter((_, index) => index !== docIndex);\n  }\n\n  resetDocumentState() {\n    this.uploadedFiles = [];\n    this.uploadedFilesName = [];\n    this.isUpload = false;\n    this.selectedFile = '';\n    this.selectedFileName = null;\n    this.documentsForm.reset();\n  }\n\n  uploadDocumentsToS3(isSaveAndNext = false) {\n    this.s3UploadService.uploadImageBase64(this.uploadedFiles, FolderNamesS3.LeadDocument).pipe(takeUntil(this.stopper)).subscribe(response => {\n      var _a;\n\n      if (response.data.length) {\n        let documents = [];\n        (_a = this.uploadedFilesName) === null || _a === void 0 ? void 0 : _a.forEach((name, index) => {\n          documents.push({\n            documentName: name,\n            filePath: response.data[index].toString()\n          });\n        });\n        this.proceedWithStatusUpdate(isSaveAndNext, documents);\n      }\n    });\n  }\n\n  proceedWithStatusUpdate(isSaveAndNext = false, documents) {\n    const userData = this.updateForm.value;\n    let postPonedStatusId = '';\n    this.addMoreLocation();\n    const addressPayload = this.prepareAddressPayload(userData);\n    const isAddressPayloadEmpty = !(addressPayload === null || addressPayload === void 0 ? void 0 : addressPayload.length);\n    const payload = this.createPayload(userData, addressPayload, isAddressPayloadEmpty, postPonedStatusId);\n\n    if (documents === null || documents === void 0 ? void 0 : documents.length) {\n      payload.documents = documents;\n    }\n\n    if (this.selectedProject) {\n      this.handleProjectPropertySelection(payload);\n    }\n\n    this.assignUsers(userData, payload);\n    this.handleLeadStatus(payload);\n\n    if (!this.isBulkUpdate) {\n      this.handleSingleUpdate(payload);\n    } else {\n      this.handleBulkUpdate(userData, addressPayload, isAddressPayloadEmpty, postPonedStatusId, documents);\n    }\n\n    this.updateLeadInfo(userData, payload);\n\n    if (!this.isBulkUpdate) {\n      this.cleanStatusForm();\n    }\n\n    if (!isSaveAndNext) {\n      if (!this.isBulkUpdate) {\n        this._store.select(getLeadStatusIsLoading).pipe(skipWhile(isLoading => isLoading), take(1)).subscribe(isLoading => {\n          if (!isLoading) {\n            this.modalRef.hide();\n          }\n        });\n      } else {\n        this.modalRef.hide();\n      }\n\n      return;\n    }\n\n    this._leadPreviewComponent.nextData();\n  }\n\n  ngOnDestroy() {\n    this.stopper.next();\n    this.stopper.complete();\n    this.isProjectSubscription.unsubscribe();\n  }\n\n}\n\nCustomStatusChangeComponent.ɵfac = function CustomStatusChangeComponent_Factory(t) {\n  return new (t || CustomStatusChangeComponent)(i0.ɵɵdirectiveInject(i1.BsModalRef), i0.ɵɵdirectiveInject(i1.BsModalService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.Store), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i4.LeadPreviewComponent), i0.ɵɵdirectiveInject(i5.NotificationsService), i0.ɵɵdirectiveInject(i6.ShareDataService), i0.ɵɵdirectiveInject(i7.Router), i0.ɵɵdirectiveInject(i8.BlobStorageService));\n};\n\nCustomStatusChangeComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CustomStatusChangeComponent,\n  selectors: [[\"custom-status-change\"]],\n  viewQuery: function CustomStatusChangeComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n      i0.ɵɵviewQuery(_c2, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.statusForm = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.noUnitFound = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trackerInfoModal = _t.first);\n    }\n  },\n  inputs: {\n    leadInfo: \"leadInfo\",\n    canShowStatusPopupInPreview: \"canShowStatusPopupInPreview\",\n    canUpdateStatus: \"canUpdateStatus\",\n    isLeadPreview: \"isLeadPreview\",\n    isLastLead: \"isLastLead\",\n    closeLeadPreviewModal: \"closeLeadPreviewModal\",\n    whatsAppComp: \"whatsAppComp\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 23,\n  vars: 21,\n  consts: [[\"id\", \"status-form\", 1, \"scrollbar\", 3, \"ngClass\"], [\"statusForm\", \"\"], [4, \"ngIf\"], [\"class\", \"text-red ml-20 mb-10\", 4, \"ngIf\"], [1, \"pr-8\", \"ml-20\"], [\"autocomplete\", \"off\", 3, \"formGroup\", \"ngClass\"], [\"class\", \"d-flex flex-wrap\", 4, \"ngIf\", \"ngIfElse\"], [\"customForm\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"justify-end mt-20 modal-footer bg-white gap-2\", 3, \"ngClass\", 4, \"ngIf\", \"ngIfElse\"], [\"ratLoader\", \"\"], [\"fieldLoader\", \"\"], [\"noUnitFound\", \"\"], [\"trackerInfoModal\", \"\"], [1, \"fw-600\", \"text-large\", \"text-slate-160\", \"ml-20\", \"fv-sm-caps\"], [1, \"bg-secondary\", \"mx-20\", \"mb-20\", \"mt-10\", \"px-16\", \"py-12\", \"br-4\"], [1, \"align-center\", \"w-100\"], [1, \"align-center\", \"w-50\"], [1, \"icon\", \"ic-person-walking\", \"ic-slate-90\", \"ic-xxs\", \"mr-8\"], [1, \"fv-sm-caps\", \"fw-600\"], [\"class\", \"align-center w-50\", 4, \"ngIf\"], [\"class\", \"d-flex mt-16\", 4, \"ngIf\"], [\"class\", \"flex-center w-100 cursor-pointer mt-16\", 3, \"click\", 4, \"ngIf\"], [1, \"icon\", \"ic-alarm\", \"ic-slate-90\", \"ic-xxs\", \"mr-8\"], [\"class\", \"text-truncate-1 break-all text-sm\", 4, \"ngIf\"], [1, \"text-truncate-1\", \"break-all\", \"text-sm\"], [1, \"d-flex\", \"mt-16\"], [1, \"icon\", \"ic-message-lines\", \"ic-slate-90\", \"ic-xxs\", \"mr-8\"], [1, \"text-black-20\", \"text-sm\", \"word-break\", \"max-w-460\", 3, \"ngClass\"], [1, \"flex-center\", \"w-100\", \"cursor-pointer\", \"mt-16\", 3, \"click\"], [1, \"text-sm\", \"fw-600\", \"text-accent-green\"], [1, \"icon\", \"ic-chevron-down\", \"ic-xxxs\", \"ic-accent-green\", \"ml-8\", \"mt-2\", 3, \"ngClass\"], [1, \"text-red\", \"ml-20\", \"mb-10\"], [1, \"d-flex\", \"flex-wrap\"], [\"class\", \"w-100 flex-center\", 4, \"ngIf\"], [\"class\", \"text-red my-10\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"text-red\", \"my-10\", 3, \"ngClass\"], [\"formControlName\", \"leadStatus\", \"type\", \"radio\", \"autocomplete\", \"off\", \"required\", \"\", 1, \"btn-check\", 3, \"value\", \"id\", \"automate-id\"], [1, \"status-badge\", 3, \"for\", \"click\"], [\"class\", \"flex-end w-100 text-error-red\", 4, \"ngIf\"], [1, \"flex-end\", \"w-100\", \"text-error-red\"], [1, \"w-100\", \"flex-center\"], [\"src\", \"../../../../assets/gifs/muso-walk.gif\", \"alt\", \"Muso Walking\", 1, \"h-100-450\"], [1, \"align-center\", \"mr-4\", \"mb-4\", \"w-100\"], [1, \"m-0\", \"status-badge\", \"bg-dark-700\", \"text-white\", \"br-5\"], [\"id\", \"clkCancelSelectedBadge\", \"data-automate-id\", \"clkCancelSelectedBadge\", 1, \"icon\", \"ic-close-secondary\", \"ic-light-pale\", \"ic-xxs\", \"ml-10\", 3, \"click\"], [1, \"p-12\", \"position-relative\", \"min-w-137\"], [\"type\", \"radio\", \"autocomplete\", \"off\", \"formControlName\", \"reason\", 1, \"btn-check\", 3, \"value\", \"id\", \"automate-id\", \"click\"], [1, \"status-badge\", 3, \"for\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"error-message\"], [1, \"checkbox-container\", \"mt-10\"], [\"type\", \"checkbox\", \"id\", \"inpMeetingDone\", \"data-automate-id\", \"inpMeetingDone\", 1, \"mr-10\", 3, \"checked\", \"change\"], [1, \"checkmark\"], [\"type\", \"checkbox\", \"id\", \"inpVisitDone\", \"data-automate-id\", \"inpVisitDone\", 1, \"mr-10\", 3, \"checked\", \"change\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [1, \"position-relative\"], [3, \"ngClass\"], [3, \"control\", \"label\"], [\"type\", \"text\", \"id\", \"soldPrice\", \"data-automate-id\", \"soldPrice\", \"formControlName\", \"soldPrice\", \"placeholder\", \"ex. 4000000\", \"maxlength\", \"10\", 3, \"keydown\"], [\"class\", \"position-absolute right-12 bottom-12 text-accent-green fw-semi-bold text-sm\", 4, \"ngIf\"], [1, \"position-absolute\", \"right-12\", \"bottom-12\", \"text-accent-green\", \"fw-semi-bold\", \"text-sm\"], [1, \"icon\", \"ic-rupee\", \"ic-accent-green\", \"ic-x-xs\", \"mr-4\"], [\"readonly\", \"\", \"id\", \"inpAppDateTime\", \"data-automate-id\", \"inpAppDateTime\", \"formControlName\", \"ScheduledDate\", \"placeholder\", \"ex. 19/06/2025, 12:00 pm\", 3, \"owlDateTime\", \"owlDateTimeTrigger\", \"min\"], [3, \"hour12Timer\", \"startAt\", \"afterPickerOpen\"], [\"dt1\", \"\"], [\"label\", \"Projects\", 3, \"control\"], [\"ResizableDropdown\", \"\", \"name\", \"Projects\", \"formControlName\", \"Projects\", \"bindLabel\", \"name\", \"bindValue\", \"name\", \"addTagText\", \"Create New Project\", \"placeholder\", \"Select/Create Project\", 1, \"bg-white\", 3, \"virtualScroll\", \"items\", \"multiple\", \"closeOnSelect\", \"addTag\"], [\"ng-option-tmp\", \"\"], [1, \"checkbox-container\"], [\"type\", \"checkbox\", 3, \"id\", \"automate-id\", \"checked\"], [\"label\", \"Properties\", 3, \"control\"], [\"ResizableDropdown\", \"\", \"name\", \"Properties\", \"formControlName\", \"Properties\", \"addTagText\", \"Create New Property\", \"class\", \"bg-white\", \"placeholder\", \"Select/Create Property\", 3, \"virtualScroll\", \"items\", \"multiple\", \"closeOnSelect\", \"addTag\", 4, \"ngIf\", \"ngIfElse\"], [\"ResizableDropdown\", \"\", \"name\", \"Properties\", \"formControlName\", \"Properties\", \"addTagText\", \"Create New Property\", \"placeholder\", \"Select/Create Property\", 1, \"bg-white\", 3, \"virtualScroll\", \"items\", \"multiple\", \"closeOnSelect\", \"addTag\"], [1, \"mt-16\", \"w-100\"], [1, \"flex-between\"], [\"class\", \"flex-center btn btn-accent-green btn-md w-170 text-sm clear-padding-x\", 3, \"click\", 4, \"ngIf\"], [1, \"flex-center\", \"btn\", \"btn-accent-green\", \"btn-md\", \"w-170\", \"text-sm\", \"clear-padding-x\", 3, \"click\"], [1, \"icon\", \"ic-cloud-upload\", \"ic-xxs\", \"mr-8\"], [\"autocomplete\", \"off\", 1, \"mb-16\", 3, \"formGroup\"], [1, \"form-group\"], [1, \"field-label\"], [\"type\", \"text\", \"formControlName\", \"docTitle\", \"id\", \"inpDocTitle\", \"data-automate-id\", \"inpDocTitle\", \"placeholder\", \"ex. Title\"], [1, \"br-6\", \"p-10\", \"w-100\", \"bg-accent-green-light\", \"mt-16\", \"custom-flex-row\"], [3, \"allowedFileType\", \"allowedFileFormat\", \"uploadedFile\", \"uploadedFileName\"], [1, \"flex-end\", \"mt-10\"], [1, \"btn-gray\", \"mr-10\", 3, \"click\"], [1, \"btn-coal\", 3, \"click\"], [1, \"d-flex\", \"flex-wrap\", \"tb-w-100\", \"ip-flex-center\", \"my-12\"], [\"class\", \"flex-col w-100\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-col\", \"w-100\"], [1, \"p-4\", \"flex-between\", \"bg-white\", \"br-6\", \"mb-10\", \"w-100\"], [1, \"align-center\"], [1, \"icon\", \"ic-file\", \"ic-sm\", \"ic-black\"], [1, \"text-truncate-1\", \"break-all\", \"fw-600\", \"ml-8\"], [\"id\", \"clkDeleteLeadDoc\", \"data-automate-id\", \"clkDeleteLeadDoc\", 1, \"icon\", \"ic-delete\", \"ic-red\", \"ic-sm\", \"cursor-pointer\", \"ml-10\", 3, \"click\"], [1, \"flex-between\", \"flex-wrap\", \"w-100\"], [1, \"w-50\", \"ph-w-100\"], [1, \"mr-10\"], [1, \"field-label-req\"], [\"label\", \"Booking under name\", 3, \"control\"], [\"type\", \"text\", \"id\", \"bookedUnderName\", \"data-automate-id\", \"bookedUnderName\", \"formControlName\", \"bookedUnderName\", \"placeholder\", \"ex. Mounika Pampana\", 3, \"blur\"], [\"label\", \"Booked Date\", 3, \"control\"], [\"readonly\", \"\", \"id\", \"inpBookDateTime\", \"data-automate-id\", \"inpBookDateTime\", \"formControlName\", \"bookedDate\", \"placeholder\", \"ex. 5/03/2025, 12:00 pm\", 3, \"owlDateTime\", \"max\", \"owlDateTimeTrigger\"], [1, \"field-rupees-tag\", \"w-50\", \"ph-w-100\"], [1, \"position-relative\", \"budget-dropdown\", \"mr-10\"], [\"label\", \"Agreement Value\", 3, \"control\"], [\"type\", \"number\", \"formControlName\", \"agreementValue\", \"min\", \"1\", \"id\", \"agreementValue\", \"data-automate-id\", \"agreementValue\", \"placeholder\", \"ex. 4000000\", \"maxlength\", \"10\", 3, \"wheel\", \"input\"], [1, \"no-validation\"], [\"showCurrencySymbol\", \"\"], [\"class\", \"position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm\", 4, \"ngIf\"], [1, \"w-100\"], [1, \"flex-between\", \"w-50\", \"ph-w-100\"], [\"class\", \"align-center\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"w-50 ph-w-100\", 4, \"ngIf\"], [\"class\", \"position-relative w-50 ph-w-100\", 4, \"ngIf\"], [\"formControlName\", \"currency\", \"ResizableDropdown\", \"\", 1, \"ml-4\", \"mt-4\", \"position-absolute\", \"top-0\", \"manage-dropdown\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [3, \"title\"], [1, \"rupees\", \"px-12\", \"py-8\", \"fw-600\", \"m-4\"], [1, \"position-absolute\", \"right-5\", \"nbottom-0\", \"text-accent-green\", \"fw-semi-bold\", \"text-sm\"], [1, \"form-check\", \"form-check-inline\", \"align-center\"], [\"type\", \"radio\", \"data-automate-id\", \"projectProperty\", \"name\", \"projectProperty\", \"formControlName\", \"projectProperty\", 1, \"radio-check-input\", \"mr-10\", 3, \"id\", \"value\", \"change\"], [1, \"fw-600\", \"text-secondary\", \"cursor-pointer\", \"text-large\", \"m-0\", 3, \"for\"], [\"label\", \"Property\", 3, \"control\"], [\"ResizableDropdown\", \"\", \"formControlName\", \"chosenProperty\", \"dropdownPosition\", \"bottom\", \"placeholder\", \"ex. ABC Property\", \"class\", \"bg-white mr-10\", \"bindValue\", \"id\", \"bindLabel\", \"title\", 3, \"virtualScroll\", \"items\", \"change\", 4, \"ngIf\", \"ngIfElse\"], [\"ResizableDropdown\", \"\", \"formControlName\", \"chosenProperty\", \"dropdownPosition\", \"bottom\", \"placeholder\", \"ex. ABC Property\", \"bindValue\", \"id\", \"bindLabel\", \"title\", 1, \"bg-white\", \"mr-10\", 3, \"virtualScroll\", \"items\", \"change\"], [\"label\", \"Project\", 3, \"control\"], [\"ResizableDropdown\", \"\", \"formControlName\", \"chosenProject\", \"dropdownPosition\", \"bottom\", \"placeholder\", \"ex. XYZ Project\", \"class\", \"bg-white\", \"bindValue\", \"id\", \"bindLabel\", \"name\", 3, \"virtualScroll\", \"items\", \"change\", 4, \"ngIf\", \"ngIfElse\"], [\"ResizableDropdown\", \"\", \"formControlName\", \"chosenProject\", \"dropdownPosition\", \"bottom\", \"placeholder\", \"ex. XYZ Project\", \"bindValue\", \"id\", \"bindLabel\", \"name\", 1, \"bg-white\", 3, \"virtualScroll\", \"items\", \"change\"], [1, \"position-relative\", \"w-50\", \"ph-w-100\"], [1, \"form-group\", \"w-100\"], [\"label\", \"Unit\", 3, \"control\"], [\"id\", \"chosenUnit\", \"data-automate-id\", \"chosenUnit\", \"ResizableDropdown\", \"\", \"bindLabel\", \"name\", \"bindValue\", \"id\", \"formControlName\", \"chosenUnit\", \"dropdownPosition\", \"bottom\", \"placeholder\", \"select\", 1, \"bg-white\", 3, \"virtualScroll\", \"items\", \"readonly\", \"change\"], [1, \"d-flex\", \"flex-wrap\", \"ph-flex-col\"], [1, \"ph-w-100\", \"dashboard-dropdown\", 3, \"ngClass\"], [\"class\", \"text-sm text-black-100 mb-4 mt-2\", 4, \"ngIf\"], [\"label\", \"This\", 3, \"control\"], [\"placeholder\", \"Select\", \"name\", \"user\", \"formControlName\", \"assignedToUserId\", \"ResizableDropdown\", \"\", 1, \"mr-10\", \"ph-mr-0\", 3, \"virtualScroll\", \"ngClass\"], [3, \"value\", 4, \"ngIf\"], [\"class\", \"w-50 ph-w-100 ph-mt-10 dashboard-dropdown\", 4, \"ngIf\"], [1, \"text-sm\", \"text-black-100\", \"mb-4\", \"mt-2\"], [1, \"text-light-gray\"], [1, \"w-50\", \"ph-w-100\", \"ph-mt-10\", \"dashboard-dropdown\"], [\"placeholder\", \"Select\", \"name\", \"user\", \"formControlName\", \"secondaryAssignTo\", \"ResizableDropdown\", \"\", 3, \"virtualScroll\", \"ngClass\"], [\"rows\", \"2\", \"id\", \"txtUpdateStatusNotes\", \"data-automate-id\", \"txtUpdateStatusNotes\", \"formControlName\", \"notes\", \"placeholder\", \"ex. I want to say \"], [1, \"justify-end\", \"mt-20\", \"modal-footer\", \"bg-white\", \"gap-2\", 3, \"ngClass\"], [\"id\", \"btnCancelUpdateStatus\", \"data-automate-id\", \"btnCancelUpdateStatus\", 1, \"fw-semi-bold\", \"text-black-200\", \"text-decoration-underline\", \"cursor-pointer\", 3, \"click\"], [1, \"border-left\", \"h-16\"], [\"class\", \"btn-coal px-10 min-w-fit-content\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn-coal\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn-coal\", \"id\", \"btnSaveUpdateStatus\", \"data-automate-id\", \"btnSaveUpdateStatus\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn-coal w-150\", \"id\", \"btnSaveUpdateStatus\", \"data-automate-id\", \"btnSaveUpdateStatus\", 3, \"click\", 4, \"ngIf\"], [1, \"btn-coal\", \"px-10\", \"min-w-fit-content\", 3, \"click\"], [\"id\", \"btnSaveUpdateStatus\", \"data-automate-id\", \"btnSaveUpdateStatus\", 1, \"btn-coal\", 3, \"click\"], [\"id\", \"btnSaveUpdateStatus\", \"data-automate-id\", \"btnSaveUpdateStatus\", 1, \"btn-coal\", \"w-150\", 3, \"click\"], [1, \"mt-20\", \"modal-footer\", 3, \"ngClass\"], [\"src\", \"assets/images/loader-rat.svg\", \"alt\", \"loader\", 1, \"rat-loader\", \"h-20px\", \"w-20px\"], [1, \"pe-none\", \"blinking\", 3, \"virtualScroll\"], [1, \"p-20\"], [1, \"text-center\"], [1, \"mt-20\", \"flex-end\"], [\"type\", \"button\", 1, \"btn-green\", 3, \"click\"], [1, \"px-20\", \"py-16\", \"fw-semi-bold\", \"bg-coal\", \"text-white\"], [1, \"p-20\", \"flex-center-col\"], [1, \"text-black-100\", \"fw-600\", \"mb-10\", \"text-center\", \"word-break\", \"line-break\"], [1, \"text-black-100\", \"fw-semi-bold\", \"text-center\", \"word-break\", \"line-break\"], [1, \"cursor-pointer\", \"text-accent-green\", \"header-3\", \"fw-600\", 3, \"click\"], [1, \"btn-green\", \"mt-30\", 3, \"click\"]],\n  template: function CustomStatusChangeComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵtemplate(2, CustomStatusChangeComponent_div_2_Template, 16, 14, \"div\", 2);\n      i0.ɵɵtemplate(3, CustomStatusChangeComponent_div_3_Template, 3, 3, \"div\", 3);\n      i0.ɵɵelementStart(4, \"div\", 4)(5, \"form\", 5)(6, \"div\");\n      i0.ɵɵtemplate(7, CustomStatusChangeComponent_div_7_Template, 3, 2, \"div\", 6);\n      i0.ɵɵtemplate(8, CustomStatusChangeComponent_ng_container_8_Template, 2, 2, \"ng-container\", 2);\n      i0.ɵɵtemplate(9, CustomStatusChangeComponent_ng_template_9_Template, 1, 1, \"ng-template\", null, 7, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(11, CustomStatusChangeComponent_ng_container_11_Template, 35, 17, \"ng-container\", 8);\n      i0.ɵɵtemplate(12, CustomStatusChangeComponent_ng_container_12_Template, 13, 16, \"ng-container\", 2);\n      i0.ɵɵtemplate(13, CustomStatusChangeComponent_ng_container_13_Template, 7, 8, \"ng-container\", 2);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵtemplate(14, CustomStatusChangeComponent_div_14_Template, 9, 10, \"div\", 9);\n      i0.ɵɵtemplate(15, CustomStatusChangeComponent_ng_template_15_Template, 2, 4, \"ng-template\", null, 10, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(17, CustomStatusChangeComponent_ng_template_17_Template, 1, 1, \"ng-template\", null, 11, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(19, CustomStatusChangeComponent_ng_template_19_Template, 6, 0, \"ng-template\", null, 12, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(21, CustomStatusChangeComponent_ng_template_21_Template, 13, 3, \"ng-template\", null, 13, i0.ɵɵtemplateRefExtractor);\n    }\n\n    if (rf & 2) {\n      const _r5 = i0.ɵɵreference(10);\n\n      const _r11 = i0.ɵɵreference(16);\n\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(14, _c10, ctx.leadStatusIsLoading || ctx.multipleLeadsIsLoading, !ctx.elementHeight && !ctx.whatsAppComp, !ctx.elementHeight && ctx.whatsAppComp));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isBulkUpdate);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isUnassignedLead);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.updateForm)(\"ngClass\", i0.ɵɵpureFunction2(18, _c11, !ctx.canUpdateStatus || ctx.isUnassignedLead, ctx.isUnassignedLead));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.canUpdateStatus && !ctx.isCustomStatusListLoading)(\"ngIfElse\", _r11);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.leadSource);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.isShowBookingFormBtn && !ctx.isSelectedAllBookedOrInvoicedLead() && (ctx.currentPath === \"/invoice\" || !(ctx.selectedStatus == null ? null : ctx.selectedStatus.shouldUseForInvoice)))(\"ngIfElse\", _r5);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.canAssignLead && !ctx.canShowStatusPopupInPreview);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.canUpdateStatus && !ctx.canShowStatusPopupInPreview);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.leadStatusIsLoading && !ctx.multipleLeadsIsLoading && ctx.canUpdateStatus)(\"ngIfElse\", _r11);\n    }\n  },\n  dependencies: [i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NumberValueAccessor, i2.RadioControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.MaxLengthValidator, i2.MinValidator, i9.NgClass, i9.NgForOf, i9.NgIf, i9.NgSwitch, i9.NgSwitchCase, i10.NgSelectComponent, i10.NgOptionComponent, i10.NgOptionTemplateDirective, i11.FormErrorWrapperComponent, i2.FormGroupDirective, i2.FormControlName, i12.OwlDateTimeTriggerDirective, i12.OwlDateTimeInputDirective, i12.OwlDateTimeComponent, i13.BrowseDropUploadComponent, i14.ResizableDropdownDirective, i9.TitleCasePipe, i15.TranslatePipe],\n  encapsulation: 2\n});", "map": {"version": 3, "mappings": ";AAAA,SAEEA,iBAFF,EAIEC,UAJF,EAKEC,YALF,EAUEC,aAVF,EAWEC,WAXF,QAaO,eAbP;AAeA,SAMEC,UANF,QAOO,gBAPP;AAQA,SAASC,aAAT,QAAsC,iBAAtC;AAGA,OAAO,KAAKC,MAAZ,MAAwB,QAAxB;AAEA,SAASC,eAAT,EAA0BC,aAA1B,QAA6D,MAA7D;AACA,SACEC,YADF,EAEEC,oBAFF,EAGEC,MAHF,EAIEC,SAJF,EAKEC,IALF,EAMEC,SANF,QAOO,gBAPP;AASA,SACEC,UADF,EAEEC,mBAFF,EAGEC,aAHF,EAIEC,wBAJF,EAKEC,gBALF,EAMEC,cANF,QAOO,uBAPP;AAQA,SAASC,aAAT,EAAwBC,UAAxB,QAA0C,kBAA1C;AAEA,SACEC,YADF,EAEEC,cAFF,EAGEC,YAHF,EAIEC,eAJF,EAKEC,WALF,EAMEC,cANF,EAOEC,qBAPF,EAQEC,qBARF,EASEC,uBATF,EAUEC,gBAVF,EAWEC,qBAXF,QAYO,gCAZP;AAaA,SAASC,wBAAT,QAAyC,oEAAzC;AACA,SAASC,0BAAT,QAA2C,0DAA3C;AACA,SAASC,gBAAT,QAAiC,0CAAjC;AACA,SAASC,cAAT,QAA+B,0CAA/B;AACA,SACEC,kBADF,EAEEC,gBAFF,EAGEC,kBAHF,QAIO,oCAJP;AAKA,SACEC,sBADF,EAEEC,8BAFF,QAGO,oCAHP;AAIA,SAASC,kBAAT,QAAmC,wCAAnC;AACA,SACEC,cADF,EAEEC,uBAFF,QAGO,mDAHP;AAIA,SAASC,gBAAT,QAAiC,yCAAjC;AACA,SACEC,uBADF,EAEEC,qBAFF,EAGEC,8BAHF,EAIEC,sBAJF,QAKO,0CALP;AAMA,SACEC,iBADF,EAEEC,2BAFF,QAGO,4CAHP;AAIA,SACEC,sBADF,EAEEC,+BAFF,EAGEC,yBAHF,QAIO,4CAJP;AAKA,SAASC,wBAAT,QAAyC,oCAAzC;AACA,SAASC,yBAAT,QAA0C,oCAA1C;AACA,SAGEC,mBAHF,EAIEC,4BAJF,QAKO,wCALP;AAMA,SAASC,4BAAT,QAA6C,sCAA7C;AACA,SACEC,qBADF,EAEEC,8BAFF,EAGEC,iBAHF,EAIEC,mBAJF,EAKEC,2BALF,EAMEC,oCANF,QAOO,sCAPP;AAUA,SAASC,6BAAT,QAA8C,mFAA9C;AAEA,SAASC,oBAAT,QAAqC,wCAArC;;;;;;;;;;;;;;;;;;;;;;;IC3G2EC;IACXA;IAA2CA;;;;;IAA3CA;IAAAA;;;;;;IAQpDA;IAEEA;IACFA;;;;;IADEA;IAAAA;;;;;;IARNA;IACEA;IACAA,4BAAK,CAAL,EAAK,IAAL,EAAK,EAAL;IACgCA;IAErBA;IACTA;IAIFA;;;;;IAPgCA;IAAAA;IAI3BA;IAAAA;;;;;;IAMTA;IACEA;IACAA;IACoFA;IAEpFA;;;;;IAFEA;IAAAA;IAAkFA;IAAAA;;;;;;;;;;;;;;IAItFA;IACEA;MAAAA;MAAA;MAAA;IAAA;IACAA;IAA+CA;IAAqCA;IACpFA;IAEFA;;;;;IAHiDA;IAAAA;IAE7CA;IAAAA;;;;;;IAlCRA,4BAA2B,CAA3B,EAA2B,KAA3B,EAA2B,EAA3B;IACiEA;;;;IACAA;IAC/DA,gCAA6D,CAA7D,EAA6D,KAA7D,EAA6D,EAA7D,EAA6D,CAA7D,EAA6D,KAA7D,EAA6D,EAA7D;IAGMA;IACAA;IAA8BA;IAAmCA;IACuCA;IAE1GA;IAYFA;IACAA;IAOAA;IAMFA;;;;;IAnC+DA;IAAAA;IAM3BA;IAAAA;IACzBA;IAAAA;IAEyBA;IAAAA;IAaPA;IAAAA;IAO0BA;IAAAA;;;;;;IAQzDA;IAA2DA;;IAA8CA;;;;IAA9CA;IAAAA;;;;;;IAY7CA;IACEA;IACFA;;;;;IADEA;IAAAA;;;;;;;;;;;;IAJJA;IAEEA;IACAA;IAGAA;IACFA;;;;;IAPwCA;IAG/BA;IAAAA;;;;;;;;IAKTA,8BAAmF,CAAnF;IAEIA;IAEAA;IAAkDA;MAAA;MAAA;MAAA;MAAA,OAASA,qDAAT;IAAkC,CAAlC;IAEhDA;;IACFA;IACFA;;;;;;;IALIA;IAAAA,yDAAoB,aAApB,EAAoB,YAApB,EAAoBC,KAApB,EAAoB,EAApB;IADiED;IAGjEA;IAAAA;IAD0BA;IAE1BA;IAAAA;;;;;;IAMJA;IACEA;IACFA;;;;;;IAJFA;IAEEA;IAGFA;;;;;IAH8CA;IAAAA;;;;;;IArBhDA;IACEA;IAQAA;IAUAA;IAMFA;;;;;IAxBQA;IAAAA;IAQiCA;IAAAA;IAWpCA;IAAAA;;;;;;IAMLA;IACEA;IACFA;;;;;;IA7BFA;IACEA;IA0BAA;IAGFA;;;;;IA7BiBA;IAAAA;IA0BiBA;IAAAA;;;;;;;;IAW9BA;IACEA;IACEA;MAAA;MAAA;MAAA;MAASE;MAA4B,OAAEF,iDAAF;IAAuB,CAA5D;IADFA;IAGAA;IAEEA;IACFA;IACFA;;;;;;;IAP4DA;IAAAA,qDAAgB,aAAhB,EAAgB,QAAhB,EAAgBG,KAAhB,EAAgB,EAAhB;IAApBH;IAGVA;IAAAA;IAC1BA;IACAA;IAAAA;;;;;;IAIFA;IACEA;IACFA;;;;;;IAHFA;IACEA;IAGFA;;;;;IAH8BA;IAAAA;;;;;;;;IAjBlCA,4BAAsB,CAAtB,EAAsB,KAAtB,EAAsB,EAAtB,EAAsB,CAAtB,EAAsB,KAAtB,EAAsB,EAAtB;IAE8DA;;IAC1DA;IAAMA;IACsCA;MAAAA;MAAA;MAAA,OAASA,0CAAT;IAA2B,CAA3B;IAA6BA;IAE3EA;IACEA;IASAA;IAKFA;;;;;IAnB4DA;IAAAA;IAKzBA;IAAAA;IASlBA;IAAAA;;;;;;IAhDrBA;IACEA;IA+BAA;IAuBFA;;;;;IAtDiBA;IAAAA;IA+BTA;IAAAA;;;;;;;;IAyBNA;IAEEA;IAAwCA;;;IAEtCA;IAEEA;MAAAA;MAAA;MAAAI,gDAAwC,KAAxC,GAA6CA,wBAAiB,IAA9D;MAAkE,OAAEJ,iEAAkC,YAAlC,EAA8CI,0BAAqB,cAArB,CAA9C,EAAF;IAAqF,CAAvJ;IAFFJ;IAGAA;IACFA;IACAA;IAAwCA;;;IAEtCA;IAEEA;MAAAA;MAAA;MAAAK,4CAAoC,KAApC,GAAyCA,sBAAe,IAAxD;MAA4D,OAAEL,iEAAkC,YAAlC,EAA8CK,0BAAqB,YAArB,CAA9C,EAAF;IAAmF,CAA/I;IAFFL;IAGAA;IACFA;IACFA;;;;;IAd0CA;IAAAA;IAGpCA;IAAAA;IAIoCA;IAAAA;IAGpCA;IAAAA;;;;;;;;;;IAbRA;IACEA;IAiBFA;;;;;IAhBKA;IAAAA;;;;;;IAgCKA;IACsFA;IACxBA;IAAoBA;;;;;IAApBA;IAAAA;;;;;;;;IAZlEA;IACEA,gCAA+B,CAA/B,EAA+B,KAA/B,EAA+B,EAA/B;IAEIA;;IACaA;IACfA;;IAEEA;IACEA;MAAAA;MAAA;MAAA,OAAWA,2CAAX;IAA8B,CAA9B;IADFA;IAGFA;IAGFA;IACFA;;;;;;IAZSA;IAAAA;IACHA;IAAAA;IAGAA;IAAAA;IADmBA;IAKfA;IAAAA;;;;;;;;IAKVA;IACEA,gCAA+B,CAA/B,EAA+B,KAA/B,EAA+B,EAA/B;IAEIA;;IACaA;IACfA;;IAEEA;IAIAA;IAA2CA;MAAAA;MAAA;MAAA,OAAmBA,2DAAnB;IAA8C,CAA9C;IACjBA;IAGhCA;;;;;;;;IAbSA;IAAAA;IACHA;IAAAA;IAGAA;IAAAA;IADmBA;IAEZA;IAAAA,mCAAmB,oBAAnB,EAAmBM,IAAnB,EAAmB,KAAnB,EAAmB,4DAAnB;IAIaN;IAAAA,qCAAsB,SAAtB,EAAsBO,mBAAtB;;;;;;IAehBP;IAAgCA,6BACiC,CADjC,EACiC,MADjC,EACiC,EADjC;IAEHA;IAC7BA;;;;;;;IAHuDA;IAAAA,wDAAmB,aAAnB,EAAmB,OAAnB,EAAmBQ,SAAnB,EAAmB,EAAnB;IACjBR;IACTA;IAAAA;;;;;;IAZvCA;IACEA,gCAA+B,CAA/B,EAA+B,KAA/B,EAA+B,EAA/B;IAEIA;IAAUA;IACZA,gDAAmF,CAAnF,EAAmF,WAAnF,EAAmF,EAAnF;IAKIA;IAMFA;IAGNA;;;;;;IAhBSA;IAAAA;IAEgBA;IAAAA;IACRA;IAAAA,qCAAsB,OAAtB,EAAsBS,mBAAtB,EAAsB,UAAtB,EAAsB,IAAtB,EAAsB,eAAtB,EAAsB,KAAtB,EAAsB,QAAtB,EAAsB,IAAtB;;;;;;IAuBTT;IAAgCA,6BACiC,CADjC,EACiC,MADjC,EACiC,EADjC;IAEHA;IAC7BA;;;;;;;IAHuDA;IAAAA,wDAAmB,aAAnB,EAAmB,OAAnB,EAAmBU,SAAnB,EAAmB,EAAnB;IACjBV;IACTA;IAAAA;;;;;;IAPjCA;IAIEA;IAMFA;;;;;IAV2DA,qCAAsB,OAAtB,EAAsBW,oBAAtB,EAAsB,UAAtB,EAAsB,IAAtB,EAAsB,eAAtB,EAAsB,KAAtB,EAAsB,QAAtB,EAAsB,IAAtB;;;;;;IAJ/DX;IACEA,gDAAuF,CAAvF,EAAuF,KAAvF,EAAuF,EAAvF;IAEIA;IAAWA;IACbA;IAWFA;IACFA;;;;;;;;;IAfuBA;IAAAA;IACdA;IAAAA;IAEOA;IAAAA,sDAA6B,UAA7B,EAA6BY,IAA7B;;;;;;;;IAoBVZ;IACEA;MAAAA;MAAA;MAAA,yCAAoB,IAApB;IAAwB,CAAxB;IACAA;IACAA;;IAEFA;;;;;IAFEA;IAAAA;;;;;;;;IAIJA,4BAAsB,CAAtB,EAAsB,MAAtB,EAAsB,EAAtB,EAAsB,CAAtB,EAAsB,KAAtB,EAAsB,EAAtB,EAAsB,CAAtB,EAAsB,KAAtB,EAAsB,EAAtB;IAG+BA;;IAAuCA;IAChEA;IAEFA;IAEFA,gCAAyE,CAAzE,EAAyE,oBAAzE,EAAyE,EAAzE;IAEIA;MAAAA;MAAA;MAAA,OAAgBA,+CAAhB;IAAuC,CAAvC,EAAwC,kBAAxC,EAAwC;MAAAA;MAAA;MAAA;IAAA,CAAxC;IAC+CA;IAEnDA,gCAA4B,EAA5B,EAA4B,KAA5B,EAA4B,EAA5B;IAC8BA;MAAAA;MAAA;MAAA,yCAAoB,KAApB;IAAyB,CAAzB;IAA2BA;;IAAkCA;IACzFA;IAAsBA;MAAAA;MAAA;MAAA,OAASA,qCAAT;IAAsB,CAAtB;IAAwBA;;IAAwCA;;;;;IAdpEA;IAAAA;IAESA;IAAAA;IAMPA;IAAAA,8CAA+B,mBAA/B,EAA+Ba,8BAA/B;IAKmCb;IAAAA;IACTA;IAAAA;;;;;;;;IAK9CA,gCAA6E,CAA7E,EAA6E,KAA7E,EAA6E,EAA7E,EAA6E,CAA7E,EAA6E,KAA7E,EAA6E,EAA7E;IAGMA;IACAA;IAAmDA;IAAwBA;IAE7EA,gCAA0B,CAA1B,EAA0B,GAA1B,EAA0B,EAA1B;IAEwCA;MAAA;MAAA;MAAA;MAAA,OAASA,oDAAT;IAAiC,CAAjC;IAAmCA;;;;;;IAJtBA;IAAAA;;;;;;IAN7DA;IACEA;IACEA;IAYFA;IACFA;;;;;IAbyBA;IAAAA;;;;;;IAlC7BA;IACEA,gCAAyB,CAAzB,EAAyB,KAAzB,EAAyB,EAAzB,EAAyB,CAAzB,EAAyB,KAAzB,EAAyB,EAAzB;IAIMA;IACSA;IACXA;IAMFA;IACAA;IAkBAA;IAgBFA;IACFA;;;;;;IA7CQA;IAAAA;IAGkFA;IAAAA;IAOhFA;IAAAA;IAkBSA;IAAAA;;;;;;IAtGvBA,8BAC2H,CAD3H,EAC2H,EAD3H;IAGIA;IAeAA;IAgBAA;IAmBAA;IAiBAA;IAkDFA;;;;;IAtHcA;IAAAA;IACGA;IAAAA;IAeAA;IAAAA;IAgBAA;IAAAA;IAmBAA;IAAAA;IAiBAA;IAAAA;;;;;;IAtEnBA;;;;;IACoBA;;;;;;IA6JNA,uCAAqE,CAArE,EAAqE,MAArE,EAAqE,GAArE;IAEIA;IACFA;;;;;IAH2CA;IACrCA;IAAAA;IACJA;IAAAA;;;;;;IALRA;IACEA;IAEEA;IAKFA;IACFA;;;;;IANgCA;IAAAA;;;;;;IAQ9BA;IAAyCA;IAAoDA;;;;;IAApDA;IAAAA;;;;;;IAI/CA;IAEEA;IAA6BA;;;;;IAA7BA;IAAAA;;;;;;;;IAMFA,gCAAsF,CAAtF,EAAsF,KAAtF,EAAsF,GAAtF,EAAsF,CAAtF,EAAsF,OAAtF,EAAsF,GAAtF;IAIMA;MAAA;MAAA;MAAA;MAAA,OAAUA,4DAAV;IAAwC,CAAxC;IAFFA;IAGAA;IAAgGA;IACvFA;;;;;;IAJWA;IAAAA;IAEwBA;IACuBA;IAAAA;IAA6BA;IAAAA;;;;;;;;IASpGA;IACmCA;MAAAA;MAAA;MAAA,OAAUA,4EAAV;IAAsC,CAAtC;IAGnCA;;;;;IAJWA,qCAAsB,OAAtB,EAAsBc,qBAAtB;;;;;;IAHfd,gCAA2F,CAA3F,EAA2F,KAA3F,EAA2F,GAA3F;IAC+BA;IAAeA;IAC5CA;IACEA;IAKFA;;;;;;;;IANqBA;IAAAA;IACgBA;IAAAA,uDAA6B,UAA7B,EAA6BY,IAA7B;;;;;;;;IAWjCZ;IAEmBA;MAAAA;MAAA;MAAA,OAAUA,2EAAV;IAAqC,CAArC;IAEnBA;;;;;IAJWA,qCAAsB,OAAtB,EAAsBe,oBAAtB;;;;;;IAJjBf,gCAA0F,CAA1F,EAA0F,KAA1F,EAA0F,GAA1F,EAA0F,CAA1F,EAA0F,KAA1F,EAA0F,EAA1F;IAE4EA;IAAcA;IACtFA;IACEA;IAKFA;;;;;;;;IAPKA;IAAAA;IACgBA;IAAAA;IACgBA;IAAAA,sDAA4B,UAA5B,EAA4BY,IAA5B;;;;;;;;IAQzCZ,iCAC6E,CAD7E,EAC6E,KAD7E,EAC6E,EAD7E;IAE0EA;IAAWA;IACnFA,iCAA8B,CAA9B,EAA8B,qBAA9B,EAA8B,GAA9B,EAA8B,CAA9B,EAA8B,WAA9B,EAA8B,GAA9B;IAKgEA;MAAAA;MAAA;MAAA,OAAUA,oDAAV;IAAqC,CAArC;IAC5DA;;;;;IAPCA;IAAAA;IAEkBA;IAAAA;IACRA;IAAAA,qCAAsB,OAAtB,EAAsBgB,iBAAtB,EAAsB,UAAtB,EAAsB,qHAAtB;;;;;;;;IAoBThB;IACEA;MAAAA;MAAA;MAAA,0CAAoB,IAApB;IAAwB,CAAxB;IACAA;IACAA;;IAEFA;;;;;IAFEA;IAAAA;;;;;;;;IAIJA,4BAAsB,CAAtB,EAAsB,MAAtB,EAAsB,EAAtB,EAAsB,CAAtB,EAAsB,KAAtB,EAAsB,EAAtB,EAAsB,CAAtB,EAAsB,KAAtB,EAAsB,EAAtB;IAG+BA;;IAAuCA;IAChEA;IAEFA;IAEFA,gCAAyE,CAAzE,EAAyE,oBAAzE,EAAyE,EAAzE;IAEIA;MAAAA;MAAA;MAAA,OAAgBA,gDAAhB;IAAuC,CAAvC,EAAwC,kBAAxC,EAAwC;MAAAA;MAAA;MAAA;IAAA,CAAxC;IAC+CA;IAEnDA,gCAA4B,EAA5B,EAA4B,KAA5B,EAA4B,EAA5B;IAC8BA;MAAAA;MAAA;MAAA,0CAAoB,KAApB;IAAyB,CAAzB;IAA2BA;;IAAkCA;IACzFA;IAAsBA;MAAAA;MAAA;MAAA,OAASA,sCAAT;IAAsB,CAAtB;IAAwBA;;IAAwCA;;;;;IAdpEA;IAAAA;IAESA;IAAAA;IAMPA;IAAAA,8CAA+B,mBAA/B,EAA+BiB,+BAA/B;IAKmCjB;IAAAA;IACTA;IAAAA;;;;;;;;IAK9CA,gCAA6E,CAA7E,EAA6E,KAA7E,EAA6E,EAA7E,EAA6E,CAA7E,EAA6E,KAA7E,EAA6E,EAA7E;IAGMA;IACAA;IAAmDA;IAAwBA;IAE7EA,gCAA0B,CAA1B,EAA0B,GAA1B,EAA0B,EAA1B;IAEwCA;MAAA;MAAA;MAAA;MAAA,OAASA,sDAAT;IAAiC,CAAjC;IAAmCA;;;;;;IAJtBA;IAAAA;;;;;;IAN7DA;IACEA;IACEA;IAYFA;IACFA;;;;;IAbyBA;IAAAA;;;;;;IAlC7BA;IACEA,gCAAyB,CAAzB,EAAyB,KAAzB,EAAyB,EAAzB,EAAyB,CAAzB,EAAyB,KAAzB,EAAyB,EAAzB;IAIMA;IACSA;IACXA;IAMFA;IACAA;IAkBAA;IAgBFA;IACFA;;;;;;IA7CQA;IAAAA;IAGkFA;IAAAA;IAOhFA;IAAAA;IAkBSA;IAAAA;;;;;;IAnCvBA,8BAC2H,CAD3H,EAC2H,EAD3H;IAGIA;IAkDFA;;;;;IAnDcA;IAAAA;IACGA;IAAAA;;;;;;;;;;;;IAzGrBA;IAGEA,gCAA0C,CAA1C,EAA0C,KAA1C,EAA0C,EAA1C,EAA0C,CAA1C,EAA0C,KAA1C,EAA0C,GAA1C,EAA0C,CAA1C,EAA0C,KAA1C,EAA0C,GAA1C;IAGmCA;IAAkBA;IAC/CA,iDAAmG,CAAnG,EAAmG,OAAnG,EAAmG,GAAnG;IAEIA;MAAAA;MAAA;MAAA,OAAQA,qDAAR;IAAoC,CAApC;IADFA;IAMNA,gCAA2B,CAA3B,EAA2B,KAA3B,EAA2B,GAA3B;IAC+BA;IAAWA;IACxCA;IACEA;IAEAA;IAA2CA;MAAAA;MAAA;MAAA,OAAmBA,6DAAnB;IAA8C,CAA9C;IACgCA;IAG/EA,kCAA4C,EAA5C,EAA4C,KAA5C,EAA4C,EAA5C;IAC2BA;IAAeA;IACxCA,kCAAqD,EAArD,EAAqD,qBAArD,EAAqD,GAArD,EAAqD,EAArD,EAAqD,OAArD,EAAqD,GAArD;IAEyBA;MAAA,OAASkB,uBAAT;IAAgC,CAAhC,EAAiC,OAAjC,EAAiC;MAAAlB;MAAA;MAAA,OAE3CA,mEAF2C;IAED,CAFhC;IAArBA;IAGAA;IACEA;IAUAA;IAGFA;IAEFA;IAGFA;IAEFA,kCAAmB,EAAnB,EAAmB,KAAnB,EAAmB,EAAnB;IAC2BA;IAAwBA;IACjDA;IACEA;IASFA;IAEFA;IAUAA;IAYAA;IAaFA;IAEAA;IAuDFA;;;;;;;;;;;IAtJ6BA;IAAAA;IASFA;IAAAA;IACZA;IAAAA,mCAAmB,KAAnB,EAAmBmB,kBAAnB,EAAmB,oBAAnB,EAAmBC,IAAnB;IAEapB;IAAAA,qCAAsB,SAAtB,EAAsBmB,0EAAtB;IAOCnB;IAAAA;IAKFA;IAAAA,6FAAiC,UAAjC,EAAiCqB,IAAjC;IAebrB;IAAAA;IAQqCA;IAAAA;IAWnBA;IAAAA;IAUAA;IAAAA;IAazBA;IAAAA;IAeeA;IAAAA;;;;;;IA2DhBA;IAA6EA;IAAOA;;;;;;IAIhFA;IAAwEA;;IAC3DA;IACTA;;IAAyCA;;;;;IAFMA;IAAqBA;IAAAA;IAEpEA;IAAAA;;;;;;IACJA;IACEA;IAAoCA;;;;;IADWA;IAC/CA;IAAAA;;;;;;IAcJA;IAA+EA;;IAElEA;IACTA;;IAAyCA;;;;;IAHaA;IAAqBA;IAAAA;IAG3EA;IAAAA;;;;;;IACJA;IACEA;IAAoCA;;;;;IADaA;IACjDA;IAAAA;;;;;;;;;;;;;IAVNA,iCAAsF,CAAtF,EAAsF,KAAtF,EAAsF,GAAtF;IACgDA;IAASA;IACvDA;IAGEA;IAIAA;IAOFA;;;;;IAdWA;IAAAA,qCAAsB,SAAtB,EAAsBA,wNAAtB;IAGGA;IAAAA;IAIgBA;IAAAA;;;;;;;;;;;;IA9BpCA;IACEA;IAAyBA;;;IAA8DA;IACvFA,iCAA0C,CAA1C,EAA0C,KAA1C,EAA0C,GAA1C;IAEIA;IACAA,iDAAuF,CAAvF,EAAuF,WAAvF,EAAuF,GAAvF;IAGIA;IAGAA;IAOFA;IAGJA;IAkBFA;IACFA;;;;;IAvC2BA;IAAAA;IAEkBA;IAAAA;IACQA;IAAAA;IAC1BA;IAAAA;IACRA;IAAAA,qCAAsB,SAAtB,EAAsBA,qDAAtB;IAEGA;IAAAA;IAGgBA;IAAAA;IAUsBA;IAAAA;;;;;;IAoB5DA;IACEA;IAAsEA;;IAA4BA;IAClGA;;IACEA;IAEFA;IACFA;;;;;IALOA;IAAAA;IAAiEA;IAAAA;IACPA;IAAAA;IAA1CA;;;;;;;;IAc7BA;IAC2CA;MAAAA;MAAA;MAAA,OAASA,+CAAT;IAA+B,CAA/B;IAAiCA;;IAC/DA;;;;IAD+DA;IAAAA;;;;;;;;IAE5EA;IACEA;MAAAA;MAAA;MAAA,OAASA,uCAAT;IAAuB,CAAvB;IAAyBA;;IAC+BA;;;;;IAD/BA;IAAAA;;;;;;;;IAE3BA;IAGEA;MAAAA;MAAA;MAAA,OAASA,qCAAa,IAAb,EAAT;IAA2B,CAA3B;IAA6BA;;IAAuCA;;;;IAAvCA;IAAAA;;;;;;;;IAC/BA;IACkEA;MAAAA;MAAA;MAAA,OAASA,uEAAT;IAA8C,CAA9C;IAAgDA;IAC7FA;;;;;;;;IAjBvBA,iCAC8F,CAD9F,EAC8F,IAD9F,EAC8F,GAD9F;IAG6CA;MAAAA;MAAA;MAAA,OAASA,4CAAT;IAA4B,CAA5B;IAA8BA;;IAAgCA;IACzGA;IACAA;IAGAA;IAGAA;IAIAA;IAGFA;;;;;IAlB2DA;IAGgBA;IAAAA;IAEhEA;IAAAA;IAGiBA;IAAAA;IAIvBA;IAAAA;IAGMA;IAAAA;;;;;;;;;;;;;IAMTA;IAEEA;IACFA;;;;;IAFEA;;;;;;IAMFA;;;;IAAWA;;;;;;;;IAIXA,iCAAkB,CAAlB,EAAkB,IAAlB,EAAkB,GAAlB;IAC0BA;IAAoFA;IAC5GA,iCAA4B,CAA5B,EAA4B,QAA5B,EAA4B,GAA5B;IAC0CA;MAAAA;MAAA;MAAA,OAASA,wCAAT;IAAwB,CAAxB;IAA0BA;IAAEA;;;;;;;;IAMxEA;IAAwDA;IAAkBA;IAC1EA,iCAAkC,CAAlC,EAAkC,IAAlC,EAAkC,GAAlC;IAC4EA;IAC1EA;IACAA;IAA0EA;IACxEA;IAA+DA;MAAAA;MAAA;MAAA,OAASA,gDAAT;IAAgC,CAAhC;IAAkCA;IACvFA;IAAQA;IACpBA;IACAA;IAAgCA;MAAAA;MAAA;MAAA,OAASA,4CAAT;IAA4B,CAA5B;IAC9BA;;IAAkCA;;;;IAAlCA;IAAAA;;;;;;;;;;;;;;;;;;;ADjYN,OAAM,MAAOsB,2BAAP,CAAkC;EAyJtCC,YACSC,QADT,EAESC,YAFT,EAGUC,WAHV,EAIUC,MAJV,EAKUC,GALV,EAMUC,qBANV,EAOUC,qBAPV,EAQUC,gBARV,EASUC,MATV,EAUUC,eAVV,EAU6C;IATpC;IACA;IACC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAjKF,eAA8B,IAAIrG,YAAJ,EAA9B;IACR,wBAAyC,IAAIM,eAAJ,CAAyB,EAAzB,CAAzC;IAES,mCAAuC,KAAvC;IACA,uBAA2B,KAA3B;IACA,qBAAyB,KAAzB;IACA,kBAAsB,KAAtB;IAEA,oBAAwB,KAAxB;IAIT,qBAAqBS,mBAArB;IACA,kBAAkBC,aAAlB;IACA,yBAAyBC,wBAAzB;IAEA,wBAA0B,EAA1B;IACA,sBAAwB,EAAxB;IACA,2BAA6B,EAA7B;IACA,kBAAoB,EAApB;IASA,oBAAuB,EAAvB;IACA,qBAAwB,EAAxB;IACA,uBAA0B,EAA1B;IACA,wBAA2B,EAA3B;IACA,qBAAwB,EAAxB;IAEA,kBAAsB,KAAtB;IACA,mBAAuB,KAAvB;IACA,sBAA0B,KAA1B;IACA,oBAAwB,KAAxB;IAEA,0BAA8B,KAA9B;IACA,mBAA0B,EAA1B;IACA,oBAA8B,EAA9B;IACA,oBAAsB,EAAtB;IACA,2BAA+B,KAA/B;IACA,8BAAkC,KAAlC;IACA,6BAAiC,IAAjC;IACA,4BAAgC,IAAhC;IAKA,qBAAyB,KAAzB;IACA,uBAAiC,EAAjC;IACA,yBAAmC,EAAnC;IACA,wBAAkC,EAAlC;IACA,0BAAoC,EAApC;IACA,yBAA6B,IAA7B;IACA,qBAA+B,EAA/B;IACA,2BAA+B,KAA/B;IACA,2BAA+B,KAA/B;IAEA,kBAAsB,KAAtB;IAGA,iCAAqC,IAArC;IACA,2BAA6B,EAA7B;IACA,gCAAgC,EAAhC;IAIA,kBAAaH,UAAb;IACA,cAAST,MAAT;IACA,6BAAwBuB,qBAAxB;IACA,oBAAeJ,YAAf;IACA,mBAAcE,WAAd;IACA,uBAAkBD,eAAlB;IACA,2BAA+B,KAA/B;IACA,uBAAuB,EAAvB;IAKA,4BAAgC,KAAhC;IAMA,qBAAyB,KAAzB;IACA,8BAAmC,EAAnC;IACA,sBAAiBE,cAAjB;IACA,mBAAoB,IAAI2E,IAAJ,EAApB;IACA,wBAA4B,KAA5B;IACA,kCAAsC,IAAtC;IAEA,qBAA+B,EAA/B;IACA,yBAAmC,EAAnC;IACA,8BAAiC,6DAAjC;IACA,oBAAuB,EAAvB;IACA,wBAA2B,IAA3B;IACA,gBAAoB,KAApB;IA+DE,KAAKC,OAAL,GAAe,IAAID,IAAJ,EAAf,CAF2C,CAG3C;IACA;;IACA,KAAKP,MAAL,CAAYS,QAAZ,CAAqB,IAAIrD,2BAAJ,EAArB;;IACA,KAAK4C,MAAL,CAAYS,QAAZ,CAAqB,IAAIjD,wBAAJ,EAArB;EACD;;EAlEgB,IAAbkD,aAAa;IACf,OAAO,CAAC,KAAKC,UAAN,IAAoB,CAAC,KAAKC,2BAAjC;EACD;;EAEmB,IAAhBC,gBAAgB;;;IAClB,OAAO,YAAKC,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEC,QAAf,KAA2BjG,UAAlC;EACD;;EAEDkG,YAAY;;;IACV,OACE,CAAC,KAAKC,mBAAN,KACA,WAAKJ,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEI,IAAF,CACVC,IAAD,IAAc;MAAA;;MACZ,iBAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEC,MAAN,MAAY,IAAZ,IAAYN,aAAZ,GAAY,MAAZ,GAAYA,GAAEO,mBAAd;IAAiC,CAFxB,CADb,CADF;EAOD;;EAEDC,wBAAwB;;;IACtB,IAAI,EAAC,WAAKT,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAES,MAAhB,CAAJ,EAA4B;MAC1B,OAAO,KAAP;IACD;;IAED,MAAMC,mBAAmB,GAAG,KAAKX,QAAL,CAAcY,KAAd,CACzBN,IAAD,IAAc;;;MACZ,kBAAI,CAACC,MAAL,MAAW,IAAX,IAAWN,aAAX,GAAW,MAAX,GAAWA,GAAEO,mBAAb,KACAF,IAAI,CAACC,MAAL,CAAYM,mBADZ;IAC+B,CAHP,CAA5B;IAMA,MAAMC,oBAAoB,GAAG,KAAKd,QAAL,CAAcK,IAAd,CAC1BC,IAAD,IAAc;;;MACZ,kBAAI,CAACC,MAAL,MAAW,IAAX,IAAWN,aAAX,GAAW,MAAX,GAAWA,GAAEO,mBAAb,KACAF,IAAI,CAACC,MAAL,CAAYM,mBADZ;IAC+B,CAHN,CAA7B;IAMA,OACEF,mBAAmB,IAAKG,oBAAoB,IAAI,CAACH,mBADnD;EAGD;;EAEDI,uBAAuB;IACrB,MAAMf,QAAQ,GAAG,KAAKA,QAAL,CAAcU,MAAd,GAAuB,KAAKV,QAA5B,GAAuC,EAAxD;IACA,OAAOA,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEY,KAAV,CAAiBN,IAAD,IAAc;MAAA;;MAAC,iBAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEC,MAAN,MAAY,IAAZ,IAAYN,aAAZ,GAAY,MAAZ,GAAYA,GAAEO,mBAAd;IAAiC,CAAhE,CAAP;EACD;;EAEDQ,UAAU;IACR,OAAO,IAAIvB,IAAJ,EAAP;EACD;;EAoBKwB,QAAQ;IAAA;;IAAA;;;MACZ,KAAI,CAACC,qBAAL,GAA6B,KAAI,CAAC5B,gBAAL,CAAsB6B,cAAtB,CAAqCC,SAArC,CAA+C,MAAK;QAC/E,KAAI,CAACrC,QAAL,GAAgB,KAAI,CAACC,YAAL,CAAkBqC,IAAlB,CAAuB,KAAI,CAACC,WAA5B,EAAyC;UACvDC,KAAK,EAAE,oCADgD;UAEvDC,mBAAmB,EAAE;QAFkC,CAAzC,CAAhB;MAID,CAL4B,CAA7B;;MAMA,KAAI,CAACjC,MAAL,CAAYkC,MAAZ,CACGC,IADH,CACQ7H,MAAM,CAAE8H,KAAD,IAAWA,KAAK,YAAYpI,aAA7B,CADd,EAEG6H,SAFH,CAEa,MAAK;QACd,KAAI,CAACQ,WAAL,GAAmB,KAAI,CAACrC,MAAL,CAAYsC,GAA/B;MACD,CAJH;;MAKA,KAAI,CAACD,WAAL,GAAmB,KAAI,CAACrC,MAAL,CAAYsC,GAA/B;;MAEA,KAAI,CAAC3C,MAAL,CAAY4C,MAAZ,CAAmBlF,mBAAnB,EACG8E,IADH,CACQ1H,SAAS,CAAC,KAAI,CAAC+H,OAAN,CADjB,EAEGX,SAFH,CAEcY,YAAD,IAAsB;;;QAC/BA,YAAY,SAAZ,gBAAY,WAAZ,GAAY,MAAZ,eAAY,CAAEC,OAAd,CAAuB1B,MAAD,IAAgB;UACpC,IAAI,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEC,mBAAR,MAA+BD,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEM,mBAAvC,CAAJ,EAAgE;YAC9D,IAAI,CAAC,KAAI,CAACqB,sBAAL,CAA4BC,QAA5B,CAAqC5B,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE6B,WAA7C,CAAL,EAAgE;cAC9D,KAAI,CAACF,sBAAL,CAA4BG,IAA5B,CAAiC9B,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE6B,WAAzC;YACD;UACF;QACF,CAND;;QAOA,KAAI,CAACF,sBAAL,CAA4BI,IAA5B,CAAiC,CAACC,CAAD,EAAYC,CAAZ,KAA0BD,CAAC,CAACE,aAAF,CAAgBD,CAAhB,CAA3D;;QACA,KAAI,CAACE,gBAAL,GAAwBV,YAAxB;QACA,IAAIxB,mBAAmB,GAAG,KAAI,CAACmC,YAAL,GACtB,KAAI,CAAC3C,QAAL,CAAcY,KAAd,CAAqBN,IAAD,IAAc;UAAA;;UAAC,iBAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEC,MAAN,MAAY,IAAZ,IAAYN,aAAZ,GAAY,MAAZ,GAAYA,GAAEO,mBAAd;QAAiC,CAApE,CADsB,GAEtB,iBAAI,CAACR,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEM,MAAf,MAAqB,IAArB,IAAqBqC,aAArB,GAAqB,MAArB,GAAqBA,GAAEpC,mBAF3B;;QAIA,IAAI,KAAI,CAACqC,iCAAL,EAAJ,EAA8C;UAC5C,KAAI,CAACC,wBAAL,GAAgC,WAAI,CAACJ,gBAAL,MAAqB,IAArB,IAAqBK,aAArB,GAAqB,MAArB,GAAqBA,GAAElJ,MAAF,CAAU0G,MAAD,IAC5DA,MAAM,CAACyC,yBAD4C,EAEnDV,IAFmD,CAE9C,CAACC,CAAD,EAASC,CAAT,KAAoBD,CAAC,CAACU,SAAF,GAAcT,CAAC,CAACS,SAFU,CAArD;QAGD,CAJD,MAIO,IAAIzC,mBAAJ,EAAyB;UAC9B,KAAI,CAACsC,wBAAL,GAAgC,WAAI,CAACJ,gBAAL,MAAqB,IAArB,IAAqBQ,aAArB,GAAqB,MAArB,GAAqBA,GAAErJ,MAAF,CAAU0G,MAAD,IAAgB;YAAA;;YAC5E,OAAC,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyC,yBAAR,KAAqC,EAACzC,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE4C,cAAT,CAAtC,IAAkE,YAAI,CAACnD,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEM,MAAF,CAAS6C,EAAtB,MAA6B7C,MAAM,CAAC6C,EAAtG;UAAwG,CADrD,EAEnDd,IAFmD,CAE9C,CAACC,CAAD,EAASC,CAAT,KAAoBD,CAAC,CAACU,SAAF,GAAcT,CAAC,CAACS,SAFU,CAArD;QAGD,CAJM,MAIA,IAAI,KAAI,CAACrB,WAAL,KAAqB,UAAzB,EAAqC;UAC1C,KAAI,CAACkB,wBAAL,GAAgC,WAAI,CAACJ,gBAAL,MAAqB,IAArB,IAAqBW,aAArB,GAAqB,MAArB,GAAqBA,GAAExJ,MAAF,CAAU0G,MAAD,IAC5D,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyC,yBAAR,KAAqCzC,MAAM,CAACM,mBADO,EAEnDyB,IAFmD,CAE9C,CAACC,CAAD,EAASC,CAAT,KAAoBD,CAAC,CAACU,SAAF,GAAcT,CAAC,CAACS,SAFU,CAArD;QAGD,CAJM,MAIA;UACL,KAAI,CAACH,wBAAL,GAAgC,WAAI,CAACJ,gBAAL,MAAqB,IAArB,IAAqBY,aAArB,GAAqB,MAArB,GAAqBA,GAAEzJ,MAAF,CAAU0G,MAAD,IAC5D,EAACA,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyC,yBAAT,KAAsC,EAACzC,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEM,mBAAT,CAAtC,IAAsE,EAACN,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE4C,cAAT,CADnB,EAEnDb,IAFmD,CAE9C,CAACC,CAAD,EAASC,CAAT,KAAoBD,CAAC,CAACU,SAAF,GAAcT,CAAC,CAACS,SAFU,CAArD;QAGD;MACF,CAjCH;;MAoCA,KAAI,CAAC/D,MAAL,CACG4C,MADH,CACUjF,4BADV,EAEG6E,IAFH,CAEQ1H,SAAS,CAAC,KAAI,CAAC+H,OAAN,CAFjB,EAGGX,SAHH,CAGcmC,SAAD,IAAmB;QAC5B,KAAI,CAACC,yBAAL,GAAiCD,SAAjC;MACD,CALH;;MAOA,KAAI,CAACrE,MAAL,CACG4C,MADH,CACUhG,cADV,EAEG4F,IAFH,CAEQ1H,SAAS,CAAC,KAAI,CAAC+H,OAAN,CAFjB,EAGGX,SAHH,CAGcqC,WAAD,IAAqB;QAC9B,IAAI,EAACA,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAE/C,MAAd,CAAJ,EAA0B;;QAC1B,IAAI+C,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEtB,QAAb,CAAsB,oCAAtB,CAAJ,EAAiE;UAC/D,KAAI,CAAC/B,mBAAL,GAA2B,IAA3B;QACD;MACF,CARH;;MAUA,KAAI,CAAClB,MAAL,CACG4C,MADH,CACUzG,0BADV,EAEGqG,IAFH,CAEQ1H,SAAS,CAAC,KAAI,CAAC+H,OAAN,CAFjB,EAGGX,SAHH,CAGcsC,IAAD,IAAc;QACvB,KAAI,CAACC,sBAAL,GAA8BD,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEC,sBAApC;QACA,KAAI,CAACC,0BAAL,GAAkCF,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEE,0BAAxC;MACD,CANH;;MAQA,KAAI,CAAC1E,MAAL,CACG4C,MADH,CACU5E,mBADV,EAEGwE,IAFH,CAEQ1H,SAAS,CAAC,KAAI,CAAC+H,OAAN,CAFjB,EAGGX,SAHH,CAGcsC,IAAD,IAAc;;;QACvB,KAAI,CAACG,gBAAL,GAAwBH,IAAxB;QACA,KAAI,CAACI,WAAL,GAAmBpJ,cAAc,CAAC,iBAAI,CAACmJ,gBAAL,MAAqB,IAArB,IAAqB5D,aAArB,GAAqB,MAArB,GAAqBA,GAAE8D,YAAvB,MAAmC,IAAnC,IAAmCnB,aAAnC,GAAmC,MAAnC,GAAmCA,GAAEoB,aAAtC,CAAjC;QACA,KAAI,CAACC,OAAL,GAAe,KAAI,CAACH,WAApB;MACD,CAPH;;MASA,KAAI,CAACI,uBAAL,GAA+B,KAAI,CAAC5E,gBAAL,CAAsB6E,sBAAtB,GAC5B/C,SAD4B,CACjBsC,IAAD,IAAc;;;QACvB,KAAI,CAACU,YAAL,CAAkBV,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEnD,MAAxB;;QACA,KAAI,CAAC8D,yBAAL,CAA+BX,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEN,EAArC;;QACA,IAAI,UAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE7C,MAAN,MAAY,IAAZ,IAAYN,aAAZ,GAAY,MAAZ,GAAYA,GAAEO,mBAAlB,EAAuC;UACrC,KAAI,CAACsC,wBAAL,GAAgC,WAAI,CAACJ,gBAAL,MAAqB,IAArB,IAAqBE,aAArB,GAAqB,MAArB,GAAqBA,GAAE/I,MAAF,CAAU0G,MAAD,IAC5D,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyC,yBAAR,MAAqCzC,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEC,mBAA7C,CADmD,EAEnD8B,IAFmD,CAE9C,CAACC,CAAD,EAASC,CAAT,KAAoBD,CAAC,CAACU,SAAF,GAAcT,CAAC,CAACS,SAFU,CAArD;UAGA,MAAM1C,MAAM,GAAQ,iBAAI,CAACuC,wBAAL,MAA6B,IAA7B,IAA6BC,aAA7B,GAA6B,MAA7B,GAA6BA,GAAElJ,MAAF,CAAU0G,MAAD,IACxDA,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEC,mBADuC,CAA7B,MAEnB,IAFmB,IAEnB0C,aAFmB,GAEnB,MAFmB,GAEnBA,GAAG,CAAH,CAFD;;UAGA,KAAI,CAACkB,YAAL,CAAkB7D,MAAlB;;UACA,KAAI,CAAC+D,WAAL,CAAiB/D,MAAjB,EAAyBmD,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEN,EAA/B;QACD;;QAED,IAAI,UAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE7C,MAAN,MAAY,IAAZ,IAAY8C,aAAZ,GAAY,MAAZ,GAAYA,GAAExC,mBAAlB,EAAuC;UACrC,KAAI,CAACiC,wBAAL,GAAgC,WAAI,CAACJ,gBAAL,MAAqB,IAArB,IAAqBY,aAArB,GAAqB,MAArB,GAAqBA,GAAEzJ,MAAF,CAAU0G,MAAD,IAC5D,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyC,yBAAR,KAAqCzC,MAAM,CAACM,mBADO,EAEnDyB,IAFmD,CAE9C,CAACC,CAAD,EAASC,CAAT,KAAoBD,CAAC,CAACU,SAAF,GAAcT,CAAC,CAACS,SAFU,CAArD;UAGA,MAAM1C,MAAM,GAAQ,iBAAI,CAACuC,wBAAL,MAA6B,IAA7B,IAA6ByB,aAA7B,GAA6B,MAA7B,GAA6BA,GAAE1K,MAAF,CAAU0G,MAAD,IACxDA,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEM,mBADuC,CAA7B,MAEnB,IAFmB,IAEnB2D,aAFmB,GAEnB,MAFmB,GAEnBA,GAAG,CAAH,CAFD;;UAGA,KAAI,CAACJ,YAAL,CAAkB7D,MAAlB;;UACA,KAAI,CAAC+D,WAAL,CAAiB/D,MAAjB,EAAyBmD,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEN,EAA/B;QACD;MACF,CAzB4B,CAA/B;;MA2BA,IACEqB,QAAQ,CAACC,IAAT,CAAcvC,QAAd,CAAuB,UAAvB,MACA,iBAAI,CAACnC,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEM,MAAf,MAAqB,IAArB,IAAqBqC,aAArB,GAAqB,MAArB,GAAqBA,GAAEpC,mBADvB,CADF,EAGE;QACA,MAAMD,MAAM,GAAQ,iBAAI,CAACuC,wBAAL,MAA6B,IAA7B,IAA6BC,aAA7B,GAA6B,MAA7B,GAA6BA,GAAElJ,MAAF,CAAU0G,MAAD,IAAgB;;;UACxE,yBAAI,CAACP,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEM,MAAf,MAAqB,IAArB,IAAqBqC,aAArB,GAAqB,MAArB,GAAqBA,GAAEpC,mBAAvB,IACID,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEC,mBADZ,GAEID,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEM,mBAFZ;QAE+B,CAHgB,CAA7B,MAInB,IAJmB,IAInBqC,aAJmB,GAInB,MAJmB,GAInBA,GAAG,CAAH,CAJD;;QAKA,KAAI,CAACkB,YAAL,CAAkB7D,MAAlB;;QACA,KAAI,CAAC+D,WAAL,CAAiB/D,MAAjB,EAAyB,WAAI,CAACP,QAAL,MAAa,IAAb,IAAaqD,aAAb,GAAa,MAAb,GAAaA,GAAED,EAAxC;;QACA;MACD;IA3HW;EA4Hb;;EAEDuB,WAAW,CAACC,OAAD,EAAuB;;;IAChC,IAAIA,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAE5E,QAAb,EAAuB;MACrB,IAAI,iBAAKA,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEM,MAAf,MAAqB,IAArB,IAAqBqC,aAArB,GAAqB,MAArB,GAAqBA,GAAEpC,mBAA3B,EACE,KAAKsC,wBAAL,GAAgC,WAAKJ,gBAAL,MAAqB,IAArB,IAAqBK,aAArB,GAAqB,MAArB,GAAqBA,GAAElJ,MAAF,CAClD0G,MAAD,IAAgB;QACd,OAAOA,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyC,yBAAf;MACD,CAHkD,CAArD,CADF,KAMK,IAAI,iBAAKhD,QAAL,MAAa,IAAb,IAAakD,aAAb,GAAa,MAAb,GAAaA,GAAE3C,MAAf,MAAqB,IAArB,IAAqB8C,aAArB,GAAqB,MAArB,GAAqBA,GAAEL,yBAA3B,EAAsD;QACzD,KAAKF,wBAAL,GAAgC,WAAKJ,gBAAL,MAAqB,IAArB,IAAqBY,aAArB,GAAqB,MAArB,GAAqBA,GAAEzJ,MAAF,CAClD0G,MAAD,IAAgB;UACd,OAAO,EAACA,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyC,yBAAT,CAAP;QACD,CAHkD,CAArD;MAKD;MACD,KAAK6B,UAAL;IACD;EACF;;EAEDC,oBAAoB;;;IAClB,IAAIC,mBAAmB,GAAG,iBAAKC,gBAAL,MAAqB,IAArB,IAAqB/E,aAArB,GAAqB,MAArB,GAAqBA,GAAEpG,MAAF,CAC5CoL,IAAD,IAAc;MAAA;;MAAC,YAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE7B,EAAN,OAAa,WAAKpD,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEC,QAA5B;IAAoC,CADN,CAArB,MAEzB,IAFyB,IAEzB0C,aAFyB,GAEzB,MAFyB,GAEzBA,GAAElC,MAFH;IAGA,IAAIwE,qBAAqB,GAAG,iBAAKC,kBAAL,MAAuB,IAAvB,IAAuBpC,aAAvB,GAAuB,MAAvB,GAAuBA,GAAElJ,MAAF,CAChDoL,IAAD,IAAc;MAAA;;MAAC,YAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE7B,EAAN,OAAa,WAAKpD,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEmF,eAA5B;IAA2C,CADT,CAAvB,MAE3B,IAF2B,IAE3BlC,aAF2B,GAE3B,MAF2B,GAE3BA,GAAExC,MAFH;IAGA,KAAK2E,eAAL,GAAuB;MACrBC,eAAe,EAAE,IADI;MAErBC,aAAa,EAAE,IAFM;MAGrBC,QAAQ,EAAE,IAHW;MAIrBC,UAAU,EAAE,IAJS;MAKrBC,cAAc,EAAE,IALK;MAMrBC,eAAe,EAAE,IANI;MAOrBC,cAAc,EAAE,IAPK;MAQrBC,aAAa,EAAE,IARM;MASrBC,UAAU,EAAE,IATS;MAUrBC,QAAQ,EAAE,IAVW;MAWrBC,KAAK,EAAE,IAXc;MAYrBC,UAAU,EAAE,IAZS;MAarBC,MAAM,EAAE,EAba;MAcrBC,gBAAgB,EAAE,CAChB,YAAKnG,QAAL,MAAa,IAAb,IAAaqD,aAAb,GAAa,MAAb,GAAaA,GAAEnD,QAAf,MAA4BjG,UAA5B,IAA0C8K,mBAA1C,GACI,WAAK/E,QAAL,MAAa,IAAb,IAAasD,aAAb,GAAa,MAAb,GAAaA,GAAEpD,QADnB,GAEI,IAHY,CAdG;MAmBrBkG,iBAAiB,EACf,KAAKpG,QAAL,CAAcoF,eAAd,KAAkCnL,UAAlC,IAAgDiL,qBAAhD,GACI,KAAKlF,QAAL,CAAcoF,eADlB,GAEI;IAtBe,CAAvB;IAwBA,KAAKiB,UAAL,GAAkB,KAAKpH,WAAL,CAAiBqH,KAAjB,CAAuB,KAAKjB,eAA5B,CAAlB;IACA,KAAKkB,aAAL,GAAqB,KAAKtH,WAAL,CAAiBqH,KAAjB,CAAuB;MAC1CE,QAAQ,EAAE,CAAC,EAAD;IADgC,CAAvB,CAArB;EAGD;;EAEDC,mBAAmB,CAAChC,QAAD,EAAc;IAC/B,KAAKiC,mBAAL,GAA2B,KAAKA,mBAAL,CAAyB7M,MAAzB,CACxB8M,cAAD,IACEC,IAAI,CAACC,SAAL,CAAeF,cAAf,KAAkCC,IAAI,CAACC,SAAL,CAAepC,QAAf,CAFX,CAA3B;EAID;;EAEDqC,eAAe;IACb,MAAM;MAAEC,QAAF;MAAYC,IAAZ;MAAkBC;IAAlB,IAAiC,KAAKZ,UAAL,CAAgBa,KAAvD;;IACA,IAAI,EAACF,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEG,IAAN,EAAD,KAAiB,EAACF,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEE,IAAP,EAAD,CAAjB,IAAmC,EAACJ,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEI,IAAV,EAAD,CAAvC,EAA0D;MACxD;IACD;;IACD,MAAMC,gBAAgB,GAAG,KAAKV,mBAAL,CAAyB7M,MAAzB,CACtB4K,QAAD,IAAkB;MAChB,OACE,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE0C,IAAN,QAAgB1C,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEuC,IAA1B,KACA,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEG,IAAV,QAAoB1C,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEsC,QAA9B,CADA,IAEA,MAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEI,IAAP,QAAiB1C,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwC,KAA3B,CAHF;IAKD,CAPsB,CAAzB;IAUA,IAAIG,gBAAgB,SAAhB,oBAAgB,WAAhB,GAAgB,MAAhB,mBAAgB,CAAE1G,MAAtB,EAA8B;IAC9B,KAAKgG,mBAAL,CAAyBrE,IAAzB,CAA8B;MAC5B2E,IAAI,EAAEA,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEG,IAAN,EADsB;MAE5BJ,QAAQ,EAAEA,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEI,IAAV,EAFkB;MAG5BF,KAAK,EAAEA,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEE,IAAP;IAHqB,CAA9B;IAMA,KAAKE,cAAL,CAAoB,gBAApB;EACD;;EAEDA,cAAc,CAACC,IAAD,EAAU;IACtB,QAAQA,IAAR;MACE,KAAK,gBAAL;QACE,KAAKjB,UAAL,CAAgBkB,UAAhB,CAA2B;UACzBR,QAAQ,EAAE,IADe;UAEzBC,IAAI,EAAE,IAFmB;UAGzBC,KAAK,EAAE;QAHkB,CAA3B;QAKA;IAPJ;EASD;;EAEDpC,UAAU;;;IACR,KAAKlC,YAAL,GAAoB6E,KAAK,CAACC,OAAN,CAAc,KAAKzH,QAAnB,CAApB;IACA,KAAK8E,oBAAL;IACA,KAAK4C,aAAL,GAAqBd,IAAI,CAACe,KAAL,CAAWC,YAAY,CAACC,OAAb,CAAqB,kBAArB,CAAX,CAArB;IACA,KAAKC,gBAAL,GACE,YAAKJ,aAAL,MAAkB,IAAlB,IAAkBzH,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEpG,MAAF,CAAU0G,MAAD,IAAgB;MACzC,OAAO,CAAC,CACN,cADM,EAEN,iBAFM,EAGN,KAHM,EAIN,SAJM,EAKN,qBALM,EAMN,kBANM,EAON4B,QAPM,CAOG5B,MAAM,CAAC6B,WAPV,CAAR;IAQD,CATiB,CAAlB,KASM,EAVR;;IAYA,KAAKlD,MAAL,CACG4C,MADH,CACUzG,0BADV,EAEGqG,IAFH,CAEQ1H,SAAS,CAAC,KAAK+H,OAAN,CAFjB,EAGGX,SAHH,CAGcsC,IAAD,IAAc;;;MACvB,KAAKqE,YAAL,GACErE,IAAI,CAACsE,SAAL,IAAkBtE,IAAI,CAACsE,SAAL,CAAetH,MAAf,GAAwB,CAA1C,GACIgD,IAAI,CAACsE,SAAL,CAAe,CAAf,EAAkBC,UADtB,GAEI,IAHN;MAIA,KAAKC,eAAL,GACExE,IAAI,CAACsE,SAAL,IAAkBtE,IAAI,CAACsE,SAAL,CAAetH,MAAf,GAAwB,CAA1C,GACIgD,IAAI,CAACsE,SAAL,CAAe,CAAf,EAAkBE,eADtB,GAEI,IAHN;MAIA,KAAKC,kBAAL,GACE,UAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEC,kBAAN,MAAwB,IAAxB,IAAwBnI,aAAxB,GAAwB,MAAxB,GAAwBA,GAAEoI,2BAD5B;MAEA,KAAKC,gBAAL,GACE,UAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEC,gBAAN,MAAsB,IAAtB,IAAsB3F,aAAtB,GAAsB,MAAtB,GAAsBA,GAAE4F,4BAD1B;;MAEA,IAAI,KAAKF,gBAAT,EAA2B;QACzB,WAAKjC,UAAL,CAAgBoC,QAAhB,CAAyB,OAAzB,OAAiC,IAAjC,IAAiC1F,aAAjC,GAAiC,MAAjC,GAAiCA,GAAE2F,aAAF,CAAgB,CAC/CpP,UAAU,CAACqP,QADoC,CAAhB,CAAjC;MAGD,CAJD,MAIO;QACL,WAAKtC,UAAL,CAAgBoC,QAAhB,CAAyB,OAAzB,OAAiC,IAAjC,IAAiCvF,aAAjC,GAAiC,MAAjC,GAAiCA,GAAE0F,eAAF,EAAjC;MACD;IACF,CAvBH;;IAyBA,iBAAKvC,UAAL,MAAe,IAAf,IAAezD,aAAf,GAAe,MAAf,GAAeA,GACXiG,GADW,CACP,oBADO,CAAf,MAC6B,IAD7B,IAC6B9F,aAD7B,GAC6B,MAD7B,GAC6BA,GACzB+F,YADyB,CACZ1H,SADY,CACD2H,GAAD,IAAQ;;;MAC/B,KAAKC,aAAL,GAAqBrO,YAAY,CAC/BoO,GAD+B,EAE/B,kBAAK/I,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEgJ,OAAf,MAAsB,IAAtB,IAAsBrG,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEmD,QAAxB,KAAoC,KAAKmC,eAFV,CAAjC;IAID,CAN0B,CAD7B;;IASA,KAAKhJ,MAAL,CACG4C,MADH,CACUnF,yBADV,EAEG+E,IAFH,CAEQ1H,SAAS,CAAC,KAAK+H,OAAN,CAFjB,EAGGX,SAHH,CAGcsC,IAAD,IAAc;MACvB,KAAKwF,UAAL,GAAkBxF,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAClByF,KADc,GAEf7G,IAFe,CAEV,CAACC,CAAD,EAASC,CAAT,KAAoBD,CAAC,CAACkC,QAAF,CAAWhC,aAAX,CAAyBD,CAAC,CAACiC,QAA3B,CAFV,CAAlB;IAGD,CAPH;;IASA,KAAK2E,gBAAL,CACG1H,IADH,CAEI/H,YAAY,CAAC,GAAD,CAFhB,EAGIC,oBAAoB,EAHxB,EAIIC,MAAM,CAAEwP,SAAD,IAAuBA,SAAS,CAAC3I,MAAV,GAAmB,CAA3C,CAJV,EAMGU,SANH,CAMciI,SAAD,IAAsB;MAC/B,KAAKnK,MAAL,CAAYS,QAAZ,CAAqB,IAAIjD,wBAAJ,CAA6B2M,SAA7B,CAArB;IACD,CARH;;IAUA,KAAKnK,MAAL,CACG4C,MADH,CACU5F,qBADV,EAEGwF,IAFH,CAEQ1H,SAAS,CAAC,KAAK+H,OAAN,CAFjB,EAGGX,SAHH,CAGcsC,IAAD,IAAc;;;MACvB,KAAK4F,WAAL,GAAmB,UAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CACnBH,KADe,QACR,IADQ,IACRlJ,aADQ,GACR,MADQ,GACRA,GACPqC,IADO,CACF,CAACC,CAAD,EAASC,CAAT,KAAoBD,CAAC,CAACgH,IAAF,CAAO9G,aAAP,CAAqBD,CAAC,CAAC+G,IAAvB,CADlB,CADX;IAGD,CAPH;;IASA,KAAKrK,MAAL,CACG4C,MADH,CACU3F,8BADV,EAEGuF,IAFH,CAEQ1H,SAAS,CAAC,KAAK+H,OAAN,CAFjB,EAGGX,SAHH,CAGcsC,IAAD,IAAkB;MAC3B,KAAK8F,oBAAL,GAA4B9F,IAA5B;IACD,CALH;;IAOA,KAAKxE,MAAL,CACG4C,MADH,CACUrF,yBADV,EAEGiF,IAFH,CAEQ1H,SAAS,CAAC,KAAK+H,OAAN,CAFjB,EAGGX,SAHH,CAGcsC,IAAD,IAAc;MACvB,KAAK+F,YAAL,GAAoB/F,IAAI,CAACyF,KAAL,GAAa7G,IAAb,CAAkB,CAACC,CAAD,EAASC,CAAT,KAAmB;QACvD,MAAMkH,KAAK,GAAGnH,CAAC,CAACgH,IAAF,IAAU,EAAxB;QACA,MAAMI,KAAK,GAAGnH,CAAC,CAAC+G,IAAF,IAAU,EAAxB;QACA,OAAOG,KAAK,CAACjH,aAAN,CAAoBkH,KAApB,CAAP;MACD,CAJmB,CAApB;IAKD,CATH;;IAWA,KAAKzK,MAAL,CACG4C,MADH,CACUtF,+BADV,EAEGkF,IAFH,CAEQ1H,SAAS,CAAC,KAAK+H,OAAN,CAFjB,EAGGX,SAHH,CAGcsC,IAAD,IAAkB;MAC3B,KAAKkG,qBAAL,GAA6BlG,IAA7B;IACD,CALH;;IAOA,MAAMmG,oBAAoB,GAAG,KAAK3K,MAAL,CAC1B4C,MAD0B,CACnB/E,qBADmB,EAE1B2E,IAF0B,CAErB1H,SAAS,CAAC,KAAK+H,OAAN,CAFY,CAA7B;;IAIA,MAAM+H,6BAA6B,GAAG,KAAK5K,MAAL,CACnC4C,MADmC,CAC5B9E,8BAD4B,EAEnC0E,IAFmC,CAE9B1H,SAAS,CAAC,KAAK+H,OAAN,CAFqB,CAAtC;;IAIA,MAAMgI,SAAS,GAAG,KAAK7K,MAAL,CACf4C,MADe,CACR3E,2BADQ,EAEfuE,IAFe,CAEV1H,SAAS,CAAC,KAAK+H,OAAN,CAFC,CAAlB;;IAIA,MAAMiI,kBAAkB,GAAG,KAAK9K,MAAL,CACxB4C,MADwB,CACjB1E,oCADiB,EAExBsE,IAFwB,CAEnB1H,SAAS,CAAC,KAAK+H,OAAN,CAFU,CAA3B;;IAIA,MAAMkI,YAAY,GAAG,KAAK/K,MAAL,CAClB4C,MADkB,CACXhG,cADW,EAElB4F,IAFkB,CAEb1H,SAAS,CAAC,KAAK+H,OAAN,CAFI,CAArB;;IAIA,MAAMmI,qBAAqB,GAAG,KAAKhL,MAAL,CAC3B4C,MAD2B,CACpB/F,uBADoB,EAE3B2F,IAF2B,CAEtB1H,SAAS,CAAC,KAAK+H,OAAN,CAFa,CAA9B;;IAIArI,aAAa,CAAC;MACZyQ,mBAAmB,EAAEN,oBADT;MAEZO,4BAA4B,EAAEN,6BAFlB;MAGZO,QAAQ,EAAEN,SAHE;MAIZO,iBAAiB,EAAEN,kBAJP;MAKZvG,WAAW,EAAEwG,YALD;MAMZM,oBAAoB,EAAEL;IANV,CAAD,CAAb,CAOG9I,SAPH,CAQE,CAAC;MACC+I,mBADD;MAECC,4BAFD;MAGCC,QAHD;MAICC,iBAJD;MAKC7G,WALD;MAMC8G;IAND,CAAD,KAOK;;;MACH,IAAIC,QAAJ;;MACA,IAAI/G,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEtB,QAAb,CAAsB,0BAAtB,CAAJ,EAAuD;QACrD,KAAKsI,aAAL,GAAqB,IAArB;MACD;;MACD,IAAIhH,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEtB,QAAb,CAAsB,+BAAtB,CAAJ,EAA4D;QAC1DqI,QAAQ,GAAGH,QAAX;MACD,CAFD,MAEO;QACLG,QAAQ,GAAGL,mBAAX;MACD;;MAED,IAAIO,WAAW,GAAGF,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE3Q,MAAV,CAAkBoL,IAAD,IAAeA,IAAI,CAAC0F,QAArC,CAAlB;MACA,KAAKC,aAAL,GAAqBJ,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE3Q,MAAV,CAAkBoL,IAAD,IAAe,CAACA,IAAI,CAAC0F,QAAtC,CAArB;MACA,KAAKE,eAAL,GAAuBpQ,YAAY,CACjCiQ,WADiC,EAEjC,WAAK1K,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEC,QAFkB,CAAnC;MAIA,KAAK8E,gBAAL,GAAwBvK,YAAY,CAClCiQ,WADkC,EAElC,WAAK1K,QAAL,MAAa,IAAb,IAAa4C,aAAb,GAAa,MAAb,GAAaA,GAAE1C,QAFmB,CAApC;MAIA,KAAK4K,iBAAL,GAAyBrQ,YAAY,CACnCiQ,WADmC,EAEnC,WAAK1K,QAAL,MAAa,IAAb,IAAa+C,aAAb,GAAa,MAAb,GAAaA,GAAEqC,eAFoB,CAArC;MAIA,KAAK0F,iBAAL,GAAyBrQ,YAAY,CACnCiQ,WADmC,EAEnC,WAAK1K,QAAL,MAAa,IAAb,IAAakD,aAAb,GAAa,MAAb,GAAaA,GAAEkC,eAFoB,CAArC;MAIA,KAAKJ,gBAAL,GAAwB,WAAK6F,eAAL,MAAoB,IAApB,IAAoBxH,aAApB,GAAoB,MAApB,GAAoBA,GAAExJ,MAAF,CACzCkR,EAAD,IAAY;QAAA;;QAAC,SAAC,iBAAK/K,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEmF,eAAf,MAA8B,IAA9B,IAA8BxC,aAA9B,GAA8B,MAA9B,GAA8BA,GAAET,QAAF,CAAW4I,EAAE,SAAF,MAAE,WAAF,GAAE,MAAF,KAAE,CAAE3H,EAAf,CAA/B;MAAiD,CADpB,CAA5C;MAGA,KAAK+B,kBAAL,GAA0B,WAAK2F,iBAAL,MAAsB,IAAtB,IAAsBxH,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEzJ,MAAF,CAC7CkR,EAAD,IAAY;QAAA;;QAAC,SAAC,iBAAK/K,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEC,QAAf,MAAuB,IAAvB,IAAuB0C,aAAvB,GAAuB,MAAvB,GAAuBA,GAAET,QAAF,CAAW4I,EAAE,SAAF,MAAE,WAAF,GAAE,MAAF,KAAE,CAAE3H,EAAf,CAAxB;MAA0C,CADT,CAAhD;MAGA,KAAKiD,UAAL,CACGwC,GADH,CACO,kBADP,EAEGC,YAFH,CAEgB1H,SAFhB,CAE2B2H,GAAD,IAAa;QACnC,KAAK5D,kBAAL,GAA0B,KAAK2F,iBAAL,CAAuBjR,MAAvB,CACvBkR,EAAD,IAAa,EAAChC,GAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAE5G,QAAL,CAAc4I,EAAE,SAAF,MAAE,WAAF,GAAE,MAAF,KAAE,CAAE3H,EAAlB,CAAD,CADW,CAA1B;MAGD,CANH;MAQA,KAAKiD,UAAL,CACGwC,GADH,CACO,mBADP,EAEGC,YAFH,CAEgB1H,SAFhB,CAE2B2H,GAAD,IAAa;QACnC,KAAK/D,gBAAL,GAAwB,KAAK6F,eAAL,CAAqBhR,MAArB,CACrBkR,EAAD,IAAa,EAAChC,GAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAE5G,QAAL,CAAc4I,EAAE,SAAF,MAAE,WAAF,GAAE,MAAF,KAAE,CAAE3H,EAAlB,CAAD,CADS,CAAxB;MAGD,CANH;MAQA,KAAK4H,iBAAL,GACEZ,4BAA4B,IAC5BE,iBADA,IAEAC,oBAHF;IAID,CAtEH;;IAyEA,IAAI,CAAC,KAAK5H,YAAV,EAAwB;MACtB,KAAKsI,UAAL,GAAkBzQ,UAAU,CAAC,iBAAKwF,QAAL,MAAa,IAAb,IAAakD,aAAb,GAAa,MAAb,GAAaA,GAAE+F,OAAf,MAAsB,IAAtB,IAAsB5F,aAAtB,GAAsB,MAAtB,GAAsBA,GAAE4H,UAAzB,CAA5B;IACD,CA1MO,CA2MR;;EACD;;EAEDC,eAAe;IACb,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAT,CAAwB,mBAAxB,CAAhB;;IACA,IAAIF,OAAJ,EAAa;MACX,KAAKG,aAAL,GAAqB,OAArB;MACA,MAAMC,gBAAgB,GAAG,GAAG,KAAKD,aAAa,EAA9C;MACA,KAAKE,UAAL,CAAgBC,aAAhB,CAA8BC,KAA9B,CAAoCC,MAApC,GAA6CJ,gBAA7C;IACD;;IACD,KAAKpM,GAAL,CAASyM,aAAT;EACD;;EAEDC,aAAa,CAACC,OAAD,EAAwB;IACnC,MAAMC,YAAY,GAAGD,OAAO,CAAC5E,KAA7B;;IAEA,IAAI6E,YAAJ,EAAkB;MAChB,MAAMjI,WAAW,GAAG,IAAIrE,IAAJ,EAApB;MACA,MAAMuM,gBAAgB,GAAG,IAAIvM,IAAJ,CAASsM,YAAT,CAAzB;MACAjI,WAAW,CAACmI,UAAZ,CAAuB,CAAvB,EAA0B,CAA1B;MACAD,gBAAgB,CAACC,UAAjB,CAA4B,CAA5B,EAA+B,CAA/B;;MACA,IAAID,gBAAgB,GAAGlI,WAAvB,EAAoC;QAClC,OAAO;UAAEoI,WAAW,EAAE;QAAf,CAAP;MACD;IACF;;IAED,OAAO,IAAP;EACD;;EAEDC,oBAAoB;IAClB,IAAIC,YAAY,GAAQ;MACtB1I,IAAI,EAAE,KAAK1D,QADW;MAEtBqM,UAAU,EAAE,MAAK;QACfC,mBAAmB,CAACC,IAApB;MACD,CAJqB;MAKtBC,qBAAqB,EAAE,KAAKA;IALN,CAAxB;IAOA,MAAMF,mBAAmB,GAAG,KAAKtN,YAAL,CAAkBqC,IAAlB,CAC1BjG,wBAD0B,EAE1B;MACEgR,YADF;MAEE7K,KAAK,EAAE;IAFT,CAF0B,CAA5B;EAOD;;EAED+C,WAAW,CAAC/D,MAAD,EAAc6C,EAAd,EAAqB;;;IAC9B,MAAMqJ,SAAS,GAAIrJ,EAAE,KAAKsJ,SAAP,IAAoBtJ,EAAE,KAAK,IAA3B,IAAmCA,EAAE,KAAK,EAA3C,GAAiDA,EAAjD,GAAsD,EAAxE;;IACA,IACE,CAAC,kBAAKpD,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEM,MAAf,MAAqB,IAArB,IAAqBqC,aAArB,GAAqB,MAArB,GAAqBA,GAAE/B,mBAAvB,MACC,iBAAKb,QAAL,MAAa,IAAb,IAAa+C,aAAb,GAAa,MAAb,GAAaA,GAAExC,MAAf,MAAqB,IAArB,IAAqB2C,aAArB,GAAqB,MAArB,GAAqBA,GAAE1C,mBADxB,MAECD,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEM,mBAFT,CAAD,KAEkC,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE8L,UAAR,MAAuB,gBAH3D,EAIE;MACA,KAAKtI,yBAAL,CAA+BoI,SAA/B;IACD;;IAED,IAAI,KAAK7K,WAAL,KAAqB,UAAzB,EAAqC;MACnC,IAAIrB,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEM,mBAAZ,EAAiC;QAC/B,KAAK+L,oBAAL,GAA4B,IAA5B;MACD,CAFD,MAEO,IAAIrM,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyC,yBAAZ,EAAuC;QAC5C,KAAK4J,oBAAL,GAA4B,KAA5B;QACA,KAAKC,2BAAL;MACD;IACF,CAPD,MAOO;MACL,IAAItM,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEC,mBAAZ,EAAiC;QAC/B,KAAKoM,oBAAL,GAA4B,IAA5B;MACD,CAFD,MAEO;QACL,KAAKA,oBAAL,GAA4B,KAA5B;QACA,KAAKC,2BAAL;MACD;IACF;;IAED9R,qBAAqB,CAAC,KAAKsL,UAAN,EAAkB,YAAlB,EAAgC9F,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE6C,EAAxC,CAArB;;IACA,IAAI,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE0J,UAAR,MAAkB,IAAlB,IAAkBzJ,aAAlB,GAAkB,MAAlB,GAAkBA,GAAE3C,MAAxB,EAAgC;MAC9B,KAAKqM,cAAL,GAAsBxM,MAAM,CAACuM,UAA7B;MACA,KAAKjN,UAAL,GAAkB,IAAlB;IACD;;IACD,KAAKuE,YAAL,CAAkB7D,MAAlB;IACA,KAAKyM,cAAL,GAAsBzM,MAAtB;IACA,IAAI0M,UAAU,GAAG,uBAAKvK,gBAAL,CAAsB7I,MAAtB,CACdqT,SAAD,IAAoB,UAAS,SAAT,aAAS,WAAT,GAAS,MAAT,YAAS,CAAE9J,EAAX,OAAkB7C,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE6C,EAA1B,CADL,OAEhB,IAFgB,IAEhBE,aAFgB,GAEhB,MAFgB,GAEhBA,GAAG,CAAH,CAFgB,MAEX,IAFW,IAEXiB,aAFW,GAEX,MAFW,GAEXA,GAAEuI,UAFS,MAEC,IAFD,IAECtI,aAFD,GAEC,MAFD,GAECA,GAAE9D,MAFpB;;IAGA,IAAIuM,UAAJ,EAAgB;MACd/R,gBAAgB,CAACZ,cAAD,EAAiB,KAAK+L,UAAtB,EAAkC,QAAlC,EAA4C,CAC1D/M,UAAU,CAACqP,QAD+C,CAA5C,CAAhB;IAGD,CAJD,MAIO;MACLzN,gBAAgB,CAACb,gBAAD,EAAmB,KAAKgM,UAAxB,EAAoC,QAApC,CAAhB;IACD;EACF;;EAEDwG,2BAA2B;IACzB,MAAMM,aAAa,GAAG,CACpB,iBADoB,EAEpB,YAFoB,EAGpB,gBAHoB,EAIpB,gBAJoB,EAKpB,eALoB,EAMpB,YANoB,CAAtB;IAQAA,aAAa,CAAClL,OAAd,CAAsBmL,KAAK,IAAIlS,gBAAgB,CAACb,gBAAD,EAAmB,KAAKgM,UAAxB,EAAoC+G,KAApC,CAA/C;EACD;;EAEDC,eAAe,CACbC,UADa,EAEbC,aAAqC,EAFxB,EAE0B;;;IAEvCC,MAAM,CAACC,IAAP,CAAY,KAAKpH,UAAL,CAAgBoC,QAA5B,EAAsCxG,OAAtC,CAA+CyL,WAAD,IAAgB;MAC5D,IAAI,CAAC,KAAKrI,eAAL,CAAqBsI,cAArB,CAAoCD,WAApC,CAAL,EAAuD;QACrD,KAAKrH,UAAL,CAAgBuH,aAAhB,CAA8BF,WAA9B;MACD;IACF,CAJD;;IAMA,KAAK,MAAMA,WAAX,IAA0BJ,UAA1B,EAAsC;MACpC,IAAIA,UAAU,CAACK,cAAX,CAA0BD,WAA1B,CAAJ,EAA4C;QAC1C,KAAKrH,UAAL,CAAgBwH,UAAhB,CACEH,WADF,EAEE,KAAKzO,WAAL,CAAiB6M,OAAjB,CAAyBwB,UAAU,CAACI,WAAD,CAAnC,EAAkD,EAAlD,CAFF;MAID;IACF;;IAED,KAAK,MAAMA,WAAX,IAA0BH,UAA1B,EAAsC;MACpC,IAAIA,UAAU,CAACI,cAAX,CAA0BD,WAA1B,CAAJ,EAA4C;QAC1C,MAAMI,eAAe,GAAG,KAAKC,aAAL,CAAmBR,UAAU,CAACG,WAAD,CAA7B,CAAxB;QACA,WAAKrH,UAAL,CAAgBwC,GAAhB,CAAoB6E,WAApB,OAAgC,IAAhC,IAAgCzN,aAAhC,GAAgC,MAAhC,GAAgCA,GAAEyI,aAAF,CAAgBoF,eAAhB,CAAhC;MACD;IACF;EACF;;EAEDE,aAAa,CAAC9H,MAAD,EAAqB;IAChC,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE1F,mBAAR,IAA8B,EAA9B,GAAmC,KAAK4D,YAAL,CAAkB8B,MAAlB,EAA0B,IAA1B,CAAnC;EACD;;EAED6H,aAAa,CAACR,UAAD,EAAqB;IAChC,MAAMO,eAAe,GAAkB,EAAvC;IACAP,UAAU,SAAV,cAAU,WAAV,GAAU,MAAV,aAAU,CAAEtL,OAAZ,CAAqBgM,SAAD,IAAsB;MACxC,QAAQA,SAAR;QACE,KAAK,UAAL;UACEH,eAAe,CAACzL,IAAhB,CAAqB/I,UAAU,CAACqP,QAAhC;UACA;;QACF,KAAK,YAAL;UACEmF,eAAe,CAACzL,IAAhB,CAAqB,KAAK6L,oBAAL,CAA0B,KAAKpK,WAA/B,CAArB;UACA;;QACF;UACE;MARJ;IAUD,CAXD;IAYA,OAAOgK,eAAP;EACD;;EAEDI,oBAAoB,CAACzO,IAAD,EAAU;IAC5B,OAAQqM,OAAD,IAAsD;MAC3D,MAAMqC,WAAW,GAAG,IAAI1O,IAAJ,CAASA,IAAT,CAApB;MACA,MAAM2O,YAAY,GAAG,IAAI3O,IAAJ,CAASqM,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAE5E,KAAlB,CAArB;MAEA,MAAMmH,aAAa,GAAGD,YAAY,SAAZ,gBAAY,WAAZ,GAAY,MAAZ,eAAY,CAAEE,QAAd,EAAtB;MACA,MAAMC,eAAe,GAAGH,YAAY,SAAZ,gBAAY,WAAZ,GAAY,MAAZ,eAAY,CAAEI,UAAd,EAAxB;MAEA,MAAM1K,WAAW,GAAG,IAAIrE,IAAJ,CAASA,IAAT,CAApB;MACA,MAAMgP,YAAY,GAAG,IAAIhP,IAAJ,CAASqM,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAE5E,KAAlB,CAArB;MAEApD,WAAW,CAAC4K,QAAZ,CAAqB,CAArB,EAAwB,CAAxB,EAA2B,CAA3B,EAA8B,CAA9B;MACAD,YAAY,CAACC,QAAb,CAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B;;MAEA,IACE5K,WAAW,CAAC6K,OAAZ,MAAyBF,YAAY,CAACE,OAAb,EAAzB,KACCN,aAAa,GAAGF,WAAW,CAACG,QAAZ,EAAhB,IACED,aAAa,KAAKF,WAAW,CAACG,QAAZ,EAAlB,IACCC,eAAe,IAAIJ,WAAW,CAACK,UAAZ,EAHvB,CADF,EAKE;QACA,OAAO;UAAEI,WAAW,EAAE;QAAf,CAAP;MACD;;MAED,OAAO,IAAP;IACD,CAvBD;EAwBD;;EAEDxK,YAAY,CAAC7D,MAAD,EAAuBsO,cAAuB,KAA9C,EAAmD;;;IAC7D,IAAI,EAAC,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,UAAR,MAAkB,IAAlB,IAAkB7M,aAAlB,GAAkB,MAAlB,GAAkBA,GAAES,MAArB,KAA+B,CAACmO,WAApC,EACE3T,gBAAgB,CAACb,gBAAD,EAAmB,KAAKgM,UAAxB,EAAoC,QAApC,CAAhB,CADF,KAEKnL,gBAAgB,CAACZ,cAAD,EAAiB,KAAK+L,UAAtB,EAAkC,QAAlC,CAAhB;IAEL,MAAMiH,UAAU,GAA2B,EAA3C;IACA,MAAMC,UAAU,GAA2B,EAA3C,CAN6D,CAQ7D;;IACA,IAAI,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/M,mBAAR,MAA+BD,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEM,mBAAvC,CAAJ,EAAgE;MAC9D,MAAMiO,YAAY,GAAkB,EAApC;MACA,CACE,CAAC,UAAD,EAAa,IAAb,EAAmB,EAAnB,CADF,EAEE,CAAC,iBAAD,EAAoB,IAApB,EAA0B,CAAC,UAAD,CAA1B,CAFF,EAGE,CAAC,YAAD,EAAe,IAAf,EAAqB,CAAC,UAAD,CAArB,CAHF,EAIE,CAAC,gBAAD,EAAmB,IAAnB,EAAyB,EAAzB,CAJF,EAKE,CAAC,iBAAD,EAAoB,UAApB,EAAgC,EAAhC,CALF,EAME,CAAC,gBAAD,EAAmB,IAAnB,EAAyB,CAAC,UAAD,CAAzB,CANF,EAOE,CAAC,eAAD,EAAkB,IAAlB,EAAwB,CAAC,KAAK3G,kBAAL,GAA0B,UAA1B,GAAuC,EAAxC,CAAxB,CAPF,EAQE,CAAC,YAAD,EAAe,IAAf,EAAqB,CAAC,UAAD,CAArB,CARF,EASE,CACE,UADF,EAEE,kBAAKnI,QAAL,MAAa,IAAb,IAAa4C,aAAb,GAAa,MAAb,GAAaA,GAAEqG,OAAf,MAAsB,IAAtB,IAAsBlG,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEgD,QAAxB,KAAoC,KAAKmC,eAF3C,EAGE,EAHF,CATF,EAcEjG,OAdF,CAcWmL,KAAD,IAAe;QACvB0B,YAAY,CAACzM,IAAb,CAAkB;UAChB0M,UAAU,EAAE,KADI;UAEhB7H,KAAK,EAAEkG,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAG,CAAH,CAFI;UAGhBA,KAAK,EAAE;YACL4B,cAAc,EAAE,QADX;YAELC,SAAS,EAAE,QAFN;YAGLC,SAAS,EAAE,QAHN;YAILC,cAAc,EAAE,QAJX;YAKL/L,EAAE,EAAE,QALC;YAMLmG,IAAI,EAAE6D,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAG,CAAH,CANN;YAOLnK,SAAS,EAAE,CAPN;YAQLmM,MAAM,EAAE,QARH;YASLpJ,KAAK,EAAE;UATF,CAHS;UAchBuH,UAAU,EAAEH,KAAK,CAAC,CAAD;QAdD,CAAlB;MAgBD,CA/BD;MAgCA7M,MAAM,mCACDA,MADC,GACK;QACTuO;MADS,CADL,CAAN;IAID;;IAED,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,YAAR,MAAoB,IAApB,IAAoB5L,aAApB,GAAoB,MAApB,GAAoBA,GAAEjB,OAAF,CAAWmL,KAAD,IAAuB;MACnDE,UAAU,CAACF,KAAK,CAACA,KAAN,CAAY7D,IAAb,CAAV,GAA+B,IAA/B;MACAgE,UAAU,CAACH,KAAK,CAACA,KAAN,CAAY7D,IAAb,CAAV,GAA+B6D,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEG,UAAtC;IACD,CAHmB,CAApB;IAIA,KAAKF,eAAL,CAAqBC,UAArB,EAAiCC,UAAjC;;IACA,IAAI,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/M,mBAAR,MAA+BD,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEM,mBAAvC,CAAJ,EAAgE;MAC9D,KAAKwF,UAAL,CACGwC,GADH,CACO,UADP,EAEGwG,QAFH,CAEY,kBAAKrP,QAAL,MAAa,IAAb,IAAaqD,aAAb,GAAa,MAAb,GAAaA,GAAE4F,OAAf,MAAsB,IAAtB,IAAsB3F,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEyC,QAAxB,KAAoC,KAAKmC,eAFrD;MAGA,KAAK7B,UAAL,CAAgBwC,GAAhB,CAAoB,iBAApB,EAAuCwG,QAAvC,CAAgD,UAAhD;MACA,KAAKhJ,UAAL,CAAgBwC,GAAhB,CAAoB,YAApB,EAAkCwG,QAAlC,CAA2C,KAAKvL,WAAhD;MAEA,KAAKuC,UAAL,CAAgBwC,GAAhB,CAAoB,UAApB,EAAgCC,YAAhC,CAA6C1H,SAA7C,CAAwD2H,GAAD,IAAQ;;;QAC7D,KAAKuG,yBAAL,GAAiC3U,YAAY,CAC3C,KAAK0L,UAAL,CAAgBa,KAAhB,CAAsBxB,cAAtB,KACA,WAAKW,UAAL,MAAe,IAAf,IAAepG,aAAf,GAAe,MAAf,GAAeA,GAAE4I,GAAF,CAAM,gBAAN,EAAwB3B,KADvC,CAD2C,EAG3C6B,GAH2C,CAA7C;MAKD,CAND;MAOA,KAAK1C,UAAL,CAAgBwC,GAAhB,CAAoB,gBAApB,EAAsCC,YAAtC,CAAmD1H,SAAnD,CAA8D2H,GAAD,IAAQ;QACnE,KAAKuG,yBAAL,GAAiC3U,YAAY,CAC3CoO,GAD2C,EAE3C,KAAK1C,UAAL,CAAgBa,KAAhB,CAAsBnB,QAFqB,CAA7C;MAID,CALD;MAMA,KAAKM,UAAL,CAAgBwC,GAAhB,CAAoB,kBAApB,EAAwCC,YAAxC,CAAqD1H,SAArD,CAAgE2H,GAAD,IAAQ;QACrE,IAAIA,GAAJ,EAAS;UACP,KAAKwG,eAAL,CAAqBpJ,gBAArB,GAAwC4C,GAAxC;;UACA,KAAK7J,MAAL,CAAYS,QAAZ,CAAqB,IAAI7C,4BAAJ,CAAiCiM,GAAjC,CAArB;;UACA,KAAK7J,MAAL,CAAY4C,MAAZ,CAAmB7E,iBAAnB,EAAsCyE,IAAtC,GAA6CN,SAA7C,CAAwDsC,IAAD,IAAc;YACnE,KAAK8L,WAAL,GAAmB9L,IAAnB;UACD,CAFD;QAGD;MACF,CARD;MASA,KAAK2C,UAAL,CAAgBwC,GAAhB,CAAoB,mBAApB,EAAyCC,YAAzC,CAAsD1H,SAAtD,CAAiE2H,GAAD,IAAQ;QACtE,IAAIA,GAAJ,EAAS;UACP,KAAKwG,eAAL,CAAqBnJ,iBAArB,GAAyC2C,GAAzC;QACD;MACF,CAJD;IAKD,CAlCD,MAkCO;MACL,IAAI0G,OAAO,GAAG,iBAAKzP,QAAL,MAAa,IAAb,IAAauE,aAAb,GAAa,MAAb,GAAaA,GAAEmL,QAAf,MAAuB,IAAvB,IAAuBlL,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEmL,GAAF,CAAOjM,IAAD,IAAeA,IAAI,CAAC6F,IAA1B,CAArC;MACA,WAAKlD,UAAL,CAAgBwC,GAAhB,CAAoB,UAApB,OAA+B,IAA/B,IAA+B+G,aAA/B,GAA+B,MAA/B,GAA+BA,GAAEP,QAAF,CAAWI,OAAX,CAA/B;IACD;;IACD,iBAAKpJ,UAAL,MAAe,IAAf,IAAewJ,aAAf,GAAe,MAAf,GAAeA,GAAEhH,GAAF,CAAM,mBAAN,CAAf,MAAyC,IAAzC,IAAyCiH,aAAzC,GAAyC,MAAzC,GAAyCA,GAAEhH,YAAF,CAAe1H,SAAf,CAA0B2H,GAAD,IAAQ;;;MACxE,KAAKgH,gBAAL,GAAwBpV,YAAY,CAClC,YAAKqF,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAE+P,SAAf,KAA4B,KAAK3J,UAAL,CAAgBa,KAAhB,CAAsB8I,SADhB,EAElCjH,GAFkC,CAApC;IAID,CALwC,CAAzC;IAOA,iBAAK1C,UAAL,MAAe,IAAf,IAAe4J,aAAf,GAAe,MAAf,GAAeA,GAAEpH,GAAF,CAAM,WAAN,CAAf,MAAiC,IAAjC,IAAiCqH,aAAjC,GAAiC,MAAjC,GAAiCA,GAAEpH,YAAF,CAAe1H,SAAf,CAA0B2H,GAAD,IAAQ;;;MAChE,KAAKgH,gBAAL,GAAwBpV,YAAY,CAClCoO,GADkC,EAElC,KAAK1C,UAAL,CAAgBa,KAAhB,CAAsBiJ,iBAAtB,KACA,WAAKnQ,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEkQ,iBADf,CAFkC,CAApC;IAKD,CANgC,CAAjC;IAQA,iBAAK9J,UAAL,MAAe,IAAf,IAAe+J,aAAf,GAAe,MAAf,GAAeA,GACXvH,GADW,CACP,oBADO,CAAf,MAC6B,IAD7B,IAC6BwH,aAD7B,GAC6B,MAD7B,GAC6BA,GACzBvH,YADyB,CACZ1H,SADY,CACD2H,GAAD,IAAQ;;;MAC/B,KAAKC,aAAL,GAAqBrO,YAAY,CAC/BoO,GAD+B,EAE/B,kBAAK/I,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEgJ,OAAf,MAAsB,IAAtB,IAAsBrG,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEmD,QAAxB,KAAoC,KAAKmC,eAFV,CAAjC;IAID,CAN0B,CAD7B;;IASA,IAAI3H,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEC,mBAAZ,EAAiC;MAC/B,WAAK6F,UAAL,CACGwC,GADH,CACO,mBADP,OAC2B,IAD3B,IAC2ByH,aAD3B,GAC2B,MAD3B,GAC2BA,GACvBjB,QADuB,CACd,kBAAKrP,QAAL,MAAa,IAAb,IAAauQ,aAAb,GAAa,MAAb,GAAaA,GAAEtH,OAAf,MAAsB,IAAtB,IAAsBuH,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEzK,QAAxB,KAAoC,KAAKmC,eAD3B,CAD3B;IAGD;EACF;;EAEDuI,gBAAgB;IACd,KAAKpK,UAAL,CAAgBwC,GAAhB,CAAoB,eAApB,EAAqC6H,SAArC,CAA+C,IAA/C;IACA,KAAKrK,UAAL,CAAgBoC,QAAhB,CAAyB,QAAzB,EAAmC4G,QAAnC,CAA4C,EAA5C;IACA,KAAKhJ,UAAL,CAAgBoC,QAAhB,CAAyB,YAAzB,EAAuC4G,QAAvC,CAAgD,EAAhD;IACA,KAAKrC,cAAL,GAAsB,IAAtB;IACA,KAAKnN,UAAL,GAAkB,KAAlB;IACA,KAAK8Q,cAAL,GAAsB,IAAtB;IACA,KAAK/D,oBAAL,GAA4B,KAA5B;IACA,KAAKC,2BAAL;EACD;;EAED+D,YAAY,CAACC,gBAAyB,KAA1B,EAA+B;;;IACzC,KAAKA,aAAL,GAAqBA,aAArB;IACArD,MAAM,CAACC,IAAP,CAAY,KAAKpH,UAAL,CAAgBoC,QAA5B,EAAsCxG,OAAtC,CAA+CmL,KAAD,IAAU;MACtD,MAAMtB,OAAO,GAAG,KAAKzF,UAAL,CAAgBwC,GAAhB,CAAoBuE,KAApB,CAAhB;;MACA,IAAItB,OAAO,IAAIA,OAAO,CAACgF,OAAvB,EAAgC;QAC9BC,OAAO,CAACC,GAAR,CAAY,kBAAkB5D,KAAK,EAAnC;MACD;IACF,CALD;IAMA,KAAKmC,eAAL,CAAqB7J,cAArB,GAAsC,iBAAKW,UAAL,MAAe,IAAf,IAAepG,aAAf,GAAe,MAAf,GAAeA,GAAEiH,KAAjB,MAAsB,IAAtB,IAAsBtE,aAAtB,GAAsB,MAAtB,GAAsBA,GAAE8C,cAA9D;IACA,MAAMuL,sBAAsB,GAAG,iDAA/B;;IACA,IAAI,CAAC,KAAKtO,YAAV,EAAwB;MACtB,IAAI,EAAC,iBAAK3C,QAAL,MAAa,IAAb,IAAa+C,aAAb,GAAa,MAAb,GAAaA,GAAEwG,IAAf,MAAmB,IAAnB,IAAmBrG,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEiE,IAAF,EAApB,CAAJ,EAAkC;QAChC,WAAK9H,qBAAL,MAA0B,IAA1B,IAA0BgE,aAA1B,GAA0B,MAA1B,GAA0BA,GAAE6N,IAAF,CAAOD,sBAAP,CAA1B;QACA;MACD;IACF,CALD,MAKO;MACL,KAAK,MAAM3Q,IAAX,IAAmB,KAAKN,QAAxB,EAAkC;QAChC,IAAI,EAAC,UAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEuJ,IAAN,MAAU,IAAV,IAAUjG,aAAV,GAAU,MAAV,GAAUA,GAAE6D,IAAF,EAAX,CAAJ,EAAyB;UACvB,WAAK9H,qBAAL,MAA0B,IAA1B,IAA0BkF,aAA1B,GAA0B,MAA1B,GAA0BA,GAAE2M,IAAF,CAAOD,sBAAP,CAA1B;UACA;QACD;MACF;IACF;;IACD,IAAI,KAAK5K,UAAL,CAAgByK,OAApB,EAA6B;MAC3B3V,qBAAqB,CAAC,KAAKkL,UAAN,CAArB;MACA;IACD;;IACD,IAAI,KAAK8K,QAAT,EAAmB;MACjB,KAAKC,WAAL;IACD;;IACD,IAAI,KAAKC,YAAL,IAAqB,KAAKhL,UAAL,CAAgBa,KAAhB,CAAsBoK,YAAtB,KAAuC,YAAhE,EAA8E;MAC5EC,MAAM,CAAC9M,QAAP,CAAgBC,IAAhB,GAAuB,KAAK2M,YAA5B;IACD,CAFD,MAEO,IAAI,KAAKG,aAAT,EAAwB;MAC7BD,MAAM,CAACE,IAAP,CAAY,KAAKD,aAAjB,EAAgC,QAAhC;IACD;;IAED,IAAI,YAAKE,aAAL,MAAkB,IAAlB,IAAkBlN,aAAlB,GAAkB,MAAlB,GAAkBA,GAAE9D,MAApB,MAA8B,iBAAKgR,aAAL,MAAkB,IAAlB,IAAkB9B,aAAlB,GAAkB,MAAlB,GAAkBA,GAAG,CAAH,CAAlB,MAAuB,IAAvB,IAAuBC,aAAvB,GAAuB,MAAvB,GAAuBA,GAAE1N,QAAF,CAAW,OAAX,CAArD,CAAJ,EAA8E;MAC5E,KAAKwP,mBAAL,CAAyBd,aAAzB;MACA;IACD;;IACD,KAAKe,uBAAL,CAA6Bf,aAA7B;EACD,CAz7BqC,CA07BtC;;;EACAgB,qBAAqB,CAACC,QAAD,EAAc;;;IACjC,OAAO,MACL,CAAC,KAAKC,kBAAL,GAA0BD,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEE,UAApC,GAAiD,KAAKtL,mBAAvD,MAA+E,WAAK1G,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAE+R,UAA9F,CADK,MAEN,IAFM,IAENpP,aAFM,GAEN,MAFM,GAENA,GAAE+M,GAAF,CAAOlL,QAAD,IAAkB;;;MAAC,OAAC;QACzBuN,UAAU,EAAE,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE5O,EAAV,MAAY,IAAZ,IAAYnD,aAAZ,GAAYA,EAAZ,GAAgBwE,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAErB,EADb;QAEzB6O,OAAO,EAAE,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEA,OAAV,MAAiB,IAAjB,IAAiBrP,aAAjB,GAAiBA,EAAjB,GAAqB6B,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwN,OAFf;QAGzBC,WAAW,EAAE,CAAC,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEC,gBAAV,MAA0B,IAA1B,IAA0BpP,aAA1B,GAA0BA,EAA1B,GAA8B0B,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE0N,gBAAzC,MAA+D,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEpL,QAAV,MAAkB,IAAlB,IAAkB7D,aAAlB,GAAkBA,EAAlB,GAAsBuB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEsC,QAA/F,CAHY;QAIzBC,IAAI,EAAE,CAAC,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEoL,YAAV,MAAsB,IAAtB,IAAsB/O,aAAtB,GAAsBA,EAAtB,GAA0BoB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE2N,YAArC,MAAuD,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEpL,IAAV,MAAc,IAAd,IAAc1D,aAAd,GAAcA,EAAd,GAAkBmB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEuC,IAAnF,CAJmB;QAKzBC,KAAK,EAAE,CAAC,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEoL,aAAV,MAAuB,IAAvB,IAAuB9N,aAAvB,GAAuBA,EAAvB,GAA2BE,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE4N,aAAtC,MAAyD,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEpL,KAAV,MAAe,IAAf,IAAezC,aAAf,GAAeA,EAAf,GAAmBC,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwC,KAAtF;MALkB,CAAD;IAMxB,CAND,CAFD;EASD;;EACDqL,aAAa,CAACR,QAAD,EAAgBS,cAAhB,EAAqCC,qBAArC,EAAqEC,iBAArE,EAA8F;;;IACzG,OAAO;MACLrP,EAAE,EAAE,KAAKpD,QAAL,CAAcoD,EADb;MAELsP,YAAY,EAAEZ,QAAQ,CAAC5L,MAAT,IAAmB4L,QAAQ,CAAC7L,UAA5B,IAA0C,KAAKjG,QAAL,CAAc0S,YAFjE;MAGLC,aAAa,EAAE1X,uBAAuB,CAAC6W,QAAQ,CAACvM,aAAV,EAAyB,iBAAK1B,gBAAL,MAAqB,IAArB,IAAqB5D,aAArB,GAAqB,MAArB,GAAqBA,GAAE8D,YAAvB,MAAmC,IAAnC,IAAmCnB,aAAnC,GAAmC,MAAnC,GAAmCA,GAAEoB,aAA9D,CAAvB,IAAuG0I,SAHjH;MAILjH,UAAU,EAAExK,uBAAuB,CAAC6W,QAAQ,CAACrM,UAAV,EAAsB,iBAAK5B,gBAAL,MAAqB,IAArB,IAAqBd,aAArB,GAAqB,MAArB,GAAqBA,GAAEgB,YAAvB,MAAmC,IAAnC,IAAmCb,aAAnC,GAAmC,MAAnC,GAAmCA,GAAEc,aAA3D,CAAvB,IAAoG0I,SAJ3G;MAKLkG,MAAM,EAAEd,QAAQ,CAACc,MAAT,IAAmB,KAAK5S,QAAL,CAAc4S,MALpC;MAML5M,KAAK,EAAE8L,QAAQ,CAAC9L,KAAT,GAAiB8L,QAAQ,CAAC9L,KAA1B,GAAkC,KAAKhG,QAAL,CAAcgG,KANlD;MAOL6M,cAAc,EAAEf,QAAQ,CAAC9L,KAAT,GAAiB,IAAjB,GAAwB,KAPnC;MAQLV,eAAe,EAAEwM,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAExM,eARtB;MASLI,cAAc,EAAEoM,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEpM,cATrB;MAULsK,SAAS,EAAE8B,QAAQ,CAAC9B,SAVf;MAWLG,iBAAiB,EAAE2B,QAAQ,CAAC3B,iBAXvB;MAYL2C,eAAe,EAAEhB,QAAQ,CAACiB,kBAAT,IAA+B,KAAK/S,QAAL,CAAc8S,eAA7C,IAAgEpG,SAZ5E;MAaLsG,aAAa,EAAElB,QAAQ,CAACmB,iBAAT,IAA8B,KAAKjT,QAAL,CAAcgT,aAbtD;MAcLE,SAAS,EAAEV,qBAAqB,GAAG,IAAH,GAAUD,cAdrC;MAeLY,aAAa,EAAErB,QAAQ,CAAC5L,MAAT,KAAoBuM,iBAApB,GAAwCxX,uBAAuB,CAAC6W,QAAQ,CAACvM,aAAV,EAAyB,iBAAK1B,gBAAL,MAAqB,IAArB,IAAqBR,aAArB,GAAqB,MAArB,GAAqBA,GAAEU,YAAvB,MAAmC,IAAnC,IAAmCT,aAAnC,GAAmC,MAAnC,GAAmCA,GAAEU,aAA9D,CAA/D,GAA8I/I,uBAAuB,CAAC6W,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEsB,UAAX,EAAuB,iBAAKvP,gBAAL,MAAqB,IAArB,IAAqBU,aAArB,GAAqB,MAArB,GAAqBA,GAAER,YAAvB,MAAmC,IAAnC,IAAmCS,aAAnC,GAAmC,MAAnC,GAAmCA,GAAER,aAA5D,CAAvB,IAAqG0I,SAf7P;MAgBL2G,cAAc,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEC,UAAV,IAAuBxB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwB,UAAjC,GAA8C,kBAAKtT,QAAL,MAAa,IAAb,IAAa4P,aAAb,GAAa,MAAb,GAAaA,GAAE2D,UAAf,MAAyB,IAAzB,IAAyB1D,aAAzB,GAAyB,MAAzB,GAAyBA,GAAEF,GAAF,CAAO6D,MAAD,IAAiBA,MAAM,CAACC,KAA9B,CAAzB,KAAiE,EAhB1H;MAiBLC,YAAY,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAElO,QAAV,IAAqBsM,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEtM,QAA/B,GAA0C,kBAAKxF,QAAL,MAAa,IAAb,IAAa8P,aAAb,GAAa,MAAb,GAAaA,GAAEJ,QAAf,MAAuB,IAAvB,IAAuBO,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEN,GAAF,CAAOgE,KAAD,IAAgBA,KAAK,CAACpK,IAA5B,CAAvB,KAA4D,EAjB/G;MAkBLqK,UAAU,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE/N,aAAV,IAA0B,CAACiM,QAAQ,CAACjM,aAAV,CAA1B,GAAqD,IAlB5D;MAmBLgO,UAAU,EAAE,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE/N,UAAV,MAAoB,IAApB,IAAoBoK,aAApB,GAAoBA,EAApB,GAAwB,IAnB/B;MAoBL4D,WAAW,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAElO,cAAV,IAA2B,CAACkM,QAAQ,CAAClM,cAAV,CAA3B,GAAuD,IApB/D;MAqBLG,QAAQ,EAAE+L,QAAQ,CAAC/L;IArBd,CAAP;EAuBD;;EACDgO,8BAA8B,CAACC,OAAD,EAAa;IACzC,IAAI,CAACA,OAAO,CAACX,cAAb,EAA6BW,OAAO,CAACX,cAAR,GAAyB,EAAzB;IAC7B,IAAI,CAACW,OAAO,CAACN,YAAb,EAA2BM,OAAO,CAACN,YAAR,GAAuB,EAAvB;;IAE3B,IAAI,KAAKnE,eAAL,CAAqB0E,iBAArB,KAA2C,UAA/C,EAA2D;MACzDD,OAAO,CAACX,cAAR,GAAyB,CAAC,GAAG,IAAIa,GAAJ,CAAQ,CAAC,GAAGF,OAAO,CAACX,cAAZ,EAA4B,KAAK9D,eAAL,CAAqB4E,oBAAjD,CAAR,CAAJ,CAAzB;IACD,CAFD,MAEO,IAAI,KAAK5E,eAAL,CAAqB0E,iBAArB,KAA2C,SAA/C,EAA0D;MAC/DD,OAAO,CAACN,YAAR,GAAuB,CAAC,GAAG,IAAIQ,GAAJ,CAAQ,CAAC,GAAGF,OAAO,CAACN,YAAZ,EAA0B,KAAKnE,eAAL,CAAqB4E,oBAA/C,CAAR,CAAJ,CAAvB;IACD;EACF;;EACDC,WAAW,CAACtC,QAAD,EAAgBkC,OAAhB,EAA4B;;;IACrCA,OAAO,CAAC9T,QAAR,GAAmB4R,QAAQ,CAAC3L,gBAAT,KAA6B,WAAKnG,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEC,QAA5C,KAAwDjG,UAA3E;IACA+Z,OAAO,CAAC5O,eAAR,GAA0B0M,QAAQ,CAAC1L,iBAAT,IAA8B,KAAKpG,QAAL,CAAcoF,eAA5C,IAA+DnL,UAAzF;EACD;;EACDoa,gBAAgB,CAACL,OAAD,EAAa;;;IAC3B,IACE,kBAAKhU,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEM,MAAf,MAAqB,IAArB,IAAqBqC,aAArB,GAAqB,MAArB,GAAqBA,GAAErC,MAAvB,MAAkC,mBAAlC,IACA,kBAAKP,QAAL,MAAa,IAAb,IAAa+C,aAAb,GAAa,MAAb,GAAaA,GAAExC,MAAf,MAAqB,IAArB,IAAqB2C,aAArB,GAAqB,MAArB,GAAqBA,GAAE3C,MAAvB,MAAkC,sBAFpC,EAGE;MACAyT,OAAO,CAACM,gBAAR,GAA2B,IAA3B;MACAN,OAAO,CAACtE,QAAR,GAAmB,iBAAK1P,QAAL,MAAa,IAAb,IAAaqD,aAAb,GAAa,MAAb,GAAaA,GAAEqM,QAAf,MAAuB,IAAvB,IAAuBpM,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEqM,GAAF,CAAOF,OAAD,IAAkB,OAAOA,OAAP,KAAmB,QAAnB,GAA8BA,OAA9B,GAAwCA,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAElG,IAAzE,CAA1C;MACAyK,OAAO,CAACT,UAAR,GAAqB,iBAAKvT,QAAL,MAAa,IAAb,IAAauE,aAAb,GAAa,MAAb,GAAaA,GAAEgP,UAAf,MAAyB,IAAzB,IAAyB/O,aAAzB,GAAyB,MAAzB,GAAyBA,GAAEmL,GAAF,CAAO4E,QAAD,IAAmBA,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEd,KAAnC,CAA9C;IACD;EACF;;EAEDe,kBAAkB,CAACR,OAAD,EAAa;IAC7B,KAAKS,mBAAL,GAA2B,IAA3B;;IACA,KAAKvV,MAAL,CAAYS,QAAZ,CAAqB,IAAIlE,gBAAJ,CAAqBuY,OAArB,EAA8B,KAAKhU,QAAL,CAAcoD,EAA5C,EAAgD,KAAhD,EAAuD,KAAvD,CAArB;;IACA,IAAI,CAAC,KAAKtD,2BAAV,EAAuC;MACrC,KAAKZ,MAAL,CAAYS,QAAZ,CAAqB,IAAI9D,kBAAJ,EAArB;IACD,CAL4B,CAM7B;;;IACA,KAAKqD,MAAL,CAAY4C,MAAZ,CAAmBnG,sBAAnB,EAA2C+F,IAA3C,CACE5H,SAAS,CAAEyJ,SAAD,IAAeA,SAAhB,CADX,EAEExJ,IAAI,CAAC,CAAD,CAFN,EAGEqH,SAHF,CAGamC,SAAD,IAAuB;MACjC,KAAKkR,mBAAL,GAA2B,KAA3B;;MACA,IAAI,CAAClR,SAAL,EAAgB;QACd,IAAI,CAAC,KAAKsN,aAAV,EAAyB;UACvB,KAAK9R,QAAL,CAAcwN,IAAd;QACD;MACF;IACF,CAVD;EAWD;;EAEDmI,gBAAgB,CAAC5C,QAAD,EAAgBS,cAAhB,EAAqCC,qBAArC,EAAqEC,iBAArE,EAAgGkC,SAAhG,EAAiH;IAC/H,KAAKC,sBAAL,GAA8B,IAA9B;;IACA,IAAI,CAAC9C,QAAQ,CAAC7L,UAAd,EAA0B;MACxB,KAAKjH,YAAL,CAAkBuN,IAAlB;MACA;IACD;;IACD,MAAMsI,cAAc,GAAG,KAAK7U,QAAL,CAAcnG,MAAd,CAAsByG,IAAD,IAAc;MAAA;;MAAC,SAAC,UAAI,CAACC,MAAL,MAAW,IAAX,IAAWN,aAAX,GAAW,MAAX,GAAWA,GAAEO,mBAAd;IAAiC,CAArE,CAAvB;IACA,MAAMsU,SAAS,GAAG,KAAKrU,wBAAL,KAAkCoU,cAAlC,GAAmD,KAAK7U,QAA1E;IACA,MAAM+U,WAAW,qBACZ,KAAKC,iBAAL,CAAuBF,SAAvB,EAAkChD,QAAlC,EAA4CS,cAA5C,EAA4DC,qBAA5D,EAAmFC,iBAAnF,EAAsGkC,SAAtG,CADY,CAAjB;;IAGA,KAAKzV,MAAL,CAAYS,QAAZ,CAAqB,IAAIjE,kBAAJ,CAAuBqZ,WAAvB,EAAoC,IAApC,CAArB;;IACA,KAAK7V,MAAL,CAAY4C,MAAZ,CAAmBlG,8BAAnB,EAAmD8F,IAAnD,CACE5H,SAAS,CAAEyJ,SAAD,IAAeA,SAAhB,CADX,EAEExJ,IAAI,CAAC,CAAD,CAFN,EAGEqH,SAHF,CAGamC,SAAD,IAAuB;MACjC,KAAKqR,sBAAL,GAA8BrR,SAA9B;MACA,KAAKvE,YAAL,CAAkBuN,IAAlB;IACD,CAND;EAOD;;EACDyI,iBAAiB,CAAC1U,IAAD,EAAYwR,QAAZ,EAA2BS,cAA3B,EAAgDC,qBAAhD,EAAgFC,iBAAhF,EAA2GkC,SAA3G,EAA4H;;;IAC3I,MAAMM,GAAG,GAAG3U,IAAI,CAACqP,GAAL,CAAUuF,QAAD,IAAmBA,QAAQ,CAAC9R,EAArC,CAAZ;IACA,MAAM+R,YAAY,GAAGF,GAAG,CAACG,IAAJ,EAArB;IACA,MAAMC,UAAU,GAAQ;MACtBC,OAAO,EAAEH,YADa;MAEtBzC,YAAY,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAExM,MAAV,MAAoB4L,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE7L,UAA9B,CAFQ;MAGtB2M,MAAM,EAAEd,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEc,MAHI;MAItB5M,KAAK,EAAE8L,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE9L,KAJK;MAKtB2M,aAAa,EAAEb,QAAQ,CAACvM,aAAT,IAA0BmH,SALnB;MAMtBjH,UAAU,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEA,UAAV,KAAwB,IANd;MAOtB2N,UAAU,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEA,UAAV,KAAwB,IAPd;MAQtBN,eAAe,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEC,kBAAV,KAAgC,IAR3B;MAStBC,aAAa,EAAElB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEmB,iBATH;MAUtB3N,eAAe,EAAEwM,QAAQ,CAACxM,eAVJ;MAWtBI,cAAc,EAAEoM,QAAQ,CAACpM,cAAT,IAA2B,IAXrB;MAYtBsK,SAAS,EAAE8B,QAAQ,CAAC9B,SAZE;MAatBG,iBAAiB,EAAE2B,QAAQ,CAAC3B,iBAbN;MActBgD,aAAa,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEjN,MAAV,MAAqBuM,iBAArB,GAAyCX,QAAQ,CAACvM,aAAlD,GAAkE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE6N,UAAV,KAAwB1G,SAdnF;MAetBwG,SAAS,EAAEV,qBAAqB,GAAG,IAAH,GAAUD,cAfpB;MAgBtBc,cAAc,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEC,UAAV,IAAuBxB,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEwB,UAAjC,GAA8C,EAhBxC;MAiBtBI,YAAY,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAElO,QAAV,IAAqBsM,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEtM,QAA/B,GAA0C,EAjBlC;MAkBtBoO,UAAU,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE/N,aAAV,IAA0B,CAACiM,QAAQ,CAACjM,aAAV,CAA1B,GAAqD,IAlB3C;MAmBtBiO,WAAW,EAAE,SAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAElO,cAAV,IAA2B,CAACkM,QAAQ,CAAClM,cAAV,CAA3B,GAAuD,IAnB9C;MAoBtBiO,UAAU,EAAE,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE/N,UAAV,MAAoB,IAApB,IAAoB7F,aAApB,GAAoBA,EAApB,GAAwB;IApBd,CAAxB;;IAsBA,IAAI0U,SAAS,SAAT,aAAS,WAAT,GAAS,MAAT,YAAS,CAAEjU,MAAf,EAAuB;MACrB2U,UAAU,CAACV,SAAX,GAAuBA,SAAvB;IACD;;IAED,IAAI,CAACU,UAAU,CAAChC,cAAhB,EAAgCgC,UAAU,CAAChC,cAAX,GAA4B,EAA5B;IAChC,IAAI,CAACgC,UAAU,CAAC3B,YAAhB,EAA8B2B,UAAU,CAAC3B,YAAX,GAA0B,EAA1B;;IAE9B,IAAI,KAAKnE,eAAT,EAA0B;MACxB,IAAI,KAAKA,eAAL,CAAqB0E,iBAArB,KAA2C,UAA/C,EAA2D;QACzDoB,UAAU,CAAChC,cAAX,GAA4B,CAAC,GAAG,IAAIa,GAAJ,CAAQ,CAAC,GAAGmB,UAAU,CAAChC,cAAf,EAA+B,KAAK9D,eAAL,CAAqB4E,oBAApD,CAAR,CAAJ,CAA5B;MACD,CAFD,MAEO,IAAI,KAAK5E,eAAL,CAAqB0E,iBAArB,KAA2C,SAA/C,EAA0D;QAC/DoB,UAAU,CAAC3B,YAAX,GAA0B,CAAC,GAAG,IAAIQ,GAAJ,CAAQ,CAAC,GAAGmB,UAAU,CAAC3B,YAAf,EAA6B,KAAKnE,eAAL,CAAqB4E,oBAAlD,CAAR,CAAJ,CAA1B;MACD;IACF;;IACD7T,IAAI,CAAC2B,OAAL,CAAciT,QAAD,IAAkB;;;MAC7B,IAAI,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE3U,MAAV,MAAgB,IAAhB,IAAgBN,aAAhB,GAAgB,MAAhB,GAAgBA,GAAEM,MAAlB,MAA6B,mBAA7B,IAAoD,eAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEA,MAAV,MAAgB,IAAhB,IAAgBqC,aAAhB,GAAgB,MAAhB,GAAgBA,GAAErC,MAAlB,MAA6B,sBAArF,EAA6G;QAC3G8U,UAAU,CAACf,gBAAX,GAA8B,IAA9B,CAD2G,CAE3G;QACA;MACD;;MACDe,UAAU,CAACnV,QAAX,GAAsB4R,QAAQ,CAAC3L,gBAAT,IAA6B,IAAnD;MACAkP,UAAU,CAACjQ,eAAX,GAA6B0M,QAAQ,CAAC1L,iBAAT,IAA8B,IAA3D;IACD,CARD;IASA,MAAMmP,UAAU,GAAGC,WAAW,CAAC,MAAK;;;MAClC,IAAI,KAAKxW,YAAL,CAAkByW,cAAlB,OAAuC,CAA3C,EAA8C;QAC5C,MAAMC,aAAa,GAAG,gBAAU,SAAV,cAAU,WAAV,GAAU,MAAV,aAAU,CAAEJ,OAAZ,MAAmB,IAAnB,IAAmBrV,aAAnB,GAAmB,MAAnB,GAAmBA,GAAES,MAA3C;;QACA,IAAIgV,aAAa,IAAI,EAArB,EAAyB;UACvB,KAAK3W,QAAL,GAAgB,KAAKC,YAAL,CAAkBqC,IAAlB,CACd,KAAKsU,gBADS,EAEdnI,MAAM,CAACoI,MAAP,CACE,EADF,EAEE;YACErU,KAAK,EAAE,oCADT;YAEEC,mBAAmB,EAAE,IAFvB;YAGEqU,QAAQ,EAAE;UAHZ,CAFF,CAFc,CAAhB;QAWD;MACF;;MACDC,aAAa,CAACP,UAAD,CAAb;IACD,CAlB6B,EAkB3B,IAlB2B,CAA9B;IAmBA,OAAOF,UAAP;EACD;;EAEDU,cAAc,CAACjE,QAAD,EAAgBkC,OAAhB,EAA4B;IACxC,KAAK9U,MAAL,CAAY4C,MAAZ,CAAmBnG,sBAAnB,EAA2C+F,IAA3C,CACE5H,SAAS,CAAEyJ,SAAD,IAAeA,SAAhB,CADX,EAEExJ,IAAI,CAAC,CAAD,CAFN,EAGEqH,SAHF,CAGamC,SAAD,IAAuB;;;MACjC,IAAIyS,gBAAgB,GAAGpP,IAAI,CAACe,KAAL,CAAWC,YAAY,CAACC,OAAb,CAAqB,kBAArB,CAAX,CAAvB;MACAmO,gBAAgB,GAAG,KAAKtT,gBAAxB;MACA,MAAMU,EAAE,GAAG0O,QAAQ,CAAC5L,MAAT,IAAmB4L,QAAQ,CAAC7L,UAA5B,IAA0C,KAAKjG,QAAL,CAAc0S,YAAnE;MACA,MAAMnS,MAAM,GAAGyV,gBAAgB,CAACC,IAAjB,CAAuBC,IAAD,IAAeA,IAAI,CAAC9S,EAAL,KAAYA,EAAZ,IAAkB8S,IAAI,CAACpJ,UAAL,CAAgBzM,IAAhB,CAAsB8V,KAAD,IAAgBA,KAAK,CAAC/S,EAAN,KAAaA,EAAlD,CAAvD,CAAf;MACA,KAAKpD,QAAL,GAAawN,gCACR,KAAKxN,QADG,GACK;QAChBO,MAAM,kCACD,KAAKP,QAAL,CAAcO,MADb,GACmB;UACvB6B,WAAW,EAAE7B,MAAM,CAAC6B,WAAP,IAAsB,KAAKpC,QAAL,CAAcO,MAAd,CAAqB6B;QADjC,CADnB,CADU;QAKhBwQ,MAAM,EAAEd,QAAQ,CAACc,MALD;QAMhBlD,QAAQ,EAAEoC,QAAQ,CAAC4B,YANH;QAOhBH,UAAU,EAAEzB,QAAQ,CAACuB,cAPL;QAQhBrN,KAAK,EAAE8L,QAAQ,CAAC9L,KARA;QAShB2M,aAAa,EAAEb,QAAQ,CAACvM,aATR;QAUhBrF,QAAQ,EAAE4R,QAAQ,CAAC3L,gBAAT,IAA6B,KAAKnG,QAAL,CAAcE,QAA3C,IAAuDjG,UAVjD;QAWhBmL,eAAe,EAAE0M,QAAQ,CAAC1L,iBAAT,IAA8B,KAAKpG,QAAL,CAAcoF,eAA5C,IAA+DnL;MAXhE,CADL,CAAb,CALiC,CAmBjC;;MACA,IAAImc,YAAY,GAAG,KAAnB;;MACA,KAAK,IAAIlQ,MAAT,IAAmB,KAAK6G,cAAxB,EAAwC;QACtC,IAAI,wBAAK1G,UAAL,MAAe,IAAf,IAAepG,aAAf,GAAe,MAAf,GAAeA,GAAEwI,QAAjB,MAAyB,IAAzB,IAAyB7F,aAAzB,GAAyB,MAAzB,GAAyBA,GAAG,QAAH,CAAzB,MAAqC,IAArC,IAAqCG,aAArC,GAAqC,MAArC,GAAqCA,GAAEmE,KAAvC,OAAiDhB,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE9C,EAAzD,MAA+D,iBAAKpD,QAAL,MAAa,IAAb,IAAakD,aAAb,GAAa,MAAb,GAAaA,GAAE3C,MAAf,MAAqB,IAArB,IAAqB8C,aAArB,GAAqB,MAArB,GAAqBA,GAAEgT,SAAtF,CAAJ,EAAqG;UACnG,KAAKrW,QAAL,CAAcO,MAAd,CAAqB8V,SAArB,GAA8B7I,gCACzB,KAAKxN,QAAL,CAAcO,MAAd,CAAqB8V,SADI,GACK;YACjCjU,WAAW,EAAE8D,MAAM,CAAC9D;UADa,CADL,CAA9B;UAIAgU,YAAY,GAAG,IAAf;UACA;QACD;MACF;;MACD,IAAI,CAACA,YAAL,EAAmB;QACjB,KAAKpW,QAAL,CAAcO,MAAd,CAAqB8V,SAArB,GAA8B7I,gCACzB,KAAKxN,QAAL,CAAcO,MAAd,CAAqB8V,SADI,GACK;UACjCjU,WAAW,EAAE;QADoB,CADL,CAA9B;MAID;IACF,CAxCD;EAyCD;;EAEDkU,qBAAqB;IACnB,KAAKpX,MAAL,CAAYS,QAAZ,CAAqB,IAAInE,kBAAJ,CAAuB,CAAvB,EAA0B,EAA1B,EAA8B,MAA9B,CAArB;;IAEA,KAAKuD,QAAL,GAAgB,KAAKC,YAAL,CAAkBqC,IAAlB,CAAuBhE,6BAAvB,EAAsD;MACpEkE,KAAK,EAAE,uDAD6D;MAEpE6K,YAAY,EAAE;QACZmK,UAAU,EAAE;MADA;IAFsD,CAAtD,CAAhB;EAMD;;EAGDC,eAAe;IACb,KAAK/F,gBAAL;IACA,KAAKgG,kBAAL,GAFa,CAIb;IACA;IACA;IACA;IACA;EACD;;EAEDC,oBAAoB,CAACxQ,MAAD,EAAY;;;IAC9B,IAAI4I,YAAY,GAAG,OAAC,IAAG5I,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE4I,YAAX,CAAD,OAAyB,IAAzB,IAAyB7O,aAAzB,GAAyB,MAAzB,GAAyBA,GAAEqC,IAAF,CAC1C,CAACC,CAAD,EAASC,CAAT,KAAmB;MAAA;;MAAC,eAAC,SAAD,KAAC,WAAD,GAAC,MAAD,IAAC,CAAE4K,KAAH,MAAQ,IAAR,IAAQnN,aAAR,GAAQ,MAAR,GAAQA,GAAEgD,SAAV,KAAsB,OAAC,SAAD,KAAC,WAAD,GAAC,MAAD,IAAC,CAAEmK,KAAH,MAAQ,IAAR,IAAQxK,aAAR,GAAQ,MAAR,GAAQA,GAAEK,SAAhC;IAAyC,CADnB,CAA5C;IAGAiD,MAAM,mCACDA,MADC,GACK;MACT4I;IADS,CADL,CAAN;IAIA,KAAK6B,cAAL,GAAsBzK,MAAtB;EACD;;EAEDmG,UAAU;IACR,KAAKsK,WAAL,GAAmB,KAAnB;IACA,KAAK5X,QAAL,CAAcwN,IAAd;IACA,KAAKlG,UAAL,CAAgBuQ,KAAhB;IACA,KAAK/W,UAAL,GAAkB,KAAlB;IACA,KAAK8Q,cAAL,GAAsB,IAAtB;EACD;;EAEDkG,oBAAoB;IAClB,IAAI,KAAKxQ,UAAL,CAAgByK,OAApB,EAA6B;MAC3B3V,qBAAqB,CAAC,KAAKkL,UAAN,CAArB;MACA;IACD;;IACD,IAAI,KAAKA,UAAL,CAAgBa,KAAhB,CAAsBlB,KAA1B,EAAiC;MAC/B,KAAKuJ,eAAL,CAAqBvJ,KAArB,GAA6B,KAAKK,UAAL,CAAgBa,KAAhB,CAAsBlB,KAAnD;IACD;;IACD,KAAK4G,oBAAL,GAA4B,KAA5B;IACA,KAAKgE,YAAL,CAAkB,IAAlB;;IACA,KAAK1R,MAAL,CACG4C,MADH,CACU7E,iBADV,EAEGyE,IAFH,CAEQ3H,IAAI,CAAC,CAAD,CAFZ,EAGGqH,SAHH,CAGcsC,IAAD,IAAc;;;MACvB,KAAK8L,WAAL,GAAmB9L,IAAnB;MACA,IAAI0I,YAAY,GAAQ;QACtBmD,eAAe,EAAE,KAAKA,eADA;QAEtBvP,QAAQ,EAAE,KAAKA,QAFO;QAGtBwP,WAAW,EAAE,KAAKA,WAHI;QAItBsH,YAAY,EAAE,WAAKzQ,UAAL,CAAgBwC,GAAhB,CAAoB,YAApB,OAAiC,IAAjC,IAAiC5I,aAAjC,GAAiC,MAAjC,GAAiCA,GAAEiH;MAJ3B,CAAxB;MAMA,KAAKnI,QAAL,GAAgB,KAAKC,YAAL,CAAkBqC,IAAlB,CAAuB/D,oBAAvB,EAA6C;QAC3DiE,KAAK,EAAE,sCADoD;QAE3D6K;MAF2D,CAA7C,CAAhB;MAIA,KAAKrN,QAAL,CAAcgY,MAAd,CAAqB3V,SAArB,CAA+B,MAAK;QAClC,KAAKwL,oBAAL,GAA4B,IAA5B;MACD,CAFD;IAGD,CAlBH;EAmBD;;EAEDoK,wBAAwB,CAAC9P,KAAD,EAAW;;;IACjC,IAAIA,KAAK,KAAK,UAAd,EAA0B;MACxB,KAAK+P,mBAAL,GAA2B,KAA3B;MACA,WAAK5Q,UAAL,CAAgBoC,QAAhB,CAAyB,cAAzB,OAAwC,IAAxC,IAAwCxI,aAAxC,GAAwC,MAAxC,GAAwCA,GAAEoP,QAAF,CAAW,IAAX,CAAxC;MACA,WAAKhJ,UAAL,CAAgBoC,QAAhB,CAAyB,eAAzB,OAAyC,IAAzC,IAAyC7F,aAAzC,GAAyC,MAAzC,GAAyCA,GAAEyM,QAAF,CAAW,IAAX,CAAzC;MACAnU,gBAAgB,CAACZ,cAAD,EAAiB,KAAK+L,UAAtB,EAAkC,gBAAlC,EAAoD,CAClE/M,UAAU,CAACqP,QADuD,CAApD,CAAhB;MAGAzN,gBAAgB,CAACb,gBAAD,EAAmB,KAAKgM,UAAxB,EAAoC,eAApC,CAAhB;MACAnL,gBAAgB,CAACb,gBAAD,EAAmB,KAAKgM,UAAxB,EAAoC,YAApC,CAAhB;IACD,CATD,MASO;MACL,KAAK4Q,mBAAL,GAA2B,IAA3B;MACA,WAAK5Q,UAAL,CAAgBoC,QAAhB,CAAyB,gBAAzB,OAA0C,IAA1C,IAA0C1F,aAA1C,GAA0C,MAA1C,GAA0CA,GAAEsM,QAAF,CAAW,IAAX,CAA1C;;MACA,IAAI,KAAKlH,kBAAT,EAA6B;QAC3BjN,gBAAgB,CAACZ,cAAD,EAAiB,KAAK+L,UAAtB,EAAkC,eAAlC,EAAmD,CACjE/M,UAAU,CAACqP,QADsD,CAAnD,CAAhB;QAGAzN,gBAAgB,CAACZ,cAAD,EAAiB,KAAK+L,UAAtB,EAAkC,YAAlC,EAAgD,CAC9D/M,UAAU,CAACqP,QADmD,CAAhD,CAAhB;MAGD;;MACDzN,gBAAgB,CAACb,gBAAD,EAAmB,KAAKgM,UAAxB,EAAoC,gBAApC,CAAhB;IACD;EACF;;EAED6Q,qBAAqB,CAAChQ,KAAD,EAAc;IACjC,KAAKqI,eAAL,CAAqB7J,cAArB,GAAsCwB,KAAtC;EACD;;EAEDiQ,gBAAgB,CAAC/T,EAAD,EAAW;IACzBlI,gBAAgB,CAACb,gBAAD,EAAmB,KAAKgM,UAAxB,EAAoC,eAApC,CAAhB;IACAnL,gBAAgB,CAACb,gBAAD,EAAmB,KAAKgM,UAAxB,EAAoC,YAApC,CAAhB;IACA,KAAK+Q,2BAAL,GAAmC,UAAnC;IACA,KAAKH,mBAAL,GAA2B,KAA3B;;IACA,IAAI7T,EAAJ,EAAQ;MACN,KAAKlE,MAAL,CAAYS,QAAZ,CAAqB,IAAItD,iBAAJ,CAAsB+G,EAAtB,CAArB;IACD;;IACD,KAAKlE,MAAL,CACG4C,MADH,CACUvF,sBADV,EAEGmF,IAFH,CAEQ1H,SAAS,CAAC,KAAK+H,OAAN,CAFjB,EAGGX,SAHH,CAGcsC,IAAD,IAAc;;;MACvB,IAAIA,IAAJ,EAAU;QACR,KAAK6L,eAAL,CAAqB4E,oBAArB,GAA4CzQ,IAAI,CAAC+P,KAAjD;QACA,KAAKlE,eAAL,CAAqB8H,WAArB,GAAmC,UAAI,CAACC,YAAL,MAAiB,IAAjB,IAAiBrX,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEsJ,IAAtD;QACA,KAAKgG,eAAL,CAAqBgI,YAArB,GAAoC,UAAI,CAACC,SAAL,MAAc,IAAd,IAAc5U,aAAd,GAAc,MAAd,GAAcA,GAAE6U,YAApD;QACA,KAAKlI,eAAL,CAAqBmI,UAArB,GAAkC,UAAI,CAACF,SAAL,MAAc,IAAd,IAAczU,aAAd,GAAc,MAAd,GAAcA,GAAE4U,UAAlD;QACA,KAAKpI,eAAL,CAAqBqI,iBAArB,GACE,UAAI,CAACC,YAAL,MAAiB,IAAjB,IAAiB3U,aAAjB,GAAiB,MAAjB,GAAiBA,GAAE0U,iBADrB;QAEA,KAAKrI,eAAL,CAAqBuI,eAArB,GAAuC,UAAI,CAACD,YAAL,MAAiB,IAAjB,IAAiBxU,aAAjB,GAAiB,MAAjB,GAAiBA,GAAE0U,SAA1D;QACA,KAAKxI,eAAL,CAAqB0E,iBAArB,GACE,KAAKmD,2BADP;MAED;IACF,CAfH;EAgBD;;EAEDY,eAAe,CAAC5U,EAAD,EAAW;;;IACxB,IAAIA,EAAJ,EAAQ;MACN,KAAKlE,MAAL,CAAYS,QAAZ,CAAqB,IAAI3D,gBAAJ,CAAqBoH,EAArB,CAArB;;MACA,IAAI,KAAK+E,kBAAT,EAA6B;QAC3BjN,gBAAgB,CAACZ,cAAD,EAAiB,KAAK+L,UAAtB,EAAkC,YAAlC,EAAgD,CAC9D/M,UAAU,CAACqP,QADmD,CAAhD,CAAhB;QAGAzN,gBAAgB,CAACZ,cAAD,EAAiB,KAAK+L,UAAtB,EAAkC,eAAlC,EAAmD,CACjE/M,UAAU,CAACqP,QADsD,CAAnD,CAAhB;MAGD,CAPD,MAOO;QACLzN,gBAAgB,CAACb,gBAAD,EAAmB,KAAKgM,UAAxB,EAAoC,YAApC,CAAhB;QACAnL,gBAAgB,CAACb,gBAAD,EAAmB,KAAKgM,UAAxB,EAAoC,eAApC,CAAhB;MACD;IACF;;IACDnL,gBAAgB,CAACb,gBAAD,EAAmB,KAAKgM,UAAxB,EAAoC,gBAApC,CAAhB;IACA,KAAK+Q,2BAAL,GAAmC,SAAnC;IACA,KAAKH,mBAAL,GAA2B,IAA3B;IACA,WAAK5Q,UAAL,CAAgBwC,GAAhB,CAAoB,YAApB,OAAiC,IAAjC,IAAiC5I,aAAjC,GAAiC,MAAjC,GAAiCA,GAAE2W,KAAF,EAAjC;IACAld,aAAa,CAAC,CACZ,KAAKwF,MAAL,CAAY4C,MAAZ,CAAmB7F,uBAAnB,CADY,EAEZ,KAAKiD,MAAL,CAAY4C,MAAZ,CAAmB1F,sBAAnB,CAFY,CAAD,CAAb,CAIGsF,IAJH,CAIQ1H,SAAS,CAAC,KAAK+H,OAAN,CAJjB,EAKGX,SALH,CAKa,CAAC,CAACmC,SAAD,EAAY0U,WAAZ,CAAD,KAA6C;;;MACtD,KAAKC,qBAAL,GAA6B3U,SAA7B;;MACA,IAAI0U,WAAJ,EAAiB;QACf,KAAKE,QAAL,GAAgBF,WAAW,CAACG,SAA5B;QACA,KAAK7I,eAAL,GAAuB;UACrB4E,oBAAoB,EAAE8D,WAAW,CAAC1O,IADb;UAErB8N,WAAW,EAAE,iBAAW,CAACgB,aAAZ,MAAyB,IAAzB,IAAyBpY,aAAzB,GAAyB,MAAzB,GAAyBA,GAAEsJ,IAFnB;UAGrBuO,eAAe,EAAE,iBAAW,CAACD,YAAZ,MAAwB,IAAxB,IAAwBjV,aAAxB,GAAwB,MAAxB,GAAwBA,GAAEmV,SAHtB;UAIrBH,iBAAiB,EAAE,iBAAW,CAACC,YAAZ,MAAwB,IAAxB,IAAwB9U,aAAxB,GAAwB,MAAxB,GAAwBA,GAAE6U,iBAJxB;UAKrB3D,iBAAiB,EAAE,KAAKmD,2BALH;UAMrBkB,cAAc,EAAEL;QANK,CAAvB;MAQD;IACF,CAlBH;EAmBD;;EAEDM,mBAAmB,CAAC5W,KAAD,EAAW;;;IAC5B,WAAKwW,QAAL,MAAa,IAAb,IAAalY,aAAb,GAAa,MAAb,GAAaA,GAAE0P,GAAF,CAAOjM,IAAD,IAAc;MAC/B,IAAIA,IAAI,CAACN,EAAL,MAAYzB,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEyB,EAAnB,CAAJ,EAA2B;QACzB,KAAKmM,eAAL,CAAqBmI,UAArB,GAAkChU,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEiU,UAAxC;QACA,KAAKpI,eAAL,CAAqBgI,YAArB,GAAoC7T,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE8U,gBAA1C;QACA,KAAKjJ,eAAL,CAAqBkJ,QAArB,GAAgC/U,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE6F,IAAtC;MACD;IACF,CANY,CAAb;EAOD;;EAEDlF,yBAAyB,CAACjB,EAAD,EAAW;;;IAClCA,EAAE,IAAI,KAAKlE,MAAL,CAAYS,QAAZ,CAAqB,IAAIrE,gBAAJ,CAAqB8H,EAArB,CAArB,CAAN;IACA,WAAKlE,MAAL,CAAY4C,MAAZ,CAAmBvG,cAAnB,OAAkC,IAAlC,IAAkC0E,aAAlC,GAAkC,MAAlC,GAAkCA,GAAEmB,SAAF,CAAasX,cAAD,IAAwB;;;MACpE,IAAI,CAACA,cAAL,EAAqB;MACrB,MAAM;QACJhJ,QADI;QAEJ6D,UAFI;QAGJoF,QAHI;QAIJrT,eAJI;QAKJI,cALI;QAMJM,KANI;QAOJD;MAPI,IAQF2S,cARJ;MASA,MAAME,WAAW,GAAGpR,KAAK,CAACC,OAAN,CAAciI,QAAd,KAA2BA,QAAQ,CAAChP,MAAT,GAAkB,CAAjE;MACA,MAAMmY,aAAa,GAAGrR,KAAK,CAACC,OAAN,CAAc8L,UAAd,KAA6BA,UAAU,CAAC7S,MAAX,GAAoB,CAAvE;MACA,IAAIkY,WAAJ,EAAiB,KAAKZ,eAAL,CAAqBtI,QAAQ,CAAC,CAAD,CAAR,CAAYtM,EAAjC;MACjB,IAAIyV,aAAJ,EAAmB,KAAK1B,gBAAL,CAAsB5D,UAAU,CAAC,CAAD,CAAV,CAAcnQ,EAApC;MACnB,MAAM0V,mBAAmB,GAAGpT,cAAc,GAAG,CAAjB,GAAqBA,cAArB,GAAsC,IAAlE;MACA,KAAK6J,eAAL,CAAqB7J,cAArB,GAAsCoT,mBAAtC;MACA,KAAKzS,UAAL,CAAgBkB,UAAhB,CAA2B;QACzBrB,MAAM,EAAE,uBAAKlG,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEM,MAAf,MAAqB,IAArB,IAAqBqC,aAArB,GAAqB,MAArB,GAAqBA,GAAEyT,SAAvB,MAAgC,IAAhC,IAAgCtT,aAAhC,GAAgC,MAAhC,GAAgCA,GAAEK,EADjB;QAEzB6C,UAAU,EAAE,iBAAKjG,QAAL,MAAa,IAAb,IAAakD,aAAb,GAAa,MAAb,GAAaA,GAAE3C,MAAf,MAAqB,IAArB,IAAqB8C,aAArB,GAAqB,MAArB,GAAqBA,GAAED,EAFV;QAGzBqC,UAAU,EAAEzK,qBAAqB,CAAC0d,cAAc,CAACjT,UAAhB,EAA4B,iBAAK5B,gBAAL,MAAqB,IAArB,IAAqBP,aAArB,GAAqB,MAArB,GAAqBA,GAAES,YAAvB,MAAmC,IAAnC,IAAmCQ,aAAnC,GAAmC,MAAnC,GAAmCA,GAAEP,aAAjE,CAHR;QAIzBsB,eAJyB;QAKzBI,cAAc,EAAEoT,mBALS;QAMzBlT,cAAc,EAAEiT,aAAa,GAAG,gBAAU,CAAC,CAAD,CAAV,MAAa,IAAb,IAAarU,aAAb,GAAa,MAAb,GAAaA,GAAEpB,EAAlB,GAAuB,IAN3B;QAOzByC,aAAa,EAAE+S,WAAW,GAAG,cAAQ,CAAC,CAAD,CAAR,MAAW,IAAX,IAAWhJ,aAAX,GAAW,MAAX,GAAWA,GAAExM,EAAhB,GAAqB,IAPtB;QAQzB0C,UAAU,EAAE6S,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEvV,EARG;QASzB4C,KATyB;QAUzBD,QAVyB;QAWzBI,gBAAgB,EAAE,WAAKnG,QAAL,MAAa,IAAb,IAAa6P,aAAb,GAAa,MAAb,GAAaA,GAAE3P,QAXR;QAYzBkG,iBAAiB,EAAE,YAAKpG,QAAL,MAAa,IAAb,IAAa8P,aAAb,GAAa,MAAb,GAAaA,GAAE1K,eAAf,MAAmCnL,UAAnC,GAAgD,WAAK+F,QAAL,MAAa,IAAb,IAAaiQ,aAAb,GAAa,MAAb,GAAaA,GAAE7K,eAA/D,GAAiF;MAZ3E,CAA3B;MAcA,KAAKuL,cAAL,GAAsB,uBAAK3Q,QAAL,MAAa,IAAb,IAAakQ,aAAb,GAAa,MAAb,GAAaA,GAAE3P,MAAf,MAAqB,IAArB,IAAqB6P,aAArB,GAAqB,MAArB,GAAqBA,GAAEiG,SAAvB,MAAgC,IAAhC,IAAgChG,aAAhC,GAAgC,MAAhC,GAAgCA,GAAEjO,WAAxD;MACA,MAAMuD,eAAe,GAAGiT,WAAW,GAAG,SAAH,GAAeC,aAAa,GAAG,UAAH,GAAgB,UAA/E;MACA,iBAAKxS,UAAL,MAAe,IAAf,IAAeiK,aAAf,GAAe,MAAf,GAAeA,GAAEzH,GAAF,CAAM,iBAAN,CAAf,MAAuC,IAAvC,IAAuC0H,aAAvC,GAAuC,MAAvC,GAAuCA,GAAElB,QAAF,CAAW1J,eAAX,CAAvC;;MAEA,IAAIA,eAAe,KAAK,SAAxB,EAAmC;QACjCzK,gBAAgB,CAACb,gBAAD,EAAmB,KAAKgM,UAAxB,EAAoC,gBAApC,CAAhB;MACD,CAFD,MAEO,IAAIV,eAAe,KAAK,UAAxB,EAAoC;QACzCzK,gBAAgB,CAACb,gBAAD,EAAmB,KAAKgM,UAAxB,EAAoC,eAApC,CAAhB;QACAnL,gBAAgB,CAACb,gBAAD,EAAmB,KAAKgM,UAAxB,EAAoC,YAApC,CAAhB;MACD;;MAED,IAAIsS,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEvV,EAAd,EAAkB,KAAKmV,mBAAL,CAAyBI,QAAQ,CAACvV,EAAlC;MAClB,KAAKkM,yBAAL,GAAiC3U,YAAY,CAAC+K,cAAD,EAAiBK,QAAQ,IAAI,KAAKmC,eAAlC,CAA7C;IACD,CA5CiC,CAAlC;EA6CD;;EAEDrF,iCAAiC;IAC/B,IAAI,CAAC2E,KAAK,CAACC,OAAN,CAAc,KAAKzH,QAAnB,CAAL,EAAmC;MACjC,OAAO,KAAP;IACD;;IACD,IAAI,KAAKA,QAAL,CAAcU,MAAd,KAAyB,CAA7B,EAAgC;MAC9B,OAAO,KAAP;IACD;;IACD,MAAMC,mBAAmB,GAAG,KAAKX,QAAL,CAAcY,KAAd,CACzBN,IAAD,IAAc;;;MACZ,kBAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEC,MAAN,MAAY,IAAZ,IAAYN,aAAZ,GAAY,MAAZ,GAAYA,GAAEO,mBAAd,MACA,UAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAED,MAAN,MAAY,IAAZ,IAAYqC,aAAZ,GAAY,MAAZ,GAAYA,GAAE/B,mBADd;IACiC,CAHT,CAA5B;IAKA,OAAOF,mBAAP;EACD;;EAEDoY,oBAAoB,CAACpX,KAAD,EAAW;IAC7B,MAAMqX,UAAU,GAAGrX,KAAK,CAACsX,MAAN,CAAa/R,KAAb,CAAmBC,IAAnB,EAAnB;;IACA,IAAI6R,UAAJ,EAAgB;MACd,KAAK3S,UAAL,CAAgBoC,QAAhB,CAAyB,iBAAzB,EAA4C4G,QAA5C,CAAqD2J,UAArD;MACA,KAAK3S,UAAL,CAAgBoC,QAAhB,CAAyB,iBAAzB,EAA4CiI,SAA5C,CAAsD,IAAtD;IACD,CAHD,MAGO;MACL,KAAKrK,UAAL,CAAgBoC,QAAhB,CAAyB,iBAAzB,EAA4CiI,SAA5C,CAAsD;QAAEwI,UAAU,EAAE;MAAd,CAAtD;IACD;EACF;;EAEDC,eAAe,CAACC,CAAD,EAAO;IACpB,KAAKC,YAAL,GAAoBD,CAAC,CAAC,CAAD,CAArB;EACD;;EAEDhI,WAAW;IACT,IAAI,CAAC,KAAK7K,aAAL,CAAmB+S,KAApB,IAA6B,CAAC,KAAKD,YAAvC,EAAqD;MACnDle,qBAAqB,CAAC,KAAKoL,aAAN,CAArB;MACA;IACD;;IACD,KAAKmL,aAAL,CAAmBrP,IAAnB,CAAwB,KAAKgX,YAA7B;IACA,KAAKE,iBAAL,CAAuBlX,IAAvB,CACE,KAAKkE,aAAL,CAAmBW,KAAnB,CAAyBV,QAAzB,IAAqC,KAAKgT,gBAD5C;IAGA,KAAKrI,QAAL,GAAgB,KAAhB;IACA,KAAK5K,aAAL,CAAmBqQ,KAAnB;IACA,KAAKyC,YAAL,GAAoB,IAApB;EACD;;EAEDI,qBAAqB,CAACC,QAAD,EAAiB;IACpC,KAAKhI,aAAL,GAAqB,KAAKA,aAAL,CAAmB7X,MAAnB,CACnB,CAAC8f,CAAD,EAAIC,KAAJ,KAAcA,KAAK,KAAKF,QADL,CAArB;IAGA,KAAKH,iBAAL,GAAyB,KAAKA,iBAAL,CAAuB1f,MAAvB,CACvB,CAAC8f,CAAD,EAAIC,KAAJ,KAAcA,KAAK,KAAKF,QADD,CAAzB;EAGD;;EAEDjD,kBAAkB;IAChB,KAAK/E,aAAL,GAAqB,EAArB;IACA,KAAK6H,iBAAL,GAAyB,EAAzB;IACA,KAAKpI,QAAL,GAAgB,KAAhB;IACA,KAAKkI,YAAL,GAAoB,EAApB;IACA,KAAKG,gBAAL,GAAwB,IAAxB;IACA,KAAKjT,aAAL,CAAmBqQ,KAAnB;EACD;;EAEDjF,mBAAmB,CAACd,gBAAyB,KAA1B,EAA+B;IAChD,KAAKrR,eAAL,CACGqa,iBADH,CACqB,KAAKnI,aAD1B,EACyCnX,aAAa,CAACuf,YADvD,EAEGpY,IAFH,CAEQ1H,SAAS,CAAC,KAAK+H,OAAN,CAFjB,EAGGX,SAHH,CAGc2Y,QAAD,IAAkB;;;MAC3B,IAAIA,QAAQ,CAACrW,IAAT,CAAchD,MAAlB,EAA0B;QACxB,IAAIiU,SAAS,GAAQ,EAArB;QACA,WAAK4E,iBAAL,MAAsB,IAAtB,IAAsBtZ,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEgC,OAAF,CAAU,CAACsH,IAAD,EAAeqQ,KAAf,KAAgC;UAC9DjF,SAAS,CAACtS,IAAV,CAAe;YACb2X,YAAY,EAAEzQ,IADD;YAEb0Q,QAAQ,EAAEF,QAAQ,CAACrW,IAAT,CAAckW,KAAd,EAAqBM,QAArB;UAFG,CAAf;QAID,CALqB,CAAtB;QAMA,KAAKtI,uBAAL,CAA6Bf,aAA7B,EAA4C8D,SAA5C;MACD;IACF,CAdH;EAeD;;EAED/C,uBAAuB,CAACf,gBAAyB,KAA1B,EAAiC8D,SAAjC,EAAkD;IACvE,MAAM7C,QAAQ,GAAG,KAAKzL,UAAL,CAAgBa,KAAjC;IACA,IAAIuL,iBAAiB,GAAG,EAAxB;IACA,KAAK3L,eAAL;IACA,MAAMyL,cAAc,GAAG,KAAKV,qBAAL,CAA2BC,QAA3B,CAAvB;IACA,MAAMU,qBAAqB,GAAG,EAACD,cAAc,SAAd,kBAAc,WAAd,GAAc,MAAd,iBAAc,CAAE7R,MAAjB,CAA9B;IACA,MAAMsT,OAAO,GAAG,KAAK1B,aAAL,CAAmBR,QAAnB,EAA6BS,cAA7B,EAA6CC,qBAA7C,EAAoEC,iBAApE,CAAhB;;IACA,IAAIkC,SAAS,SAAT,aAAS,WAAT,GAAS,MAAT,YAAS,CAAEjU,MAAf,EAAuB;MACrBsT,OAAO,CAACW,SAAR,GAAoBA,SAApB;IACD;;IACD,IAAI,KAAKpF,eAAT,EAA0B;MACxB,KAAKwE,8BAAL,CAAoCC,OAApC;IACD;;IACD,KAAKI,WAAL,CAAiBtC,QAAjB,EAA2BkC,OAA3B;IACA,KAAKK,gBAAL,CAAsBL,OAAtB;;IACA,IAAI,CAAC,KAAKrR,YAAV,EAAwB;MACtB,KAAK6R,kBAAL,CAAwBR,OAAxB;IACD,CAFD,MAEO;MACL,KAAKU,gBAAL,CAAsB5C,QAAtB,EAAgCS,cAAhC,EAAgDC,qBAAhD,EAAuEC,iBAAvE,EAA0FkC,SAA1F;IACD;;IACD,KAAKoB,cAAL,CAAoBjE,QAApB,EAA8BkC,OAA9B;;IACA,IAAI,CAAC,KAAKrR,YAAV,EAAwB;MACtB,KAAK6T,eAAL;IACD;;IACD,IAAI,CAAC3F,aAAL,EAAoB;MAClB,IAAI,CAAC,KAAKlO,YAAV,EAAwB;QACtB,KAAKzD,MAAL,CAAY4C,MAAZ,CAAmBnG,sBAAnB,EAA2C+F,IAA3C,CACE5H,SAAS,CAAEyJ,SAAD,IAAeA,SAAhB,CADX,EAEExJ,IAAI,CAAC,CAAD,CAFN,EAGEqH,SAHF,CAGamC,SAAD,IAAuB;UACjC,IAAI,CAACA,SAAL,EAAgB;YACd,KAAKxE,QAAL,CAAcwN,IAAd;UACD;QACF,CAPD;MAQD,CATD,MASO;QACL,KAAKxN,QAAL,CAAcwN,IAAd;MACD;;MACD;IACD;;IACD,KAAKnN,qBAAL,CAA2B+a,QAA3B;EACD;;EAEDC,WAAW;IACT,KAAKrY,OAAL,CAAasY,IAAb;IACA,KAAKtY,OAAL,CAAauY,QAAb;IACA,KAAKpZ,qBAAL,CAA2BqZ,WAA3B;EACD;;AAl/CqC;;;mBAA3B1b,6BAA2BtB;AAAA;;;QAA3BsB;EAA2B2b;EAAAC;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MC1HxCld;MAEEA;MAsCAA;MAEAA,+BAAwB,CAAxB,EAAwB,MAAxB,EAAwB,CAAxB,EAAwB,CAAxB,EAAwB,KAAxB;MAIMA;MAwDAA;MAoBAA;MA6HAA;MA8JAA;MAyCAA;MAOFA;MAINA;MAoBAA;MAOAA;MAIAA;MASAA;;;;;;;;MAhfEA;MACMA;MAAAA;MAsC6BA;MAAAA;MAG3BA;MAAAA,2CAAwB,SAAxB,EAAwBA,gGAAxB;MAG6BA;MAAAA,6EAAoD,UAApD,EAAoDmd,IAApD;MAwDhBnd;MAAAA;MAkJZA;MAAAA,iNAC8G,UAD9G,EAC8God,GAD9G;MA6JYpd;MAAAA;MAyCAA;MAAAA;MAYpBA;MAAAA,sGAA2E,UAA3E,EAA2Emd,IAA3E", "names": ["ChangeDetectorRef", "ElementRef", "EventEmitter", "SimpleChanges", "TemplateRef", "Validators", "NavigationEnd", "moment", "BehaviorSubject", "combineLatest", "debounceTime", "distinctUntilChanged", "filter", "<PERSON><PERSON><PERSON><PERSON>", "take", "takeUntil", "EMPTY_GUID", "LEAD_STATUS_REASONS", "UPDATE_STATUS", "UPDATE_STATUS_PAST_TENSE", "VALIDATION_CLEAR", "VALIDATION_SET", "FolderNamesS3", "LeadSource", "assignToSort", "changeCalendar", "formatBudget", "getTimeZoneDate", "only<PERSON><PERSON>bers", "onPickerOpened", "patchFormControlValue", "patchTimeZoneWithTime", "setTimeZoneDateWithTime", "toggleValidation", "validate<PERSON>ll<PERSON>orm<PERSON><PERSON>s", "LeadAppointmentComponent", "getGlobalSettingsAnonymous", "FetchInvoiceById", "getBookingData", "FetchBulkOperation", "UpdateLeadStatus", "UpdateMultipleLead", "getLeadStatusIsLoading", "getMultipleLeadStatusIsLoading", "LeadPreviewChanged", "getPermissions", "getPermissionsIsLoading", "FetchProjectById", "getIsProjectByIdLoading", "getProjectsIDWithName", "getProjectsIDWithNameIsLoading", "getSelectedProjectById", "FetchPropertyById", "FetchPropertyWithIdNameList", "getPropertyListDetails", "getPropertyListDetailsIsLoading", "getPropertyWithIdNameList", "FetchLocationsWithGoogle", "getLocationsWithGoogleApi", "getCustomStatusList", "getCustomStatusListIsLoading", "FetchReportingManagerDetails", "getAdminsAndReportees", "getAdminsAndReporteesIsLoading", "getManagerDetails", "getUserBasicDetails", "getUsersListForReassignment", "getUsersListForReassignmentIsLoading", "BulkOperationTrackerComponent", "BookingFormComponent", "i0", "i_r35", "ctx_r43", "i_r42", "ctx_r49", "ctx_r51", "_r63", "ctx_r55", "index_r70", "ctx_r56", "index_r76", "ctx_r72", "_r13", "ctx_r79", "ctx_r110", "ctx_r113", "ctx_r102", "ctx_r121", "$event", "ctx_r7", "_r94", "_r96", "CustomStatusChangeComponent", "constructor", "modalRef", "modalService", "formBuilder", "_store", "cdr", "_leadPreviewComponent", "_notificationsService", "shareDataService", "router", "s3UploadService", "Date", "maxDate", "dispatch", "canShowStatus", "hide<PERSON>tatus", "canShowStatusPopupInPreview", "isUnassignedLead", "leadInfo", "_a", "assignTo", "isBookedLead", "canUpdateBookedLead", "some", "lead", "status", "shouldUseForBooking", "isSelectedOnlySomeBooked", "length", "allBookedOrInvoiced", "every", "shouldUseForInvoice", "someBookedOrInvoiced", "isSelectedAllBookedLead", "getMaxDate", "ngOnInit", "isProjectSubscription", "isProjectUnit$", "subscribe", "show", "noUnitFound", "class", "ignoreBackdropClick", "events", "pipe", "event", "currentPath", "url", "select", "stopper", "customStatus", "for<PERSON>ach", "bookedAndInvoiceStatus", "includes", "displayName", "push", "sort", "a", "b", "localeCompare", "customStatusList", "isBulkUpdate", "_b", "isSelectedAllBookedOrInvoicedLead", "customStatusListFiltered", "_c", "shouldUseForBookingCancel", "orderRank", "_d", "shouldBeHidden", "id", "_e", "_f", "isLoading", "isCustomStatusListLoading", "permissions", "data", "isDualOwnershipEnabled", "isPastDateSelectionEnabled", "userBasicDetails", "currentDate", "timeZoneInfo", "baseUTcOffset", "minDate", "getMiniBookingFormDatas", "getMiniBookingformData", "makeRequired", "patchMiniBookDataIfBooked", "showReasons", "_g", "_h", "location", "href", "ngOnChanges", "changes", "initialize", "initializeUpdateForm", "isPrimaryUserActive", "primaryAgentList", "user", "isSecondaryUserActive", "secondaryAgentList", "secondaryUserId", "defaultControls", "bookedUnderName", "ScheduledDate", "Projects", "bookedDate", "agreementValue", "projectProperty", "chosenProperty", "chosenProject", "chosen<PERSON><PERSON><PERSON>", "currency", "notes", "leadStatus", "reason", "assignedToUserId", "secondaryAssignTo", "updateForm", "group", "documentsForm", "doc<PERSON><PERSON><PERSON>", "clearManualLocation", "manualLocationsList", "manualLocation", "JSON", "stringify", "addMoreLocation", "locality", "city", "state", "value", "trim", "filteredLocation", "removeLocation", "type", "patchValue", "Array", "isArray", "allLeadStatus", "parse", "localStorage", "getItem", "updateLeadStates", "currencyList", "countries", "currencies", "defaultCurrency", "isProjectMandatory", "leadProjectSetting", "isProjectMandatoryOnBooking", "isNotesMandatory", "leadNotesSetting", "isNotesMandatoryOnUpdateLead", "controls", "setValidators", "required", "clearValidators", "get", "valueChanges", "val", "budgetInWords", "enquiry", "placesList", "slice", "searchPlaceTerm$", "searchStr", "projectList", "name", "projectListIsLoading", "propertyList", "nameA", "nameB", "propertyListIsLoading", "adminsWithReportees$", "adminsWithReporteesIsLoading$", "allUsers$", "allUsersIsLoading$", "permissions$", "permissionsIsLoading$", "adminsWithReportees", "adminsWithReporteesIsLoading", "allUsers", "allUsersIsLoading", "permissionsIsLoading", "userList", "canAssignLead", "activeUsers", "isActive", "deactiveUsers", "primaryUserList", "secondaryUserList", "el", "isUserListLoading", "leadSource", "ngAfterViewInit", "element", "document", "getElementById", "elementHeight", "calculatedHeight", "statusForm", "nativeElement", "style", "height", "detectChanges", "dateValidator", "control", "selectedDate", "selectedDateTime", "setSeconds", "invalidDate", "openAppointmentPopup", "initialState", "closeModal", "appointmentModalRef", "hide", "closeLeadPreviewModal", "invoiceId", "undefined", "actionName", "isShowBookingFormBtn", "clearBookingFormValidations", "childTypes", "callBackReason", "selectedStatus", "childCount", "statusObj", "fieldsToClear", "field", "addFormControls", "formValues", "validators", "Object", "keys", "controlName", "hasOwnProperty", "removeControl", "addControl", "validatorsArray", "getValidators", "reasonChanged", "validator", "validateScheduleTime", "currentTime", "selectedTime", "selectedHours", "getHours", "selectedMinutes", "getMinutes", "scheduleDate", "setHours", "getTime", "invalidTime", "isSubStatus", "customFields", "isRequired", "lastModifiedOn", "createdOn", "created<PERSON>y", "lastModifiedBy", "module", "setValue", "miniagreementValueInWords", "selectedProject", "userDetails", "project", "projects", "map", "_j", "_k", "_l", "soldPriceInWords", "soldPrice", "_m", "_o", "soldPriceCurrency", "_p", "_q", "_r", "_s", "_t", "deselectStatuses", "setErrors", "selected<PERSON><PERSON>on", "updateStatus", "isSaveAndNext", "invalid", "console", "log", "invalidLeadNameMessage", "warn", "isUpload", "addDocument", "navigateLink", "shareContent", "window", "whatsAppShare", "open", "uploadedFiles", "uploadDocumentsToS3", "proceedWithStatusUpdate", "prepareAddressPayload", "userData", "showLocationSearch", "locationId", "placeId", "subLocality", "enquiredLocality", "enquiredCity", "enquiredState", "createPayload", "addressPayload", "isAddressPayloadEmpty", "postPonedStatusId", "leadStatusId", "scheduledDate", "rating", "IsNotesUpdated", "unmatchedBudget", "leadExpected<PERSON>udget", "purchasedFrom", "purchasedFromWhom", "addresses", "postponedDate", "revertDate", "propertiesList", "Properties", "properties", "proper", "title", "projectsList", "proje", "projectIds", "unitTypeId", "propertyIds", "handleProjectPropertySelection", "payload", "isprojectproperty", "Set", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assignUsers", "handleLeadStatus", "isFullyCompleted", "property", "handleSingleUpdate", "leadStatusIsLoading", "handleBulkUpdate", "documents", "multipleLeadsIsLoading", "notBookedLeads", "leadItems", "bulkPayload", "createBulkPayload", "ids", "leadData", "ArrayLeadIds", "flat", "payloadObj", "LeadIds", "intervalId", "setInterval", "getModalsCount", "numberOfLeads", "trackerInfoModal", "assign", "keyboard", "clearInterval", "updateLeadInfo", "masterLeadStatus", "find", "item", "child", "hasSubStatus", "childType", "openBulkUpdatedStatus", "moduleType", "cleanStatusForm", "resetDocumentState", "updateSelectedReason", "isVisitDone", "reset", "fullBookingFormModal", "miniBookDate", "onHide", "switchTabProjectProperty", "isShowUnitInfoField", "onInputAgreementValue", "onPropertyChange", "isSelectedPropertyOrProject", "buildername", "ownerDetails", "sealablearea", "dimension", "saleableArea", "carpetarea", "carpetArea", "brokerageCurrency", "monetaryInfo", "brockragecharge", "brokerage", "onProjectChange", "projectData", "isUnitInfoDataLoading", "unitInfo", "unitTypes", "builderDetail", "allprojectdata", "onChoosenUnitChange", "superBuildUpArea", "unitname", "bookingDetails", "unitType", "hasProjects", "hasProperties", "validAgreementValue", "checkBookedUnderName", "inputValue", "target", "whitespace", "onFileSelection", "e", "selectedFile", "valid", "uploadedFilesName", "selected<PERSON><PERSON><PERSON><PERSON>", "onClickRemoveDocument", "docIndex", "_", "index", "uploadImageBase64", "LeadDocument", "response", "documentName", "filePath", "toString", "nextData", "ngOnDestroy", "next", "complete", "unsubscribe", "selectors", "viewQuery", "_r11", "_r5"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Desktop\\Translate\\Leadrat-Black-Web\\src\\app\\features\\leads\\custom-status-change\\custom-status-change.component.ts", "C:\\Users\\<USER>\\Desktop\\Translate\\Leadrat-Black-Web\\src\\app\\features\\leads\\custom-status-change\\custom-status-change.component.html"], "sourcesContent": ["import {\r\n  AfterViewInit,\r\n  ChangeDetectorRef,\r\n  Component,\r\n  ElementRef,\r\n  EventEmitter,\r\n  Input,\r\n  OnChanges,\r\n  OnDestroy,\r\n  OnInit,\r\n  SimpleChanges,\r\n  TemplateRef,\r\n  ViewChild,\r\n} from '@angular/core';\r\n\r\nimport {\r\n  AbstractControl,\r\n  FormBuilder,\r\n  FormGroup,\r\n  ValidationErrors,\r\n  ValidatorFn,\r\n  Validators,\r\n} from '@angular/forms';\r\nimport { NavigationEnd, Router } from '@angular/router';\r\nimport { Store } from '@ngrx/store';\r\nimport { NotificationsService } from 'angular2-notifications';\r\nimport * as moment from 'moment';\r\nimport { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';\r\nimport { BehaviorSubject, combineLatest, Subscription } from 'rxjs';\r\nimport {\r\n  debounceTime,\r\n  distinctUntilChanged,\r\n  filter,\r\n  skipWhile,\r\n  take,\r\n  takeUntil,\r\n} from 'rxjs/operators';\r\n\r\nimport {\r\n  EMPTY_GUID,\r\n  LEAD_STATUS_REASONS,\r\n  UPDATE_STATUS,\r\n  UPDATE_STATUS_PAST_TENSE,\r\n  VALIDATION_CLEAR,\r\n  VALIDATION_SET,\r\n} from 'src/app/app.constants';\r\nimport { FolderNamesS3, LeadSource } from 'src/app/app.enum';\r\nimport { AppState } from 'src/app/app.reducer';\r\nimport {\r\n  assignToSort,\r\n  changeCalendar,\r\n  formatBudget,\r\n  getTimeZoneDate,\r\n  onlyNumbers,\r\n  onPickerOpened,\r\n  patchFormControlValue,\r\n  patchTimeZoneWithTime,\r\n  setTimeZoneDateWithTime,\r\n  toggleValidation,\r\n  validateAllFormFields,\r\n} from 'src/app/core/utils/common.util';\r\nimport { LeadAppointmentComponent } from 'src/app/features/leads/lead-appointment/lead-appointment.component';\r\nimport { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';\r\nimport { FetchInvoiceById } from 'src/app/reducers/invoice/invoice.actions';\r\nimport { getBookingData } from 'src/app/reducers/invoice/invoice.reducer';\r\nimport {\r\n  FetchBulkOperation,\r\n  UpdateLeadStatus,\r\n  UpdateMultipleLead,\r\n} from 'src/app/reducers/lead/lead.actions';\r\nimport {\r\n  getLeadStatusIsLoading,\r\n  getMultipleLeadStatusIsLoading,\r\n} from 'src/app/reducers/lead/lead.reducer';\r\nimport { LeadPreviewChanged } from 'src/app/reducers/loader/loader.actions';\r\nimport {\r\n  getPermissions,\r\n  getPermissionsIsLoading,\r\n} from 'src/app/reducers/permissions/permissions.reducers';\r\nimport { FetchProjectById } from 'src/app/reducers/project/project.action';\r\nimport {\r\n  getIsProjectByIdLoading,\r\n  getProjectsIDWithName,\r\n  getProjectsIDWithNameIsLoading,\r\n  getSelectedProjectById,\r\n} from 'src/app/reducers/project/project.reducer';\r\nimport {\r\n  FetchPropertyById,\r\n  FetchPropertyWithIdNameList,\r\n} from 'src/app/reducers/property/property.actions';\r\nimport {\r\n  getPropertyListDetails,\r\n  getPropertyListDetailsIsLoading,\r\n  getPropertyWithIdNameList,\r\n} from 'src/app/reducers/property/property.reducer';\r\nimport { FetchLocationsWithGoogle } from 'src/app/reducers/site/site.actions';\r\nimport { getLocationsWithGoogleApi } from 'src/app/reducers/site/site.reducer';\r\nimport {\r\n  CustomField,\r\n  CustomStatus,\r\n  getCustomStatusList,\r\n  getCustomStatusListIsLoading,\r\n} from 'src/app/reducers/status/status.reducer';\r\nimport { FetchReportingManagerDetails } from 'src/app/reducers/teams/teams.actions';\r\nimport {\r\n  getAdminsAndReportees,\r\n  getAdminsAndReporteesIsLoading,\r\n  getManagerDetails,\r\n  getUserBasicDetails,\r\n  getUsersListForReassignment,\r\n  getUsersListForReassignmentIsLoading,\r\n} from 'src/app/reducers/teams/teams.reducer';\r\nimport { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';\r\nimport { ShareDataService } from 'src/app/services/shared/share-data.service';\r\nimport { BulkOperationTrackerComponent } from 'src/app/shared/components/bulk-operation-tracker/bulk-operation-tracker.component';\r\nimport { LeadPreviewComponent } from 'src/app/shared/components/lead-preview/lead-preview.component';\r\nimport { BookingFormComponent } from '../booking-form/booking-form.component';\r\n\r\n@Component({\r\n  selector: 'custom-status-change',\r\n  templateUrl: './custom-status-change.component.html',\r\n})\r\nexport class CustomStatusChangeComponent\r\n  implements OnInit, AfterViewInit, OnChanges, OnDestroy {\r\n  private stopper: EventEmitter<void> = new EventEmitter<void>();\r\n  searchPlaceTerm$: BehaviorSubject<any> = new BehaviorSubject<any>('');\r\n  @Input() leadInfo: any;\r\n  @Input() canShowStatusPopupInPreview: boolean = false;\r\n  @Input() canUpdateStatus: boolean = false;\r\n  @Input() isLeadPreview: boolean = false;\r\n  @Input() isLastLead: boolean = false;\r\n  @Input() closeLeadPreviewModal: any;\r\n  @Input() whatsAppComp: boolean = false;\r\n  updateForm: FormGroup;\r\n  params: any;\r\n\r\n  dispSubStatus: any = LEAD_STATUS_REASONS;\r\n  leadStatus: any = UPDATE_STATUS;\r\n  currentLeadStatus: any = UPDATE_STATUS_PAST_TENSE;\r\n\r\n  updateLeadStates: any[] = [];\r\n  callBackReason: any[] = [];\r\n  notInterestedReason: any[] = [];\r\n  placesList: any[] = [];\r\n  minDate: Date;\r\n  maxDate: Date;\r\n  allLeadStatus: any;\r\n  leadAuditHistory: any;\r\n  selectedStatus: CustomStatus;\r\n  reasons: Array<string>;\r\n  selectedReason: any;\r\n  portfolioLink: string;\r\n  navigateLink: string = '';\r\n  whatsAppShare: string = '';\r\n  defaultCurrency: string = '';\r\n  soldPriceInWords: string = '';\r\n  budgetInWords: string = '';\r\n  leadSource: string;\r\n  hideStatus: boolean = false;\r\n  isVisitDone: boolean = false;\r\n  didCustDenyOtp: boolean = false;\r\n  isBulkUpdate: boolean = false;\r\n  disableStatus: boolean;\r\n  showLocationSearch: boolean = false;\r\n  projectList: Array<any> = [];\r\n  propertyList: Array<string> = [];\r\n  currencyList: any[] = [];\r\n  leadStatusIsLoading: boolean = false;\r\n  multipleLeadsIsLoading: boolean = false;\r\n  propertyListIsLoading: boolean = true;\r\n  projectListIsLoading: boolean = true;\r\n  isNotesMandatory: boolean;\r\n  isProjectMandatory: boolean;\r\n  elementHeight: any;\r\n  assignedToUserId: string;\r\n  canAssignLead: boolean = false;\r\n  primaryUserList: Array<Object> = [];\r\n  secondaryUserList: Array<Object> = [];\r\n  primaryAgentList: Array<Object> = [];\r\n  secondaryAgentList: Array<Object> = [];\r\n  isUserListLoading: boolean = true;\r\n  deactiveUsers: Array<Object> = [];\r\n  isLeadStatusLoading: boolean = false;\r\n  canUpdateBookedLead: boolean = false;\r\n  isDualOwnershipEnabled: boolean;\r\n  isReadMore: boolean = false;\r\n  customStatusList: CustomStatus[];\r\n  defaultControls: any;\r\n  isCustomStatusListLoading: boolean = true;\r\n  manualLocationsList: any[] = [];\r\n  customStatusListFiltered: any = [];\r\n  @ViewChild('statusForm') statusForm: ElementRef;\r\n  @ViewChild('noUnitFound') noUnitFound: TemplateRef<any>;\r\n  @ViewChild('trackerInfoModal') trackerInfoModal: any;\r\n  EMPTY_GUID = EMPTY_GUID;\r\n  moment = moment;\r\n  patchFormControlValue = patchFormControlValue;\r\n  formatBudget = formatBudget;\r\n  onlyNumbers = onlyNumbers;\r\n  getTimeZoneDate = getTimeZoneDate;\r\n  isShowUnitInfoField: boolean = false;\r\n  selectedProject: any = {};\r\n  isSelectedPropertyOrProject: string;\r\n  unitInfo: any;\r\n  isUnitInfoDataLoading: boolean;\r\n  miniagreementValueInWords: string;\r\n  isShowBookingFormBtn: boolean = false;\r\n  currentPath: string;\r\n  getMiniBookingFormDatas: Subscription;\r\n  private isProjectSubscription: Subscription;\r\n  userDetails: any;\r\n  userBasicDetails: any;\r\n  isSaveAndNext: boolean = false;\r\n  bookedAndInvoiceStatus: string[] = [];\r\n  onPickerOpened = onPickerOpened\r\n  currentDate: Date = new Date();\r\n  trackerModalOpen: boolean = false;\r\n  isPastDateSelectionEnabled: boolean = true;\r\n  documentsForm: FormGroup;\r\n  uploadedFiles: Array<string> = [];\r\n  uploadedFilesName: Array<string> = [];\r\n  fileFormatToBeUploaded: string = 'application/pdf,image/x-png,image/gif,image/jpeg,image/tiff';\r\n  selectedFile: string = '';\r\n  selectedFileName: string = null;\r\n  isUpload: boolean = false;\r\n\r\n  get canShowStatus(): boolean {\r\n    return !this.hideStatus && !this.canShowStatusPopupInPreview;\r\n  }\r\n\r\n  get isUnassignedLead(): boolean {\r\n    return this.leadInfo?.assignTo == EMPTY_GUID;\r\n  }\r\n\r\n  isBookedLead(): boolean {\r\n    return (\r\n      !this.canUpdateBookedLead &&\r\n      this.leadInfo?.some(\r\n        (lead: any) =>\r\n          lead?.status?.shouldUseForBooking\r\n      )\r\n    );\r\n  }\r\n\r\n  isSelectedOnlySomeBooked(): boolean {\r\n    if (!this.leadInfo?.length) {\r\n      return false;\r\n    }\r\n\r\n    const allBookedOrInvoiced = this.leadInfo.every(\r\n      (lead: any) =>\r\n        lead.status?.shouldUseForBooking ||\r\n        lead.status.shouldUseForInvoice\r\n    );\r\n\r\n    const someBookedOrInvoiced = this.leadInfo.some(\r\n      (lead: any) =>\r\n        lead.status?.shouldUseForBooking ||\r\n        lead.status.shouldUseForInvoice\r\n    );\r\n\r\n    return (\r\n      allBookedOrInvoiced || (someBookedOrInvoiced && !allBookedOrInvoiced)\r\n    );\r\n  }\r\n\r\n  isSelectedAllBookedLead(): boolean {\r\n    const leadInfo = this.leadInfo.length ? this.leadInfo : [];\r\n    return leadInfo?.every((lead: any) => lead?.status?.shouldUseForBooking);\r\n  }\r\n\r\n  getMaxDate() {\r\n    return new Date();\r\n  }\r\n\r\n  constructor(\r\n    public modalRef: BsModalRef,\r\n    public modalService: BsModalService,\r\n    private formBuilder: FormBuilder,\r\n    private _store: Store<AppState>,\r\n    private cdr: ChangeDetectorRef,\r\n    private _leadPreviewComponent: LeadPreviewComponent,\r\n    private _notificationsService: NotificationsService,\r\n    private shareDataService: ShareDataService,\r\n    private router: Router,\r\n    private s3UploadService: BlobStorageService\r\n  ) {\r\n    this.maxDate = new Date();\r\n    // this.minDate = new Date();\r\n    // this.minDate.setDate(this.minDate.getDate());\r\n    this._store.dispatch(new FetchPropertyWithIdNameList());\r\n    this._store.dispatch(new FetchLocationsWithGoogle());\r\n  }\r\n  async ngOnInit() {\r\n    this.isProjectSubscription = this.shareDataService.isProjectUnit$.subscribe(() => {\r\n      this.modalRef = this.modalService.show(this.noUnitFound, {\r\n        class: 'modal-500 top-modal ip-modal-unset',\r\n        ignoreBackdropClick: true,\r\n      });\r\n    });\r\n    this.router.events\r\n      .pipe(filter((event) => event instanceof NavigationEnd))\r\n      .subscribe(() => {\r\n        this.currentPath = this.router.url;\r\n      });\r\n    this.currentPath = this.router.url;\r\n\r\n    this._store.select(getCustomStatusList)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((customStatus: any) => {\r\n        customStatus?.forEach((status: any) => {\r\n          if (status?.shouldUseForBooking || status?.shouldUseForInvoice) {\r\n            if (!this.bookedAndInvoiceStatus.includes(status?.displayName)) {\r\n              this.bookedAndInvoiceStatus.push(status?.displayName);\r\n            }\r\n          }\r\n        });\r\n        this.bookedAndInvoiceStatus.sort((a: string, b: string) => a.localeCompare(b));\r\n        this.customStatusList = customStatus;\r\n        let shouldUseForBooking = this.isBulkUpdate\r\n          ? this.leadInfo.every((lead: any) => lead?.status?.shouldUseForBooking)\r\n          : this.leadInfo?.status?.shouldUseForBooking;\r\n\r\n        if (this.isSelectedAllBookedOrInvoicedLead()) {\r\n          this.customStatusListFiltered = this.customStatusList?.filter((status: any) =>\r\n            status.shouldUseForBookingCancel\r\n          ).sort((a: any, b: any) => a.orderRank - b.orderRank);\r\n        } else if (shouldUseForBooking) {\r\n          this.customStatusListFiltered = this.customStatusList?.filter((status: any) =>\r\n            (status?.shouldUseForBookingCancel && !status?.shouldBeHidden) || this.leadInfo?.status.id === status.id\r\n          ).sort((a: any, b: any) => a.orderRank - b.orderRank);\r\n        } else if (this.currentPath === '/invoice') {\r\n          this.customStatusListFiltered = this.customStatusList?.filter((status: any) =>\r\n            status?.shouldUseForBookingCancel || status.shouldUseForInvoice\r\n          ).sort((a: any, b: any) => a.orderRank - b.orderRank);\r\n        } else {\r\n          this.customStatusListFiltered = this.customStatusList?.filter((status: any) =>\r\n            !status?.shouldUseForBookingCancel && !status?.shouldUseForInvoice && !status?.shouldBeHidden\r\n          ).sort((a: any, b: any) => a.orderRank - b.orderRank);\r\n        }\r\n      });\r\n\r\n\r\n    this._store\r\n      .select(getCustomStatusListIsLoading)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((isLoading: any) => {\r\n        this.isCustomStatusListLoading = isLoading;\r\n      });\r\n\r\n    this._store\r\n      .select(getPermissions)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((permissions: any) => {\r\n        if (!permissions?.length) return;\r\n        if (permissions?.includes('Permissions.Leads.UpdateBookedLead')) {\r\n          this.canUpdateBookedLead = true;\r\n        }\r\n      });\r\n\r\n    this._store\r\n      .select(getGlobalSettingsAnonymous)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        this.isDualOwnershipEnabled = data?.isDualOwnershipEnabled;\r\n        this.isPastDateSelectionEnabled = data?.isPastDateSelectionEnabled\r\n      });\r\n\r\n    this._store\r\n      .select(getUserBasicDetails)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        this.userBasicDetails = data;\r\n        this.currentDate = changeCalendar(this.userBasicDetails?.timeZoneInfo?.baseUTcOffset)\r\n        this.minDate = this.currentDate\r\n      });\r\n\r\n    this.getMiniBookingFormDatas = this.shareDataService.getMiniBookingformData()\r\n      .subscribe((data: any) => {\r\n        this.makeRequired(data?.status);\r\n        this.patchMiniBookDataIfBooked(data?.id);\r\n        if (data?.status?.shouldUseForBooking) {\r\n          this.customStatusListFiltered = this.customStatusList?.filter((status: any) =>\r\n            status?.shouldUseForBookingCancel || status?.shouldUseForBooking\r\n          ).sort((a: any, b: any) => a.orderRank - b.orderRank);\r\n          const status: any = this.customStatusListFiltered?.filter((status: any) =>\r\n            status?.shouldUseForBooking\r\n          )?.[0];\r\n          this.makeRequired(status);\r\n          this.showReasons(status, data?.id);\r\n        }\r\n\r\n        if (data?.status?.shouldUseForInvoice) {\r\n          this.customStatusListFiltered = this.customStatusList?.filter((status: any) =>\r\n            status?.shouldUseForBookingCancel || status.shouldUseForInvoice\r\n          ).sort((a: any, b: any) => a.orderRank - b.orderRank);\r\n          const status: any = this.customStatusListFiltered?.filter((status: any) =>\r\n            status?.shouldUseForInvoice\r\n          )?.[0];\r\n          this.makeRequired(status);\r\n          this.showReasons(status, data?.id);\r\n        }\r\n      });\r\n\r\n    if (\r\n      location.href.includes('/invoice') ||\r\n      this.leadInfo?.status?.shouldUseForBooking\r\n    ) {\r\n      const status: any = this.customStatusListFiltered?.filter((status: any) =>\r\n        this.leadInfo?.status?.shouldUseForBooking\r\n          ? status?.shouldUseForBooking\r\n          : status?.shouldUseForInvoice\r\n      )?.[0];\r\n      this.makeRequired(status);\r\n      this.showReasons(status, this.leadInfo?.id);\r\n      return;\r\n    }\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes?.leadInfo) {\r\n      if (this.leadInfo?.status?.shouldUseForBooking)\r\n        this.customStatusListFiltered = this.customStatusList?.filter(\r\n          (status: any) => {\r\n            return status?.shouldUseForBookingCancel;\r\n          }\r\n        );\r\n      else if (this.leadInfo?.status?.shouldUseForBookingCancel) {\r\n        this.customStatusListFiltered = this.customStatusList?.filter(\r\n          (status: any) => {\r\n            return !status?.shouldUseForBookingCancel;\r\n          }\r\n        );\r\n      }\r\n      this.initialize();\r\n    }\r\n  }\r\n\r\n  initializeUpdateForm(): void {\r\n    let isPrimaryUserActive = this.primaryAgentList?.filter(\r\n      (user: any) => user?.id === this.leadInfo?.assignTo\r\n    )?.length;\r\n    let isSecondaryUserActive = this.secondaryAgentList?.filter(\r\n      (user: any) => user?.id === this.leadInfo?.secondaryUserId\r\n    )?.length;\r\n    this.defaultControls = {\r\n      bookedUnderName: null,\r\n      ScheduledDate: null,\r\n      Projects: null,\r\n      bookedDate: null,\r\n      agreementValue: null,\r\n      projectProperty: null,\r\n      chosenProperty: null,\r\n      chosenProject: null,\r\n      chosenUnit: null,\r\n      currency: null,\r\n      notes: null,\r\n      leadStatus: null,\r\n      reason: '',\r\n      assignedToUserId: [\r\n        this.leadInfo?.assignTo !== EMPTY_GUID && isPrimaryUserActive\r\n          ? this.leadInfo?.assignTo\r\n          : null,\r\n      ],\r\n      secondaryAssignTo:\r\n        this.leadInfo.secondaryUserId !== EMPTY_GUID && isSecondaryUserActive\r\n          ? this.leadInfo.secondaryUserId\r\n          : null,\r\n    };\r\n    this.updateForm = this.formBuilder.group(this.defaultControls);\r\n    this.documentsForm = this.formBuilder.group({\r\n      docTitle: [''],\r\n    });\r\n  }\r\n\r\n  clearManualLocation(location: any) {\r\n    this.manualLocationsList = this.manualLocationsList.filter(\r\n      (manualLocation: any) =>\r\n        JSON.stringify(manualLocation) != JSON.stringify(location)\r\n    );\r\n  }\r\n\r\n  addMoreLocation() {\r\n    const { locality, city, state }: any = this.updateForm.value;\r\n    if (!city?.trim() && !state?.trim() && !locality?.trim()) {\r\n      return;\r\n    }\r\n    const filteredLocation = this.manualLocationsList.filter(\r\n      (location: any) => {\r\n        return (\r\n          city?.trim() == location?.city &&\r\n          locality?.trim() == location?.locality &&\r\n          state?.trim() == location?.state\r\n        );\r\n      }\r\n    );\r\n\r\n    if (filteredLocation?.length) return;\r\n    this.manualLocationsList.push({\r\n      city: city?.trim(),\r\n      locality: locality?.trim(),\r\n      state: state?.trim(),\r\n    });\r\n\r\n    this.removeLocation('changeLocation');\r\n  }\r\n\r\n  removeLocation(type: any) {\r\n    switch (type) {\r\n      case 'changeLocation':\r\n        this.updateForm.patchValue({\r\n          locality: null,\r\n          city: null,\r\n          state: null,\r\n        });\r\n        break;\r\n    }\r\n  }\r\n\r\n  initialize(): void {\r\n    this.isBulkUpdate = Array.isArray(this.leadInfo);\r\n    this.initializeUpdateForm();\r\n    this.allLeadStatus = JSON.parse(localStorage.getItem('masterleadstatus'));\r\n    this.updateLeadStates =\r\n      this.allLeadStatus?.filter((status: any) => {\r\n        return ![\r\n          'Meeting Done',\r\n          'Site Visit Done',\r\n          'New',\r\n          'Pending',\r\n          'Site Visit Not Done',\r\n          'Meeting Not Done',\r\n        ].includes(status.displayName);\r\n      }) || [];\r\n\r\n    this._store\r\n      .select(getGlobalSettingsAnonymous)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        this.currencyList =\r\n          data.countries && data.countries.length > 0\r\n            ? data.countries[0].currencies\r\n            : null;\r\n        this.defaultCurrency =\r\n          data.countries && data.countries.length > 0\r\n            ? data.countries[0].defaultCurrency\r\n            : null;\r\n        this.isProjectMandatory =\r\n          data?.leadProjectSetting?.isProjectMandatoryOnBooking;\r\n        this.isNotesMandatory =\r\n          data?.leadNotesSetting?.isNotesMandatoryOnUpdateLead;\r\n        if (this.isNotesMandatory) {\r\n          this.updateForm.controls['notes']?.setValidators([\r\n            Validators.required,\r\n          ]);\r\n        } else {\r\n          this.updateForm.controls['notes']?.clearValidators();\r\n        }\r\n      });\r\n\r\n    this.updateForm\r\n      ?.get('leadExpectedBudget')\r\n      ?.valueChanges.subscribe((val) => {\r\n        this.budgetInWords = formatBudget(\r\n          val,\r\n          this.leadInfo?.enquiry?.currency || this.defaultCurrency\r\n        );\r\n      });\r\n\r\n    this._store\r\n      .select(getLocationsWithGoogleApi)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        this.placesList = data\r\n          ?.slice()\r\n          .sort((a: any, b: any) => a.location.localeCompare(b.location));\r\n      });\r\n\r\n    this.searchPlaceTerm$\r\n      .pipe(\r\n        debounceTime(500),\r\n        distinctUntilChanged(),\r\n        filter((searchStr: string) => searchStr.length > 2)\r\n      )\r\n      .subscribe((searchStr: string) => {\r\n        this._store.dispatch(new FetchLocationsWithGoogle(searchStr));\r\n      });\r\n\r\n    this._store\r\n      .select(getProjectsIDWithName)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        this.projectList = data\r\n          ?.slice()\r\n          ?.sort((a: any, b: any) => a.name.localeCompare(b.name));\r\n      });\r\n\r\n    this._store\r\n      .select(getProjectsIDWithNameIsLoading)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: boolean) => {\r\n        this.projectListIsLoading = data;\r\n      });\r\n\r\n    this._store\r\n      .select(getPropertyWithIdNameList)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        this.propertyList = data.slice().sort((a: any, b: any) => {\r\n          const nameA = a.name || '';\r\n          const nameB = b.name || '';\r\n          return nameA.localeCompare(nameB);\r\n        });\r\n      });\r\n\r\n    this._store\r\n      .select(getPropertyListDetailsIsLoading)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: boolean) => {\r\n        this.propertyListIsLoading = data;\r\n      });\r\n\r\n    const adminsWithReportees$ = this._store\r\n      .select(getAdminsAndReportees)\r\n      .pipe(takeUntil(this.stopper));\r\n\r\n    const adminsWithReporteesIsLoading$ = this._store\r\n      .select(getAdminsAndReporteesIsLoading)\r\n      .pipe(takeUntil(this.stopper));\r\n\r\n    const allUsers$ = this._store\r\n      .select(getUsersListForReassignment)\r\n      .pipe(takeUntil(this.stopper));\r\n\r\n    const allUsersIsLoading$ = this._store\r\n      .select(getUsersListForReassignmentIsLoading)\r\n      .pipe(takeUntil(this.stopper));\r\n\r\n    const permissions$ = this._store\r\n      .select(getPermissions)\r\n      .pipe(takeUntil(this.stopper));\r\n\r\n    const permissionsIsLoading$ = this._store\r\n      .select(getPermissionsIsLoading)\r\n      .pipe(takeUntil(this.stopper));\r\n\r\n    combineLatest({\r\n      adminsWithReportees: adminsWithReportees$,\r\n      adminsWithReporteesIsLoading: adminsWithReporteesIsLoading$,\r\n      allUsers: allUsers$,\r\n      allUsersIsLoading: allUsersIsLoading$,\r\n      permissions: permissions$,\r\n      permissionsIsLoading: permissionsIsLoading$,\r\n    }).subscribe(\r\n      ({\r\n        adminsWithReportees,\r\n        adminsWithReporteesIsLoading,\r\n        allUsers,\r\n        allUsersIsLoading,\r\n        permissions,\r\n        permissionsIsLoading,\r\n      }) => {\r\n        let userList;\r\n        if (permissions?.includes('Permissions.Leads.Assign')) {\r\n          this.canAssignLead = true;\r\n        }\r\n        if (permissions?.includes('Permissions.Users.AssignToAny')) {\r\n          userList = allUsers;\r\n        } else {\r\n          userList = adminsWithReportees;\r\n        }\r\n\r\n        let activeUsers = userList?.filter((user: any) => user.isActive);\r\n        this.deactiveUsers = userList?.filter((user: any) => !user.isActive);\r\n        this.primaryUserList = assignToSort(\r\n          activeUsers,\r\n          this.leadInfo?.assignTo\r\n        );\r\n        this.primaryAgentList = assignToSort(\r\n          activeUsers,\r\n          this.leadInfo?.assignTo\r\n        );\r\n        this.secondaryUserList = assignToSort(\r\n          activeUsers,\r\n          this.leadInfo?.secondaryUserId\r\n        );\r\n        this.secondaryUserList = assignToSort(\r\n          activeUsers,\r\n          this.leadInfo?.secondaryUserId\r\n        );\r\n        this.primaryAgentList = this.primaryUserList?.filter(\r\n          (el: any) => !this.leadInfo?.secondaryUserId?.includes(el?.id)\r\n        );\r\n        this.secondaryAgentList = this.secondaryUserList?.filter(\r\n          (el: any) => !this.leadInfo?.assignTo?.includes(el?.id)\r\n        );\r\n        this.updateForm\r\n          .get('assignedToUserId')\r\n          .valueChanges.subscribe((val: any) => {\r\n            this.secondaryAgentList = this.secondaryUserList.filter(\r\n              (el: any) => !val?.includes(el?.id)\r\n            );\r\n          });\r\n\r\n        this.updateForm\r\n          .get('secondaryAssignTo')\r\n          .valueChanges.subscribe((val: any) => {\r\n            this.primaryAgentList = this.primaryUserList.filter(\r\n              (el: any) => !val?.includes(el?.id)\r\n            );\r\n          });\r\n\r\n        this.isUserListLoading =\r\n          adminsWithReporteesIsLoading &&\r\n          allUsersIsLoading &&\r\n          permissionsIsLoading;\r\n      }\r\n    );\r\n\r\n    if (!this.isBulkUpdate) {\r\n      this.leadSource = LeadSource[this.leadInfo?.enquiry?.leadSource];\r\n    }\r\n    // this.conditionalStatusRendering();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    const element = document.getElementById('bulk-status-table');\r\n    if (element) {\r\n      this.elementHeight = 'unset';\r\n      const calculatedHeight = `${this.elementHeight}`;\r\n      this.statusForm.nativeElement.style.height = calculatedHeight;\r\n    }\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  dateValidator(control: { value: any }) {\r\n    const selectedDate = control.value;\r\n\r\n    if (selectedDate) {\r\n      const currentDate = new Date();\r\n      const selectedDateTime = new Date(selectedDate);\r\n      currentDate.setSeconds(0, 0);\r\n      selectedDateTime.setSeconds(0, 0);\r\n      if (selectedDateTime > currentDate) {\r\n        return { invalidDate: true };\r\n      }\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  openAppointmentPopup() {\r\n    let initialState: any = {\r\n      data: this.leadInfo,\r\n      closeModal: () => {\r\n        appointmentModalRef.hide();\r\n      },\r\n      closeLeadPreviewModal: this.closeLeadPreviewModal,\r\n    };\r\n    const appointmentModalRef = this.modalService.show(\r\n      LeadAppointmentComponent,\r\n      {\r\n        initialState,\r\n        class: 'right-modal modal-550 ip-modal-unset',\r\n      }\r\n    );\r\n  }\r\n\r\n  showReasons(status: any, id: any) {\r\n    const invoiceId = (id !== undefined && id !== null && id !== '') ? id : '';\r\n    if (\r\n      (this.leadInfo?.status?.shouldUseForInvoice ||\r\n        this.leadInfo?.status?.shouldUseForBooking ||\r\n        status?.shouldUseForInvoice) && status?.actionName !== 'Booking Cancel'\r\n    ) {\r\n      this.patchMiniBookDataIfBooked(invoiceId);\r\n    }\r\n\r\n    if (this.currentPath === '/invoice') {\r\n      if (status?.shouldUseForInvoice) {\r\n        this.isShowBookingFormBtn = true;\r\n      } else if (status?.shouldUseForBookingCancel) {\r\n        this.isShowBookingFormBtn = false;\r\n        this.clearBookingFormValidations();\r\n      }\r\n    } else {\r\n      if (status?.shouldUseForBooking) {\r\n        this.isShowBookingFormBtn = true;\r\n      } else {\r\n        this.isShowBookingFormBtn = false;\r\n        this.clearBookingFormValidations();\r\n      }\r\n    }\r\n\r\n    patchFormControlValue(this.updateForm, 'leadStatus', status?.id);\r\n    if (status?.childTypes?.length) {\r\n      this.callBackReason = status.childTypes;\r\n      this.hideStatus = true;\r\n    }\r\n    this.makeRequired(status);\r\n    this.selectedStatus = status;\r\n    let childCount = this.customStatusList.filter(\r\n      (statusObj: any) => statusObj?.id === status?.id\r\n    )?.[0]?.childTypes?.length;\r\n    if (childCount) {\r\n      toggleValidation(VALIDATION_SET, this.updateForm, 'reason', [\r\n        Validators.required,\r\n      ]);\r\n    } else {\r\n      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'reason');\r\n    }\r\n  }\r\n\r\n  clearBookingFormValidations() {\r\n    const fieldsToClear = [\r\n      'bookedUnderName',\r\n      'bookedDate',\r\n      'agreementValue',\r\n      'chosenProperty',\r\n      'chosenProject',\r\n      'chosenUnit'\r\n    ];\r\n    fieldsToClear.forEach(field => toggleValidation(VALIDATION_CLEAR, this.updateForm, field));\r\n  }\r\n\r\n  addFormControls(\r\n    formValues: { [key: string]: any },\r\n    validators: { [key: string]: any } = {}\r\n  ): void {\r\n    Object.keys(this.updateForm.controls).forEach((controlName) => {\r\n      if (!this.defaultControls.hasOwnProperty(controlName)) {\r\n        this.updateForm.removeControl(controlName);\r\n      }\r\n    });\r\n\r\n    for (const controlName in formValues) {\r\n      if (formValues.hasOwnProperty(controlName)) {\r\n        this.updateForm.addControl(\r\n          controlName,\r\n          this.formBuilder.control(formValues[controlName], [])\r\n        );\r\n      }\r\n    }\r\n\r\n    for (const controlName in validators) {\r\n      if (validators.hasOwnProperty(controlName)) {\r\n        const validatorsArray = this.getValidators(validators[controlName]);\r\n        this.updateForm.get(controlName)?.setValidators(validatorsArray);\r\n      }\r\n    }\r\n  }\r\n\r\n  reasonChanged(reason: CustomStatus) {\r\n    reason?.shouldUseForBooking ? '' : this.makeRequired(reason, true);\r\n  }\r\n\r\n  getValidators(validators: string[]): ValidatorFn[] {\r\n    const validatorsArray: ValidatorFn[] = [];\r\n    validators?.forEach((validator: string) => {\r\n      switch (validator) {\r\n        case 'required':\r\n          validatorsArray.push(Validators.required);\r\n          break;\r\n        case 'futureDate':\r\n          validatorsArray.push(this.validateScheduleTime(this.currentDate));\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    });\r\n    return validatorsArray;\r\n  }\r\n\r\n  validateScheduleTime(Date: any): ValidatorFn {\r\n    return (control: AbstractControl): ValidationErrors | null => {\r\n      const currentTime = new Date(Date);\r\n      const selectedTime = new Date(control?.value);\r\n\r\n      const selectedHours = selectedTime?.getHours();\r\n      const selectedMinutes = selectedTime?.getMinutes();\r\n\r\n      const currentDate = new Date(Date);\r\n      const scheduleDate = new Date(control?.value);\r\n\r\n      currentDate.setHours(0, 0, 0, 0);\r\n      scheduleDate.setHours(0, 0, 0, 0);\r\n\r\n      if (\r\n        currentDate.getTime() == scheduleDate.getTime() &&\r\n        (selectedHours < currentTime.getHours() ||\r\n          (selectedHours === currentTime.getHours() &&\r\n            selectedMinutes <= currentTime.getMinutes()))\r\n      ) {\r\n        return { invalidTime: true };\r\n      }\r\n\r\n      return null;\r\n    };\r\n  }\r\n\r\n  makeRequired(status: CustomStatus, isSubStatus: boolean = false) {\r\n    if (!status?.childTypes?.length && !isSubStatus)\r\n      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'reason');\r\n    else toggleValidation(VALIDATION_SET, this.updateForm, 'reason');\r\n\r\n    const formValues: { [key: string]: any } = {};\r\n    const validators: { [key: string]: any } = {};\r\n\r\n    // this should be refactored\r\n    if (status?.shouldUseForBooking || status?.shouldUseForInvoice) {\r\n      const customFields: CustomField[] = [];\r\n      [\r\n        ['Projects', null, []],\r\n        ['bookedUnderName', null, ['required']],\r\n        ['bookedDate', null, ['required']],\r\n        ['agreementValue', null, []],\r\n        ['projectProperty', 'Property', []],\r\n        ['chosenProperty', null, ['required']],\r\n        ['chosenProject', null, [this.isProjectMandatory ? 'required' : '']],\r\n        ['chosenUnit', null, ['required']],\r\n        [\r\n          'currency',\r\n          this.leadInfo?.enquiry?.currency || this.defaultCurrency,\r\n          [],\r\n        ],\r\n      ].forEach((field: any) => {\r\n        customFields.push({\r\n          isRequired: false,\r\n          value: field?.[1],\r\n          field: {\r\n            lastModifiedOn: 'string',\r\n            createdOn: 'string',\r\n            createdBy: 'string',\r\n            lastModifiedBy: 'string',\r\n            id: 'string',\r\n            name: field?.[0],\r\n            orderRank: 0,\r\n            module: 'string',\r\n            notes: 'any',\r\n          },\r\n          validators: field[2],\r\n        });\r\n      });\r\n      status = {\r\n        ...status,\r\n        customFields,\r\n      };\r\n    }\r\n\r\n    status?.customFields?.forEach((field: CustomField) => {\r\n      formValues[field.field.name] = null;\r\n      validators[field.field.name] = field?.validators;\r\n    });\r\n    this.addFormControls(formValues, validators);\r\n    if (status?.shouldUseForBooking || status?.shouldUseForInvoice) {\r\n      this.updateForm\r\n        .get('currency')\r\n        .setValue(this.leadInfo?.enquiry?.currency || this.defaultCurrency);\r\n      this.updateForm.get('projectProperty').setValue('Property');\r\n      this.updateForm.get('bookedDate').setValue(this.currentDate);\r\n\r\n      this.updateForm.get('currency').valueChanges.subscribe((val) => {\r\n        this.miniagreementValueInWords = formatBudget(\r\n          this.updateForm.value.agreementValue ||\r\n          this.updateForm?.get('agreementValue').value,\r\n          val\r\n        );\r\n      });\r\n      this.updateForm.get('agreementValue').valueChanges.subscribe((val) => {\r\n        this.miniagreementValueInWords = formatBudget(\r\n          val,\r\n          this.updateForm.value.currency\r\n        );\r\n      });\r\n      this.updateForm.get('assignedToUserId').valueChanges.subscribe((val) => {\r\n        if (val) {\r\n          this.selectedProject.assignedToUserId = val;\r\n          this._store.dispatch(new FetchReportingManagerDetails(val));\r\n          this._store.select(getManagerDetails).pipe().subscribe((data: any) => {\r\n            this.userDetails = data;\r\n          });\r\n        }\r\n      });\r\n      this.updateForm.get('secondaryAssignTo').valueChanges.subscribe((val) => {\r\n        if (val) {\r\n          this.selectedProject.secondaryAssignTo = val;\r\n        }\r\n      });\r\n    } else {\r\n      let project = this.leadInfo?.projects?.map((data: any) => data.name);\r\n      this.updateForm.get('Projects')?.setValue(project);\r\n    }\r\n    this.updateForm?.get('soldPriceCurrency')?.valueChanges.subscribe((val) => {\r\n      this.soldPriceInWords = formatBudget(\r\n        this.leadInfo?.soldPrice || this.updateForm.value.soldPrice,\r\n        val\r\n      );\r\n    });\r\n\r\n    this.updateForm?.get('soldPrice')?.valueChanges.subscribe((val) => {\r\n      this.soldPriceInWords = formatBudget(\r\n        val,\r\n        this.updateForm.value.soldPriceCurrency ||\r\n        this.leadInfo?.soldPriceCurrency\r\n      );\r\n    });\r\n\r\n    this.updateForm\r\n      ?.get('leadExpectedBudget')\r\n      ?.valueChanges.subscribe((val) => {\r\n        this.budgetInWords = formatBudget(\r\n          val,\r\n          this.leadInfo?.enquiry?.currency || this.defaultCurrency\r\n        );\r\n      });\r\n\r\n    if (status?.shouldUseForBooking) {\r\n      this.updateForm\r\n        .get('soldPriceCurrency')\r\n        ?.setValue(this.leadInfo?.enquiry?.currency || this.defaultCurrency);\r\n    }\r\n  }\r\n\r\n  deselectStatuses() {\r\n    this.updateForm.get('ScheduledDate').setErrors(null)\r\n    this.updateForm.controls['reason'].setValue('');\r\n    this.updateForm.controls['leadStatus'].setValue('');\r\n    this.selectedStatus = null;\r\n    this.hideStatus = false;\r\n    this.selectedReason = null;\r\n    this.isShowBookingFormBtn = false;\r\n    this.clearBookingFormValidations();\r\n  }\r\n\r\n  updateStatus(isSaveAndNext: boolean = false) {\r\n    this.isSaveAndNext = isSaveAndNext;\r\n    Object.keys(this.updateForm.controls).forEach((field) => {\r\n      const control = this.updateForm.get(field);\r\n      if (control && control.invalid) {\r\n        console.log(`Invalid field: ${field}`);\r\n      }\r\n    });\r\n    this.selectedProject.agreementValue = this.updateForm?.value?.agreementValue;\r\n    const invalidLeadNameMessage = 'Lead Name is invalid, Please Rename to continue';\r\n    if (!this.isBulkUpdate) {\r\n      if (!this.leadInfo?.name?.trim()) {\r\n        this._notificationsService?.warn(invalidLeadNameMessage);\r\n        return;\r\n      }\r\n    } else {\r\n      for (const lead of this.leadInfo) {\r\n        if (!lead?.name?.trim()) {\r\n          this._notificationsService?.warn(invalidLeadNameMessage);\r\n          return;\r\n        }\r\n      }\r\n    }\r\n    if (this.updateForm.invalid) {\r\n      validateAllFormFields(this.updateForm);\r\n      return;\r\n    }\r\n    if (this.isUpload) {\r\n      this.addDocument();\r\n    }\r\n    if (this.navigateLink && this.updateForm.value.shareContent !== 'Properties') {\r\n      window.location.href = this.navigateLink;\r\n    } else if (this.whatsAppShare) {\r\n      window.open(this.whatsAppShare, '_blank');\r\n    }\r\n\r\n    if (this.uploadedFiles?.length && this.uploadedFiles?.[0]?.includes('data:')) {\r\n      this.uploadDocumentsToS3(isSaveAndNext);\r\n      return;\r\n    }\r\n    this.proceedWithStatusUpdate(isSaveAndNext);\r\n  }\r\n  // Helper methods to keep the main method clean and readable\r\n  prepareAddressPayload(userData: any): any {\r\n    return (\r\n      (this.showLocationSearch ? userData?.locationId : this.manualLocationsList) || this.leadInfo?.locationId\r\n    )?.map((location: any) => ({\r\n      locationId: location?.id ?? location?.id,\r\n      placeId: location?.placeId ?? location?.placeId,\r\n      subLocality: (location?.enquiredLocality ?? location?.enquiredLocality) || (location?.locality ?? location?.locality),\r\n      city: (location?.enquiredCity ?? location?.enquiredCity) || (location?.city ?? location?.city),\r\n      state: (location?.enquiredState ?? location?.enquiredState) || (location?.state ?? location?.state),\r\n    }));\r\n  }\r\n  createPayload(userData: any, addressPayload: any, isAddressPayloadEmpty: boolean, postPonedStatusId: string): any {\r\n    return {\r\n      id: this.leadInfo.id,\r\n      leadStatusId: userData.reason || userData.leadStatus || this.leadInfo.leadStatusId,\r\n      scheduledDate: setTimeZoneDateWithTime(userData.ScheduledDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset) || undefined,\r\n      bookedDate: setTimeZoneDateWithTime(userData.bookedDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset) || undefined,\r\n      rating: userData.rating || this.leadInfo.rating,\r\n      notes: userData.notes ? userData.notes : this.leadInfo.notes,\r\n      IsNotesUpdated: userData.notes ? true : false,\r\n      bookedUnderName: userData?.bookedUnderName,\r\n      agreementValue: userData?.agreementValue,\r\n      soldPrice: userData.soldPrice,\r\n      soldPriceCurrency: userData.soldPriceCurrency,\r\n      unmatchedBudget: userData.leadExpectedBudget || this.leadInfo.unmatchedBudget || undefined,\r\n      purchasedFrom: userData.purchasedFromWhom || this.leadInfo.purchasedFrom,\r\n      addresses: isAddressPayloadEmpty ? null : addressPayload,\r\n      postponedDate: userData.reason === postPonedStatusId ? setTimeZoneDateWithTime(userData.ScheduledDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset) : setTimeZoneDateWithTime(userData?.revertDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset) || undefined,\r\n      propertiesList: userData?.Properties ? userData?.Properties : this.leadInfo?.properties?.map((proper: any) => proper.title) || [],\r\n      projectsList: userData?.Projects ? userData?.Projects : this.leadInfo?.projects?.map((proje: any) => proje.name) || [],\r\n      projectIds: userData?.chosenProject ? [userData.chosenProject] : null,\r\n      unitTypeId: userData?.chosenUnit ?? null,\r\n      propertyIds: userData?.chosenProperty ? [userData.chosenProperty] : null,\r\n      currency: userData.currency,\r\n    };\r\n  }\r\n  handleProjectPropertySelection(payload: any): void {\r\n    if (!payload.propertiesList) payload.propertiesList = [];\r\n    if (!payload.projectsList) payload.projectsList = [];\r\n\r\n    if (this.selectedProject.isprojectproperty === 'Property') {\r\n      payload.propertiesList = [...new Set([...payload.propertiesList, this.selectedProject.selectedpropertyname])];\r\n    } else if (this.selectedProject.isprojectproperty === 'Project') {\r\n      payload.projectsList = [...new Set([...payload.projectsList, this.selectedProject.selectedpropertyname])];\r\n    }\r\n  }\r\n  assignUsers(userData: any, payload: any): void {\r\n    payload.assignTo = userData.assignedToUserId || this.leadInfo?.assignTo || EMPTY_GUID;\r\n    payload.secondaryUserId = userData.secondaryAssignTo || this.leadInfo.secondaryUserId || EMPTY_GUID;\r\n  }\r\n  handleLeadStatus(payload: any): void {\r\n    if (\r\n      this.leadInfo?.status?.status === 'meeting_scheduled' ||\r\n      this.leadInfo?.status?.status === 'site_visit_scheduled'\r\n    ) {\r\n      payload.isFullyCompleted = true;\r\n      payload.projects = this.leadInfo?.projects?.map((project: any) => typeof project === 'string' ? project : project?.name);\r\n      payload.properties = this.leadInfo?.properties?.map((property: any) => property?.title);\r\n    }\r\n  }\r\n\r\n  handleSingleUpdate(payload: any): void {\r\n    this.leadStatusIsLoading = true;\r\n    this._store.dispatch(new UpdateLeadStatus(payload, this.leadInfo.id, false, false));\r\n    if (!this.canShowStatusPopupInPreview) {\r\n      this._store.dispatch(new LeadPreviewChanged());\r\n    }\r\n    // Close modal when response is received\r\n    this._store.select(getLeadStatusIsLoading).pipe(\r\n      skipWhile((isLoading) => isLoading),\r\n      take(1)\r\n    ).subscribe((isLoading: boolean) => {\r\n      this.leadStatusIsLoading = false;\r\n      if (!isLoading) {\r\n        if (!this.isSaveAndNext) {\r\n          this.modalRef.hide();\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  handleBulkUpdate(userData: any, addressPayload: any, isAddressPayloadEmpty: boolean, postPonedStatusId: string, documents?: any[]): void {\r\n    this.multipleLeadsIsLoading = true;\r\n    if (!userData.leadStatus) {\r\n      this.modalService.hide();\r\n      return;\r\n    }\r\n    const notBookedLeads = this.leadInfo.filter((lead: any) => !lead.status?.shouldUseForBooking);\r\n    const leadItems = this.isSelectedOnlySomeBooked() ? notBookedLeads : this.leadInfo;\r\n    const bulkPayload = {\r\n      ...this.createBulkPayload(leadItems, userData, addressPayload, isAddressPayloadEmpty, postPonedStatusId, documents),\r\n    };\r\n    this._store.dispatch(new UpdateMultipleLead(bulkPayload, true));\r\n    this._store.select(getMultipleLeadStatusIsLoading).pipe(\r\n      skipWhile((isLoading) => isLoading),\r\n      take(1)\r\n    ).subscribe((isLoading: boolean) => {\r\n      this.multipleLeadsIsLoading = isLoading;\r\n      this.modalService.hide();\r\n    });\r\n  }\r\n  createBulkPayload(lead: any, userData: any, addressPayload: any, isAddressPayloadEmpty: boolean, postPonedStatusId: string, documents?: any[]): any {\r\n    const ids = lead.map((leadData: any) => leadData.id);\r\n    const ArrayLeadIds = ids.flat();\r\n    const payloadObj: any = {\r\n      LeadIds: ArrayLeadIds,\r\n      leadStatusId: userData?.reason || userData?.leadStatus,\r\n      rating: userData?.rating,\r\n      notes: userData?.notes,\r\n      scheduledDate: userData.ScheduledDate || undefined,\r\n      bookedDate: userData?.bookedDate || null,\r\n      revertDate: userData?.revertDate || null,\r\n      unmatchedBudget: userData?.leadExpectedBudget || null,\r\n      purchasedFrom: userData?.purchasedFromWhom,\r\n      bookedUnderName: userData.bookedUnderName,\r\n      agreementValue: userData.agreementValue || null,\r\n      soldPrice: userData.soldPrice,\r\n      soldPriceCurrency: userData.soldPriceCurrency,\r\n      postponedDate: userData?.reason === postPonedStatusId ? userData.ScheduledDate : userData?.revertDate || undefined,\r\n      addresses: isAddressPayloadEmpty ? null : addressPayload,\r\n      propertiesList: userData?.Properties ? userData?.Properties : [],\r\n      projectsList: userData?.Projects ? userData?.Projects : [],\r\n      projectIds: userData?.chosenProject ? [userData.chosenProject] : null,\r\n      propertyIds: userData?.chosenProperty ? [userData.chosenProperty] : null,\r\n      unitTypeId: userData?.chosenUnit ?? null,\r\n    };\r\n    if (documents?.length) {\r\n      payloadObj.documents = documents;\r\n    }\r\n\r\n    if (!payloadObj.propertiesList) payloadObj.propertiesList = [];\r\n    if (!payloadObj.projectsList) payloadObj.projectsList = [];\r\n\r\n    if (this.selectedProject) {\r\n      if (this.selectedProject.isprojectproperty === 'Property') {\r\n        payloadObj.propertiesList = [...new Set([...payloadObj.propertiesList, this.selectedProject.selectedpropertyname])];\r\n      } else if (this.selectedProject.isprojectproperty === 'Project') {\r\n        payloadObj.projectsList = [...new Set([...payloadObj.projectsList, this.selectedProject.selectedpropertyname])];\r\n      }\r\n    }\r\n    lead.forEach((leadData: any) => {\r\n      if (leadData?.status?.status === 'meeting_scheduled' || leadData?.status?.status === 'site_visit_scheduled') {\r\n        payloadObj.isFullyCompleted = true;\r\n        // payloadObj.projects = leadData?.projects?.map((project: any) => project?.name);\r\n        // payloadObj.properties = leadData?.properties?.map((property: any) => property?.title);\r\n      }\r\n      payloadObj.assignTo = userData.assignedToUserId || null;\r\n      payloadObj.secondaryUserId = userData.secondaryAssignTo || null;\r\n    });\r\n    const intervalId = setInterval(() => {\r\n      if (this.modalService.getModalsCount() === 0) {\r\n        const numberOfLeads = payloadObj?.LeadIds?.length;\r\n        if (numberOfLeads >= 50) {\r\n          this.modalRef = this.modalService.show(\r\n            this.trackerInfoModal,\r\n            Object.assign(\r\n              {},\r\n              {\r\n                class: 'modal-400 top-modal ph-modal-unset',\r\n                ignoreBackdropClick: true,\r\n                keyboard: false,\r\n              }\r\n            )\r\n          );\r\n        }\r\n      }\r\n      clearInterval(intervalId);\r\n    }, 2000);\r\n    return payloadObj;\r\n  }\r\n\r\n  updateLeadInfo(userData: any, payload: any): void {\r\n    this._store.select(getLeadStatusIsLoading).pipe(\r\n      skipWhile((isLoading) => isLoading),\r\n      take(1)\r\n    ).subscribe((isLoading: boolean) => {\r\n      let masterLeadStatus = JSON.parse(localStorage.getItem('masterleadstatus'));\r\n      masterLeadStatus = this.customStatusList;\r\n      const id = userData.reason || userData.leadStatus || this.leadInfo.leadStatusId;\r\n      const status = masterLeadStatus.find((item: any) => item.id === id || item.childTypes.some((child: any) => child.id === id));\r\n      this.leadInfo = {\r\n        ...this.leadInfo,\r\n        status: {\r\n          ...this.leadInfo.status,\r\n          displayName: status.displayName || this.leadInfo.status.displayName\r\n        },\r\n        rating: userData.rating,\r\n        projects: userData.projectsList,\r\n        properties: userData.propertiesList,\r\n        notes: userData.notes,\r\n        scheduledDate: userData.ScheduledDate,\r\n        assignTo: userData.assignedToUserId || this.leadInfo.assignTo || EMPTY_GUID,\r\n        secondaryUserId: userData.secondaryAssignTo || this.leadInfo.secondaryUserId || EMPTY_GUID\r\n      };\r\n      // Handle sub-status display name\r\n      let hasSubStatus = false;\r\n      for (let reason of this.callBackReason) {\r\n        if (this.updateForm?.controls?.['reason']?.value === reason?.id && this.leadInfo?.status?.childType) {\r\n          this.leadInfo.status.childType = {\r\n            ...this.leadInfo.status.childType,\r\n            displayName: reason.displayName\r\n          };\r\n          hasSubStatus = true;\r\n          break;\r\n        }\r\n      }\r\n      if (!hasSubStatus) {\r\n        this.leadInfo.status.childType = {\r\n          ...this.leadInfo.status.childType,\r\n          displayName: ''\r\n        };\r\n      }\r\n    });\r\n  }\r\n\r\n  openBulkUpdatedStatus() {\r\n    this._store.dispatch(new FetchBulkOperation(1, 10, 'lead'));\r\n\r\n    this.modalRef = this.modalService.show(BulkOperationTrackerComponent, {\r\n      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',\r\n      initialState: {\r\n        moduleType: 'lead',\r\n      },\r\n    });\r\n  }\r\n\r\n\r\n  cleanStatusForm(): void {\r\n    this.deselectStatuses();\r\n    this.resetDocumentState();\r\n\r\n    // this.updateForm.controls['scheduledDate'].setValue('');\r\n    // this.updateForm.controls['notes'].setValue('');\r\n    // this.updateForm.controls['rating'].setValue('');\r\n    // this.updateForm.controls['projectsList'].setValue([]);\r\n    // this.updateForm.controls['propertiesList'].setValue([]);\r\n  }\r\n\r\n  updateSelectedReason(reason: any) {\r\n    let customFields = [...reason?.customFields]?.sort(\r\n      (a: any, b: any) => a?.field?.orderRank - b?.field?.orderRank\r\n    );\r\n    reason = {\r\n      ...reason,\r\n      customFields,\r\n    };\r\n    this.selectedReason = reason;\r\n  }\r\n\r\n  closeModal() {\r\n    this.isVisitDone = false;\r\n    this.modalRef.hide();\r\n    this.updateForm.reset();\r\n    this.hideStatus = false;\r\n    this.selectedReason = null;\r\n  }\r\n\r\n  fullBookingFormModal() {\r\n    if (this.updateForm.invalid) {\r\n      validateAllFormFields(this.updateForm);\r\n      return;\r\n    }\r\n    if (this.updateForm.value.notes) {\r\n      this.selectedProject.notes = this.updateForm.value.notes;\r\n    }\r\n    this.isShowBookingFormBtn = false;\r\n    this.updateStatus(true);\r\n    this._store\r\n      .select(getManagerDetails)\r\n      .pipe(take(1))\r\n      .subscribe((data: any) => {\r\n        this.userDetails = data;\r\n        let initialState: any = {\r\n          selectedProject: this.selectedProject,\r\n          leadInfo: this.leadInfo,\r\n          userDetails: this.userDetails,\r\n          miniBookDate: this.updateForm.get('bookedDate')?.value,\r\n        };\r\n        this.modalRef = this.modalService.show(BookingFormComponent, {\r\n          class: 'right-modal modal-550 ip-modal-unset',\r\n          initialState,\r\n        });\r\n        this.modalRef.onHide.subscribe(() => {\r\n          this.isShowBookingFormBtn = true;\r\n        });\r\n      });\r\n  }\r\n\r\n  switchTabProjectProperty(value: any) {\r\n    if (value === 'Property') {\r\n      this.isShowUnitInfoField = false;\r\n      this.updateForm.controls['projectsList']?.setValue(null);\r\n      this.updateForm.controls['chosenProject']?.setValue(null);\r\n      toggleValidation(VALIDATION_SET, this.updateForm, 'chosenProperty', [\r\n        Validators.required,\r\n      ]);\r\n      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProject');\r\n      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenUnit');\r\n    } else {\r\n      this.isShowUnitInfoField = true;\r\n      this.updateForm.controls['chosenProperty']?.setValue(null);\r\n      if (this.isProjectMandatory) {\r\n        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenProject', [\r\n          Validators.required,\r\n        ]);\r\n        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenUnit', [\r\n          Validators.required,\r\n        ]);\r\n      }\r\n      toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProperty');\r\n    }\r\n  }\r\n\r\n  onInputAgreementValue(value: number) {\r\n    this.selectedProject.agreementValue = value;\r\n  }\r\n\r\n  onPropertyChange(id: string) {\r\n    toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProject');\r\n    toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenUnit');\r\n    this.isSelectedPropertyOrProject = 'Property';\r\n    this.isShowUnitInfoField = false;\r\n    if (id) {\r\n      this._store.dispatch(new FetchPropertyById(id));\r\n    }\r\n    this._store\r\n      .select(getPropertyListDetails)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        if (data) {\r\n          this.selectedProject.selectedpropertyname = data.title;\r\n          this.selectedProject.buildername = data.ownerDetails?.name;\r\n          this.selectedProject.sealablearea = data.dimension?.saleableArea;\r\n          this.selectedProject.carpetarea = data.dimension?.carpetArea;\r\n          this.selectedProject.brokerageCurrency =\r\n            data.monetaryInfo?.brokerageCurrency;\r\n          this.selectedProject.brockragecharge = data.monetaryInfo?.brokerage;\r\n          this.selectedProject.isprojectproperty =\r\n            this.isSelectedPropertyOrProject;\r\n        }\r\n      });\r\n  }\r\n\r\n  onProjectChange(id: string) {\r\n    if (id) {\r\n      this._store.dispatch(new FetchProjectById(id));\r\n      if (this.isProjectMandatory) {\r\n        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenUnit', [\r\n          Validators.required,\r\n        ]);\r\n        toggleValidation(VALIDATION_SET, this.updateForm, 'chosenProject', [\r\n          Validators.required,\r\n        ]);\r\n      } else {\r\n        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenUnit');\r\n        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProject');\r\n      }\r\n    }\r\n    toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProperty');\r\n    this.isSelectedPropertyOrProject = 'Project';\r\n    this.isShowUnitInfoField = true;\r\n    this.updateForm.get('chosenUnit')?.reset();\r\n    combineLatest([\r\n      this._store.select(getIsProjectByIdLoading),\r\n      this._store.select(getSelectedProjectById),\r\n    ])\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe(([isLoading, projectData]: [boolean, any]) => {\r\n        this.isUnitInfoDataLoading = isLoading;\r\n        if (projectData) {\r\n          this.unitInfo = projectData.unitTypes;\r\n          this.selectedProject = {\r\n            selectedpropertyname: projectData.name,\r\n            buildername: projectData.builderDetail?.name,\r\n            brockragecharge: projectData.monetaryInfo?.brokerage,\r\n            brokerageCurrency: projectData.monetaryInfo?.brokerageCurrency,\r\n            isprojectproperty: this.isSelectedPropertyOrProject,\r\n            allprojectdata: projectData,\r\n          };\r\n        }\r\n      });\r\n  }\r\n\r\n  onChoosenUnitChange(event: any) {\r\n    this.unitInfo?.map((data: any) => {\r\n      if (data.id === event?.id) {\r\n        this.selectedProject.carpetarea = data?.carpetArea;\r\n        this.selectedProject.sealablearea = data?.superBuildUpArea;\r\n        this.selectedProject.unitname = data?.name;\r\n      }\r\n    });\r\n  }\r\n\r\n  patchMiniBookDataIfBooked(id: string) {\r\n    id && this._store.dispatch(new FetchInvoiceById(id));\r\n    this._store.select(getBookingData)?.subscribe((bookingDetails: any) => {\r\n      if (!bookingDetails) return;\r\n      const {\r\n        projects,\r\n        properties,\r\n        unitType,\r\n        bookedUnderName,\r\n        agreementValue,\r\n        notes,\r\n        currency,\r\n      } = bookingDetails;\r\n      const hasProjects = Array.isArray(projects) && projects.length > 0;\r\n      const hasProperties = Array.isArray(properties) && properties.length > 0;\r\n      if (hasProjects) this.onProjectChange(projects[0].id);\r\n      if (hasProperties) this.onPropertyChange(properties[0].id);\r\n      const validAgreementValue = agreementValue > 0 ? agreementValue : null;\r\n      this.selectedProject.agreementValue = validAgreementValue;\r\n      this.updateForm.patchValue({\r\n        reason: this.leadInfo?.status?.childType?.id,\r\n        leadStatus: this.leadInfo?.status?.id,\r\n        bookedDate: patchTimeZoneWithTime(bookingDetails.bookedDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),\r\n        bookedUnderName,\r\n        agreementValue: validAgreementValue,\r\n        chosenProperty: hasProperties ? properties[0]?.id : null,\r\n        chosenProject: hasProjects ? projects[0]?.id : null,\r\n        chosenUnit: unitType?.id,\r\n        notes,\r\n        currency,\r\n        assignedToUserId: this.leadInfo?.assignTo,\r\n        secondaryAssignTo: this.leadInfo?.secondaryUserId !== EMPTY_GUID ? this.leadInfo?.secondaryUserId : null,\r\n      });\r\n      this.selectedReason = this.leadInfo?.status?.childType?.displayName;\r\n      const projectProperty = hasProjects ? 'Project' : hasProperties ? 'Property' : 'Property';\r\n      this.updateForm?.get('projectProperty')?.setValue(projectProperty);\r\n\r\n      if (projectProperty === 'Project') {\r\n        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProperty');\r\n      } else if (projectProperty === 'Property') {\r\n        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenProject');\r\n        toggleValidation(VALIDATION_CLEAR, this.updateForm, 'chosenUnit');\r\n      }\r\n\r\n      if (unitType?.id) this.onChoosenUnitChange(unitType.id);\r\n      this.miniagreementValueInWords = formatBudget(agreementValue, currency || this.defaultCurrency);\r\n    });\r\n  }\r\n\r\n  isSelectedAllBookedOrInvoicedLead(): boolean {\r\n    if (!Array.isArray(this.leadInfo)) {\r\n      return false;\r\n    }\r\n    if (this.leadInfo.length === 0) {\r\n      return false;\r\n    }\r\n    const allBookedOrInvoiced = this.leadInfo.every(\r\n      (lead: any) =>\r\n        lead?.status?.shouldUseForBooking ||\r\n        lead?.status?.shouldUseForInvoice\r\n    );\r\n    return allBookedOrInvoiced;\r\n  }\r\n\r\n  checkBookedUnderName(event: any) {\r\n    const inputValue = event.target.value.trim();\r\n    if (inputValue) {\r\n      this.updateForm.controls['bookedUnderName'].setValue(inputValue);\r\n      this.updateForm.controls['bookedUnderName'].setErrors(null);\r\n    } else {\r\n      this.updateForm.controls['bookedUnderName'].setErrors({ whitespace: true });\r\n    }\r\n  }\r\n\r\n  onFileSelection(e: any) {\r\n    this.selectedFile = e[0];\r\n  }\r\n\r\n  addDocument() {\r\n    if (!this.documentsForm.valid || !this.selectedFile) {\r\n      validateAllFormFields(this.documentsForm);\r\n      return;\r\n    }\r\n    this.uploadedFiles.push(this.selectedFile);\r\n    this.uploadedFilesName.push(\r\n      this.documentsForm.value.docTitle || this.selectedFileName\r\n    );\r\n    this.isUpload = false;\r\n    this.documentsForm.reset();\r\n    this.selectedFile = null;\r\n  }\r\n\r\n  onClickRemoveDocument(docIndex: number) {\r\n    this.uploadedFiles = this.uploadedFiles.filter(\r\n      (_, index) => index !== docIndex\r\n    );\r\n    this.uploadedFilesName = this.uploadedFilesName.filter(\r\n      (_, index) => index !== docIndex\r\n    );\r\n  }\r\n\r\n  resetDocumentState() {\r\n    this.uploadedFiles = [];\r\n    this.uploadedFilesName = [];\r\n    this.isUpload = false;\r\n    this.selectedFile = '';\r\n    this.selectedFileName = null;\r\n    this.documentsForm.reset();\r\n  }\r\n\r\n  uploadDocumentsToS3(isSaveAndNext: boolean = false) {\r\n    this.s3UploadService\r\n      .uploadImageBase64(this.uploadedFiles, FolderNamesS3.LeadDocument)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((response: any) => {\r\n        if (response.data.length) {\r\n          let documents: any = [];\r\n          this.uploadedFilesName?.forEach((name: string, index: number) => {\r\n            documents.push({\r\n              documentName: name,\r\n              filePath: response.data[index].toString(),\r\n            });\r\n          });\r\n          this.proceedWithStatusUpdate(isSaveAndNext, documents);\r\n        }\r\n      });\r\n  }\r\n\r\n  proceedWithStatusUpdate(isSaveAndNext: boolean = false, documents?: any[]) {\r\n    const userData = this.updateForm.value;\r\n    let postPonedStatusId = '';\r\n    this.addMoreLocation();\r\n    const addressPayload = this.prepareAddressPayload(userData);\r\n    const isAddressPayloadEmpty = !addressPayload?.length;\r\n    const payload = this.createPayload(userData, addressPayload, isAddressPayloadEmpty, postPonedStatusId);\r\n    if (documents?.length) {\r\n      payload.documents = documents;\r\n    }\r\n    if (this.selectedProject) {\r\n      this.handleProjectPropertySelection(payload);\r\n    }\r\n    this.assignUsers(userData, payload);\r\n    this.handleLeadStatus(payload);\r\n    if (!this.isBulkUpdate) {\r\n      this.handleSingleUpdate(payload);\r\n    } else {\r\n      this.handleBulkUpdate(userData, addressPayload, isAddressPayloadEmpty, postPonedStatusId, documents);\r\n    }\r\n    this.updateLeadInfo(userData, payload);\r\n    if (!this.isBulkUpdate) {\r\n      this.cleanStatusForm();\r\n    }\r\n    if (!isSaveAndNext) {\r\n      if (!this.isBulkUpdate) {\r\n        this._store.select(getLeadStatusIsLoading).pipe(\r\n          skipWhile((isLoading) => isLoading),\r\n          take(1)\r\n        ).subscribe((isLoading: boolean) => {\r\n          if (!isLoading) {\r\n            this.modalRef.hide();\r\n          }\r\n        });\r\n      } else {\r\n        this.modalRef.hide();\r\n      }\r\n      return;\r\n    }\r\n    this._leadPreviewComponent.nextData();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.stopper.next();\r\n    this.stopper.complete();\r\n    this.isProjectSubscription.unsubscribe();\r\n  }\r\n}\r\n", "<div class=\"scrollbar\" id=\"status-form\" #statusForm\r\n  [ngClass]=\"{'pe-none blinking': (leadStatusIsLoading || multipleLeadsIsLoading), 'h-100-333 ph-h-100-358': !elementHeight && !whatsAppComp, 'h-100-460' : !elementHeight && whatsAppComp}\">\r\n  <div *ngIf=\"!isBulkUpdate\">\r\n    <div class=\"fw-600 text-large text-slate-160 ml-20 fv-sm-caps\">{{'GLOBAL.current' | translate}}\r\n      {{'GLOBAL.lead' | translate}} {{'GLOBAL.status' | translate}}</div>\r\n    <div class=\"bg-secondary mx-20 mb-20 mt-10 px-16 py-12 br-4\">\r\n      <div class=\"align-center w-100\">\r\n        <div class=\"align-center w-50\">\r\n          <span class=\"icon ic-person-walking ic-slate-90 ic-xxs mr-8\"></span>\r\n          <h5 class=\"fv-sm-caps fw-600\">{{ leadInfo?.status?.displayName }}<span\r\n              *ngIf=\"leadInfo?.status?.childType?.displayName\"> - {{leadInfo.status.childType.displayName}}</span></h5>\r\n        </div>\r\n        <div class=\"align-center w-50\" *ngIf=\"leadInfo?.scheduledDate\">\r\n          <span class=\"icon ic-alarm ic-slate-90 ic-xxs mr-8\"></span>\r\n          <div>\r\n            <h5 class=\"fv-sm-caps fw-600\">{{leadInfo?.scheduledDate ?\r\n              getTimeZoneDate(leadInfo?.scheduledDate, userBasicDetails?.timeZoneInfo?.baseUTcOffset, 'dateWithTime'):\r\n              '---'}}</h5>\r\n            <div class=\"text-truncate-1 break-all text-sm\"\r\n              *ngIf=\"userBasicDetails?.timeZoneInfo?.timeZoneName && leadInfo?.scheduledDate &&  userBasicDetails?.shouldShowTimeZone\">\r\n              ({{userBasicDetails?.timeZoneInfo?.timeZoneName }})\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"d-flex mt-16\" *ngIf=\"leadInfo?.notes\">\r\n        <span class=\"icon ic-message-lines ic-slate-90 ic-xxs mr-8\"></span>\r\n        <p class=\"text-black-20 text-sm word-break max-w-460\"\r\n          [ngClass]=\"leadInfo?.notes?.length > 170 && !isReadMore ? 'text-truncate-2' : ''\">{{leadInfo?.notes ?\r\n          leadInfo?.notes:'---' }}\r\n        </p>\r\n      </div>\r\n      <div class=\"flex-center w-100 cursor-pointer mt-16\" *ngIf=\"leadInfo?.notes?.length > 170\"\r\n        (click)=\"isReadMore = !isReadMore\">\r\n        <span class=\"text-sm fw-600 text-accent-green\">read {{isReadMore ? 'less' : 'more'}}</span>\r\n        <span class=\"icon ic-chevron-down ic-xxxs ic-accent-green ml-8 mt-2\"\r\n          [ngClass]=\"{'rotate-180': isReadMore}\"></span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div class=\"text-red ml-20 mb-10\" *ngIf=\"isUnassignedLead\">{{'LEADS.lead-unassigned-status' | translate}}</div>\r\n\r\n  <div class=\"pr-8 ml-20\">\r\n    <form [formGroup]=\"updateForm\" autocomplete=\"off\"\r\n      [ngClass]=\"{ 'pe-none': !canUpdateStatus || isUnassignedLead, 'grid-blur': isUnassignedLead}\">\r\n      <div>\r\n        <div class=\"d-flex flex-wrap\" *ngIf=\"canUpdateStatus && !isCustomStatusListLoading else ratLoader\">\r\n          <ng-container *ngIf=!hideStatus>\r\n            <ng-container *ngIf=!canShowStatusPopupInPreview>\r\n              <div *ngIf=\"currentPath !== '/invoice'\" [ngClass]=\"{'d-none': !isSelectedOnlySomeBooked()}\"\r\n                class=\"text-red my-10\">\r\n                Update the leads, except for\r\n                <span *ngIf=\"bookedAndInvoiceStatus?.length > 0\">\r\n                  {{ bookedAndInvoiceStatus.join(', ') }}\r\n                </span>\r\n                as these leads cannot be updated further.\r\n              </div>\r\n              <ng-container *ngFor=\"let customStatus of customStatusListFiltered; let i = index\">\r\n                <ng-container>\r\n                  <input formControlName=\"leadStatus\" type=\"radio\" class=\"btn-check\" [value]=\"customStatus.id\"\r\n                    id=\"leadOption{{i}}\" data-automate-id=\"leadOption{{i}}\" autocomplete=\"off\" required>\r\n                  <label class=\"status-badge\" for=\"leadOption{{i}}\" (click)=\"showReasons(customStatus)\"\r\n                    [class.active]=\"updateForm?.controls['leadStatus']?.value === customStatus.id\">\r\n                    {{ customStatus.actionName | titlecase }}\r\n                  </label>\r\n                </ng-container>\r\n              </ng-container>\r\n              <ng-container\r\n                *ngIf=\"updateForm?.controls['leadStatus']?.dirty || updateForm?.controls['leadStatus']?.touched\">\r\n                <div class=\"flex-end w-100 text-error-red\" *ngIf=\"updateForm?.controls['leadStatus']?.errors?.required\">\r\n                  Status is a required field.\r\n                </div>\r\n              </ng-container>\r\n            </ng-container>\r\n            <div class=\"w-100 flex-center\" *ngIf=\"canShowStatusPopupInPreview\">\r\n              <img src=\"../../../../assets/gifs/muso-walk.gif\" alt=\"Muso Walking\" class=\"h-100-450\" />\r\n            </div>\r\n          </ng-container>\r\n          <div *ngIf=hideStatus>\r\n            <div class=\"align-center mr-4 mb-4 w-100\">\r\n              <div class=\"m-0 status-badge bg-dark-700 text-white br-5\">{{ selectedStatus?.displayName | titlecase }}\r\n              </div><a class=\"icon ic-close-secondary ic-light-pale ic-xxs ml-10\" id=\"clkCancelSelectedBadge\"\r\n                data-automate-id=\"clkCancelSelectedBadge\" (click)=\"deselectStatuses()\"></a>\r\n            </div>\r\n            <div class=\"p-12 position-relative min-w-137\">\r\n              <ng-container *ngFor=\"let reason of callBackReason; let i = index\">\r\n                <input type=\"radio\" class=\"btn-check\" [value]=\"reason.id\" id=\"option{{i}}\"\r\n                  (click)=\"updateSelectedReason(reason); reasonChanged(reason);\" data-automate-id=\"option{{i}}\"\r\n                  autocomplete=\"off\" formControlName=\"reason\">\r\n                <label class=\"status-badge\" [class.active]=\"updateForm?.controls['reason']?.value === reason.id\"\r\n                  for=\"option{{i}}\">\r\n                  {{ reason.displayName }}\r\n                </label>\r\n              </ng-container>\r\n              <ng-container *ngIf=\"updateForm?.controls['reason']?.dirty || updateForm?.controls['reason']?.touched\">\r\n                <div class=\"error-message\" *ngIf=\"updateForm?.controls['reason']?.errors?.required\">\r\n                  Sub-Status is a required field.\r\n                </div>\r\n              </ng-container>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <ng-container *ngIf=\"leadSource\">\r\n          <ng-container\r\n            *ngIf=\"['Referral', 'Walk In', 'Direct'].includes(leadSource) && (leadInfo?.leadStatus?.baseStatus == 'New')\">\r\n            <label class=\"checkbox-container mt-10\">{{\"LEAD_FORM.meeting\" | translate}} {{\"LEAD_FORM.done\" |\r\n              translate}}\r\n              <input type=\"checkbox\" class=\"mr-10\" id=\"inpMeetingDone\" data-automate-id=\"inpMeetingDone\"\r\n                [checked]=\"isMeetingDone\"\r\n                (change)=\"isMeetingDone?isMeetingDone = false:isMeetingDone = true; patchFormControlValue(updateForm, 'leadStatus' , currentLeadStatus['meeting-done'])\">\r\n              <span class=\"checkmark\"></span>\r\n            </label>\r\n            <label class=\"checkbox-container mt-10\">{{\"LEAD_FORM.visit\" | translate}} {{\"LEAD_FORM.done\" |\r\n              translate}}\r\n              <input type=\"checkbox\" class=\"mr-10\" id=\"inpVisitDone\" data-automate-id=\"inpVisitDone\"\r\n                [checked]=\"isVisitDone\"\r\n                (change)=\"isVisitDone?isVisitDone = false:isVisitDone = true; patchFormControlValue(updateForm, 'leadStatus' , currentLeadStatus['visit-done'])\">\r\n              <span class=\"checkmark\"></span>\r\n            </label>\r\n          </ng-container>\r\n        </ng-container>\r\n\r\n        <ng-template #customForm>\r\n          <ng-container\r\n            *ngFor=\"let field of selectedReason?.customFields?.length ? selectedReason?.customFields : selectedStatus?.customFields\">\r\n            <ng-container [ngSwitch]=\"field?.field?.name\">\r\n              <ng-container *ngSwitchCase=\"'TotalSoldPrice'\">\r\n                <div class=\"position-relative\">\r\n                  <div [ngClass]=\"field?.validators?.includes('required') ? 'field-label-req' : 'field-label'\">\r\n                    {{\"LEAD_FORM.sold-price\"\r\n                    | translate}}</div>\r\n                  <form-errors-wrapper [control]=\"updateForm?.controls['soldPrice']\"\r\n                    label=\"{{'LEAD_FORM.sold-price' | translate}}\">\r\n                    <input type=\"text\" id=\"soldPrice\" data-automate-id=\"soldPrice\" formControlName=\"soldPrice\"\r\n                      (keydown)=\"onlyNumbers($event)\" placeholder=\"ex. 4000000\" maxlength=\"10\">\r\n                  </form-errors-wrapper>\r\n                  <div *ngIf=\"updateForm?.controls['soldPrice']?.value\"\r\n                    class=\"position-absolute right-12 bottom-12 text-accent-green fw-semi-bold text-sm\"><span\r\n                      class=\"icon ic-rupee ic-accent-green ic-x-xs mr-4\"></span>{{soldPriceInWords}}</div>\r\n                </div>\r\n              </ng-container>\r\n              <ng-container *ngSwitchCase=\"'ScheduledDate'\">\r\n                <div class=\"position-relative\">\r\n                  <div [ngClass]=\"field?.validators?.includes('required') ? 'field-label-req' : 'field-label'\">\r\n                    {{'LEAD_FORM.schedule-date'\r\n                    | translate}}</div>\r\n                  <form-errors-wrapper [control]=\"updateForm?.controls['ScheduledDate']\"\r\n                    label=\"{{'LEAD_FORM.schedule-date' | translate}}\">\r\n                    <input [owlDateTime]=\"dt1\" [owlDateTimeTrigger]=\"dt1\"\r\n                      [min]=\"!isPastDateSelectionEnabled ? minDate : null\" readonly id=\"inpAppDateTime\"\r\n                      data-automate-id=\"inpAppDateTime\" formControlName=\"ScheduledDate\"\r\n                      placeholder=\"ex. 19/06/2025, 12:00 pm\">\r\n                    <owl-date-time #dt1 [hour12Timer]=\"'true'\" (afterPickerOpen)=\"onPickerOpened(currentDate)\"\r\n                      [startAt]=\"currentDate\"></owl-date-time>\r\n                  </form-errors-wrapper>\r\n                </div>\r\n              </ng-container>\r\n              <ng-container *ngSwitchCase=\"'Projects'\">\r\n                <div class=\"position-relative\">\r\n                  <div [ngClass]=\"field?.validators?.includes('required') ? 'field-label-req' : 'field-label'\">\r\n                    Project(s)</div>\r\n                  <form-errors-wrapper [control]=\"updateForm?.controls['Projects']\" label=\"Projects\">\r\n                    <ng-select [virtualScroll]=\"true\" [items]=\"projectList\" [multiple]=\"true\" [closeOnSelect]=\"false\"\r\n                      ResizableDropdown [addTag]=\"true\" name=\"Projects\" formControlName=\"Projects\" bindLabel=\"name\"\r\n                      bindValue=\"name\" addTagText=\"Create New Project\" class=\"bg-white\"\r\n                      placeholder=\"Select/Create Project\">\r\n                      <ng-template ng-option-tmp let-item=\"item\" let-item$=\"item$\" let-index=\"index\">\r\n                        <div class=\"checkbox-container\"><input type=\"checkbox\" id=\"item-{{index}}\"\r\n                            data-automate-id=\"item-{{index}}\" [checked]=\"item$.selected\"><span\r\n                            class=\"checkmark\"></span>{{item.name}}\r\n                        </div>\r\n                      </ng-template>\r\n                    </ng-select>\r\n                  </form-errors-wrapper>\r\n                </div>\r\n              </ng-container>\r\n              <ng-container *ngSwitchCase=\"'Property'\">\r\n                <form-errors-wrapper [control]=\"updateForm?.controls['Properties']\" label=\"Properties\">\r\n                  <div [ngClass]=\"field?.validators?.includes('required') ? 'field-label-req' : 'field-label'\">\r\n                    Property(s)</div>\r\n                  <ng-select *ngIf=\"!propertyListIsLoading else fieldLoader\" [virtualScroll]=\"true\"\r\n                    [items]=\"propertyList\" [multiple]=\"true\" [closeOnSelect]=\"false\" ResizableDropdown [addTag]=\"true\"\r\n                    name=\"Properties\" formControlName=\"Properties\" addTagText=\"Create New Property\" class=\"bg-white\"\r\n                    placeholder=\"Select/Create Property\">\r\n                    <ng-template ng-option-tmp let-item=\"item\" let-item$=\"item$\" let-index=\"index\">\r\n                      <div class=\"checkbox-container\"><input type=\"checkbox\" id=\"item-{{index}}\"\r\n                          data-automate-id=\"item-{{index}}\" [checked]=\"item$.selected\"><span\r\n                          class=\"checkmark\"></span>{{item}}\r\n                      </div>\r\n                    </ng-template>\r\n                  </ng-select>\r\n                </form-errors-wrapper>\r\n              </ng-container>\r\n              <ng-container *ngSwitchCase=\"'Document'\">\r\n                <div class=\"mt-16 w-100\">\r\n                  <div class=\"flex-between\">\r\n                    <div\r\n                      [ngClass]=\"field?.validators?.includes('required') ? 'field-label-req mt-0' : 'field-label mt-0'\">\r\n                      Add\r\n                      Documents</div>\r\n                    <div class=\"flex-center btn btn-accent-green btn-md w-170 text-sm clear-padding-x\" *ngIf=\"!isUpload\"\r\n                      (click)=\"isUpload = true\">\r\n                      <span class=\"icon ic-cloud-upload ic-xxs mr-8\"></span>\r\n                      {{ (uploadedFiles?.length ? 'LEADS.upload-another-document' : 'LEADS.upload-new-document') |\r\n                      translate }}\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"isUpload\">\r\n                    <form class=\"mb-16\" [formGroup]=\"documentsForm\" autocomplete=\"off\">\r\n                      <div class=\"form-group\">\r\n                        <div class=\"field-label\">{{'LEADS.document-title' | translate }}</div>\r\n                        <input type=\"text\" formControlName=\"docTitle\" id=\"inpDocTitle\" data-automate-id=\"inpDocTitle\"\r\n                          placeholder=\"ex. Title\">\r\n                      </div>\r\n                    </form>\r\n                    <div class=\"br-6 p-10 w-100 bg-accent-green-light mt-16 custom-flex-row\">\r\n                      <browse-drop-upload [allowedFileType]=\"'imgPdfDoc'\" [allowedFileFormat]=\"fileFormatToBeUploaded\"\r\n                        (uploadedFile)=\"onFileSelection($event)\"\r\n                        (uploadedFileName)=\"selectedFileName = $event\"></browse-drop-upload>\r\n                    </div>\r\n                    <div class=\"flex-end mt-10\">\r\n                      <div class=\"btn-gray mr-10\" (click)=\"isUpload = false\">{{ 'BUTTONS.cancel' | translate }}</div>\r\n                      <div class=\"btn-coal\" (click)=\"addDocument()\">{{ 'SIDEBAR.add' | translate }} Document</div>\r\n                    </div>\r\n                  </div>\r\n                  <ng-container *ngIf=\"uploadedFiles?.length\">\r\n                    <div class=\"d-flex flex-wrap tb-w-100 ip-flex-center my-12\">\r\n                      <div *ngFor=\"let doc of uploadedFiles; let i = index\" class=\"flex-col w-100\">\r\n                        <div class=\"p-4 flex-between bg-white br-6 mb-10 w-100\">\r\n                          <div class=\"align-center\">\r\n                            <span class=\"icon ic-file ic-sm ic-black\"></span>\r\n                            <div class=\"text-truncate-1 break-all fw-600 ml-8\">{{uploadedFilesName[i]}}</div>\r\n                          </div>\r\n                          <div class=\"align-center\">\r\n                            <a class=\"icon ic-delete ic-red ic-sm cursor-pointer ml-10\" id=\"clkDeleteLeadDoc\"\r\n                              data-automate-id=\"clkDeleteLeadDoc\" (click)=\"onClickRemoveDocument(i)\"></a>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </ng-container>\r\n                </div>\r\n              </ng-container>\r\n            </ng-container>\r\n          </ng-container>\r\n        </ng-template>\r\n\r\n        <ng-container\r\n          *ngIf=\"(isShowBookingFormBtn)\r\n          && !isSelectedAllBookedOrInvoicedLead() && (currentPath === '/invoice' || !selectedStatus?.shouldUseForInvoice) else customForm\">\r\n          <div class=\"flex-between flex-wrap w-100\">\r\n            <div class=\"w-50 ph-w-100\">\r\n              <div class=\"mr-10\">\r\n                <div class=\"field-label-req\">Booking under name</div>\r\n                <form-errors-wrapper [control]=\"updateForm.controls['bookedUnderName']\" label=\"Booking under name\">\r\n                  <input type=\"text\" id=\"bookedUnderName\" data-automate-id=\"bookedUnderName\"\r\n                    (blur)=\"checkBookedUnderName($event)\" formControlName=\"bookedUnderName\"\r\n                    placeholder=\"ex. Mounika Pampana\">\r\n                </form-errors-wrapper>\r\n              </div>\r\n            </div>\r\n            <div class=\"w-50 ph-w-100\">\r\n              <div class=\"field-label-req\">Booked Date</div>\r\n              <form-errors-wrapper [control]=\"updateForm.controls['bookedDate']\" label=\"Booked Date\">\r\n                <input [owlDateTime]=\"dt1\" [max]=\"currentDate\" [owlDateTimeTrigger]=\"dt1\" readonly id=\"inpBookDateTime\"\r\n                  data-automate-id=\"inpBookDateTime\" formControlName=\"bookedDate\" placeholder=\"ex. 5/03/2025, 12:00 pm\">\r\n                <owl-date-time #dt1 [hour12Timer]=\"'true'\" (afterPickerOpen)=\"onPickerOpened(currentDate)\"\r\n                  [startAt]=\"updateForm.controls['bookedDate'].value ? null : currentDate\"></owl-date-time>\r\n              </form-errors-wrapper>\r\n            </div>\r\n            <div class=\"field-rupees-tag w-50 ph-w-100\">\r\n              <div class=\"field-label\">Agreement Value</div>\r\n              <div class=\"position-relative budget-dropdown mr-10\">\r\n                <form-errors-wrapper [control]=\"updateForm.controls['agreementValue']\" label=\"Agreement Value\">\r\n                  <input type=\"number\" (wheel)=\"$event.preventDefault()\" formControlName=\"agreementValue\" min=\"1\"\r\n                    id=\"agreementValue\" data-automate-id=\"agreementValue\" placeholder=\"ex. 4000000\" maxlength=\"10\"\r\n                    (input)=\"onInputAgreementValue($event.target.value)\">\r\n                  <div class=\"no-validation\">\r\n                    <ng-container *ngIf=\"currencyList?.length > 1 ; else showCurrencySymbol\">\r\n                      <ng-select formControlName=\"currency\" class=\"ml-4 mt-4 position-absolute top-0 manage-dropdown\"\r\n                        ResizableDropdown>\r\n                        <ng-option *ngFor=\"let curr of currencyList\" [value]=\"curr.currency\">\r\n                          <span [title]=\"curr.currency\">\r\n                            {{curr.currency}}\r\n                          </span>\r\n                        </ng-option>\r\n                      </ng-select>\r\n                    </ng-container>\r\n                    <ng-template #showCurrencySymbol>\r\n                      <h5 class=\"rupees px-12 py-8 fw-600 m-4\">{{ leadInfo?.enquiry?.currency || defaultCurrency }}</h5>\r\n                    </ng-template>\r\n                  </div>\r\n                </form-errors-wrapper>\r\n                <div *ngIf=\"updateForm.controls['agreementValue']?.value\"\r\n                  class=\"position-absolute right-5 nbottom-0 text-accent-green fw-semi-bold text-sm\">\r\n                  {{miniagreementValueInWords}}</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"w-100\">\r\n              <div class=\"field-label\">Choose Property/ Project</div>\r\n              <div class=\"flex-between w-50 ph-w-100\">\r\n                <div class=\"align-center\" *ngFor=\"let data of ['Property', 'Project']; let i = index\">\r\n                  <div class=\"form-check form-check-inline align-center\">\r\n                    <input type=\"radio\" id=\"projectProperty{{ i }}\" data-automate-id=\"projectProperty\"\r\n                      name=\"projectProperty\" class=\"radio-check-input mr-10\" formControlName='projectProperty'\r\n                      (change)=\"switchTabProjectProperty(data)\" [value]=\"data\" />\r\n                    <label class=\"fw-600 text-secondary cursor-pointer text-large m-0\" for=\"projectProperty{{ i }}\">{{\r\n                      data }}</label>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"w-50 ph-w-100\" *ngIf=\"updateForm.get('projectProperty')?.value === 'Property'\">\r\n              <div class=\"field-label-req\">Choose Property</div>\r\n              <form-errors-wrapper [control]=\"updateForm.controls['chosenProperty']\" label=\"Property\">\r\n                <ng-select [virtualScroll]=\"true\" *ngIf=\"!propertyListIsLoading else fieldLoader\" ResizableDropdown\r\n                  formControlName=\"chosenProperty\" (change)=\"onPropertyChange($event?.id)\" dropdownPosition=\"bottom\"\r\n                  placeholder=\"ex. ABC Property\" class=\"bg-white mr-10\" [items]=\"propertyList\" bindValue=\"id\"\r\n                  bindLabel=\"title\">\r\n                </ng-select>\r\n              </form-errors-wrapper>\r\n            </div>\r\n            <div class=\"w-50 ph-w-100\" *ngIf=\"updateForm.get('projectProperty')?.value === 'Project'\">\r\n              <div class=\"mr-10\">\r\n                <div [ngClass]=\"isProjectMandatory ? 'field-label-req' : 'field-label'\">Choose Project</div>\r\n                <form-errors-wrapper [control]=\"updateForm.controls['chosenProject']\" label=\"Project\">\r\n                  <ng-select [virtualScroll]=\"true\" *ngIf=\"!projectListIsLoading else fieldLoader\" ResizableDropdown\r\n                    formControlName=\"chosenProject\" dropdownPosition=\"bottom\" placeholder=\"ex. XYZ Project\"\r\n                    class=\"bg-white\" (change)=\"onProjectChange($event?.id)\" [items]=\"projectList\" bindValue=\"id\"\r\n                    bindLabel=\"name\">\r\n                  </ng-select>\r\n                </form-errors-wrapper>\r\n              </div>\r\n            </div>\r\n            <div class=\"position-relative w-50 ph-w-100\"\r\n              *ngIf=\"isShowUnitInfoField && updateForm.controls['chosenProject']?.value\">\r\n              <div [ngClass]=\"isProjectMandatory ? 'field-label-req' : 'field-label'\">Choose Unit</div>\r\n              <div class=\"form-group w-100\">\r\n                <form-errors-wrapper [control]=\"updateForm.controls['chosenUnit']\" label=\"Unit\">\r\n                  <ng-select [virtualScroll]=\"true\" id=\"chosenUnit\" data-automate-id=\"chosenUnit\" ResizableDropdown\r\n                    [items]=\"unitInfo\" bindLabel=\"name\" bindValue=\"id\" formControlName=\"chosenUnit\"\r\n                    dropdownPosition=\"bottom\" placeholder=\"select\" class=\"bg-white\"\r\n                    [readonly]=\"!updateForm.controls['chosenProject']?.value\" (change)=\"onChoosenUnitChange($event)\">\r\n                  </ng-select>\r\n                </form-errors-wrapper>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <!-- Add Document section to booking form -->\r\n          <ng-container\r\n            *ngFor=\"let field of selectedReason?.customFields?.length ? selectedReason?.customFields : selectedStatus?.customFields\">\r\n            <ng-container [ngSwitch]=\"field?.field?.name\">\r\n              <ng-container *ngSwitchCase=\"'Document'\">\r\n                <div class=\"mt-16 w-100\">\r\n                  <div class=\"flex-between\">\r\n                    <div\r\n                      [ngClass]=\"field?.validators?.includes('required') ? 'field-label-req mt-0' : 'field-label mt-0'\">\r\n                      Add\r\n                      Documents</div>\r\n                    <div class=\"flex-center btn btn-accent-green btn-md w-170 text-sm clear-padding-x\" *ngIf=\"!isUpload\"\r\n                      (click)=\"isUpload = true\">\r\n                      <span class=\"icon ic-cloud-upload ic-xxs mr-8\"></span>\r\n                      {{ (uploadedFiles?.length ? 'LEADS.upload-another-document' : 'LEADS.upload-new-document') |\r\n                      translate }}\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"isUpload\">\r\n                    <form class=\"mb-16\" [formGroup]=\"documentsForm\" autocomplete=\"off\">\r\n                      <div class=\"form-group\">\r\n                        <div class=\"field-label\">{{'LEADS.document-title' | translate }}</div>\r\n                        <input type=\"text\" formControlName=\"docTitle\" id=\"inpDocTitle\" data-automate-id=\"inpDocTitle\"\r\n                          placeholder=\"ex. Title\">\r\n                      </div>\r\n                    </form>\r\n                    <div class=\"br-6 p-10 w-100 bg-accent-green-light mt-16 custom-flex-row\">\r\n                      <browse-drop-upload [allowedFileType]=\"'imgPdfDoc'\" [allowedFileFormat]=\"fileFormatToBeUploaded\"\r\n                        (uploadedFile)=\"onFileSelection($event)\"\r\n                        (uploadedFileName)=\"selectedFileName = $event\"></browse-drop-upload>\r\n                    </div>\r\n                    <div class=\"flex-end mt-10\">\r\n                      <div class=\"btn-gray mr-10\" (click)=\"isUpload = false\">{{ 'BUTTONS.cancel' | translate }}</div>\r\n                      <div class=\"btn-coal\" (click)=\"addDocument()\">{{ 'SIDEBAR.add' | translate }} Document</div>\r\n                    </div>\r\n                  </div>\r\n                  <ng-container *ngIf=\"uploadedFiles?.length\">\r\n                    <div class=\"d-flex flex-wrap tb-w-100 ip-flex-center my-12\">\r\n                      <div *ngFor=\"let doc of uploadedFiles; let i = index\" class=\"flex-col w-100\">\r\n                        <div class=\"p-4 flex-between bg-white br-6 mb-10 w-100\">\r\n                          <div class=\"align-center\">\r\n                            <span class=\"icon ic-file ic-sm ic-black\"></span>\r\n                            <div class=\"text-truncate-1 break-all fw-600 ml-8\">{{uploadedFilesName[i]}}</div>\r\n                          </div>\r\n                          <div class=\"align-center\">\r\n                            <a class=\"icon ic-delete ic-red ic-sm cursor-pointer ml-10\" id=\"clkDeleteLeadDoc\"\r\n                              data-automate-id=\"clkDeleteLeadDoc\" (click)=\"onClickRemoveDocument(i)\"></a>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </ng-container>\r\n                </div>\r\n              </ng-container>\r\n            </ng-container>\r\n          </ng-container>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"canAssignLead && !canShowStatusPopupInPreview\">\r\n          <div class=\"field-label\">{{'BUTTONS.assign' | translate }} {{'GLOBAL.to' | translate }}</div>\r\n          <div class=\"d-flex flex-wrap ph-flex-col\">\r\n            <div class=\"ph-w-100 dashboard-dropdown\" [ngClass]=\"isDualOwnershipEnabled?'w-50':'w-100'\">\r\n              <div class=\"text-sm text-black-100 mb-4 mt-2\" *ngIf=\"isDualOwnershipEnabled\">primary</div>\r\n              <form-errors-wrapper [control]=\"updateForm?.controls['assignedToUserId']\" label=\"This\">\r\n                <ng-select [virtualScroll]=\"true\" placeholder=\"Select\" name=\"user\" formControlName=\"assignedToUserId\"\r\n                  ResizableDropdown class=\"mr-10 ph-mr-0\" [ngClass]=\"{'blinking': isUserListLoading}\">\r\n                  <ng-option *ngIf=\"leadInfo.assignTo != EMPTY_GUID\" [value]=\"EMPTY_GUID\">{{ 'LEADS.unassign-lead' |\r\n                    translate}}<span class=\"text-light-gray\">\r\n                      ({{'LEADS.mark-unassigned' | translate}})</span></ng-option>\r\n                  <ng-option *ngFor=\"let user of primaryAgentList\" [value]=\"user.id\">\r\n                    {{user.firstName}} {{user.lastName}}</ng-option>\r\n                  <!-- <ng-option *ngFor=\"let user of deactiveUsers\" [value]=\"user.id\" [disabled]=\"true\">\r\n                    {{ user.firstName }} {{ user.lastName }} <span class=\"error-message-custom top-10\"\r\n                      *ngIf=\"!user.isActive\">\r\n                      (Disabled)</span>\r\n                  </ng-option> -->\r\n                </ng-select>\r\n              </form-errors-wrapper>\r\n            </div>\r\n            <div class=\"w-50 ph-w-100 ph-mt-10 dashboard-dropdown\" *ngIf=\"isDualOwnershipEnabled\">\r\n              <div class=\"text-sm text-black-100 mb-4 mt-2\">secondary</div>\r\n              <ng-select [virtualScroll]=\"true\" placeholder=\"Select\" name=\"user\" formControlName=\"secondaryAssignTo\"\r\n                ResizableDropdown\r\n                [ngClass]=\"{'blinking': isUserListLoading,'pe-none':!updateForm?.controls['assignedToUserId']?.value}\">\r\n                <ng-option *ngIf=\"leadInfo.secondaryUserId != EMPTY_GUID\" [value]=\"EMPTY_GUID\">{{ 'LEADS.unassign-lead'\r\n                  |\r\n                  translate}}<span class=\"text-light-gray\">\r\n                    ({{'LEADS.mark-unassigned' | translate}})</span></ng-option>\r\n                <ng-option *ngFor=\"let user of secondaryAgentList\" [value]=\"user.id\">\r\n                  {{user.firstName}} {{user.lastName}}</ng-option>\r\n                <!-- <ng-option *ngFor=\"let user of deactiveUsers\" [value]=\"user.id\" [disabled]=\"true\">\r\n                  {{ user.firstName }} {{ user.lastName }} <span class=\"error-message-custom top-10\"\r\n                    *ngIf=\"!user.isActive\">\r\n                    (Disabled)</span>\r\n                </ng-option> -->\r\n              </ng-select>\r\n            </div>\r\n          </div>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"canUpdateStatus && !canShowStatusPopupInPreview\">\r\n          <div [ngClass]=\"isNotesMandatory ? 'field-label-req' : 'field-label'\">{{'TASK.notes' | translate}}</div>\r\n          <form-errors-wrapper [control]=\"updateForm?.controls['notes']\" label=\"{{'TASK.notes' | translate}}\">\r\n            <textarea rows=\"2\" id=\"txtUpdateStatusNotes\" data-automate-id=\"txtUpdateStatusNotes\" formControlName=\"notes\"\r\n              placeholder=\"ex. I want to say \"></textarea>\r\n          </form-errors-wrapper>\r\n        </ng-container>\r\n      </div>\r\n    </form>\r\n  </div>\r\n</div>\r\n<div class=\"justify-end mt-20 modal-footer bg-white gap-2\" [ngClass]=\"{'d-none': isCustomStatusListLoading}\"\r\n  *ngIf=\"(!leadStatusIsLoading && !multipleLeadsIsLoading && canUpdateStatus) else ratLoader\">\r\n  <h5 class=\"fw-semi-bold text-black-200 text-decoration-underline cursor-pointer\" id=\"btnCancelUpdateStatus\"\r\n    data-automate-id=\"btnCancelUpdateStatus\" (click)=\"modalService.hide()\">{{'BUTTONS.cancel' | translate}}</h5>\r\n  <div class=\"border-left h-16\"></div>\r\n  <button *ngIf=\"canShowStatusPopupInPreview && canUpdateStatus && !isUnassignedLead\"\r\n    class=\"btn-coal px-10 min-w-fit-content\" (click)=\"openAppointmentPopup()\">{{'BUTTONS.update-lead-status' |\r\n    translate}}</button>\r\n  <button class=\"btn-coal\" *ngIf=\"!canShowStatusPopupInPreview && canUpdateStatus && !isUnassignedLead\"\r\n    (click)=\"updateStatus()\">{{(isLeadPreview && !whatsAppComp ?\r\n    'BUTTONS.save-and-close' : 'BUTTONS.save') | translate}}</button>\r\n  <button\r\n    *ngIf=\"!isLastLead && !canShowStatusPopupInPreview && !isBulkUpdate && canUpdateStatus && !isUnassignedLead && !whatsAppComp\"\r\n    class=\"btn-coal\" id=\"btnSaveUpdateStatus\" data-automate-id=\"btnSaveUpdateStatus\"\r\n    (click)=\"updateStatus(true)\">{{'BUTTONS.save-and-next' | translate}}</button>\r\n  <button *ngIf=\"isShowBookingFormBtn && canUpdateBookedLead && !isBulkUpdate\" class=\"btn-coal w-150\"\r\n    id=\"btnSaveUpdateStatus\" data-automate-id=\"btnSaveUpdateStatus\" (click)=\"fullBookingFormModal(fullBookingForm)\">Save\r\n    & fill booking form</button>\r\n</div>\r\n\r\n<ng-template #ratLoader>\r\n  <div class=\"mt-20 modal-footer\"\r\n    [ngClass]=\"{'justify-end': leadStatusIsLoading || multipleLeadsIsLoading,'justify-center': isCustomStatusListLoading}\">\r\n    <img src=\"assets/images/loader-rat.svg\" class=\"rat-loader h-20px w-20px\" alt=\"loader\">\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #fieldLoader>\r\n  <ng-select [virtualScroll]=\"true\" class=\"pe-none blinking\"></ng-select>\r\n</ng-template>\r\n\r\n<ng-template #noUnitFound>\r\n  <div class=\"p-20\">\r\n    <h3 class=\"text-center\">There is no unit information provided for this project. Please check with the admin.</h3>\r\n    <div class=\"mt-20 flex-end\">\r\n      <button type=\"button\" class=\"btn-green\" (click)=\"modalRef.hide()\">OK</button>\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #trackerInfoModal>\r\n  <h5 class=\"px-20 py-16 fw-semi-bold bg-coal text-white\">Bulk Update Status</h5>\r\n  <div class=\"p-20 flex-center-col\">\r\n    <h4 class=\"text-black-100 fw-600 mb-10 text-center word-break line-break\">Bulk Update Status In progress.\r\n    </h4>\r\n    <h5 class=\"text-black-100 fw-semi-bold text-center word-break line-break\">You can check\r\n      <span class=\"cursor-pointer text-accent-green header-3 fw-600\" (click)=\"openBulkUpdatedStatus()\">“Bulk Operation\r\n        Tracker”</span> to view updated status\r\n    </h5>\r\n    <button class=\"btn-green mt-30\" (click)=\"modalService.hide()\">\r\n      {{'BULK_LEAD.got-it' | translate}}</button>\r\n  </div>\r\n</ng-template>"]}, "metadata": {}, "sourceType": "module"}