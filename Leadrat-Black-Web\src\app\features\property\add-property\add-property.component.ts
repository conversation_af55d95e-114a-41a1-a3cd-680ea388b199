import {
  Attribute,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  On<PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { OwlDateTimeComponent } from '@danielmoncada/angular-datetime-picker';
import { Store } from '@ngrx/store';
import { CountryCode, isPossiblePhoneNumber } from 'libphonenumber-js';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { BehaviorSubject } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  takeUntil
} from 'rxjs/operators';

import { NotificationsService } from 'angular2-notifications';
import {
  ATTR_NO,
  ATTR_NO_ALL,
  BHK_NO,
  BHK_NO_ALL,
  BHK_TYPE,
  COMPLETIONSTATUS,
  EMPTY_GUID,
  FACING,
  FURNISH_STATUS,
  lockInPeriodList,
  MONTHS,
  noticePeriodList,
  OFFERINGTYPE,
  POSSESSION_DATE_FILTER_LIST,
  PROPERTY_TYPE_IMAGES,
  SALE_TYPE_LIST,
  securityDepositDates,
  TRANSACTION_TYPE_LIST,
  VALIDATION_CLEAR,
  VALIDATION_SET
} from 'src/app/app.constants';
import {
  BHKType,
  EnquiryType,
  Facing,
  FolderNamesS3,
  FurnishStatus,
  PaymentFrequency,
  PossessionType,
  PropertyType,
  SaleType,
  TaxationMode
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { Property } from 'src/app/core/interfaces/leads.interface';
import { PropertyDimension } from 'src/app/core/interfaces/property.interface';
import {
  assignToSort,
  changeCalendar,
  convertUrlsToLinks,
  formatBudget,
  generateEnumList,
  getAreaUnit,
  getAWSImagePath,
  getBHKDisplayString,
  getBRDisplayString,
  getLocalityDetailsByObj,
  getLocationDetailsByObj,
  getPropertyTypeId,
  onlyNumbers,
  onlyNumbersWithDecimal,
  onPickerOpened,
  patchFormControlValue,
  patchTimeZoneDate,
  setPropertySubTypeList,
  setTimeZoneDate,
  toggleValidation,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import { FetchAllAttributes } from 'src/app/reducers/amenities-attributes/amenities-attributes.action';
import { getAllAttributes } from 'src/app/reducers/amenities-attributes/amenities-attributes.reducer';
import {
  getGlobalAnonymousIsLoading,
  getGlobalSettingsAnonymous,
} from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchProjectList } from 'src/app/reducers/lead/lead.actions';
import {
  getProjectList,
  getProjectListIsLoading,
} from 'src/app/reducers/lead/lead.reducer';
import {
  FetchAreaUnitList,
  FetchPropertyAttributeList,
} from 'src/app/reducers/master-data/master-data.actions';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  AddProperty,
  AddWaterMark,
  FetchGalleryDropdownData,
  FetchPropertyById,
  FetchPropertyByIdSuccess,
  UpdateGallery,
  UpdateProperty,
  UploadBrochure,
} from 'src/app/reducers/property/property.actions';
import {
  getBrochureList,
  getGalleryDropdownData,
  getGalleryDropdownDataIsLoading,
  getPropertyAddIsLoading,
  getPropertyListDetails,
  getPropertyListDetailsIsLoading,
  getPropertyUpdateIsLoading,
  getWaterMarkImage,
} from 'src/app/reducers/property/property.reducer';
import { FetchLocationsWithGoogle } from 'src/app/reducers/site/site.actions';
import {
  getLocationsWithGoogleApi,
  getLocationsWithGoogleApiIsLoading,
} from 'src/app/reducers/site/site.reducer';

import { getAttendanceSettings } from 'src/app/reducers/attendance/attendance.reducer';
import { FetchListingSourceWithId } from 'src/app/reducers/listing-site/listing-site.actions';
import { getListingSiteLoaders } from 'src/app/reducers/listing-site/listing-site.reducer';
import { getAreaUnitIsLoading, getAreaUnits, getIsAttributesLoading } from 'src/app/reducers/master-data/master-data.reducer';
import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'add-property',
  templateUrl: './add-property.component.html',
})
export class AddPropertiesComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('previewModal') previewModal: TemplateRef<any>;
  @ViewChild('deleteDocumentModal') deleteDocumentModal: TemplateRef<any>;
  @ViewChild('resolutionWarningModal') resolutionWarningModal: TemplateRef<any>;
  @ViewChild('fileUploadComponent') fileUploadComponent: any;
  searchPlaceTerm$: BehaviorSubject<any> = new BehaviorSubject<any>('');
  s3BucketUrl = env.s3ImageBucketURL;
  basicInfoForm: FormGroup;
  propertyDetailForm: FormGroup;
  attributeForm: FormGroup;
  publishForm: FormGroup;
  propertyTypeImages: Object = PROPERTY_TYPE_IMAGES;
  bhkNoList: Array<string>;
  bhkTypeList: Array<string> = BHK_TYPE;
  enquiredForList: Array<Object> = TRANSACTION_TYPE_LIST.slice(1, 3);
  saleTypeList: Array<Object> = SALE_TYPE_LIST;
  furnishStatusList: Array<{ dispName: string; value: string }> =
    FURNISH_STATUS;
  facingList: Array<{ displayName: string; value: string }> = FACING;
  projectList: Array<string> = [];
  isProjectListLoading: boolean = true;
  brokerageUnits: Array<string> = [];
  placesList: any[] = [];
  listingPlacesList: any[] = [];
  isPlacesListLoading: boolean = true;
  areaSizeUnits: Array<PropertyDimension> = [];
  isAreaSizeUnitsLoading: boolean = true;
  propertyTypeList: Array<Property> = JSON.parse(
    localStorage.getItem('propertyType')
  );
  propertySubTypeList: Array<{ displayName: string }> = [];
  masterAttributeList: any[] = [];
  isMasterAttributeListLoading: boolean = true;
  basicAttributes: Array<Attribute> = [];
  filteredBasicAttributes: Array<Attribute> = [];
  selectedAdditionalAttr: Array<string> = [];
  selectedAttributes: any;
  galleryImageArray: any;
  galleryVideoArray: any;
  selectedAmenities: Array<string> = [];
  selectedFile: Array<string> = [];
  stepOneSubmitted: boolean = false;
  showSubmitConfirmation: boolean = false;
  isGalleryCarouselVisible: boolean = false;
  isShowManualLocation: boolean = false;
  canViewOwner: boolean = false;
  canAssign: boolean = false;
  canList: boolean = false;
  dropdownOptions: number[] = Array.from({ length: 200 }, (_, i) => i + 1); // [1..200]
  floorDropdownOptions: (number | string)[] = []; // for floorNumber (dynamic based on numberOfFloors)
  budgetInWords: string = '';
  maintenanceCostInWords: string = '';
  depositAmountInWords: string = '';
  commonAreaChargesInWords: string = '';
  currentStep: number = 1;
  imageIndex: number;
  propType: number;
  currentDelete: number;
  selectedPropertyInfo: any;
  isSelectedPropertyInfoLoading: boolean = true;
  activePropertyId: any;
  basicInfoFormValues: any;
  propInfoFormValues: any;
  attrFormValues: any;
  fileFormatToBeUploaded: string = 'application/pdf';
  fileName: string = '';
  patchFormControlValue = patchFormControlValue;
  formatBudget = formatBudget;
  onlyNumbers = onlyNumbers;
  onlyNumbersWithDecimal = onlyNumbersWithDecimal;
  getBHKDisplayString = getBHKDisplayString;
  getBRDisplayString = getBRDisplayString;
  onPickerOpened = onPickerOpened;
  getAWSImagePath = getAWSImagePath;
  convertUrlsToLinks = convertUrlsToLinks
  docList: any;
  galleryDropdownData: string[];
  isGalleryDropdownDataLoading: boolean = true;
  galleryPayload: any = {};
  galleryS3Paths: Array<string> = [];
  galleryS3PathsVid: Array<any> = [];
  galleryMapping: any = {};
  galleryOrderRanks: { [key: string]: number } = {};
  coverImgIndex = 0;
  coverImg: string = '';
  draggedImageIndex: number = -1;
  draggedOverImageIndex: number = -1;

  // Image resolution validation
  lowResolutionImages: any[] = [];
  showResolutionWarning: boolean = false;
  pendingImageUpload: any[] = [];
  MIN_IMAGE_WIDTH: number = 600;
  MIN_IMAGE_HEIGHT: number = 800;
  dimensionCheckInProgress: boolean = false;
  imagesToProcess: any[] = [];
  links: any[] = [];
  currency: any[] = [];
  defaultCurrency: string = '';
  countryCode: any[];
  timeZone: any[];
  newUrl: string = '';
  preferredCountries = ['in'];
  hasInternationalSupport: boolean = false;
  isBrokerageUnitRequired: boolean = false;
  @ViewChild('ownerNoInput') ownerNoInput: any;
  @ViewChild('tenantPOCInput') tenantPOCInput: any;
  @ViewChild('coWorkingOperatorPOCInput') coWorkingOperatorPOCInput: any;
  userList: Array<any> = [];
  activeUsers: Array<any> = [];
  vidPathUrl: any;
  videoPayload: any = [];
  allAttributes: any;

  addImage: AnimationOptions = {
    path: 'assets/animations/gallery-image.json',
  };
  inactiveUsers: any;
  addVideo: AnimationOptions = {
    path: 'assets/animations/gallery-video.json',
  };
  addDoc: AnimationOptions = {
    path: 'assets/animations/gallery-doc.json',
  };
  isGlobalSettingsLoading: boolean = true;
  attributeExpandedLists: { [key: string]: string[] } = {};

  waterMarkSettingsObj: any = {
    isWaterMarkEnabled: false,
    toAddWaterMark: false,
    fetchingFromGallery: false,
    watermarkLogo: '',
    watermarkOpacity: null,
    watermarkPosition: null,
    watermarkSize: null,
  };
  userData: any;
  currentDate: Date = new Date();
  selectedPossessionDate: any;;
  currentPath: string;
  offeringType = OFFERINGTYPE;
  completionStatus = COMPLETIONSTATUS;
  numbers: number[] = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
  paymentList: any[];
  view360Url: any[] = [];
  selectedFileSize: any;
  listingSource: any[];
  listingSourceIsLoading: any;
  addressList: any[];
  filteredListingSource: any[] = [];
  addresses: any = {};
  filteredAddresses: any[][] = [];
  listingAddressisLoading: boolean;
  showLeftNav: boolean = true;
  taxationModeList = [
    { label: 'GST(incl)', value: TaxationMode.GSTInclusive },
    {
      label: 'GST(excl)',
      value: TaxationMode.GSTExclusive,
    },
  ];
  globalSettingsDetails: any;
  previewLanguage: string;
  deactiveUsers: any;
  isForNotes: boolean;
  isGeoFenceEnabled: boolean = false;
  addressData: any;
  isPropertyUpdateIsLoading: boolean = false;
  isPropertyAddIsLoading: boolean = false;
  goodResolutionImages: any;
  viewListing: boolean;
  get isClickOfficeSpace(): boolean {
    return this.basicInfoForm.get('propertySubType').value === 'Office Space';
  }

  get isClickCoWorkingOfficeSpace(): boolean {
    return (
      this.basicInfoForm.get('propertySubType').value ===
      'Co Working Office Space'
    );
  }

  securityDepositDates: any = securityDepositDates;
  lockInPeriodList: any = lockInPeriodList;
  noticePeriodList: any = noticePeriodList;
  dateFilterList = POSSESSION_DATE_FILTER_LIST;
  selectedTotalFloor: number[] = [];
  selectedFloors: any[] = [];
  isAllSelected: boolean = false;
  selectedPermitType: string = 'dldPermitNumber';
  isImageUploading: boolean = false;
  isVideoUploading: boolean = false;
  isDocumentUploading: boolean = false;

  isOpenPossessionModal: boolean = false;
  selectedPossession: any;
  selectedMonthAndYear: Date;
  isValidPossDate: boolean;
  selectedMonth: any;
  selectedYear: any;
  @ViewChild('dt5') dt5: OwlDateTimeComponent<any>;

  securityDepositInWords: string = '';

  get propertyContacts(): FormArray {
    return this.propertyDetailForm.get('propertyContacts') as FormArray;
  }

  constructor(
    private modalRef: BsModalRef,
    private modalRefClose: BsModalRef,
    private modalConfirm: BsModalRef,
    private fb: FormBuilder,
    private modalService: BsModalService,
    private _store: Store<AppState>,
    public metaTitle: Title,
    private headerTitle: HeaderTitleService,
    private imgService: BlobStorageService,
    private activatedRoute: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    public router: Router,
    public _notificationService: NotificationsService,
    private shareDataService: ShareDataService,
    public trackingService: TrackingService,
  ) { }

  ngOnInit(): void {
    this.shareDataService.showLeftNav$.subscribe(show => {
      this.showLeftNav = show;
    });

    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;
    this.bhkNoList = BHK_NO;
    this.viewListing = this.currentPath?.includes('listing') ? true : false;
    this.paymentList = generateEnumList(PaymentFrequency);
    this._store
      .select(getWaterMarkImage)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (!this.waterMarkSettingsObj.fetchingFromGallery) {
          return;
        }
        let waterMarkUrls: any = [];
        if (data) {
          data.forEach((url: any) => {
            let startIndex = url.indexOf(this.s3BucketUrl);
            if (startIndex !== -1) {
              let slicedString = url.slice(
                startIndex + this.s3BucketUrl.length
              );
              waterMarkUrls.push(slicedString);
            }
          });
        }

        this.galleryS3Paths?.push(...waterMarkUrls);
        if (this.galleryS3Paths.length === waterMarkUrls.length) {
          this.coverImgIndex = 0;
          this.coverImg = this.galleryS3Paths[0];
        } else {
          this.coverImg = this.galleryS3Paths[this.coverImgIndex];
        }
        this.galleryS3Paths?.forEach((img: string) => {
          this.galleryMapping = {
            ...this.galleryMapping,
            [img]: this.galleryMapping[img] || this.galleryDropdownData[0],
          };
        });
        this.updateImageOrderRanks();
      });

    this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isGlobalSettingsLoading = isLoading;
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsDetails = data;
        (this.defaultCurrency = data?.countries?.length
          ? data.countries[0].defaultCurrency
          : null),
          (this.currency = data?.countries?.length
            ? data.countries[0].currencies
            : null);
        this.brokerageUnits = [this.defaultCurrency, '%'];
        this.hasInternationalSupport = data?.hasInternationalSupport;
        this.preferredCountries = data?.hasInternationalSupport
          ? data?.countries?.length
            ? [data.countries[0].code?.toLowerCase()]
            : ['in']
          : ['in'];
        this.timeZone = data?.defaultTimeZone;
        this.propertyDetailForm?.patchValue({
          currency:
            this.selectedPropertyInfo?.monetaryInfo?.currency ||
            this.defaultCurrency,
        });
        this.waterMarkSettingsObj.isWaterMarkEnabled =
          data?.leadProjectSetting?.isWaterMarksOnImagesEnabled;
        this.waterMarkSettingsObj.watermarkLogo =
          data?.leadProjectSetting?.waterMarkUrl;
        this.waterMarkSettingsObj.watermarkOpacity =
          data?.leadProjectSetting?.opacity;
        this.waterMarkSettingsObj.watermarkPosition =
          data?.leadProjectSetting?.waterMarkPosition;
        this.waterMarkSettingsObj.watermarkSize =
          data?.leadProjectSetting?.imageSize;
        // if (!this.selectedPropertyInfo) {
        //   this.waterMarkSettingsObj.toAddWaterMark = data?.leadProjectSetting?.isWaterMarksOnImagesEnabled
        // }
      });
    this.basicInfoForm = this.fb.group({
      enquiredFor: ['Rent', Validators.required],
      propertyType: [null, Validators.required],
      propertySubType: [null, Validators.required],
      noOfBHK: [null],
      title: ['', Validators.required],
      assignedTo: null,
      listingOnBehalf: null,
      propertySize: [null],
      propertyLength: [
        null],
      propertyBreadth: [null],
      areaUnit: [null],
      carpetArea: [null],
      buildUpArea: [null],
      saleableArea: [null],
      netArea: [null],
      carpetAreaUnit: [this.globalSettingsDetails?.defaultValues?.masterAreaUnit],
      buildUpAreaUnit: [this.globalSettingsDetails?.defaultValues?.masterAreaUnit],
      saleableAreaUnit: [this.globalSettingsDetails?.defaultValues?.masterAreaUnit],
      netAreaUnit: [this.globalSettingsDetails?.defaultValues?.masterAreaUnit],
      lengthAndBreadthAreaUnit: [null],
      bhkType: [null],
      titleWithLanguage: [null],
      aboutProperty: [''],
      coWorkingOperator: [null],
      offeringType: [null],
      completionStatus: [null],
      aboutPropertyWithLanguage: [null],
      isPriceVissible: [false],
      isFeatured: [false],
    });

    this.propertyDetailForm = this.fb.group({
      saleType: ['New'],
      expectedPrice: ['', Validators.required],
      maintenanceCost: [null],
      depositAmount: [null],
      securityDepositAmount: [null],
      securityDepositUnit: [null],
      commonAreaCharges: [null],
      commonAreaChargesUnit: [null],
      lockInPeriod: [null],
      noticePeriod: [null],
      escalation: [null],
      globalRange: [null],
      globalDate: [null],
      tenantPOCName: [
        null,
        Validators.compose([
          Validators.maxLength(75),
          ValidationUtil.onlyAlphaNumericValidator,
        ]),
      ],
      tenantPOCdesignation: [null],
      tenantPOCPhone: ['', this.contactNumberValidator('tenantPOCInput')],
      coWorkingOperatorPOCInput: [
        '',
        this.contactNumberValidator('coWorkingOperatorPOCInput'),
      ],
      coWorkingOperatorPOCName: [null],
      areaSpreadover: [null],
      areaSpreadoverUnit: [null],

      currency: [this.defaultCurrency],
      isNegotiable: [false],
      locationId: [null],
      enquiredLocality: null,
      enquiredCity: null,
      enquiredState: null,
      enquiredCountry: null,
      enquiredPincode: null,
      postalCode: null,
      country: null,
      enquiredSubCommunity: null,
      enquiredCommunity: null,
      brokerage: [null],
      brokerageUnit: [null],
      possessionDate: [null, this.possessionDateValidator()],
      rating: [''],
      project: [null],
      notes: [''],
      // serviceCharges: [null],
      noOfChequesAllowed: [null],
      refrenceNo: [null],
      dtcmPermit: [null],
      dldPermitNumber: [null],
      paymentFrequency: [null],
      taxationMode: [0],
      propertyContacts: this.fb.array([
        this.createContactFormGroup()
      ]),
    });
    this.attributeForm = this.fb.group({
      numberOfBedrooms: [''],
      numberOfBathrooms: [''],
      numberOfUtilities: [''],
      numberOfKitchens: [''],
      numberOfBalconies: [''],
      numberOfDrawingOrLivingRooms: [''],
      numberOfFloors: [null],
      floorNumber: [null],
      maximumOccupants: [''],
      furnishStatus: [null],
      facing: [null],
      noOfFloorsOccupied: [null],
      numberOfBikeParking: [null],
      numberOfCarParking: [null],
      numberOfWashRooms: [null],
      numberOfConferenceRooms: [null],
      numberOfMeetingRooms: [null],
      numberOfWorkstation: [null],
      numberOfCabins: [null],
      numberOfLivingRooms: [null],
      numberOfParking: [null],
    });

    this._store.dispatch(new FetchPropertyAttributeList());
    this._store.dispatch(new FetchAreaUnitList());
    this._store.dispatch(new FetchProjectList());
    this._store.dispatch(new FetchLocationsWithGoogle());
    this._store.dispatch(new FetchAllAttributes());
    this._store.dispatch(new FetchListingSourceWithId())

    this.metaTitle.setTitle('CRM | Properties');

    this._store
      .select(getAttendanceSettings)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isGeoFenceEnabled = data?.isGeoFenceEnabled;
      });

    this._store
      .select(getAllAttributes)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const allAttr = data
          .map((attr: any) => ({
            ...attr,
            selected: this.selectedAdditionalAttr.includes(attr?.id)
          })).filter((attr: any) =>
            attr.isActive
          )
        // this.filteredAttributes = activeAttribute
        this.allAttributes = allAttr
        if (this.allAttributes?.length) {
          this.getPropertyAttributes();
        }
      })

    this._store
      .select(getIsAttributesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isMasterAttributeListLoading = isLoading
      });

    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });

    this._store
      .select(getAreaUnitIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isAreaSizeUnitsLoading = isLoading
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });

    this._store
      .select(getPropertyUpdateIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isPropertyUpdateIsLoading = isLoading;
      })

    this._store
      .select(getPropertyAddIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isPropertyAddIsLoading = isLoading;
      })


    this._store
      .select(getListingSiteLoaders)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.listingSourceIsLoading = data?.listingSiteSourcesWithId;
        this.listingAddressisLoading = data?.addressWithId;
      })

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userList = data;
        this.userList = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.userList = assignToSort(this.userList, '');
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');

        this.deactiveUsers = data?.filter((user: any) => !user.isActive)?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.deactiveUsers = assignToSort(this.deactiveUsers, '');
      });

    this._store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userList = data;
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.inactiveUsers = data?.filter((user: any) => !user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');

        this.deactiveUsers = data?.filter((user: any) => !user.isActive)?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.deactiveUsers = assignToSort(this.deactiveUsers, '');
      });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Properties.ViewOwnerInfo')) {
          this.canViewOwner = true;
        }
        if (permissions?.includes('Permissions.Properties.Assign')) {
          this.canAssign = true;
        }
        if (permissions?.includes('Permissions.Properties.PublishProperty')) {
          this.canList = true;
        }
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this._store.dispatch(new FetchUsersListForReassignment());
        } else {
          this._store.dispatch(new FetchAdminsAndReportees());
        }
      });
    this._store
      .select(getLocationsWithGoogleApiIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isPlacesListLoading = isLoading;
      });
    this._store
      .select(getLocationsWithGoogleApi)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.placesList = data
          ?.slice()
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
      });

    this.activatedRoute.params.subscribe((params: any) => {
      if ((params || {}).id) {
        this.activePropertyId = params.id;
        if (this.activePropertyId) {
          this._store.dispatch(new FetchPropertyById(this.activePropertyId));
        }
      }
    });

    this.headerTitle.setLangTitle(
      this.activePropertyId?.id || this.activePropertyId
        ? 'BUTTONS.edit-property'
        : 'BUTTONS.add-property'
    );

    this.attributeForm.get('numberOfFloors')?.valueChanges.subscribe((value: number | string) => {
      if (value === 'Ground Floor') {
        this.floorDropdownOptions = ['B2', 'B1', '0'];
      } else if (typeof value === 'number' && value > 0) {
        const basementOptions = ['B2', 'B1'];
        const groundOption = ['0'];
        const upperFloors = Array.from({ length: value }, (_, i) => i + 1);
        this.floorDropdownOptions = [...basementOptions, ...groundOption, ...upperFloors];
      } else {
        const basementOptions = ['B2', 'B1'];
        const groundOption = ['0'];
        const upperFloors = Array.from({ length: 200 }, (_, i) => i + 1);
        this.floorDropdownOptions = [...basementOptions, ...groundOption, ...upperFloors];
      }
      const currentFloor = this.attributeForm.get('floorNumber')?.value;
      if (currentFloor && !this.floorDropdownOptions.includes(currentFloor)) {
        this.attributeForm.get('floorNumber')?.setValue(null);
      }
    });

    this.basicInfoForm.valueChanges.subscribe((data: any) => {
      this.basicInfoFormValues = data;
    });

    this.propertyDetailForm.valueChanges.subscribe((data: any) => {
      this.propInfoFormValues = data;
    });

    this.attributeForm
      .get('numberOfFloors')
      .valueChanges.subscribe((data: any) => {
        if (data) {
          this.selectedTotalFloor = Array.from(
            { length: data },
            (_, i) => i + 1
          );
          this.attributeForm.get('noOfFloorsOccupied')?.setValue([]);
        } else {
          this.selectedTotalFloor = [];
        }
      });

    this.attributeForm
      .get('noOfFloorsOccupied')
      .valueChanges.subscribe((data: any) => {
        if (
          Number(data?.length) ===
          Number(this.attributeForm?.get('numberOfFloors')?.value)
        ) {
          this.isAllSelected = true;
        } else {
          this.isAllSelected = false;
        }
      });

    this.attributeForm.valueChanges.subscribe((data: any) => {
      this.attrFormValues = data;
      const escalationValue = data?.escalation;
      if (escalationValue != null) {
        this.propertyDetailForm
          .get('escalation')
          .setErrors(
            escalationValue < 1 || escalationValue > 99
              ? { between1to99: true }
              : null
          );
      }
    });

    this.basicInfoForm.get('propertyLength').valueChanges.subscribe((val) => {
      this.lengthAndBreadthAreaUnitValidator();
      this.areaUnitValidator();
      if (this.basicInfoForm.controls['propertyLength'].value === null) {
        this.basicInfoForm.patchValue({
          lengthAndBreadthAreaUnit: null,
        });
      }
    });

    this.basicInfoForm.get('propertyBreadth').valueChanges.subscribe((val) => {
      this.lengthAndBreadthAreaUnitValidator();
      this.areaUnitValidator();
      if (this.basicInfoForm.controls['propertyBreadth'].value === null) {
        this.basicInfoForm.patchValue({
          lengthAndBreadthAreaUnit: null,
        });
      }
    });

    this.basicInfoForm.get('carpetArea').valueChanges.subscribe((val) => {
      this.otherAreaUnitValidator('carpetArea');
      if (this.basicInfoForm.controls['carpetArea'].value === null) {
        this.basicInfoForm.patchValue({
          carpetAreaUnit: null,
        });
      }
    });

    this.basicInfoForm.get('buildUpArea').valueChanges.subscribe((val) => {
      this.otherAreaUnitValidator('buildUpArea');
      if (this.basicInfoForm.controls['buildUpArea'].value === null) {
        this.basicInfoForm.patchValue({
          buildUpAreaUnit: null,
        });
      }
    });

    this.basicInfoForm.get('saleableArea').valueChanges.subscribe((val) => {
      this.otherAreaUnitValidator('saleableArea');
      if (this.basicInfoForm.controls['saleableArea'].value === null) {
        this.basicInfoForm.patchValue({
          saleableAreaUnit: null,
        });
      }
    });

    this.basicInfoForm.get('netArea').valueChanges.subscribe((val) => {
      this.otherAreaUnitValidator('netArea');
      if (this.basicInfoForm.controls['netArea'].value === null) {
        this.basicInfoForm.patchValue({
          netAreaUnit: null,
        });
      }
    });

    this.basicInfoForm
      .get('lengthAndBreadthAreaUnit')
      .valueChanges.subscribe((val) => {
        if (this.isPlot)
          this.basicInfoForm.patchValue({
            areaUnit: val,
          });
      });

    this.basicInfoForm
      .get('propertySubType')
      .valueChanges.subscribe((pSubType: string) => {
        const ptype = this.basicInfoForm.controls['propertyType'].value;
        if (pSubType == 'Plot' && ptype == 'Residential') {
          toggleValidation(VALIDATION_CLEAR, this.basicInfoForm, 'noOfBHK');
          toggleValidation(VALIDATION_CLEAR, this.basicInfoForm, 'bhkType');
        } else if (pSubType != 'Plot' && ptype == 'Residential') {
          toggleValidation(VALIDATION_SET, this.basicInfoForm, 'noOfBHK', [
            Validators.required,
          ]);
          toggleValidation(VALIDATION_SET, this.basicInfoForm, 'bhkType', [
            Validators.required,
          ]);
        }

        if (this.currentPath.includes('listing')) {
          toggleValidation(VALIDATION_CLEAR, this.basicInfoForm, 'bhkType');
        }
        if (pSubType !== 'Plot') {
          this.basicInfoForm.controls['propertyLength'].setValue(null);
          this.basicInfoForm.controls['propertyBreadth'].setValue(null);
        }
        const isCommercial =
          this.basicInfoForm?.get('propertyType')?.value == 'Commercial';
        if (
          !['Office Space', 'Co Working Office Space']?.includes(pSubType) &&
          isCommercial
        ) {
          this.filteredBasicAttributes = [...this.basicAttributes]?.filter(
            (item: any) =>
              ['maximumOccupants', 'floorNumber', 'numberOfFloors']?.includes(
                item?.attributeName
              )
          );
        } else {
          this.filteredBasicAttributes = this.basicAttributes;
        }
      });

    this.basicInfoForm
      .get('propertyType')
      .valueChanges.subscribe((pType: string) => {
        if (pType != 'Residential') {
          toggleValidation(VALIDATION_CLEAR, this.basicInfoForm, 'noOfBHK');
          toggleValidation(VALIDATION_CLEAR, this.basicInfoForm, 'bhkType');
        } else {
          toggleValidation(VALIDATION_SET, this.basicInfoForm, 'noOfBHK', [
            Validators.required,
          ]);
          toggleValidation(VALIDATION_SET, this.basicInfoForm, 'bhkType', [
            Validators.required,
          ]);
        }
        this.basicInfoForm.patchValue({
          propertySubType: '',
          noOfBHK: null,
          bhkType: null,
        });
        this.propType = PropertyType[pType as keyof typeof PropertyType];
        this.getPropertyAttributes();
      });

    this.basicInfoForm.controls['propertyType'].valueChanges.subscribe(
      (val: string) => {
        this.propertySubTypeList = setPropertySubTypeList(
          val,
          this.propertyTypeList
        );
      }
    );

    this.propertyDetailForm
      .get('brokerage')
      .valueChanges.subscribe((val: any) => {
        const brokerageUnit =
          this.propertyDetailForm.get('brokerageUnit')?.value;
        if (val) {
          if (brokerageUnit === '%' && val > 99) {
            this.propertyDetailForm
              .get('brokerage')
              ?.setErrors({ percentageField: true });
          } else {
            this.propertyDetailForm.get('brokerage')?.setErrors(null);
          }
          this.propertyDetailForm.get('brokerage')?.markAllAsTouched();
          toggleValidation(
            VALIDATION_SET,
            this.propertyDetailForm,
            'brokerageUnit',
            [Validators.required]
          );
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.propertyDetailForm,
            'brokerageUnit'
          );
        }
      });

    this.propertyDetailForm
      .get('brokerageUnit')
      .valueChanges.subscribe((val: any) => {
        const brokerage = this.propertyDetailForm.get('brokerage')?.value;
        if (val) {
          if (val === '%' && brokerage > 99) {
            this.propertyDetailForm
              .get('brokerage')
              ?.setErrors({ percentageField: true });
          } else {
            this.propertyDetailForm.get('brokerage')?.setErrors(null);
          }
          this.propertyDetailForm.get('brokerage')?.markAllAsTouched();
        }
      });

    this.propertyDetailForm.get('currency').valueChanges.subscribe((val) => {
      this.budgetInWords = formatBudget(
        this.selectedPropertyInfo?.monetaryInfo?.expectedPrice ||
        this.propertyDetailForm.value.expectedPrice,
        val
      );
      this.maintenanceCostInWords = formatBudget(
        this.selectedPropertyInfo?.monetaryInfo?.maintenanceCost ||
        this.propertyDetailForm.value.maintenanceCost,
        val
      );
      this.depositAmountInWords = formatBudget(
        this.selectedPropertyInfo?.monetaryInfo?.depositAmount ||
        this.propertyDetailForm.value.depositAmount,
        val
      );
      this.commonAreaChargesInWords = formatBudget(
        this.selectedPropertyInfo?.dimension?.commonAreaCharges ||
        this.propertyDetailForm.value.commonAreaCharges,
        val
      );
      if (
        this.propertyDetailForm.get('brokerage').value &&
        this.propertyDetailForm.value.brokerageUnit !== '%'
      ) {
        this.propertyDetailForm.get('brokerageUnit').patchValue(val);
      }
      this.brokerageUnits = [val, '%'];
    });

    this.propertyDetailForm
      .get('expectedPrice')
      .valueChanges.subscribe((expectedPrice) => {
        this.budgetInWords = formatBudget(
          expectedPrice,
          this.propertyDetailForm.value.currency ||
          this.selectedPropertyInfo?.monetaryInfo?.currency ||
          this.defaultCurrency
        );
      });

    this.propertyDetailForm
      .get('maintenanceCost')
      .valueChanges.subscribe((maintenanceCost) => {
        this.maintenanceCostInWords = formatBudget(
          maintenanceCost,
          this.propertyDetailForm.value.currency ||
          this.selectedPropertyInfo?.monetaryInfo?.currency
        );
      });
    // if (this.basicInfoForm.get('enquiredFor').value === 'Rent') {
    this.propertyDetailForm
      .get('expectedPrice')
      .valueChanges.subscribe((value) => {
        const paymentFrequencyControl =
          this.propertyDetailForm.get('paymentFrequency');
        if (value && this.basicInfoForm.get('enquiredFor').value === 'Rent') {
          paymentFrequencyControl.setValidators([Validators.required]);
        } else {
          paymentFrequencyControl.clearValidators();
        }
        paymentFrequencyControl.updateValueAndValidity();
      });
    // }

    this.propertyDetailForm
      .get('depositAmount')
      .valueChanges.subscribe((depositAmount) => {
        this.depositAmountInWords = formatBudget(
          depositAmount,
          this.propertyDetailForm.value.currency ||
          this.selectedPropertyInfo?.monetaryInfo?.currency
        );
      });
    this.propertyDetailForm
      .get('commonAreaCharges')
      .valueChanges.subscribe((areaAmount) => {
        this.commonAreaChargesInWords = formatBudget(
          areaAmount,
          this.propertyDetailForm.value.currency ||
          this.selectedPropertyInfo?.dimension?.currency
        );
      });

    this.propertyDetailForm.get('currency').valueChanges.subscribe((value) => {
      this.propertyDetailForm
        .get('currency')
        .setValue(value, { emitEvent: false });
    });

    this.propertyDetailForm
      .get('securityDepositAmount')
      .valueChanges.subscribe((val: any) => {
        const securityDepositUnit =
          this.propertyDetailForm.get('securityDepositUnit')?.value;
        if (val) {
          if (securityDepositUnit === '%' && val > 99) {
            this.propertyDetailForm
              .get('securityDepositAmount')
              ?.setErrors({ percentageField: true });
          } else {
            this.propertyDetailForm.get('securityDepositAmount')?.setErrors(null);
          }
          this.propertyDetailForm.get('securityDepositAmount')?.markAllAsTouched();
          toggleValidation(
            VALIDATION_SET,
            this.propertyDetailForm,
            'securityDepositUnit',
            [Validators.required]
          );
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.propertyDetailForm,
            'securityDepositUnit'
          );
        }
      });

    this.propertyDetailForm
      .get('securityDepositUnit')
      .valueChanges.subscribe((val: any) => {
        const securityDepositAmount = this.propertyDetailForm.get('securityDepositAmount')?.value;
        if (val) {
          if (val === '%' && securityDepositAmount > 99) {
            this.propertyDetailForm
              .get('securityDepositAmount')
              ?.setErrors({ percentageField: true });
          } else {
            this.propertyDetailForm.get('securityDepositAmount')?.setErrors(null);
          }
          this.propertyDetailForm.get('securityDepositAmount')?.markAllAsTouched();
        }
        // Update the security deposit in words
        this.securityDepositInWords = formatBudget(
          securityDepositAmount,
          val === '%' ? '%' : (this.propertyDetailForm.get('currency')?.value || this.defaultCurrency)
        );
      });



    this.propertyDetailForm.controls['globalRange'].valueChanges.subscribe(
      (value) => {
        if (value === 'Custom Date') {
          const possessionDate = this.propertyDetailForm.get('globalDate').value || null;
          if (possessionDate) {
            this.convertDateToMonth(possessionDate);
          }
        } else if (
          value === '6 Months' ||
          value === '1 Year' ||
          value === '2 Years'
        ) {
          this.calculateDateRange(value);
          this.selectedPossession = null;
          this.isValidPossDate = false;
        }
        this.propertyDetailForm.get('possessionDate').updateValueAndValidity();
      }
    );


    /* Fetching Places List */
    this.searchPlaceTerm$
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        filter((searchStr: string) => searchStr.length > 2)
      )
      .subscribe((searchStr: string) => {
        this._store.dispatch(new FetchLocationsWithGoogle(searchStr));
      });
    this._store.dispatch(new FetchGalleryDropdownData());

    this._store
      .select(getGalleryDropdownDataIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isGalleryDropdownDataLoading = isLoading;
      });

    this._store
      .select(getGalleryDropdownData)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.galleryDropdownData = data;
      });

    this._store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isProjectListLoading = isLoading;
      });

    this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getPropertyListDetailsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isSelectedPropertyInfoLoading: boolean) => {
        this.isSelectedPropertyInfoLoading = isSelectedPropertyInfoLoading;
      });

    this._store
      .select(getPropertyListDetails)
      .pipe(
        takeUntil(this.stopper),
        filter((propertyData: any) => Object.keys(propertyData)?.length && propertyData?.id === this.activePropertyId)
      )
      .subscribe((propertyData: any) => {
        this.selectedPropertyInfo = propertyData || {};

        if (propertyData?.noOfBHK > 5) {
          this.bhkNoList = BHK_NO_ALL;
        }
        propertyData.attributes.map((item: any) => {
          const isFloorAttribute = ['numberOfFloors', 'floorNumber'].includes(item.attributeName);

          if (isFloorAttribute && item?.value > 5) {
            this.attributeExpandedLists[item.attributeName] = Array.from(
              { length: 50 },
              (_, i) => (i + 1).toString()
            );
          } else if (item?.value > 5) {
            this.attributeExpandedLists[item.attributeName] = ATTR_NO_ALL;
          } else {
            this.attributeExpandedLists[item.attributeName] = ATTR_NO;
          }
        })

        const {
          enquiredFor,
          saleType,
          aboutProperty,
          furnishStatus,
          facing,
          rating,
          notes,
          title,
          assignedTo,
          listingOnBehalf,
          possessionDate,
          bhkType,
          noOfBHK,
        } = propertyData;
        if (Array.isArray(propertyData.links)) {
          this.links = propertyData.links.filter((link: null) => link !== null);
        } else {
          this.links = [];
        }
        if (Array.isArray(propertyData?.view360Url)) {
          this.view360Url = propertyData?.view360Url.filter((link: null) => link !== null);
        } else {
          this.view360Url = [];
        }

        /* patch basic-info form values */
        this.basicInfoForm.patchValue({
          enquiredFor: EnquiryType[enquiredFor || 3],
          propertyType: propertyData?.propertyType?.displayName,
          areaUnit: getAreaUnit(
            propertyData.dimension?.areaUnitId,
            this.areaSizeUnits
          )?.id,
          propertySubType: propertyData?.propertyType?.childType?.displayName,
          noOfBHK: noOfBHK !== 0 ? noOfBHK?.toString() : null,
          bhkType: bhkType ? BHKType[bhkType] : null,
          title,
          assignedTo: assignedTo,
          listingOnBehalf: listingOnBehalf?.[0],
          aboutProperty,
          lengthAndBreadthAreaUnit: getAreaUnit(
            propertyData.dimension?.areaUnitId,
            this.areaSizeUnits
          )?.id,
          carpetArea: propertyData?.dimension?.carpetArea,
          carpetAreaUnit: propertyData.dimension?.carpetAreaId || this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
          buildUpArea: propertyData?.dimension?.buildUpArea,
          buildUpAreaUnit: propertyData.dimension?.buildUpAreaId || this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
          saleableArea: propertyData?.dimension?.saleableArea,
          saleableAreaUnit: propertyData.dimension?.saleableAreaId || this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
          netArea: propertyData?.dimension?.netArea,
          netAreaUnit: propertyData.dimension?.netAreaUnitId || this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
          propertyLength: propertyData?.dimension?.length,
          propertyBreadth: propertyData?.dimension?.breadth,
          coWorkingOperator: propertyData?.coWorkingOperator,
          offeringType: propertyData?.offeringType > 0 ? propertyData?.offeringType : null,
          completionStatus: propertyData?.completionStatus > 0 ? propertyData?.completionStatus : null,
          isPriceVissible: propertyData?.monetaryInfo?.isPriceVissible,
          titleWithLanguage: propertyData?.titleWithLanguage,
          aboutPropertyWithLanguage: propertyData?.aboutPropertyWithLanguage,
          isFeatured: propertyData.tagInfo?.isFeatured,
        });
        this.basicInfoForm.patchValue({
          areaUnit: getAreaUnit(
            propertyData.dimension?.areaUnitId,
            this.areaSizeUnits
          )?.id,
        });
        this.basicInfoForm.controls['propertySize'].setValue(
          propertyData.dimension?.area == 0
            ? null
            : propertyData.dimension?.area
        );
        /* patch property detail form values */
        if (
          this.selectedPropertyInfo?.address &&
          !this.selectedPropertyInfo.address.placeId
        ) {
          const { city, state, subLocality, postalCode } =
            this.selectedPropertyInfo.address;
          if (city || state || subLocality || postalCode) {
            this.isShowManualLocation = true;
            const addressFields = ['enquiredLocality', 'enquiredCity', 'enquiredState', 'enquiredCountry'];
            if (this.isShowManualLocation) {
              addressFields.forEach(field => {
                toggleValidation(VALIDATION_SET, this.propertyDetailForm, field, [Validators.required]);
              });
            } else {
              addressFields.forEach(field => {
                toggleValidation(VALIDATION_CLEAR, this.propertyDetailForm, field);
              });
            }
          }
        }
        this.propertyDetailForm?.patchValue({
          saleType: SaleType[saleType],
          expectedPrice: propertyData.monetaryInfo?.expectedPrice,
          maintenanceCost: propertyData.monetaryInfo?.maintenanceCost || null,
          depositAmount: propertyData.monetaryInfo?.depositAmount,
          currency: propertyData.monetaryInfo?.currency
            ? propertyData.monetaryInfo?.currency
            : this.defaultCurrency,
          isNegotiable: propertyData.monetaryInfo?.isNegotiable,
          enquiredLocality:
            getLocalityDetailsByObj(this.selectedPropertyInfo?.address) || null,
          enquiredCity: this.selectedPropertyInfo?.address?.city,
          enquiredState: this.selectedPropertyInfo?.address?.state,
          enquiredSubCommunity: this.selectedPropertyInfo?.address?.subCommunity,
          enquiredCommunity: this.selectedPropertyInfo?.address?.community,
          enquiredPincode: this.selectedPropertyInfo?.address?.postalCode,
          enquiredCountry: this.selectedPropertyInfo?.address?.country,
          brokerage: propertyData.monetaryInfo?.brokerage,
          brokerageUnit: propertyData.monetaryInfo?.brokerageCurrency,
          possessionDate: patchTimeZoneDate(
            propertyData.possessionDate,
            this.userData?.timeZoneInfo?.baseUTcOffset
          ),
          globalRange:
            propertyData.possesionType === 0 ? null
              : PossessionType[propertyData.possesionType],
          globalDate: propertyData.enquiry?.possessionDate || null,
          project: propertyData?.project,
          notes,
          securityDepositAmount: propertyData.securityDepositAmount || null,
          securityDepositUnit: propertyData.securityDepositUnit || null,
          commonAreaCharges: propertyData.dimension?.commonAreaCharges,
          commonAreaChargesUnit: propertyData.dimension?.commonAreaChargesId,
          noticePeriod: propertyData.noticePeriod || null,
          lockInPeriod: propertyData.lockInPeriod || null,
          escalation: propertyData?.monetaryInfo?.escalationPercentage,
          tenantPOCName: propertyData?.tenantContactInfo?.name,
          tenantPOCdesignation: propertyData?.tenantContactInfo?.designation,
          tenantPOCPhone: propertyData?.tenantContactInfo?.phone,
          coWorkingOperatorPOCName: propertyData?.coWorkingOperatorName || null,
          coWorkingOperatorPOCInput:
            propertyData?.coWorkingOperatorPhone || null,
          noOfChequesAllowed:
            propertyData?.monetaryInfo?.noOfChequesAllowed || null,
          refrenceNo: propertyData?.refrenceNo || null,
          dtcmPermit: propertyData?.dtcmPermit || null,
          dldPermitNumber: propertyData?.dldPermitNumber || null,
          paymentFrequency:
            propertyData?.monetaryInfo?.paymentFrequency || null,
          taxationMode: propertyData?.taxationMode || 0,
          propertyContacts: this.patchPropertyContacts(propertyData?.propertyOwnerDetails),
        });

        if (propertyData?.possesionType === 5) {
          this.convertDateToMonth(propertyData?.possessionDate)
        }

        if (this.selectedPropertyInfo?.address?.placeId) {
          this.propertyDetailForm.patchValue({
            ...this.propertyDetailForm.value,
            enquiredState: null,
            enquiredCity: null,
            enquiredSubCommunity: null,
            enquiredCommunity: null,
            enquiredPincode: null,
            enquiredCountry: null,
            enquiredLocality: null,
            locationId: {
              id: EMPTY_GUID,
              location: getLocationDetailsByObj(
                this.selectedPropertyInfo?.address
              ),
              placeId: this.selectedPropertyInfo?.address?.placeId,
            },
          });
          this.placesList = [
            ...this.placesList,
            {
              id: EMPTY_GUID,
              location: getLocationDetailsByObj(
                this.selectedPropertyInfo?.address
              ),
              placeId: this.selectedPropertyInfo?.address?.placeId,
            },
          ];
        }
        this.propertyDetailForm.controls['rating'].setValue(rating);
        this.docList = propertyData?.brochures?.map((doc: any) => {
          return {
            ...doc,
            url: doc.url.replace(this.s3BucketUrl, ''),
          };
        });
        /* patch attribute form values */
        const propertyAttributes = propertyData.attributes;
        this.selectedAdditionalAttr = [];
        const savedAttributesSelection = propertyAttributes?.reduce(
          (acc: any, cur: any) => {
            const [attr]: any = this.allAttributes.filter(
              (item: any) => item?.id === cur.masterPropertyAttributeId
            );
            const isAdditional =
              attr?.attributeType.toLowerCase()?.replace(/\s+/g, '') ===
              'additional';
            if (isAdditional) {
              this.selectedAdditionalAttr.push(cur.masterPropertyAttributeId);
            } else {
              acc = {
                ...acc,
                [attr?.attributeName]: cur.value,
              };
            }
            return acc;
          },
          {}
        );

        if (!this.isAgricultural || !this.isPlot) {
          this.attributeForm.patchValue({
            furnishStatus:
              !furnishStatus &&
                !this.attributeForm?.get?.('furnishStatus')?.value
                ? null
                : this.attributeForm?.get?.('furnishStatus')?.value ||
                FurnishStatus[furnishStatus],
            facing:
              !facing && !this.attributeForm?.get?.('facing')?.value
                ? null
                : this.attributeForm?.get?.('facing')?.value || Facing[facing],
            numberOfBedrooms:
              this.attributeForm?.get?.('numberOfBedrooms')?.value ||
              savedAttributesSelection?.numberOfBedrooms,
            numberOfBathrooms:
              this.attributeForm?.get?.('numberOfBathrooms')?.value ||
              savedAttributesSelection?.numberOfBathrooms,
            numberOfUtilities:
              this.attributeForm?.get?.('numberOfUtilities')?.value ||
              savedAttributesSelection?.numberOfUtilities,
            numberOfKitchens:
              this.attributeForm?.get?.('numberOfKitchens')?.value ||
              savedAttributesSelection?.numberOfKitchens,
            numberOfBalconies:
              this.attributeForm?.get?.('numberOfBalconies')?.value ||
              savedAttributesSelection?.numberOfBalconies,
            numberOfDrawingOrLivingRooms:
              this.attributeForm?.get?.('numberOfDrawingOrLivingRooms')
                ?.value ||
              savedAttributesSelection?.numberOfDrawingOrLivingRooms,
            numberOfFloors:
              propertyData?.attributes?.filter(
                (att: any) => att?.attributeName === 'numberOfFloors'
              )?.[0]?.value ||
              this.attributeForm?.get?.('numberOfFloors')?.value ||
              savedAttributesSelection?.numberOfFloors,
            floorNumber:
              this.attributeForm?.get?.('floorNumber')?.value ||
              savedAttributesSelection?.floorNumber,
            maximumOccupants:
              this.attributeForm?.get?.('maximumOccupants')?.value ||
              savedAttributesSelection?.maximumOccupants,
            noOfFloorsOccupied: propertyData?.noOfFloorsOccupied,
            numberOfConferenceRooms:
              this.attributeForm?.get?.('numberOfConferenceRooms')?.value ||
              savedAttributesSelection?.numberOfConferenceRooms,
            numberOfWashRooms:
              this.attributeForm?.get?.('numberOfWashRooms')?.value ||
              savedAttributesSelection?.numberOfWashRooms,
            numberOfWorkstation:
              this.attributeForm?.get?.('numberOfWorkstation')?.value ||
              savedAttributesSelection?.numberOfWorkstation,
            numberOfMeetingRooms:
              this.attributeForm?.get?.('numberOfMeetingRooms')?.value ||
              savedAttributesSelection?.numberOfMeetingRooms,
            numberOfBikeParking:
              this.attributeForm?.get?.('numberOfBikeParking')?.value ||
              savedAttributesSelection?.numberOfBikeParking,
            numberOfCarParking:
              this.attributeForm?.get?.('numberOfCarParking')?.value ||
              savedAttributesSelection?.numberOfCarParking,
            numberOfCabins:
              this.attributeForm?.get?.('numberOfCabins')?.value ||
              savedAttributesSelection?.numberOfCabins,
            numberOfParking: this.attributeForm?.get?.('numberOfParking')?.value ||
              savedAttributesSelection?.numberOfParking,
            numberOfLivingRooms: this.attributeForm?.get?.('numberOfLivingRooms')?.value ||
              savedAttributesSelection?.numberOfLivingRooms,
          });
          if (
            Number(propertyData?.noOfFloorsOccupied?.length) ===
            Number(this.attributeForm?.get('numberOfFloors')?.value)
          ) {
            this.isAllSelected = true;
          } else {
            this.isAllSelected = false;
          }
        }
        let images = this.selectedPropertyInfo.imageUrls
          ? Object.entries(this.selectedPropertyInfo.imageUrls)
          : [];
        this.waterMarkSettingsObj.toAddWaterMark =
          this.selectedPropertyInfo?.isWaterMarkEnabled;
        this.galleryS3Paths = [];
        if (images.length > 0) {
          let allImages: { path: string, orderRank: number, isCover: boolean, category: string }[] = [];
          images.forEach((arr: any) => {
            let category = arr[0];
            let arrImg = arr[1];
            arrImg.forEach((img: any) => {
              const cleanedImagePath = img.imageFilePath.replace(this.s3BucketUrl, '');
              allImages.push({
                path: cleanedImagePath,
                orderRank: img.orderRank || 0,
                isCover: img.isCoverImage,
                category: category
              });
            });
          });
          allImages.sort((a, b) => a.orderRank - b.orderRank);
          const coverImage = allImages.find(img => img.isCover);
          allImages.forEach((img) => {
            this.galleryMapping[img.path] = img.category;
            this.galleryOrderRanks[img.path] = img.orderRank;
          });
          if (coverImage && !this.galleryS3Paths.includes(coverImage.path)) {
            this.galleryS3Paths.push(coverImage.path);
            this.coverImgIndex = 0;
            this.coverImg = coverImage.path;
          }
          allImages.forEach((img) => {
            if (!img.isCover && !this.galleryS3Paths.includes(img.path)) {
              this.galleryS3Paths.push(img.path);
            }
          });

          if (!coverImage && this.galleryS3Paths.length > 0) {
            this.coverImgIndex = 0;
            this.coverImg = this.galleryS3Paths[0];
          }
        }
        let galleryVideos = this.selectedPropertyInfo?.videos;
        this.galleryS3PathsVid = [];
        this.videoPayload = [];
        galleryVideos?.map((item: any) => {
          if (!this.galleryS3PathsVid.includes(item.imageFilePath)) {
            this.galleryS3PathsVid?.push({
              name: item?.name,
              imageFilePath: item.imageFilePath,
            });
            this.videoPayload.push({
              name: item?.name,
              imageFilePath: item.imageFilePath,
              isCoverImage: item.isCoverImage,
              galleryType: 2,
            });
          }
        });

        !this.galleryS3Paths?.length ? (this.coverImg = '') : this.coverImg;
        this.getPropertyAttributes();
      });

    this.onUnitChange('areaUnit');
    this.patchBasicInfoUnits()
  }

  get readonlyCondition(): boolean {
    const lengthValue = this.basicInfoForm.controls['propertyLength'].value;
    const breadthValue = this.basicInfoForm.controls['propertyBreadth'].value;

    return (
      (lengthValue === null ||
        lengthValue === undefined ||
        lengthValue === 0) &&
      (breadthValue === null ||
        breadthValue === undefined ||
        breadthValue === 0)
    );
  }

  possessionDateValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (this.basicInfoForm.get('enquiredFor').value === 'Sale' && this.propertyDetailForm?.get('globalRange')?.value === 'Under Construction') {
        return null;
      }
      const value = control.value;
      if (!value) {
        return { required: true };
      }
      return null;
    };
  }

  onDateSelected(event: any) {
    let selectedDate: Date;
    if (event instanceof Date) {
      selectedDate = event;
    } else if (event.value && event.value instanceof Date) {
      selectedDate = event.value;
    } else if (event.target && event.target.value) {
      selectedDate = new Date(event.target.value);
    } else if (typeof event === 'string') {
      selectedDate = new Date(event);
    } else {
      selectedDate = new Date(event);
    }
    this.propertyDetailForm.controls['possessionDate'].setValue(selectedDate);
    this.propertyDetailForm.controls['globalDate'].setValue(selectedDate);
    this.propertyDetailForm.controls['globalRange'].setValue('Custom Date');
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    };
    this.selectedPossessionDate = selectedDate.toLocaleDateString('en-US', options);
    this.selectedPossession = this.selectedPossessionDate;
    this.propertyDetailForm.controls['possessionDate'].markAsDirty();
    this.propertyDetailForm.controls['globalDate'].markAsDirty();
    this.propertyDetailForm.controls['globalRange'].markAsDirty();
    this.propertyDetailForm.controls['possessionDate'].markAsTouched();
    this.isValidPossDate = false;
  }

  getSelectedCountryCodeContactNo(numType: string): any {
    switch (numType) {
      case 'ownerNoInput':
        return this.ownerNoInput?.selectedCountry;
      case 'tenantPOCInput':
        return this.tenantPOCInput?.selectedCountry;
      case 'coWorkingOperatorPOCInput':
        return this.coWorkingOperatorPOCInput?.selectedCountry;
      case 'contactPhoneInput':
      case 'alternatePhoneInput':
        if (this.globalSettingsDetails?.countries?.length && this.globalSettingsDetails?.countries[0]?.code) {
          return { dialCode: this.globalSettingsDetails.countries[0].code };
        }
        return { dialCode: 'IN' };
      default:
        return null;
    }
  }

  contactNumberValidator(numType: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      let inputValue: any;
      switch (numType) {
        case 'tenantPOCInput':
          inputValue = this.propertyDetailForm?.get('tenantPOCPhone')?.value;
          break;
        case 'coWorkingOperatorPOCInput':
          inputValue = this.propertyDetailForm?.get(
            'coWorkingOperatorPOCInput'
          )?.value;
          break;
        case 'contactPhoneInput':
        case 'alternatePhoneInput':
          inputValue = control.value;
          break;
        default:
          return null;
      }
      if (!inputValue?.length) {
        return null;
      }
      let defaultCountry: CountryCode =
        this.getSelectedCountryCodeContactNo(numType)?.dialCode || 'IN';
      try {
        const validNumber = isPossiblePhoneNumber(inputValue, defaultCountry);
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }

  locationValidator() {
    if (!this.isShowManualLocation) {
      toggleValidation(VALIDATION_SET, this.propertyDetailForm, 'locationId', [
        Validators.required,
      ]);
      toggleValidation(
        VALIDATION_CLEAR,
        this.propertyDetailForm,
        'enquiredLocality'
      );
    } else {
      toggleValidation(
        VALIDATION_SET,
        this.propertyDetailForm,
        'enquiredLocality',
        [Validators.required]
      );
      toggleValidation(VALIDATION_CLEAR, this.propertyDetailForm, 'locationId');
    }
    this.handleNextClick();
  }


  lengthAndBreadthAreaUnitValidator() {
    if (
      this.basicInfoForm.controls['propertyLength'].value != null &&
      this.basicInfoForm.controls['propertyBreadth'].value != null &&
      this.basicInfoForm.controls['propertyLength'].value >= 0 &&
      this.basicInfoForm.controls['propertyBreadth'].value >= 0
    ) {
      this.basicInfoForm.controls['lengthAndBreadthAreaUnit'].setValidators([
        Validators.required,
      ]);
    } else {
      this.basicInfoForm.controls['lengthAndBreadthAreaUnit'].clearValidators();
    }
    this.basicInfoForm.controls[
      'lengthAndBreadthAreaUnit'
    ].updateValueAndValidity();
  }

  areaUnitValidator() {
    if (
      !(
        this.basicInfoForm.controls['propertyLength'].value != null &&
        this.basicInfoForm.controls['propertyBreadth'].value != null &&
        this.basicInfoForm.controls['propertyLength'].value >= 0 &&
        this.basicInfoForm.controls['propertyBreadth'].value >= 0
      )
    ) {
      this.basicInfoForm.controls['areaUnit'].setValidators([
        Validators.required,
      ]);
    } else {
      this.basicInfoForm.controls['areaUnit'].clearValidators();
    }
    this.basicInfoForm.controls['areaUnit'].updateValueAndValidity();
  }

  updateBrokerageUnit(value: any) {
    const dropdownControl = this.propertyDetailForm.get('brokerageUnit');
    if (!value) {
      dropdownControl.setValue(null);
    } else if (value < 100 && value) {
      dropdownControl.setValue('%');
    } else if (value >= 100) {
      dropdownControl.setValue(this.brokerageUnits[0]);
    }
  }

  updateSecurityDepositUnit(value: any) {
    const dropdownControl = this.propertyDetailForm.get('securityDepositUnit');
    if (!value) {
      dropdownControl.setValue(null);
    } else if (value < 100 && value) {
      dropdownControl.setValue('%');
    } else if (value >= 100) {
      dropdownControl.setValue(this.brokerageUnits[0]);
    }
  }

  createContactFormGroup(data?: any): FormGroup {
    return this.fb.group({
      name: [
        data?.name || data?.contactName || '',
        Validators.compose([
          Validators.maxLength(75),
          ValidationUtil.onlyAlphaNumericValidator,
        ]),
      ],
      phone: [data?.phone || data?.contactPhone || '', this.contactNumberValidator('contactPhoneInput')],
      alternatePhone: [data?.alternatePhone || '', this.contactNumberValidator('alternatePhoneInput')],
      email: [data?.email || data?.contactEmail || '', ValidationUtil.emailValidatorMinLength],
    });
  }

  patchPropertyContacts(contacts: any[]) {
    if (!contacts || !Array.isArray(contacts) || contacts.length === 0) {
      return;
    }
    while (this.propertyContacts.length) {
      this.propertyContacts.removeAt(0);
    }
    contacts.forEach(contact => {
      const mappedContact = {
        name: contact.name || contact.contactName,
        phone: contact.phone || contact.contactPhone,
        email: contact.email || contact.contactEmail,
        alternatePhone: contact.alternateContactNo || contact.alternatePhone
      };
      this.propertyContacts.push(this.createContactFormGroup(mappedContact));
    });
  }

  newContactAdded = false;

  addContact(): void {
    this.propertyContacts.push(this.createContactFormGroup());
    this.newContactAdded = true;
    setTimeout(() => {
      this.newContactAdded = false;
    }, 500);
  }

  removeContact(index: number): void {
    if (this.propertyContacts.length > 1) {
      this.propertyContacts.removeAt(index);
    }
  }

  otherAreaUnitValidator(key: string) {
    if (
      this.basicInfoForm.controls[key].value != null &&
      this.basicInfoForm.controls[key].value >= 0
    ) {
      this.basicInfoForm.controls[key + 'Unit'].setValidators([
        Validators.required,
      ]);
    } else {
      this.basicInfoForm.controls[key + 'Unit'].clearValidators();
    }
    this.basicInfoForm.controls[key + 'Unit'].updateValueAndValidity();
  }

  appendToBHKList(bhkNo: string) {
    if (bhkNo != '5+') return;
    this.bhkNoList = BHK_NO_ALL;;
  }

  appendToAttrList(attrNo: string, attributeName: string) {
    if (attrNo != '5+') return;
    this.attributeExpandedLists[attributeName] = [...ATTR_NO_ALL];
  }

  addInputField() {
    if (typeof this.newUrl === 'string' && this.newUrl.trim() !== '') {
      this.links = [...this.links, this.newUrl.trim()];
      this.newUrl = '';
    }
  }

  removeInputField(link: string) {
    const index = this.links.indexOf(link);
    if (index !== -1) {
      this.links.splice(index, 1);
    }
  }

  validateBudget(event: any) {
    const keyCode = event.keyCode;
    const excludedKeys = [8, 37, 39, 46];
    if (
      !(
        (keyCode >= 48 && keyCode <= 57) ||
        (keyCode >= 96 && keyCode <= 105) ||
        excludedKeys.includes(keyCode)
      )
    ) {
      event.preventDefault();
    }
  }

  getAttributesByType(attrType: string = 'additional') {
    const filteredAttributes = this.allAttributes
      .filter((item: any) => item.attributeType?.toLowerCase().replace(/\s+/g, '') === attrType)
      .filter((attr: any) => attr.basePropertyType.includes(this.propType));
    const propertySubType = this.basicInfoForm?.get('propertySubType')?.value;
    const propertyType = this.propertyDetailForm?.get('propertyType')?.value;
    if (!([
      "Office Space",
      "Co Working Office Space"
    ].includes(propertySubType) && propertyType !== "Commercial")) {
      return filteredAttributes.filter((data: any) =>
        ![
          'IsHavingReception',
          'IsHavingPantryRoom',
          'IsHavingServerRoom',
          'IsHavingTrainingRoom'
        ].includes(data?.attributeName)
      );
    }
    return filteredAttributes;
  }

  getPropertyAttributes(): any[] {
    const basicAttrs = this.getAttributesByType('basic');
    let { furnishStatus, facing, ...basicAttributeForm } =
      this.attributeForm.value;
    this.basicAttributes = basicAttrs.reduce((acc: any, cur: any) => {
      acc = [
        ...acc,
        {
          attributeName: cur.attributeName,
          attributeDisplayName: cur.attributeDisplayName,
          masterPropertyAttributeId: cur.id,
          value: basicAttributeForm[cur.attributeName]?.toString(),
        },
      ];
      return acc;
    }, []);
    const addAttributes = this.selectedAdditionalAttr.map((attrId: string) => {
      return {
        masterPropertyAttributeId: attrId,
      };
    });
    return [...this.basicAttributes, ...addAttributes];
  }

  onMinus(attributeName: string) {
    let value = this.attributeForm.controls[attributeName].value;
    if (value === undefined) {
      value = null;
      this.attributeForm.controls[attributeName].setValue(value);
    } else if (value > 0) {
      value--;
      this.attributeForm.controls[attributeName].setValue(value);
    }
  }

  onPlus(attributeName: string) {
    let value = this.attributeForm.controls[attributeName].value;
    if (value === undefined) {
      value = null;
      value++;
      this.attributeForm.controls[attributeName].setValue(value);
    } else if (value < 99 || value === undefined) {
      value++;
      this.attributeForm.controls[attributeName].setValue(value);
    }
  }

  isFormValid(form: FormGroup): boolean {
    if (form.valid) {
      return true;
    }
    validateAllFormFields(form);
    return false;
  }

  get isResidential(): boolean {
    return this.basicInfoForm.get('propertyType').value === 'Residential';
  }
  get isAgricultural(): boolean {
    return this.basicInfoForm.get('propertyType').value === 'Agricultural';
  }
  get isFarmHouse(): boolean {
    return this.basicInfoForm.get('propertySubType').value === 'Farm House';
  }
  get isRental(): boolean {
    return this.basicInfoForm.get('enquiredFor').value === 'Rent';
  }
  get f() {
    return this.basicInfoForm.controls;
  }
  get isPlot(): boolean {
    return this.basicInfoForm.get('propertySubType').value === 'Plot';
  }
  get canTakeNextStep(): boolean {
    switch (this.currentStep) {
      case 1:
        return this.isFormValid(this.basicInfoForm);
      case 2:
        return this.isFormValid(this.propertyDetailForm)
      case 3:
        return this.isFormValid(this.attributeForm);
      default:
        return true;
    }
  }

  addMorePropertyInfo() {
    this.modalConfirm.hide();
    this.currentStep = this.isAgricultural || this.isPlot ? 4 : 3;
    this.attributeForm
      ?.get('numberOfBedrooms')
      .setValue(
        this.attributeForm?.get('numberOfBedrooms')?.value ||
        Math.round(Number(this.basicInfoForm?.value?.noOfBHK)) ||
        ''
      );
    this.attributeForm
      ?.get('numberOfKitchens')
      .setValue(Number(this.basicInfoForm?.value?.noOfBHK) ? 1 : null);
    this.showSubmitConfirmation = false;
  }

  addSelectedAmenities(event: any) {
    this.selectedAmenities = [...event];
    if (this.selectedPropertyInfo) {
      this.selectedPropertyInfo = {
        ...this.selectedPropertyInfo,
        amenities: [...new Set([...event])],
      };
    }
    this.cdr.detectChanges();
  }

  onAdditionalAttrSelection(selectedAttributes: any) {
    this.selectedAdditionalAttr = [...selectedAttributes];
  }

  openImage(i: number) {
    this.imageIndex = i;
    this.isGalleryCarouselVisible = true;
  }

  deleteImage(index: number) {
    const deletedImageUrl = this.galleryS3Paths[index];
    this.galleryS3Paths.splice(index, 1);
    if (index === this.coverImgIndex || this.galleryS3Paths.length === 0) {
      this.coverImgIndex = 0;
      this.coverImg = this.galleryS3Paths.length > 0 ? this.galleryS3Paths[0] : '';
    } else if (index < this.coverImgIndex) {
      this.coverImgIndex--;
    }
    delete this.galleryOrderRanks[deletedImageUrl];
    this.updateImageOrderRanks();
    if (this.selectedPropertyInfo?.id || this.activePropertyId?.id) {
      this.updateGalleryOrder();
    }
  }

  fileUploadToS3() {
    if (this.selectedFileSize > 15728640) {
      this._notificationService.warn(`File size should be less than 15 MB.`);
      return;
    }
    if (this.selectedFile?.[0]?.includes('data:')) {
      this.isDocumentUploading = true
      this.imgService
        .uploadImageBase64(this.selectedFile, FolderNamesS3.PropertyDocument)
        .pipe(takeUntil(this.stopper))
        .subscribe((response: any) => {
          this.isDocumentUploading = false

          let nameWithoutExtension = this.fileName.slice(
            0,
            this.fileName.lastIndexOf('.')
          );
          if (response.data.length) {
            if (this.docList) {
              let payload: any = {
                id: this.activePropertyId.id || this.activePropertyId,
                brochureDtos: [
                  ...this.docList,
                  {
                    name: nameWithoutExtension,
                    url: response.data?.[0] || '',
                  },
                ],
              };
              this._store.dispatch(
                new UploadBrochure(
                  this.activePropertyId.id || this.activePropertyId,
                  payload
                )
              );
            } else {
              let payload: any = {
                id: this.activePropertyId.id || this.activePropertyId,
                brochureDtos: [
                  {
                    name: nameWithoutExtension,
                    url: response.data?.[0] || '',
                  },
                ],
              };
              this._store.dispatch(
                new UploadBrochure(
                  this.activePropertyId.id || this.activePropertyId,
                  payload
                )
              );
            }
            this._store
              .select(getBrochureList)
              .pipe(takeUntil(this.stopper))
              .subscribe((data: any) => {
                this.docList = data;
              });
          }
        });
    }
  }

  initDeleteDocument(index: number) {
    this.modalRef = this.modalService.show(
      this.deleteDocumentModal,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
        }
      )
    );
    this.currentDelete = index;
  }

  removeDocument(index: number) {
    const updatedDocList = this.docList.filter(
      (doc: any, i: any) => i !== index
    );
    this.docList = updatedDocList;
    let payload: any = {
      id: this.activePropertyId.id || this.activePropertyId,
      brochureDtos: updatedDocList,
    };
    this._store.dispatch(
      new UploadBrochure(
        this.activePropertyId.id || this.activePropertyId,
        payload
      )
    );
    this.modalRef.hide();
  }

  handlePreviousClick() {
    if (this.currentStep > 1) {
      if (this.currentStep === 4 && (this.isAgricultural || this.isPlot)) {
        this.currentStep = this.currentStep - 2;
      } else {
        --this.currentStep;
      }
    }
  }

  handleNextClick() {
    if (this.currentStep === 2) {
      this.propertyDetailForm.controls['possessionDate'].markAsTouched();
    }
    Object.keys(this.propertyDetailForm.controls).forEach((field) => {
      const control = this.propertyDetailForm.get(field);
      if (control) {
        control.markAsTouched();
        if (control.invalid) {
          console.log(`Invalid field: ${field}`);
        }
      }
    });
    if (this.canTakeNextStep) {
      ++this.currentStep;
    }
  }

  handleStepsNavigation(targetStep: number) {
    if (this.currentStep > targetStep) {
      this.currentStep = targetStep;
    } else {
      switch (targetStep) {
        case 1:
          this.currentStep = targetStep;
          break;

        case 2:
          if (this.basicInfoForm.valid) {
            this.currentStep = targetStep;
          }
          break;
        case 3: if (this.activePropertyId && this.basicInfoForm.valid && this.propertyDetailForm.valid) {
          this.currentStep = targetStep;
        }
          break;
        case 4: if (this.activePropertyId && this.basicInfoForm.valid && this.propertyDetailForm.valid) {
          this.currentStep = targetStep;
        }
          break;
      }
      const trackingSteps: Record<number, string> = {
        1: 'BasicInfo',
        2: 'PropertyInfo',
        3: 'Attributes',
        4: 'Amenities',
        5: 'Gallery'
      };
      if (trackingSteps[targetStep]) {
        this.trackingService.trackFeature(`Web.Property.AddProperty.${trackingSteps[targetStep]}.Click  `);
      }
    }
  }


  updateSecondStep() {
    this.updateImageOrderRanks();
    let mapping = Object.entries(this.galleryMapping);
    this.galleryPayload = {};
    mapping.forEach((item: any) => {
      if (this.galleryS3Paths.includes(item[0])) {
        if (!this.galleryPayload.hasOwnProperty(item[1])) {
          this.galleryPayload = {
            ...this.galleryPayload,
            [item[1]]: [],
          };
        }
        this.galleryPayload[item[1]].push({
          imageFilePath: item[0],
          isCoverImage: this.galleryS3Paths[this.coverImgIndex] === item[0],
          galleryType: 1,
          orderRank: this.galleryOrderRanks[item[0]] || 0,
          imageSegregationType: 0
        });
      }
    });

    let payload = {
      isWaterMarkEnabled: this.waterMarkSettingsObj.toAddWaterMark,
      propertyId: this.selectedPropertyInfo?.id || this.activePropertyId?.id,
      imageUrls: {
        ...this.galleryPayload,
        videos: this.videoPayload,
      },
    };
    if (this.selectedPropertyInfo?.id || this.activePropertyId?.id) {
      this._store.dispatch(new UpdateGallery(payload));
    }

    this.handlePropertyUpdate({});
    return true;
  }

  uploadImage(e: any) {
    // e is now [base64Array, imgNames, uploadDate, fileObjects]
    const base64Array = e[0];
    const fileObjects = e[3];

    this.imagesToProcess = base64Array;
    this.lowResolutionImages = [];
    this.pendingImageUpload = [];
    this.goodResolutionImages = [];

    if (!this.viewListing) {
      this.processImageUpload(fileObjects || this.imagesToProcess);
      return;
    }
    if (this.imagesToProcess && this.imagesToProcess.length > 0) {
      this.imagesToProcess.forEach((image, index) => {
        const img = new Image();
        img.onload = () => {
          const fileObject = fileObjects && fileObjects[index] ? fileObjects[index] : null;
          const dimensions = {
            width: img.width,
            height: img.height,
            fileName: fileObject ? fileObject.name : `Image ${index + 1}`,
            file: fileObject || image
          };
          if (img.width < this.MIN_IMAGE_WIDTH || img.height < this.MIN_IMAGE_HEIGHT) {
            this.lowResolutionImages.push(dimensions);
          } else {
            this.goodResolutionImages.push(fileObject || image);
          }

          this.pendingImageUpload.push(dimensions);
          if (this.pendingImageUpload.length === this.imagesToProcess.length) {
            if (this.goodResolutionImages.length > 0) {
              this.processImageUpload(this.goodResolutionImages);
            }
            if (this.lowResolutionImages.length > 0) {
              this.showResolutionWarning = true;
              this.modalRef = this.modalService.show(this.resolutionWarningModal, {
                class: 'modal-600 modal-dialog-centered ip-modal-unset',
                keyboard: false
              });
            }
          }
        };
        img.onerror = () => {
          const fileObject = fileObjects && fileObjects[index] ? fileObjects[index] : null;
          this.pendingImageUpload.push({
            fileName: fileObject ? fileObject.name : `Image ${index + 1}`,
            file: fileObject || image
          });
          if (this.pendingImageUpload.length === this.imagesToProcess.length) {
            const validImages = this.pendingImageUpload
              .filter(item => item.file)
              .map(item => item.file);

            if (validImages.length > 0) {
              this.processImageUpload(validImages);
            }
          }
        };
        if (typeof image === 'string' && image.startsWith('data:')) {
          img.src = image;
        }
      });
    }
  }

  processImageUpload(e: any) {
    this.galleryImageArray = e;
    if (
      this.galleryImageArray?.length &&
      !this.waterMarkSettingsObj.toAddWaterMark
    ) {
      if (this.selectedFileSize > 500 * 1024 * 1024) {
        this._notificationService.warn(`Image size should be less than 500 MB.`);
        return;
      }
      this.galleryImageArray = e;
      if (this.galleryImageArray?.length) {
        // Filter for File objects instead of base64 strings
        let filesToBeUploadToS3Bucket = this.galleryImageArray.filter(
          (file: any) => file instanceof File
        );
        if (filesToBeUploadToS3Bucket.length) {
          this.isImageUploading = true;
          this.imgService
            .uploadGalleryFilesMax500MB(filesToBeUploadToS3Bucket, FolderNamesS3.Images)
            .pipe(takeUntil(this.stopper))
            .subscribe(
              (res: any) => {
                if (res?.data) {
                  this.isImageUploading = false
                  let pathArr = res?.data;
                  const previousImageCount = this.galleryS3Paths?.length || 0;
                  pathArr?.forEach((path: string) => {
                    this.galleryS3Paths?.push(path);
                  });
                  // Set cover image only if this is the first upload
                  if (previousImageCount === 0 && this.galleryS3Paths?.length > 0) {
                    this.coverImgIndex = 0;
                    this.coverImg = this.galleryS3Paths[0];
                  }
                  this.galleryS3Paths?.forEach((img: string, index: number) => {
                    this.galleryMapping = {
                      ...this.galleryMapping,
                      [img]:
                        this.galleryMapping[img] || this.galleryDropdownData[0],
                    };
                    if (!this.galleryOrderRanks[img]) {
                      this.galleryOrderRanks[img] = index;
                    }
                  });
                  this.updateImageOrderRanks();
                  this._notificationService.success(
                    'Image uploaded successfully.'
                  );
                }
              },
              (error) => {
                this.isImageUploading = false
                this._notificationService.error(
                  'Failed to upload image. Please try again.'
                );
              }
            );
        }
      }
    }
  }

  removeLocation(type: any) {
    switch (type) {
      case 'location':
        this.propertyDetailForm.value.locationId = null;
        this.propertyDetailForm.value.enquiredLocality = null;
        this.propertyDetailForm.value.enquiredCity = null;
        this.propertyDetailForm.value.enquiredState = null;
        this.propertyDetailForm.value.enquiredSubCommunity = null;
        this.propertyDetailForm.value.enquiredCommunity = null;
        this.propertyDetailForm.value.enquiredPincode = null;
        this.propertyDetailForm.value.enquiredCountry = null;
        break;
      case 'changeLocation':
        this.propertyDetailForm.patchValue({
          enquiredLocality: null,
          enquiredCity: null,
          enquiredState: null,
          enquiredSubCommunity: null,
          enquiredCommunity: null,
          enquiredPincode: null,
          enquiredCountry: null,
        });
        break;
      case 'changeLocality':
        this.propertyDetailForm.patchValue({
          locationId: null,
        });
        break;
    }
  }

  uploadVideo(e: FileList) {
    let fileSize = e;
    if (fileSize && Array.isArray(fileSize)) {
      if (fileSize.some((size) => size[2] > 500 * 1024 * 1024)) {
        this._notificationService.warn(`Video size should be less than 500 MB.`);
        return;
      }
    }
    this.galleryVideoArray = e;
    if (this.galleryVideoArray?.length) {
      // Extract File objects from the 4th element of each video data array
      let filesToBeUploadToS3Bucket = this.galleryVideoArray
        .filter((imagePath: any) => {
          return imagePath[3] instanceof File;
        })
        .map((imagePath: any) => imagePath[3]);
      if (filesToBeUploadToS3Bucket.length) {
        this.isVideoUploading = true
        this.imgService
          .uploadGalleryFilesMax500MB(filesToBeUploadToS3Bucket, FolderNamesS3.Images)
          .pipe(takeUntil(this.stopper))
          .subscribe((res: any) => {
            if (res?.data) {
              this.isVideoUploading = false
              let pathArr = res?.data;
              pathArr?.map((path: string, index: number) => {
                this.vidPathUrl = getAWSImagePath(path);
                this.galleryS3PathsVid?.push({
                  imageFilePath: this.vidPathUrl,
                  name: this.galleryVideoArray[index][1],
                });
                // Add directly to videoPayload to avoid rebuilding entire array
                this.videoPayload.push({
                  name: this.galleryVideoArray[index][1],
                  imageFilePath: this.vidPathUrl,
                  isCoverImage: true,
                  galleryType: 2,
                });
              });
              this._notificationService.success('Video uploaded successfully.');
            }
          }, (error) => {
            this.isVideoUploading = false
            this._notificationService.error('Failed to upload video. Please try again.');
          });
      }
    }
  }


  deleteVideo(index: any) {
    this.galleryS3PathsVid.splice(index, 1);
    this.videoPayload = this.videoPayload.filter(
      (item: any, idx: any) => idx !== index
    );
  }

  handlePropertyUpdate(
    galleryImages: any) {
    const propertyType = this.basicInfoForm.controls['propertyType'].value;
    const propertySubType =
      this.basicInfoForm.controls['propertySubType'].value;
    const bhkType = this.basicInfoForm.controls['bhkType'].value;
    const { conversionFactor, id } =
      getAreaUnit(
        this.basicInfoForm.controls['lengthAndBreadthAreaUnit'].value
          ? this.basicInfoForm.controls['lengthAndBreadthAreaUnit'].value
          : this.basicInfoForm.controls['areaUnit'].value,
        this.areaSizeUnits
      ) || {};
    const carpetAreaConversionFactor =
      getAreaUnit(
        this.basicInfoForm.controls['carpetAreaUnit'].value,
        this.areaSizeUnits
      ) || {};
    const buildUpAreaConversionFactor =
      getAreaUnit(
        this.basicInfoForm.controls['buildUpAreaUnit'].value,
        this.areaSizeUnits
      ) || {};
    const saleableAreaConversionFactor =
      getAreaUnit(
        this.basicInfoForm.controls['saleableAreaUnit'].value,
        this.areaSizeUnits
      ) || {};
    const netAreaConversionFactor =
      getAreaUnit(
        this.basicInfoForm.controls['netAreaUnit'].value,
        this.areaSizeUnits
      ) || {};
    const furnishStatus = this.attributeForm.controls['furnishStatus'].value;
    const facing = this.attributeForm.controls['facing'].value;
    let propertyPayLoad: any = {
      ...this.selectedPropertyInfo,
      ...this.basicInfoForm.value,
      ...this.propertyDetailForm.value,

      title: this.basicInfoForm.value.title,
      titleWithLanguage: this.basicInfoForm.value.titleWithLanguage,
      assignedTo: this.basicInfoForm.value.assignedTo,
      listingOnBehalf: this.basicInfoForm.value.listingOnBehalf ? [this.basicInfoForm.value.listingOnBehalf] : null,
      saleType: SaleType[this.propertyDetailForm.value.saleType],
      enquiredFor: EnquiryType[this.basicInfoForm.value.enquiredFor],
      offeringType: this.basicInfoForm.value.offeringType,
      completionStatus: this.basicInfoForm.value.completionStatus,
      aboutProperty: this.basicInfoForm.value.aboutProperty,
      aboutPropertyWithLanguage:
        this.basicInfoForm.value.aboutPropertyWithLanguage,
      notes: this.propertyDetailForm.value.notes,
      furnishStatus: FurnishStatus[furnishStatus],
      status: this.selectedPropertyInfo?.status,
      rating: this.propertyDetailForm.value.rating,
      shareCount: this.selectedPropertyInfo?.shareCount,
      possessionDate: this.propertyDetailForm.value.globalRange === 'Under Construction' ?
        null :
        setTimeZoneDate(
          this.propertyDetailForm.value.possessionDate,
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
      possesionType: this.propertyDetailForm.value.globalRange
        ? PossessionType[this.propertyDetailForm.value.globalRange]
        : 0,
      facing: Facing[facing],
      noOfBHK:
        this.basicInfoForm.controls['propertySubType'].value != 'Plot' &&
          this.isResidential
          ? parseFloat(this.basicInfoForm.value.noOfBHK) || 0
          : 0,
      bhkType:
        this.basicInfoForm.controls['propertySubType'].value != 'Plot' &&
          this.isResidential
          ? BHKType[this.basicInfoForm.value.bhkType]
          : 0,
      monetaryInfo: {
        isNegotiable: this.propertyDetailForm.value.isNegotiable,
        expectedPrice: this.propertyDetailForm.value.expectedPrice,
        maintenanceCost: this.propertyDetailForm.value.maintenanceCost,
        depositAmount: this.propertyDetailForm.value.depositAmount,
        currency: this.propertyDetailForm.value.currency
          ? this.propertyDetailForm.value.currency
          : this.defaultCurrency,
        brokerage: this.propertyDetailForm.value?.brokerage,
        brokerageCurrency: this.propertyDetailForm.value?.brokerageUnit,
        escalationPercentage: this.propertyDetailForm.value?.escalation,
        noOfChequesAllowed: this.propertyDetailForm.value?.noOfChequesAllowed,
        isPriceVissible: this.basicInfoForm.value.isPriceVissible,
        paymentFrequency: this.propertyDetailForm.value.paymentFrequency,
      },
      propertyOwnerDetails: this.propertyContacts.value.map((contact: any) => ({
        name: contact.name,
        phone: contact.phone?.toString(),
        email: contact.email,
        alternateContactNo: contact.alternatePhone?.toString()
      })) || [],
      tenantContactInfo: {
        name: this.propertyDetailForm.value.tenantPOCName,
        phone: this.propertyDetailForm.value.tenantPOCPhone?.toString(),
        designation: this.propertyDetailForm.value.tenantPOCdesignation,
      },
      coWorkingOperator: this.basicInfoForm.value.coWorkingOperator,
      coWorkingOperatorName:
        this.propertyDetailForm.value.coWorkingOperatorPOCName,
      coWorkingOperatorPhone:
        this.propertyDetailForm.value.coWorkingOperatorPOCInput,
      securityDepositAmount: this.propertyDetailForm.value.securityDepositAmount || null,
      securityDepositUnit: this.propertyDetailForm.value.securityDepositUnit,
      refrenceNo: this.propertyDetailForm.value.refrenceNo,
      dtcmPermit:
        this.selectedPermitType === 'dtcmPermit'
          ? this.propertyDetailForm.value.dtcmPermit
          : null,
      dldPermitNumber:
        this.selectedPermitType === 'dldPermitNumber'
          ? this.propertyDetailForm.value.dldPermitNumber
          : null,
      lockInPeriod: this.propertyDetailForm.value.lockInPeriod || 0,
      noticePeriod: this.propertyDetailForm.value.noticePeriod || 0,
      noOfFloorsOccupied: this.attributeForm.value?.noOfFloorsOccupied,
      dimension: {
        area: this.basicInfoForm.value.propertySize || 0,
        areaUnitId: id || EMPTY_GUID,
        conversionFactor: conversionFactor,
        length: this.basicInfoForm.controls['propertyLength'].value,
        breadth: this.basicInfoForm.controls['propertyBreadth'].value,
        carpetArea: this.basicInfoForm.controls['carpetArea'].value,
        carpetAreaId: this.basicInfoForm.controls['carpetAreaUnit'].value,
        carpetAreaConversionFactor: carpetAreaConversionFactor.conversionFactor,
        buildUpArea: this.basicInfoForm.controls['buildUpArea'].value,
        buildUpAreaId: this.basicInfoForm.controls['buildUpAreaUnit'].value,
        buildUpAreaConversionFactor:
          buildUpAreaConversionFactor.conversionFactor,
        saleableArea: this.basicInfoForm.controls['saleableArea'].value,
        saleableAreaId: this.basicInfoForm.controls['saleableAreaUnit'].value,
        saleableAreaConversionFactor:
          saleableAreaConversionFactor.conversionFactor,
        netArea: this.basicInfoForm.controls['netArea'].value,
        netAreaUnitId: this.basicInfoForm.controls['netAreaUnit'].value,
        netAreaConversionFactor:
          netAreaConversionFactor.conversionFactor,
        commonAreaCharges:
          this.propertyDetailForm.controls['commonAreaCharges'].value,
        commonAreaChargesId:
          this.propertyDetailForm.controls['commonAreaChargesUnit'].value,
        currency: this.propertyDetailForm.value.currency
          ? this.propertyDetailForm.value.currency
          : this.defaultCurrency,
      },
      tagInfo: {
        isFeatured: this.basicInfoForm.value.isFeatured || false,
      },
      imageUrls: this.galleryPayload,
      brochures: this.docList,
      attributes: this.getPropertyAttributes(),
      amenities: this.selectedAmenities.length
        ? this.selectedAmenities
        : this.selectedPropertyInfo?.amenities,
      links: this.links,
      view360Url: this.view360Url,
      project: this.propertyDetailForm?.value?.project,
      propertyTypeId: getPropertyTypeId(
        this.propertyTypeList,
        propertyType,
        propertySubType,
        bhkType
      ),
      taxationMode: this.propertyDetailForm.value.taxationMode,

    };
    if (!this.isShowManualLocation) {
      propertyPayLoad.address = {
        locationId:
          this.propertyDetailForm.value?.locationId?.id ??
          this.propertyDetailForm.value?.locationId?.id,
        placeId: this.propertyDetailForm.value?.locationId?.placeId
          ? this.propertyDetailForm.value?.locationId?.placeId
          : this.selectedPropertyInfo?.address?.placeId,
      };
    } else {
      propertyPayLoad.address = {
        subLocality:
          this.propertyDetailForm.value?.enquiredLocality ??
          this.propertyDetailForm.value?.enquiredLocality,
        city:
          this.propertyDetailForm.value?.enquiredCity ??
          this.propertyDetailForm.value?.enquiredCity,
        state:
          this.propertyDetailForm.value?.enquiredState ??
          this.propertyDetailForm.value?.enquiredState,
        subCommunity:
          this.propertyDetailForm.value?.enquiredSubCommunity ??
          this.propertyDetailForm.value?.enquiredSubCommunity,
        community:
          this.propertyDetailForm.value?.enquiredCommunity ??
          this.propertyDetailForm.value?.enquiredCommunity,
        country: this.propertyDetailForm.value?.enquiredCountry ??
          this.propertyDetailForm.value?.enquiredCountry,
        postalCode: this.propertyDetailForm.value?.enquiredPincode ??
          this.propertyDetailForm.value?.enquiredPincode,
      };
    }
    this.galleryPayload = {};
    // propertyPayLoad = {
    //   ...propertyPayLoad,
    //   id: this.activePropertyId.id || this.activePropertyId,
    // };
    let mapping = Object.entries(this.galleryMapping);
    mapping.forEach((item: any) => {
      if (this.galleryS3Paths.includes(item[0])) {
        if (!this.galleryPayload.hasOwnProperty(item[1])) {
          this.galleryPayload[item[1]] = [];
        }
        this.galleryPayload[item[1]].push({
          imageFilePath: item[0],
          isCoverImage: this.galleryS3Paths[this.coverImgIndex] === item[0],
        });
      }
    });
    propertyPayLoad.imageUrls = this.galleryPayload;
    propertyPayLoad.imageUrls = {
      ...propertyPayLoad.imageUrls,
      videos: this.videoPayload,
    };
    (propertyPayLoad.isWaterMarkEnabled =
      this.waterMarkSettingsObj.toAddWaterMark);
    if (this.activePropertyId) {

      this._store.dispatch(
        new UpdateProperty(
          propertyPayLoad,
          propertyPayLoad.id ||
          this.activePropertyId.id ||
          this.activePropertyId
        )
      );
      this.closeModal();
    } else {
      this._store.dispatch(new AddProperty(propertyPayLoad));
      this._store
        .select((state) => state.property.addedPropertyId)
        .pipe(takeUntil(this.stopper))
        .subscribe((propertyId: any) => {
          if (propertyId && Object.keys(propertyId).length > 0) {
            this.activePropertyId = propertyId;
          }
        })
    }
  }

  setImageCategory(e: any, url: any) {
    this.galleryMapping = {
      ...this.galleryMapping,
      [url]: e,
    };
  }

  onSetCoverImage(img: string, index: number) {
    this.galleryS3Paths.splice(index, 1);
    this.galleryS3Paths.unshift(img);
    this.coverImgIndex = 0;
    this.coverImg = img;
    this.updateImageOrderRanks();
    if (this.selectedPropertyInfo?.id || this.activePropertyId?.id) {
      this.updateGalleryOrder();
    }
  }

  onDragStart(index: number, event: DragEvent) {
    this.draggedImageIndex = index;
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('text/plain', index.toString());
    }
  }

  onDragOver(index: number, event: DragEvent) {
    event.preventDefault();
    this.draggedOverImageIndex = index;
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }
  }

  onDragLeave() {
    this.draggedOverImageIndex = -1;
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    if (this.draggedImageIndex !== -1 && this.draggedOverImageIndex !== -1 && this.draggedImageIndex !== this.draggedOverImageIndex) {
      const draggedImage = this.galleryS3Paths[this.draggedImageIndex];
      this.galleryS3Paths.splice(this.draggedImageIndex, 1);
      this.galleryS3Paths.splice(this.draggedOverImageIndex, 0, draggedImage);
      if (this.draggedOverImageIndex === 0) {
        this.coverImgIndex = 0;
        this.coverImg = draggedImage;
      }
      else if (this.coverImgIndex === this.draggedImageIndex) {
        this.coverImgIndex = this.draggedOverImageIndex;
      }
      else if (
        (this.coverImgIndex > this.draggedImageIndex && this.coverImgIndex <= this.draggedOverImageIndex) ||
        (this.coverImgIndex < this.draggedImageIndex && this.coverImgIndex >= this.draggedOverImageIndex)
      ) {
        this.coverImgIndex += this.draggedImageIndex < this.draggedOverImageIndex ? -1 : 1;
      }
      this.updateImageOrderRanks();
      this.updateGalleryOrder();
    }
    this.draggedImageIndex = -1;
    this.draggedOverImageIndex = -1;
  }

  updateImageOrderRanks() {
    this.galleryS3Paths.forEach((url, index) => {
      this.galleryOrderRanks[url] = index;
    });
  }

  isFloorAttribute(attrName: string): boolean {
    return attrName === 'numberOfFloors' || attrName === 'floorNumber';
  }

  getFloorOptions(attrName: string): (number | string)[] {
    if (attrName === 'numberOfFloors') {
      const numericFloors = Array.from({ length: 200 }, (_, i) => i + 1);
      return ['Ground Floor', ...numericFloors];
    } else if (attrName === 'floorNumber') {
      return this.floorDropdownOptions;
    }
    return this.dropdownOptions;
  }


  onSaveAndNextClick(): void {
    const updateSuccess = this.updateSecondStep();
    if (updateSuccess) {
      const interval = setInterval(() => {
        const isLoading = this.isPropertyAddIsLoading;
        if (!isLoading) {
          clearInterval(interval);
          if (this.activePropertyId.id) {
            this.activePropertyId = this.activePropertyId.id;
            this._store.dispatch(new FetchPropertyById(this.activePropertyId));
          }
          this.handleNextClick();
        }
      }, 100);
    }
  }

  onSaveAndNextClickStep2(): void {
    this.validateLocationOnly();
    this.propertyDetailForm.controls['possessionDate'].markAsTouched();
    Object.keys(this.propertyDetailForm.controls).forEach((field) => {
      const control = this.propertyDetailForm.get(field);
      if (control) {
        control.markAsTouched();
      }
    });
    setTimeout(() => {
      const isStep2Valid = this.isFormValid(this.propertyDetailForm);
      const isStep1Valid = this.isFormValid(this.basicInfoForm);
      if (isStep1Valid && isStep2Valid) {
        const updateSuccess = this.updateSecondStep();
        if (updateSuccess) {
          const checkSaveComplete = () => {
            const isLoading = this.isPropertyAddIsLoading;
            if (!isLoading) {
              if (this.activePropertyId?.id) {
                this.activePropertyId = this.activePropertyId.id;
                this._store.dispatch(new FetchPropertyById(this.activePropertyId));
              }
              this.handleNextClick();
            } else {
              setTimeout(checkSaveComplete, 100);
            }
          };
          setTimeout(checkSaveComplete, 100);
        }
      } else {
        if (!isStep2Valid) {
          Object.keys(this.propertyDetailForm.controls).forEach((field) => {
            const control = this.propertyDetailForm.get(field);
            if (control && control.invalid) {
              console.log(`Invalid field: ${field}`, control.errors);
            }
          });
        }
      }
    }, 100);
  }

  validateLocationOnly() {
    if (!this.isShowManualLocation) {
      toggleValidation(VALIDATION_SET, this.propertyDetailForm, 'locationId', [
        Validators.required,
      ]);
      toggleValidation(
        VALIDATION_CLEAR,
        this.propertyDetailForm,
        'enquiredLocality'
      );
    } else {
      toggleValidation(
        VALIDATION_SET,
        this.propertyDetailForm,
        'enquiredLocality',
        [Validators.required]
      );
      toggleValidation(VALIDATION_CLEAR, this.propertyDetailForm, 'locationId');
    }
  }

  updateGalleryOrder() {
    let mapping = Object.entries(this.galleryMapping);
    this.galleryPayload = {};
    mapping.forEach((item: any) => {
      if (this.galleryS3Paths.includes(item[0])) {
        if (!this.galleryPayload.hasOwnProperty(item[1])) {
          this.galleryPayload = {
            ...this.galleryPayload,
            [item[1]]: [],
          };
        }

        this.galleryPayload[item[1]].push({
          imageFilePath: item[0],
          isCoverImage: this.galleryS3Paths[this.coverImgIndex] === item[0],
          galleryType: 1,
          orderRank: this.galleryOrderRanks[item[0]] || 0,
          imageSegregationType: 0
        });
      }
    });
    let payload = {
      isWaterMarkEnabled: this.waterMarkSettingsObj.toAddWaterMark,
      propertyId: this.selectedPropertyInfo?.id || this.activePropertyId?.id,
      imageUrls: {
        ...this.galleryPayload,
        videos: this.videoPayload,
      },
    };
    if (this.selectedPropertyInfo?.id || this.activePropertyId?.id) {
      this._store.dispatch(new UpdateGallery(payload));
    }
  }

  closeModal() {
    this.isValidPossDate = false;
    this.propertyDetailForm.controls['possessionDate'].markAsUntouched();
    if (
      this.propertyDetailForm.controls['globalRange'].value === 'Custom Date' &&
      !this.selectedMonthAndYear
    ) {
      this.propertyDetailForm.controls['globalRange'].setValue(null);
      this.propertyDetailForm.controls['possessionDate'].setValue(null);
      this.propertyDetailForm.controls['globalDate'].setValue(null);
      this.selectedPossession = null;
    }

    this.isOpenPossessionModal = false;
    this.modalRef?.hide();
    this.modalRefClose?.hide();
    this.modalConfirm?.hide();
  }

  goToManage() {
    this.router.navigate(['/properties/manage-properties']);
  }

  allAttributeSelectionCheck() {
    return (
      this.getAttributesByType().length > 0 &&
      this.selectedAdditionalAttr.length ===
      this.getAttributesByType().length - 2
    );
  }

  lengthAndBreadthChanged(): void {
    const length = this.basicInfoForm.controls.propertyLength.value;
    const breadth = this.basicInfoForm.controls.propertyBreadth.value;
    if (length >= 0 && breadth >= 0) {
      this.basicInfoForm.patchValue({
        propertySize: Number((length * breadth).toFixed(2)),
      });
    }
  }

  waterMarkImages(data: any) {
    let filedata = data;
    if (filedata && Array.isArray(filedata)) {
      if (filedata.some((size) => size.size > 5000000)) {
        return;
      }
    }

    if (this.waterMarkSettingsObj.toAddWaterMark) {
      let imgURL = this.waterMarkSettingsObj.watermarkLogo;
      let watermarkpayload = {
        Url: imgURL,
        files: filedata,
        WaterMarkPosition:
          this.waterMarkSettingsObj.watermarkPosition === 0
            ? '0'
            : this.waterMarkSettingsObj.watermarkPosition || 0,
        Opacity: this.waterMarkSettingsObj.watermarkOpacity || 100,
        Background: false,
        ImageSize: this.waterMarkSettingsObj.watermarkSize || 7,
      };
      this.waterMarkSettingsObj.fetchingFromGallery = true;
      this._store.dispatch(new AddWaterMark(watermarkpayload));
    }
  }

  onUnitChange(unit: any) {
    const areaUnit = this.basicInfoForm.get(unit).value;
    this.basicInfoForm.controls[unit]?.setValue(null);
    if (
      !this.basicInfoForm.get('saleableAreaUnit').value &&
      !this.basicInfoForm.get('buildUpAreaUnit').value &&
      !this.basicInfoForm.get('carpetAreaUnit').value &&
      !this.basicInfoForm.get('areaUnit').value &&
      !this.basicInfoForm.get('netAreaUnit').value
    ) {
      this.basicInfoForm.controls['saleableAreaUnit'].setValue(areaUnit);
      this.basicInfoForm.controls['buildUpAreaUnit'].setValue(areaUnit);
      this.basicInfoForm.controls['carpetAreaUnit'].setValue(areaUnit);
      this.basicInfoForm.controls['areaUnit'].setValue(areaUnit);
      this.basicInfoForm.controls['netAreaUnit'].setValue(areaUnit);
    } else {
      this.basicInfoForm.controls[unit].setValue(areaUnit);
    }
  }

  onSelectionChange(event: any, controlName: string) {
    const lastUser = event[event.length - 1];
    if (lastUser && !lastUser.isActive) {
      event.pop();
    }
    const newlySelectedItems = event.map((item: any) => item?.id);
    this.basicInfoForm?.get(controlName)?.setValue(newlySelectedItems);
  }

  addManualLocation() {
    this.isShowManualLocation = !this.isShowManualLocation;
    const addressFields = ['enquiredLocality', 'enquiredCity', 'enquiredState', 'enquiredCountry'];
    if (this.isShowManualLocation) {
      addressFields.forEach(field => {
        toggleValidation(VALIDATION_SET, this.propertyDetailForm, field, [Validators.required]);
      });
    } else {
      addressFields.forEach(field => {
        toggleValidation(VALIDATION_CLEAR, this.propertyDetailForm, field);
      });
      this.propertyDetailForm.patchValue({
        enquiredLocality: null,
        enquiredCity: null,
        enquiredState: null,
        enquiredCountry: null
      });
    }
  }

  handleSelectAll() {
    if (!this.isAllSelected) {
      this.selectedFloors = [...this.selectedTotalFloor];
      this.attributeForm
        .get('noOfFloorsOccupied')
        .setValue(this.selectedFloors);
      this.isAllSelected = true;
    } else {
      this.selectedFloors = [];
      this.attributeForm
        .get('noOfFloorsOccupied')
        .setValue(this.selectedFloors);
      this.isAllSelected = false;
    }
  }

  patchBasicInfoUnits() {
    const defaultUnits = {
      areaUnit: this.basicInfoForm?.get('areaUnit')?.value ?? this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
      carpetAreaUnit: this.basicInfoForm?.get('carpetAreaUnit')?.value ?? this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
      buildUpAreaUnit: this.basicInfoForm?.get('buildUpAreaUnit')?.value ?? this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
      saleableAreaUnit: this.basicInfoForm?.get('saleableAreaUnit')?.value ?? this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
      netAreaUnit: this.basicInfoForm?.get('netAreaUnit')?.value ?? this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
    };
    this.basicInfoForm?.patchValue(defaultUnits);
  }

  monthChanged(event: any) {
    const selectedMonth = event.getMonth();
    const selectedYear = event.getFullYear();
    const lastDateOfMonth = new Date(selectedYear, selectedMonth + 1, 0);
    this.selectedMonthAndYear = lastDateOfMonth;
    this.propertyDetailForm.controls['globalDate'].setValue(this.selectedMonthAndYear);
    this.propertyDetailForm.controls['possessionDate'].setValue(this.selectedMonthAndYear);
    this.propertyDetailForm.controls['globalRange'].setValue('Custom Date');
    this.propertyDetailForm.controls['globalDate'].markAsDirty();
    this.propertyDetailForm.controls['possessionDate'].markAsDirty();
    this.propertyDetailForm.controls['globalRange'].markAsDirty();

    this.isValidPossDate = false;
    this.selectedMonth = this.selectedMonthAndYear.toLocaleString('default', {
      month: 'short',
    });
    this.selectedYear = this.selectedMonthAndYear.getFullYear().toString();
    this.selectedPossession = `${this.selectedMonth} ${this.selectedYear}`;
    this.isOpenPossessionModal = false;
    if (this.dt5) {
      this.dt5.close();
    }
  }

  closePossessionModal() {
    this.isValidPossDate = false;
    this.propertyDetailForm.controls['possessionDate'].markAsUntouched();
    if (
      this.propertyDetailForm.controls['globalRange'].value === 'Custom Date' &&
      !this.selectedMonthAndYear
    ) {
      this.propertyDetailForm.controls['globalRange'].setValue(null);
      this.propertyDetailForm.controls['possessionDate'].setValue(null);
      this.propertyDetailForm.controls['globalDate'].setValue(null);
      this.selectedPossession = null;
    }
    this.isOpenPossessionModal = false;
  }

  handlePossessionRangeChange(value: string): void {
    this.propertyDetailForm.get('globalRange')?.setValue(value);
    if (value === 'Custom Date') {
      this.currentDate = new Date();
      if (this.selectedPossession === '6 Months' ||
        this.selectedPossession === '1 Year' ||
        this.selectedPossession === '2 Years' ||
        this.selectedPossession === 'Under Construction') {
        this.selectedMonth = null;
        this.selectedYear = null;
        this.selectedMonthAndYear = null;
        this.propertyDetailForm.controls['globalDate'].setValue(null);
        this.propertyDetailForm.controls['possessionDate'].setValue(null);
      } else {
        this.convertDateToMonth(
          this.propertyDetailForm.get('globalDate').value ?? this.selectedPropertyInfo?.possessionDate
        );
      }

      this.isOpenPossessionModal = true;
    } else if (
      value === '6 Months' ||
      value === '1 Year' ||
      value === '2 Years'
    ) {
      this.calculateDateRange(value);
      const endDate = this.propertyDetailForm.get('globalDate').value;
      this.propertyDetailForm.controls['possessionDate'].setValue(endDate);
      this.propertyDetailForm.controls['possessionDate'].markAsTouched();
      this.selectedPossession = value;
      this.selectedMonth = null;
      this.selectedYear = null;
      this.selectedMonthAndYear = null;
      this.isValidPossDate = false;
      this.isOpenPossessionModal = false;
    } else if (value === 'Under Construction') {
      // For Under Construction, set possessionDate to null
      this.propertyDetailForm.controls['possessionDate'].setValue(null);
      this.propertyDetailForm.controls['globalDate'].setValue(null);
      this.propertyDetailForm.controls['possessionDate'].markAsDirty();
      this.propertyDetailForm.controls['possessionDate'].markAsTouched();
      this.propertyDetailForm.controls['globalDate'].markAsDirty();

      this.selectedPossession = value;
      this.selectedMonth = null;
      this.selectedYear = null;
      this.selectedMonthAndYear = null;
      this.isOpenPossessionModal = false;
    }
    this.propertyDetailForm.controls['globalRange'].markAsDirty();
    this.propertyDetailForm.get('possessionDate').updateValueAndValidity();
  }

  openPreview(language: 'eng' | 'arabic', isForNotes: boolean): void {
    this.isForNotes = isForNotes
    this.previewLanguage = language;
    this.modalRef = this.modalService.show(this.previewModal, {
      class: 'modal-600 modal-dialog-centered ip-modal-unset',
    });
  }

  continueWithLowResImages() {
    this.modalRef.hide();
    this.showResolutionWarning = false;
    if (this.lowResolutionImages && this.lowResolutionImages.length > 0) {
      const lowResFiles = this.lowResolutionImages.map(item => item.file);
      this.processImageUpload(lowResFiles);
    }
    setTimeout(() => {
      this.pendingImageUpload = [];
      this.lowResolutionImages = [];
      this.goodResolutionImages = [];
      this.imagesToProcess = [];
    }, 500);
  }

  cancelLowResImages() {
    this.closeModal();
    this.showResolutionWarning = false;
    this.pendingImageUpload = [];
    this.lowResolutionImages = [];
    this.imagesToProcess = [];
    setTimeout(() => {
      try {
        const fileInput = this.fileUploadComponent?.fileInputRef?.nativeElement
          || document.querySelector('input[type="file"]') as HTMLInputElement;

        if (fileInput) {
          fileInput.click();
          return;
        }

        const uploadButton = document.querySelector('.upload-button') as HTMLElement;
        if (uploadButton) {
          uploadButton.click();
          return;
        }
        this._notificationService.info("Please click on '+ Add Photos' to upload new images");
      } catch {
        this._notificationService.info("Please click on '+ Add Photos' to upload new images");
      }
    }, 300);
  }

  getPreviewContent(): string {
    if (this.isForNotes) {
      return this.propertyDetailForm.controls['notes'].value
    }
    if (this.previewLanguage === 'arabic') {
      return this.basicInfoForm.controls['aboutPropertyWithLanguage'].value || this.selectedPropertyInfo.aboutPropertyWithLanguage;
    }
    return this.basicInfoForm.controls['aboutProperty'].value || this.selectedPropertyInfo.aboutProperty;
  }

  convertDateToMonth(data: any) {
    if (!data) return;
    this.selectedMonthAndYear = data;
    this.selectedMonth = MONTHS[parseInt(data?.slice(5, 7), 10) - 1];
    this.selectedYear = parseInt(data?.slice(0, 4), 10);
    this.selectedPossession = this.selectedMonth
      ? this.selectedMonth + ' ' + this.selectedYear
      : null;
  }

  calculateDateRange(value: string): Date {
    const currentDate = new Date(this.currentDate);
    let startDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth() + 1,
      1
    );
    let endDate: Date;

    if (value === '6 Months') {
      endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 6, 0);
    } else if (value === '1 Year') {
      endDate = new Date(startDate.getFullYear() + 1, startDate.getMonth(), 0);
    } else if (value === '2 Years') {
      endDate = new Date(startDate.getFullYear() + 2, startDate.getMonth(), 0);
    }

    this.propertyDetailForm.controls['globalDate'].setValue(endDate);
    return endDate;
  }

  ngOnDestroy() {
    this.waterMarkSettingsObj.fetchingFromGallery = false;
    this._store.dispatch(new FetchPropertyByIdSuccess({}))
    this.stopper.next();
    this.stopper.complete();
  }
}
