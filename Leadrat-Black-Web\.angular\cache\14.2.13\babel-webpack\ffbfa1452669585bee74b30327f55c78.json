{"ast": null, "code": "import Stack from './_Stack.js';\nimport baseIsEqual from './_baseIsEqual.js';\n/** Used to compose bitmasks for value comparisons. */\n\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n/**\n * The base implementation of `_.isMatch` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The object to inspect.\n * @param {Object} source The object of property values to match.\n * @param {Array} matchData The property names, values, and compare flags to match.\n * @param {Function} [customizer] The function to customize comparisons.\n * @returns {boolean} Returns `true` if `object` is a match, else `false`.\n */\n\nfunction baseIsMatch(object, source, matchData, customizer) {\n  var index = matchData.length,\n      length = index,\n      noCustomizer = !customizer;\n\n  if (object == null) {\n    return !length;\n  }\n\n  object = Object(object);\n\n  while (index--) {\n    var data = matchData[index];\n\n    if (noCustomizer && data[2] ? data[1] !== object[data[0]] : !(data[0] in object)) {\n      return false;\n    }\n  }\n\n  while (++index < length) {\n    data = matchData[index];\n    var key = data[0],\n        objValue = object[key],\n        srcValue = data[1];\n\n    if (noCustomizer && data[2]) {\n      if (objValue === undefined && !(key in object)) {\n        return false;\n      }\n    } else {\n      var stack = new Stack();\n\n      if (customizer) {\n        var result = customizer(objValue, srcValue, key, object, source, stack);\n      }\n\n      if (!(result === undefined ? baseIsEqual(srcValue, objValue, COMPARE_PARTIAL_FLAG | COMPARE_UNORDERED_FLAG, customizer, stack) : result)) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n\nexport default baseIsMatch;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "baseIsEqual", "COMPARE_PARTIAL_FLAG", "COMPARE_UNORDERED_FLAG", "baseIsMatch", "object", "source", "matchData", "customizer", "index", "length", "noCustomizer", "Object", "data", "key", "objValue", "srcValue", "undefined", "stack", "result"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/lodash-es/_baseIsMatch.js"], "sourcesContent": ["import Stack from './_Stack.js';\nimport baseIsEqual from './_baseIsEqual.js';\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n\n/**\n * The base implementation of `_.isMatch` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The object to inspect.\n * @param {Object} source The object of property values to match.\n * @param {Array} matchData The property names, values, and compare flags to match.\n * @param {Function} [customizer] The function to customize comparisons.\n * @returns {boolean} Returns `true` if `object` is a match, else `false`.\n */\nfunction baseIsMatch(object, source, matchData, customizer) {\n  var index = matchData.length,\n      length = index,\n      noCustomizer = !customizer;\n\n  if (object == null) {\n    return !length;\n  }\n  object = Object(object);\n  while (index--) {\n    var data = matchData[index];\n    if ((noCustomizer && data[2])\n          ? data[1] !== object[data[0]]\n          : !(data[0] in object)\n        ) {\n      return false;\n    }\n  }\n  while (++index < length) {\n    data = matchData[index];\n    var key = data[0],\n        objValue = object[key],\n        srcValue = data[1];\n\n    if (noCustomizer && data[2]) {\n      if (objValue === undefined && !(key in object)) {\n        return false;\n      }\n    } else {\n      var stack = new Stack;\n      if (customizer) {\n        var result = customizer(objValue, srcValue, key, object, source, stack);\n      }\n      if (!(result === undefined\n            ? baseIsEqual(srcValue, objValue, COMPARE_PARTIAL_FLAG | COMPARE_UNORDERED_FLAG, customizer, stack)\n            : result\n          )) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\nexport default baseIsMatch;\n"], "mappings": "AAAA,OAAOA,KAAP,MAAkB,aAAlB;AACA,OAAOC,WAAP,MAAwB,mBAAxB;AAEA;;AACA,IAAIC,oBAAoB,GAAG,CAA3B;AAAA,IACIC,sBAAsB,GAAG,CAD7B;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,WAAT,CAAqBC,MAArB,EAA6BC,MAA7B,EAAqCC,SAArC,EAAgDC,UAAhD,EAA4D;EAC1D,IAAIC,KAAK,GAAGF,SAAS,CAACG,MAAtB;EAAA,IACIA,MAAM,GAAGD,KADb;EAAA,IAEIE,YAAY,GAAG,CAACH,UAFpB;;EAIA,IAAIH,MAAM,IAAI,IAAd,EAAoB;IAClB,OAAO,CAACK,MAAR;EACD;;EACDL,MAAM,GAAGO,MAAM,CAACP,MAAD,CAAf;;EACA,OAAOI,KAAK,EAAZ,EAAgB;IACd,IAAII,IAAI,GAAGN,SAAS,CAACE,KAAD,CAApB;;IACA,IAAKE,YAAY,IAAIE,IAAI,CAAC,CAAD,CAArB,GACIA,IAAI,CAAC,CAAD,CAAJ,KAAYR,MAAM,CAACQ,IAAI,CAAC,CAAD,CAAL,CADtB,GAEI,EAAEA,IAAI,CAAC,CAAD,CAAJ,IAAWR,MAAb,CAFR,EAGM;MACJ,OAAO,KAAP;IACD;EACF;;EACD,OAAO,EAAEI,KAAF,GAAUC,MAAjB,EAAyB;IACvBG,IAAI,GAAGN,SAAS,CAACE,KAAD,CAAhB;IACA,IAAIK,GAAG,GAAGD,IAAI,CAAC,CAAD,CAAd;IAAA,IACIE,QAAQ,GAAGV,MAAM,CAACS,GAAD,CADrB;IAAA,IAEIE,QAAQ,GAAGH,IAAI,CAAC,CAAD,CAFnB;;IAIA,IAAIF,YAAY,IAAIE,IAAI,CAAC,CAAD,CAAxB,EAA6B;MAC3B,IAAIE,QAAQ,KAAKE,SAAb,IAA0B,EAAEH,GAAG,IAAIT,MAAT,CAA9B,EAAgD;QAC9C,OAAO,KAAP;MACD;IACF,CAJD,MAIO;MACL,IAAIa,KAAK,GAAG,IAAIlB,KAAJ,EAAZ;;MACA,IAAIQ,UAAJ,EAAgB;QACd,IAAIW,MAAM,GAAGX,UAAU,CAACO,QAAD,EAAWC,QAAX,EAAqBF,GAArB,EAA0BT,MAA1B,EAAkCC,MAAlC,EAA0CY,KAA1C,CAAvB;MACD;;MACD,IAAI,EAAEC,MAAM,KAAKF,SAAX,GACEhB,WAAW,CAACe,QAAD,EAAWD,QAAX,EAAqBb,oBAAoB,GAAGC,sBAA5C,EAAoEK,UAApE,EAAgFU,KAAhF,CADb,GAEEC,MAFJ,CAAJ,EAGO;QACL,OAAO,KAAP;MACD;IACF;EACF;;EACD,OAAO,IAAP;AACD;;AAED,eAAef,WAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}