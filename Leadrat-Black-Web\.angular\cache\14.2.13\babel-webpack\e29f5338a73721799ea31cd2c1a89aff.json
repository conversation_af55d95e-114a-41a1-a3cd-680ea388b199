{"ast": null, "code": "/**\n * The base implementation of `_.reduce` and `_.reduceRight`, without support\n * for iteratee shorthands, which iterates over `collection` using `eachFunc`.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} accumulator The initial value.\n * @param {boolean} initAccum Specify using the first or last element of\n *  `collection` as the initial value.\n * @param {Function} eachFunc The function to iterate over `collection`.\n * @returns {*} Returns the accumulated value.\n */\nfunction baseReduce(collection, iteratee, accumulator, initAccum, eachFunc) {\n  eachFunc(collection, function (value, index, collection) {\n    accumulator = initAccum ? (initAccum = false, value) : iteratee(accumulator, value, index, collection);\n  });\n  return accumulator;\n}\n\nexport default baseReduce;", "map": {"version": 3, "names": ["baseReduce", "collection", "iteratee", "accumulator", "initAccum", "eachFunc", "value", "index"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/lodash-es/_baseReduce.js"], "sourcesContent": ["/**\n * The base implementation of `_.reduce` and `_.reduceRight`, without support\n * for iteratee shorthands, which iterates over `collection` using `eachFunc`.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} accumulator The initial value.\n * @param {boolean} initAccum Specify using the first or last element of\n *  `collection` as the initial value.\n * @param {Function} eachFunc The function to iterate over `collection`.\n * @returns {*} Returns the accumulated value.\n */\nfunction baseReduce(collection, iteratee, accumulator, initAccum, eachFunc) {\n  eachFunc(collection, function(value, index, collection) {\n    accumulator = initAccum\n      ? (initAccum = false, value)\n      : iteratee(accumulator, value, index, collection);\n  });\n  return accumulator;\n}\n\nexport default baseReduce;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAT,CAAoBC,UAApB,EAAgCC,QAAhC,EAA0CC,WAA1C,EAAuDC,SAAvD,EAAkEC,QAAlE,EAA4E;EAC1EA,QAAQ,CAACJ,UAAD,EAAa,UAASK,KAAT,EAAgBC,KAAhB,EAAuBN,UAAvB,EAAmC;IACtDE,WAAW,GAAGC,SAAS,IAClBA,SAAS,GAAG,KAAZ,EAAmBE,KADD,IAEnBJ,QAAQ,CAACC,WAAD,EAAcG,KAAd,EAAqBC,KAArB,EAA4BN,UAA5B,CAFZ;EAGD,CAJO,CAAR;EAKA,OAAOE,WAAP;AACD;;AAED,eAAeH,UAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}