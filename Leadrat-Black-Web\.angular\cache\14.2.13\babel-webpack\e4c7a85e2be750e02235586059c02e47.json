{"ast": null, "code": "import getNative from './_getNative.js';\nimport root from './_root.js';\n/* Built-in method references that are verified to be native. */\n\nvar Promise = getNative(root, 'Promise');\nexport default Promise;", "map": {"version": 3, "names": ["getNative", "root", "Promise"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/lodash-es/_Promise.js"], "sourcesContent": ["import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar Promise = getNative(root, 'Promise');\n\nexport default Promise;\n"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,iBAAtB;AACA,OAAOC,IAAP,MAAiB,YAAjB;AAEA;;AACA,IAAIC,OAAO,GAAGF,SAAS,CAACC,IAAD,EAAO,SAAP,CAAvB;AAEA,eAAeC,OAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}