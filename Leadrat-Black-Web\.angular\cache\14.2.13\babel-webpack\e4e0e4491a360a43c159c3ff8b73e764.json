{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.timeout = exports.TimeoutError = void 0;\n\nvar async_1 = require(\"../scheduler/async\");\n\nvar isDate_1 = require(\"../util/isDate\");\n\nvar lift_1 = require(\"../util/lift\");\n\nvar innerFrom_1 = require(\"../observable/innerFrom\");\n\nvar createErrorClass_1 = require(\"../util/createErrorClass\");\n\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\n\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\n\nexports.TimeoutError = createErrorClass_1.createErrorClass(function (_super) {\n  return function TimeoutErrorImpl(info) {\n    if (info === void 0) {\n      info = null;\n    }\n\n    _super(this);\n\n    this.message = 'Timeout has occurred';\n    this.name = 'TimeoutError';\n    this.info = info;\n  };\n});\n\nfunction timeout(config, schedulerArg) {\n  var _a = isDate_1.isValidDate(config) ? {\n    first: config\n  } : typeof config === 'number' ? {\n    each: config\n  } : config,\n      first = _a.first,\n      each = _a.each,\n      _b = _a.with,\n      _with = _b === void 0 ? timeoutErrorFactory : _b,\n      _c = _a.scheduler,\n      scheduler = _c === void 0 ? schedulerArg !== null && schedulerArg !== void 0 ? schedulerArg : async_1.asyncScheduler : _c,\n      _d = _a.meta,\n      meta = _d === void 0 ? null : _d;\n\n  if (first == null && each == null) {\n    throw new TypeError('No timeout provided.');\n  }\n\n  return lift_1.operate(function (source, subscriber) {\n    var originalSourceSubscription;\n    var timerSubscription;\n    var lastValue = null;\n    var seen = 0;\n\n    var startTimer = function (delay) {\n      timerSubscription = executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n        try {\n          originalSourceSubscription.unsubscribe();\n          innerFrom_1.innerFrom(_with({\n            meta: meta,\n            lastValue: lastValue,\n            seen: seen\n          })).subscribe(subscriber);\n        } catch (err) {\n          subscriber.error(err);\n        }\n      }, delay);\n    };\n\n    originalSourceSubscription = source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      seen++;\n      subscriber.next(lastValue = value);\n      each > 0 && startTimer(each);\n    }, undefined, undefined, function () {\n      if (!(timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.closed)) {\n        timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      }\n\n      lastValue = null;\n    }));\n    !seen && startTimer(first != null ? typeof first === 'number' ? first : +first - scheduler.now() : each);\n  });\n}\n\nexports.timeout = timeout;\n\nfunction timeoutErrorFactory(info) {\n  throw new exports.TimeoutError(info);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "timeout", "TimeoutError", "async_1", "require", "isDate_1", "lift_1", "innerFrom_1", "createErrorClass_1", "OperatorSubscriber_1", "executeSchedule_1", "createErrorClass", "_super", "TimeoutErrorImpl", "info", "message", "name", "config", "schedulerArg", "_a", "isValidDate", "first", "each", "_b", "with", "_with", "timeoutErrorFactory", "_c", "scheduler", "asyncScheduler", "_d", "meta", "TypeError", "operate", "source", "subscriber", "originalSourceSubscription", "timerSubscription", "lastValue", "seen", "startTimer", "delay", "executeSchedule", "unsubscribe", "innerFrom", "subscribe", "err", "error", "createOperatorSubscriber", "next", "undefined", "closed", "now"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/rxjs/dist/cjs/internal/operators/timeout.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.timeout = exports.TimeoutError = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar isDate_1 = require(\"../util/isDate\");\nvar lift_1 = require(\"../util/lift\");\nvar innerFrom_1 = require(\"../observable/innerFrom\");\nvar createErrorClass_1 = require(\"../util/createErrorClass\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nexports.TimeoutError = createErrorClass_1.createErrorClass(function (_super) {\n    return function TimeoutErrorImpl(info) {\n        if (info === void 0) { info = null; }\n        _super(this);\n        this.message = 'Timeout has occurred';\n        this.name = 'TimeoutError';\n        this.info = info;\n    };\n});\nfunction timeout(config, schedulerArg) {\n    var _a = (isDate_1.isValidDate(config) ? { first: config } : typeof config === 'number' ? { each: config } : config), first = _a.first, each = _a.each, _b = _a.with, _with = _b === void 0 ? timeoutErrorFactory : _b, _c = _a.scheduler, scheduler = _c === void 0 ? schedulerArg !== null && schedulerArg !== void 0 ? schedulerArg : async_1.asyncScheduler : _c, _d = _a.meta, meta = _d === void 0 ? null : _d;\n    if (first == null && each == null) {\n        throw new TypeError('No timeout provided.');\n    }\n    return lift_1.operate(function (source, subscriber) {\n        var originalSourceSubscription;\n        var timerSubscription;\n        var lastValue = null;\n        var seen = 0;\n        var startTimer = function (delay) {\n            timerSubscription = executeSchedule_1.executeSchedule(subscriber, scheduler, function () {\n                try {\n                    originalSourceSubscription.unsubscribe();\n                    innerFrom_1.innerFrom(_with({\n                        meta: meta,\n                        lastValue: lastValue,\n                        seen: seen,\n                    })).subscribe(subscriber);\n                }\n                catch (err) {\n                    subscriber.error(err);\n                }\n            }, delay);\n        };\n        originalSourceSubscription = source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n            seen++;\n            subscriber.next((lastValue = value));\n            each > 0 && startTimer(each);\n        }, undefined, undefined, function () {\n            if (!(timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.closed)) {\n                timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n            }\n            lastValue = null;\n        }));\n        !seen && startTimer(first != null ? (typeof first === 'number' ? first : +first - scheduler.now()) : each);\n    });\n}\nexports.timeout = timeout;\nfunction timeoutErrorFactory(info) {\n    throw new exports.TimeoutError(info);\n}\n"], "mappings": "AAAA;;AACAA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;EAAEC,KAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACE,OAAR,GAAkBF,OAAO,CAACG,YAAR,GAAuB,KAAK,CAA9C;;AACA,IAAIC,OAAO,GAAGC,OAAO,CAAC,oBAAD,CAArB;;AACA,IAAIC,QAAQ,GAAGD,OAAO,CAAC,gBAAD,CAAtB;;AACA,IAAIE,MAAM,GAAGF,OAAO,CAAC,cAAD,CAApB;;AACA,IAAIG,WAAW,GAAGH,OAAO,CAAC,yBAAD,CAAzB;;AACA,IAAII,kBAAkB,GAAGJ,OAAO,CAAC,0BAAD,CAAhC;;AACA,IAAIK,oBAAoB,GAAGL,OAAO,CAAC,sBAAD,CAAlC;;AACA,IAAIM,iBAAiB,GAAGN,OAAO,CAAC,yBAAD,CAA/B;;AACAL,OAAO,CAACG,YAAR,GAAuBM,kBAAkB,CAACG,gBAAnB,CAAoC,UAAUC,MAAV,EAAkB;EACzE,OAAO,SAASC,gBAAT,CAA0BC,IAA1B,EAAgC;IACnC,IAAIA,IAAI,KAAK,KAAK,CAAlB,EAAqB;MAAEA,IAAI,GAAG,IAAP;IAAc;;IACrCF,MAAM,CAAC,IAAD,CAAN;;IACA,KAAKG,OAAL,GAAe,sBAAf;IACA,KAAKC,IAAL,GAAY,cAAZ;IACA,KAAKF,IAAL,GAAYA,IAAZ;EACH,CAND;AAOH,CARsB,CAAvB;;AASA,SAASb,OAAT,CAAiBgB,MAAjB,EAAyBC,YAAzB,EAAuC;EACnC,IAAIC,EAAE,GAAId,QAAQ,CAACe,WAAT,CAAqBH,MAArB,IAA+B;IAAEI,KAAK,EAAEJ;EAAT,CAA/B,GAAmD,OAAOA,MAAP,KAAkB,QAAlB,GAA6B;IAAEK,IAAI,EAAEL;EAAR,CAA7B,GAAgDA,MAA7G;EAAA,IAAsHI,KAAK,GAAGF,EAAE,CAACE,KAAjI;EAAA,IAAwIC,IAAI,GAAGH,EAAE,CAACG,IAAlJ;EAAA,IAAwJC,EAAE,GAAGJ,EAAE,CAACK,IAAhK;EAAA,IAAsKC,KAAK,GAAGF,EAAE,KAAK,KAAK,CAAZ,GAAgBG,mBAAhB,GAAsCH,EAApN;EAAA,IAAwNI,EAAE,GAAGR,EAAE,CAACS,SAAhO;EAAA,IAA2OA,SAAS,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgBT,YAAY,KAAK,IAAjB,IAAyBA,YAAY,KAAK,KAAK,CAA/C,GAAmDA,YAAnD,GAAkEf,OAAO,CAAC0B,cAA1F,GAA2GF,EAAlW;EAAA,IAAsWG,EAAE,GAAGX,EAAE,CAACY,IAA9W;EAAA,IAAoXA,IAAI,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB,IAAhB,GAAuBA,EAAlZ;;EACA,IAAIT,KAAK,IAAI,IAAT,IAAiBC,IAAI,IAAI,IAA7B,EAAmC;IAC/B,MAAM,IAAIU,SAAJ,CAAc,sBAAd,CAAN;EACH;;EACD,OAAO1B,MAAM,CAAC2B,OAAP,CAAe,UAAUC,MAAV,EAAkBC,UAAlB,EAA8B;IAChD,IAAIC,0BAAJ;IACA,IAAIC,iBAAJ;IACA,IAAIC,SAAS,GAAG,IAAhB;IACA,IAAIC,IAAI,GAAG,CAAX;;IACA,IAAIC,UAAU,GAAG,UAAUC,KAAV,EAAiB;MAC9BJ,iBAAiB,GAAG3B,iBAAiB,CAACgC,eAAlB,CAAkCP,UAAlC,EAA8CP,SAA9C,EAAyD,YAAY;QACrF,IAAI;UACAQ,0BAA0B,CAACO,WAA3B;UACApC,WAAW,CAACqC,SAAZ,CAAsBnB,KAAK,CAAC;YACxBM,IAAI,EAAEA,IADkB;YAExBO,SAAS,EAAEA,SAFa;YAGxBC,IAAI,EAAEA;UAHkB,CAAD,CAA3B,EAIIM,SAJJ,CAIcV,UAJd;QAKH,CAPD,CAQA,OAAOW,GAAP,EAAY;UACRX,UAAU,CAACY,KAAX,CAAiBD,GAAjB;QACH;MACJ,CAZmB,EAYjBL,KAZiB,CAApB;IAaH,CAdD;;IAeAL,0BAA0B,GAAGF,MAAM,CAACW,SAAP,CAAiBpC,oBAAoB,CAACuC,wBAArB,CAA8Cb,UAA9C,EAA0D,UAAUnC,KAAV,EAAiB;MACrHqC,iBAAiB,KAAK,IAAtB,IAA8BA,iBAAiB,KAAK,KAAK,CAAzD,GAA6D,KAAK,CAAlE,GAAsEA,iBAAiB,CAACM,WAAlB,EAAtE;MACAJ,IAAI;MACJJ,UAAU,CAACc,IAAX,CAAiBX,SAAS,GAAGtC,KAA7B;MACAsB,IAAI,GAAG,CAAP,IAAYkB,UAAU,CAAClB,IAAD,CAAtB;IACH,CAL6C,EAK3C4B,SAL2C,EAKhCA,SALgC,EAKrB,YAAY;MACjC,IAAI,EAAEb,iBAAiB,KAAK,IAAtB,IAA8BA,iBAAiB,KAAK,KAAK,CAAzD,GAA6D,KAAK,CAAlE,GAAsEA,iBAAiB,CAACc,MAA1F,CAAJ,EAAuG;QACnGd,iBAAiB,KAAK,IAAtB,IAA8BA,iBAAiB,KAAK,KAAK,CAAzD,GAA6D,KAAK,CAAlE,GAAsEA,iBAAiB,CAACM,WAAlB,EAAtE;MACH;;MACDL,SAAS,GAAG,IAAZ;IACH,CAV6C,CAAjB,CAA7B;IAWA,CAACC,IAAD,IAASC,UAAU,CAACnB,KAAK,IAAI,IAAT,GAAiB,OAAOA,KAAP,KAAiB,QAAjB,GAA4BA,KAA5B,GAAoC,CAACA,KAAD,GAASO,SAAS,CAACwB,GAAV,EAA9D,GAAiF9B,IAAlF,CAAnB;EACH,CAhCM,CAAP;AAiCH;;AACDvB,OAAO,CAACE,OAAR,GAAkBA,OAAlB;;AACA,SAASyB,mBAAT,CAA6BZ,IAA7B,EAAmC;EAC/B,MAAM,IAAIf,OAAO,CAACG,YAAZ,CAAyBY,IAAzB,CAAN;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script"}