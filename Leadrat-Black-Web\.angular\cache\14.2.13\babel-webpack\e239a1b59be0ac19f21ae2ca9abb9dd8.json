{"ast": null, "code": "import { AsapAction } from './AsapAction';\nimport { AsapScheduler } from './AsapScheduler';\nexport const asapScheduler = new AsapScheduler(AsapAction);\nexport const asap = asapScheduler;", "map": {"version": 3, "names": ["AsapAction", "AsapScheduler", "asapScheduler", "asap"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/rxjs/dist/esm/internal/scheduler/asap.js"], "sourcesContent": ["import { AsapAction } from './AsapAction';\nimport { AsapScheduler } from './AsapScheduler';\nexport const asapScheduler = new AsapScheduler(AsapAction);\nexport const asap = asapScheduler;\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,cAA3B;AACA,SAASC,aAAT,QAA8B,iBAA9B;AACA,OAAO,MAAMC,aAAa,GAAG,IAAID,aAAJ,CAAkBD,UAAlB,CAAtB;AACP,OAAO,MAAMG,IAAI,GAAGD,aAAb", "ignoreList": []}, "metadata": {}, "sourceType": "module"}