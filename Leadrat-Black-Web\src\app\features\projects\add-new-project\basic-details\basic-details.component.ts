import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
  Output,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { OwlDateTimeComponent } from '@danielmoncada/angular-datetime-picker';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { CountryCode, isPossiblePhoneNumber } from 'libphonenumber-js';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import {
  BehaviorSubject,
  debounceTime,
  distinctUntilChanged,
  filter,
  takeUntil,
} from 'rxjs';

import {
  EMPTY_GUID,
  FACING,
  MONTHS,
  POSSESSION_DATE_FILTER_LIST,
  VALIDATION_CLEAR,
  VALIDATION_SET,
} from 'src/app/app.constants';
import { Facing, PossessionType, ProjectStatus } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  changeCalendar,
  convertUrlsToLinks,
  formatBudget,
  getISODateFormat,
  getLocationDetailsByObj,
  getProjectSubTypeDisplayName,
  onlyNumbers,
  onlyNumbersWithDecimal,
  onPickerOpened,
  patchTimeZoneDate,
  setPropertySubTypeList,
  setTimeZoneDate,
  toggleValidation,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { getAttendanceSettings } from 'src/app/reducers/attendance/attendance.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { LoaderHide } from 'src/app/reducers/loader/loader.actions';
import {
  FetchAreaUnitList,
  FetchAssociatedBanks,
} from 'src/app/reducers/master-data/master-data.actions';
import {
  getAreaUnits,
  getAssociatedBanks,
} from 'src/app/reducers/master-data/master-data.reducer';
import {
  AddProject,
  ClearFiltersPayload,
  FetchProjectBasicDetailsById,
  HasProjectName,
  UpdateProject,
} from 'src/app/reducers/project/project.action';
import {
  doesProjectExists,
  getBasicDetailsById,
} from 'src/app/reducers/project/project.reducer';
import { FetchLocationsWithGoogle } from 'src/app/reducers/site/site.actions';
import {
  getLocationsWithGoogleApi,
  getLocationsWithGoogleApiIsLoading,
} from 'src/app/reducers/site/site.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'basic-details',
  templateUrl: './basic-details.component.html',
})
export class BasicDetailsComponent implements OnInit, OnDestroy, AfterViewInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('previewModal') previewModal: TemplateRef<any>;
  @ViewChild('dt5') dt5: OwlDateTimeComponent<any>;
  @ViewChild('reraNumberField') reraNumberField!: ElementRef<HTMLInputElement>;
  @ViewChild('basicDetailsForm')
  basicDetailsForm!: ElementRef<HTMLInputElement>;
  projectDetailForm: FormGroup;
  @ViewChild('builderNumber') builderNumber: any;
  @ViewChild('pointOfContact') pointOfContact: any;
  areaSizeUnits: Array<any>;
  RERANumberList: Array<any> = [];
  selectedProject: any;
  projectTypeList: Array<any> = JSON.parse(
    localStorage.getItem('projectType') || '[]'
  );
  projectSubType: any = [];
  markers: any[] = [];
  selectedDataId: any;
  associatedBanks: any;
  associatedBanksList: any = [];
  onlyNumbers = onlyNumbers;
  onlyNumbersWithDecimal = onlyNumbersWithDecimal;
  budgetValidation: boolean = false;
  lowerBudgetInWords: string = '';
  upperBudgetInWords: string = '';
  brochures: any;
  brokerageUnits: Array<string> = []
  concatenatedVal: string;

  currencyList: any[] = [];
  preferredCountries = ['in'];
  defaultCurrency: string;
  budgetInWords: string = '';
  newUrl: string = '';
  links: any[] = [];
  filters: any = {
    showGlobalFilter: false,
  };
  facingList: Array<{ displayName: string; value: string }> = FACING;
  dateFilterList = POSSESSION_DATE_FILTER_LIST;
  possessionDate: any = PossessionType;
  isOpenPossessionModal: boolean = false;
  hasInternationalSupport: boolean = false;
  syncingCurrency: boolean = false;
  statusList: any = Object.values(ProjectStatus).slice(1, 8);
  dataSubscription: any;
  checkDuplicacy: boolean = false;
  selectedDataName: any;
  selectedMonthAndYear: any;
  currentMonth: Date = new Date();
  selectedMonth: any;
  selectedYear: any;
  isPresentMonth: boolean;
  isValidPossDate: boolean = false;
  selectedPossession: any;
  facingArray: any = [];
  mapCenter: google.maps.LatLngLiteral;
  zoom = 12;
  searchQuery: string = '';
  selectedAddress: string = '';
  locationArr: any = ['', '', '', ''];
  facingPayload: any = [];
  basicDetailCurrency: any;
  placesList: any[] = [];
  isPlacesListLoading: any;
  isManualLocation: boolean = false;
  searchPlaceTerm$: BehaviorSubject<any> = new BehaviorSubject<any>('');
  userBasicDetails: any;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened;
  convertUrlsToLinks = convertUrlsToLinks
  globalSettingsDetails: any;
  isForNotes: boolean;
  isGeoFenceEnabled: boolean = false;
  // fetchNextLeads$: Subscription;
  @Output() projectSubTypeChange = new EventEmitter<any>();
  getProjectSubTypeDisplayName(subTypeId: string): string {
    if (!subTypeId) return '';
    const subType = this.projectSubType?.find((st: { id: string }) => st.id === subTypeId);
    return subType?.displayName;
  }

  get isPlot(): boolean {
    return this.projectDetailForm.get('propertySubType').value === 'Plot';
  }
  constructor(
    private elRef: ElementRef,
    private _notificationService: NotificationsService,
    private activatedRoute: ActivatedRoute,
    private fb: FormBuilder,
    private sharedDataService: ShareDataService,
    private router: Router,
    public modalRef: BsModalRef,
    private _store: Store<AppState>,
    public modalService: BsModalService,
  ) { }

  ngAfterViewInit(): void {
    this.activatedRoute.params.subscribe((params: any) => {
      if ((params || {}).id) {
        this.sharedDataService.setProjectTitleId(params.id);
        this.selectedDataId = params.id;
        this._store.dispatch(new FetchProjectBasicDetailsById(params.id));
        this._store
          .select(getUserBasicDetails)
          .pipe(takeUntil(this.stopper))
          .subscribe((data: any) => {
            this.userBasicDetails = data;
            this.currentDate = changeCalendar(
              this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
            );
          });
        this.dataSubscription = this._store
          .select(getBasicDetailsById)
          .pipe(
            takeUntil(this.stopper),
            filter((data) => !!data)
          )
          .subscribe((data) => {
            if (data?.length) {
              this.sharedDataService.sendBasicDetailCurrency(
                data?.[0]?.monetaryInfo?.currency
              );
            }
            this.selectedProject = data?.[0];
            this.selectedDataName = this.selectedProject?.name;
            this.brochures = this.selectedProject?.brochures;
            this.links = this.selectedProject?.reraNumbers
              ? this.selectedProject?.reraNumbers
              : [];

            this.projectSubType = [];
            if (this.selectedProject?.possesionType === 5) {
              this.convertDateToMonth(this.selectedProject?.possessionDate);
            }
            if (this.selectedProject) {
              let bankList: any = [];
              if (Array.isArray(this.selectedProject.associatedBank)) {
                this.selectedProject.associatedBank?.map((item: any) => {
                  bankList.unshift(item?.name);
                });
              } else {
                bankList = null;
              }
              if (
                this.selectedProject.address?.state ||
                this.selectedProject.address?.city ||
                this.selectedProject.address?.postalCode ||
                this.selectedProject.address?.locality
              ) {
                this.locationArr[0] = this.selectedProject.address?.state || '';
                this.locationArr[1] = this.selectedProject.address?.city || '';
                this.locationArr[2] =
                  this.selectedProject.address?.postalCode || '';
                this.locationArr[3] =
                  this.selectedProject.address?.locality || '';
                this.searchQuery =
                  this.locationArr[0] +
                  ' ' +
                  this.locationArr[1] +
                  ' ' +
                  this.locationArr[2] +
                  ' ' +
                  this.locationArr[3];
                this.searchLocation();
              }
              this.facingPayload = [];
              this.facingArray = [];
              this.selectedProject?.facings?.forEach((item: any) => {
                const facingValue = FACING[item - 1]?.value;
                if (!this.facingArray.includes(facingValue)) {
                  this.facingArray.push(facingValue);
                }
                if (item !== 0 && !this.facingPayload.includes(item)) {
                  this.facingPayload.push(item);
                }
              });
              if (
                this.selectedProject?.address &&
                !this.selectedProject.address.placeId
              ) {
                const { city, state, subLocality, postalCode } =
                  this.selectedProject.address;
                if (city || state || subLocality || postalCode) {
                  this.isManualLocation = true;
                  // const addressFields = ['locality', 'city', 'state', 'country'];
                  // if (this.isManualLocation) {
                  //   addressFields.forEach(field => {
                  //     toggleValidation(VALIDATION_SET, this.projectDetailForm, field, [Validators.required]);
                  //   });
                  // } else {
                  //   addressFields.forEach(field => {
                  //     toggleValidation(VALIDATION_CLEAR, this.projectDetailForm, field);
                  //   });
                  // }
                }
              }
              const projectCurrency = this.selectedProject.monetaryInfo?.currency ||
                this.defaultCurrency;
              this.brokerageUnits = [projectCurrency, '%'];

              this.projectDetailForm.patchValue({
                name: this.selectedProject.name,
                builderName: this.selectedProject.builderDetail?.name,
                builderNumber: this.selectedProject.builderDetail?.contactNo
                  ? this.selectedProject.builderDetail?.contactNo.includes('+')
                    ? this.selectedProject.builderDetail?.contactNo
                    : '+91' + this.selectedProject.builderDetail?.contactNo
                  : null,
                pointOfContact: this.selectedProject.builderDetail
                  ?.pointOfContact
                  ? this.selectedProject.builderDetail?.pointOfContact.includes(
                    '+'
                  )
                    ? this.selectedProject.builderDetail?.pointOfContact
                    : '+91' + this.selectedProject.builderDetail?.pointOfContact
                  : null,
                status: this.selectedProject.status
                  ? ProjectStatus[this.selectedProject.status]
                  : null,
                area:
                  this.selectedProject.area === 0
                    ? null
                    : this.selectedProject.area,
                carpetAreaUnitId:
                  this.selectedProject.area === 0
                    ? null
                    : this.selectedProject.areaUnitId ||
                    this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
                certificates: this.selectedProject.certificates,
                facing: Facing[this.selectedProject.facing],
                projectDescription: this.selectedProject.description,
                totalFlats: this.selectedProject.totalFlats || 0,
                totalFloors: this.selectedProject.totalFloor || 0,
                totalBlocks: this.selectedProject.totalBlocks || 0,
                projectType: this.selectedProject.projectType?.displayName,
                projectSubType: this.selectedProject.projectType?.childType?.id,
                description: this.selectedProject.description,
                startDate: patchTimeZoneDate(
                  this.selectedProject.startDate,
                  this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
                ),
                endDate: patchTimeZoneDate(
                  this.selectedProject.endDate,
                  this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
                ),
                globalDate: this.selectedProject.possessionDate,
                state: this.selectedProject.address?.state,
                city: this.selectedProject.address?.city,
                pinCode: this.selectedProject.address?.postalCode,
                country: this.selectedProject.address?.country,
                locality: this.selectedProject.address?.subLocality,
                longitude: this.selectedProject.address?.longitude,
                latitude: this.selectedProject.address?.latitude,
                locationId: null,
                bankList: this.selectedProject.associatedBanks,
                BrokerageAmount: this.selectedProject.monetaryInfo?.brokerage,
                minimumPrice: this.selectedProject.minimumPrice,
                maximumPrice: this.selectedProject.maximumPrice,
                currency: projectCurrency,
                reraNumber: this.links,
                globalRange:
                  this.selectedProject.possesionType === 0
                    ? null
                    : PossessionType[this.selectedProject.possesionType],
                BrokerageAmountUnit:
                  this.selectedProject.monetaryInfo?.brokerageCurrency,
                notes: this.selectedProject?.notes,
              });
              if (this.selectedProject?.address?.placeId) {
                this.projectDetailForm.patchValue({
                  ...this.projectDetailForm.value,
                  state: null,
                  city: null,
                  locality: null,
                  pinCode: null,
                  locationId: {
                    id: EMPTY_GUID,
                    location: getLocationDetailsByObj(
                      this.selectedProject?.address
                    ),
                    placeId: this.selectedProject?.address?.placeId,
                  },
                });
                this.placesList = [
                  ...this.placesList,
                  {
                    id: EMPTY_GUID,
                    location: getLocationDetailsByObj(
                      this.selectedProject?.address
                    ),
                    placeId: this.selectedProject?.address?.placeId,
                  },
                ];
              }
            }
          });
      }
    });
  }

  ngOnInit(): void {
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data) this.globalSettingsDetails = data;
        this.hasInternationalSupport = data?.hasInternationalSupport;
        this.currencyList = data?.countries?.length
          ? data.countries[0].currencies
          : null;
        this.defaultCurrency = data?.countries?.length
          ? data.countries[0].defaultCurrency
          : null;
        this.preferredCountries = data?.countries?.length
          ? [data.countries[0].code?.toLowerCase()]
          : ['in'];
        const currency = this.selectedProject?.monetaryInfo?.currency ||
          this.defaultCurrency;
        this.brokerageUnits = [currency, '%'];
        this.projectDetailForm?.patchValue({
          currency: currency,
          carpetAreaUnitId:
            this.projectDetailForm.get('carpetAreaUnitId').value ||
            this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        });
      });

    this.projectDetailForm = this.fb.group({
      name: ['', Validators.required],
      builderName: [null],
      builderNumber: ['', [this.contactNumberValidator('builder')]],
      pointOfContact: ['', [this.contactNumberValidator('point')]],
      status: [null],
      projectType: [null, Validators.required],
      projectSubType: [null, Validators.required],
      area: [null],
      carpetAreaUnitId: [null],
      certificates: [null],
      BrokerageAmount: [null],
      BrokerageAmountUnit: [null],
      facing: [null],
      projectDescription: [null],
      totalFlats: [0],
      totalFloors: [0],
      totalBlocks: [0],
      propertyLength: [null],
      propertyBreadth: [null],
      lengthAndBreadthAreaUnit: [null],

      reraNumberList: [
        null,
        Validators.pattern(/^[\d\w\s!@#$%^&*()-+=\[\]{};:'",.<>?\\/]{10,}$/),
      ],
      reraNumber: [null],
      state: [null],
      city: [null],
      pinCode: [null, Validators.pattern(/^[1-9]\d{5}$/)],
      country: [null],
      bankList: [null],
      startDate: [null],
      endDate: [null],
      possessionDate: [null],
      minimumPrice: [null],
      maximumPrice: [null],
      currency: [this.defaultCurrency],
      globalRange: [null],
      globalDate: [null],
      locality: [null],
      longitude: [null],
      latitude: [null],
      locationId: [null],
      notes: [null],
    });

    this.projectDetailForm.controls['area'].valueChanges.subscribe((value) => {
      if (value) {
        toggleValidation(
          VALIDATION_SET,
          this.projectDetailForm,
          'carpetAreaUnitId',
          [Validators.required]
        );
      } else {
        this.projectDetailForm.controls['carpetAreaUnitId'].setValue(null);
        toggleValidation(
          VALIDATION_CLEAR,
          this.projectDetailForm,
          'carpetAreaUnitId'
        );
        this.projectDetailForm.patchValue({
          carpetAreaUnitId: this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
        });
      }
    });

    this.projectDetailForm.get('propertyLength').valueChanges.subscribe((val) => {
      this.lengthAndBreadthAreaUnitValidator();
      this.areaUnitValidator();
      if (this.projectDetailForm.controls['propertyLength'].value === null) {
        this.projectDetailForm.patchValue({
          lengthAndBreadthAreaUnit: null,
        });
      }
    });

    this.projectDetailForm.get('propertyBreadth').valueChanges.subscribe((val) => {
      this.lengthAndBreadthAreaUnitValidator();
      this.areaUnitValidator();
      if (this.projectDetailForm.controls['propertyBreadth'].value === null) {
        this.projectDetailForm.patchValue({
          lengthAndBreadthAreaUnit: null,
        });
      }
    });

    this.projectDetailForm
      .get('lengthAndBreadthAreaUnit')
      .valueChanges.subscribe((val) => {
        if (this.isPlot)
          this.projectDetailForm.patchValue({
            areaUnit: val,
          });
      });

    this.projectDetailForm.controls['name'].valueChanges.subscribe((value) => {
      if (value) {
        this._store.dispatch(new HasProjectName(value));
        this._store.dispatch(new LoaderHide());
      } else {
        this.checkDuplicacy = false;
      }
    });
    this._store
      .select(doesProjectExists)
      .pipe(takeUntil(this.stopper))
      .subscribe((projectData: any) => {
        if (projectData?.canNavigate) {
          if (
            this.selectedDataName ===
            this.projectDetailForm.controls['name'].value
          ) {
            this.checkDuplicacy = false;
          } else if (!this.projectDetailForm.controls['name'].value) {
            this.checkDuplicacy = false;
          } else {
            this.checkDuplicacy = true;
          }
        } else {
          this.checkDuplicacy = false;
        }
      });

    this.projectDetailForm.controls['globalRange'].valueChanges.subscribe(
      (value) => {
        if (value === 'Custom Date') {
          this.convertDateToMonth(
            this.projectDetailForm.get('globalDate').value
          );
        } else if (
          value === '6 Months' ||
          value === '1 Year' ||
          value === '2 Years'
        ) {
          this.calculateDateRange(value);
          this.selectedPossession = null;
          this.isValidPossDate = false;
        }
      }
    );

    this.projectDetailForm.controls['projectType'].valueChanges.subscribe(
      (val: string) => {
        this.projectSubType = setPropertySubTypeList(
          val,
          this.projectTypeList,
          true
        );
        this.projectDetailForm.patchValue({
          projectSubType: null,
        });
      }
    );

    this.projectDetailForm.controls['projectSubType'].valueChanges.subscribe((val) => {
      this.projectSubTypeChange.emit(val);
      this.sharedDataService.setProjectSubType(val);
    });

    this.projectDetailForm.statusChanges.subscribe((status) => {
      this.sharedDataService.stopShareNavigate([status, this.selectedDataId]);
    });

    this.sharedDataService.validateBasicDetail$.subscribe((data) => {
      if (this.selectedDataId) {
        Object.values(this.projectDetailForm.controls).forEach((control) => {
          control.markAsTouched();
        });
      }
    });

    this._store
      .select(getAttendanceSettings)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isGeoFenceEnabled = data?.isGeoFenceEnabled;
      });

    this._store.dispatch(new FetchAssociatedBanks());
    this._store
      .select(getAssociatedBanks)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.associatedBanks = data?.data ? data.data : null;
        this.associatedBanksList = [];
        this.associatedBanks?.map((item: any) => {
          this.associatedBanksList.push(item?.name);
        });
      });

    this._store.dispatch(new FetchAreaUnitList());
    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });

    this.sharedDataService.updateSharedTabData(0);

    this.projectDetailForm
      .get('BrokerageAmount')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe((val) => {
        if (val) {
          toggleValidation(
            VALIDATION_SET,
            this.projectDetailForm,
            'BrokerageAmountUnit',
            [Validators.required]
          );
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.projectDetailForm,
            'BrokerageAmountUnit'
          );
        }
      });

    this.projectDetailForm
      .get('BrokerageAmountUnit')
      .valueChanges.subscribe((val) => {
        const brokerageControl = this.projectDetailForm.get('BrokerageAmount');
        const brokerageValue = brokerageControl.value;
        if (val) {
          toggleValidation(
            VALIDATION_SET,
            this.projectDetailForm,
            'BrokerageAmount',
            [Validators.required]
          );
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.projectDetailForm,
            'BrokerageAmount'
          );
        }
        if (brokerageValue > 99 && val === '%') {
          brokerageControl.setErrors({ percentageField: true });
        } else {
          brokerageControl.setErrors(null);
        }
      });

    this.projectDetailForm.get('currency').valueChanges.subscribe((value) => {
      if (!this.syncingCurrency) {
        this.syncingCurrency = true;
        this.projectDetailForm.get('currency').setValue(value);
        this.syncingCurrency = false;
      }
    });

    this.projectDetailForm.get('currency').valueChanges.subscribe((val) => {
      this.lowerBudgetInWords = formatBudget(
        this.selectedProject?.minimumPrice ||
        this.projectDetailForm.value.minimumPrice,
        val
      );
      this.upperBudgetInWords = formatBudget(
        this.selectedProject?.maximumPrice ||
        this.projectDetailForm.value.maximumPrice,
        val
      );
      if (
        this.projectDetailForm.get('BrokerageAmount').value &&
        this.projectDetailForm.value.BrokerageAmountUnit !== '%'
      ) {
        this.projectDetailForm.get('BrokerageAmountUnit').patchValue(val);
      }
      this.brokerageUnits = [val, '%'];
    });

    this.projectDetailForm.get('minimumPrice').valueChanges.subscribe((val) => {
      this.lowerBudgetInWords = formatBudget(
        val,
        this.projectDetailForm.value.currency ||
        this.selectedProject?.monetaryInfo?.currency ||
        this.defaultCurrency
      );
      if (!this.projectDetailForm.value.maximumPrice) {
        this.projectDetailForm.value.maximumPrice = val;
      } else {
        val > this.projectDetailForm.get('maximumPrice').value
          ? (this.budgetValidation = true)
          : (this.budgetValidation = false);
        if (val === null) {
          this.budgetValidation = false;
        }
      }
    });

    this.projectDetailForm.get('maximumPrice').valueChanges.subscribe((val) => {
      this.upperBudgetInWords = formatBudget(
        val,
        this.projectDetailForm.value.currency ||
        this.selectedProject?.monetaryInfo?.currency
      );
      this.projectDetailForm.get('minimumPrice').value > val
        ? (this.budgetValidation = true)
        : (this.budgetValidation = false);
      if (val === null) {
        this.budgetValidation = false;
      }
    });

    this._store
      .select(getLocationsWithGoogleApi)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.placesList = data
          ?.slice()
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
      });

    this._store
      .select(getLocationsWithGoogleApiIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isPlacesListLoading = isLoading;
      });

    this.searchPlaceTerm$
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        filter((searchStr: string) => searchStr.length > 2)
      )
      .subscribe((searchStr: string) => {
        this._store.dispatch(new FetchLocationsWithGoogle(searchStr));
      });
    this.patchUnits()
  }


  lengthAndBreadthAreaUnitValidator() {
    if (
      this.projectDetailForm.controls['propertyLength'].value != null &&
      this.projectDetailForm.controls['propertyBreadth'].value != null &&
      this.projectDetailForm.controls['propertyLength'].value >= 0 &&
      this.projectDetailForm.controls['propertyBreadth'].value >= 0
    ) {
      this.projectDetailForm.controls['lengthAndBreadthAreaUnit'].setValidators([
        Validators.required,
      ]);
    } else {
      this.projectDetailForm.controls['lengthAndBreadthAreaUnit'].clearValidators();
    }
    this.projectDetailForm.controls[
      'lengthAndBreadthAreaUnit'
    ].updateValueAndValidity();
  }

  areaUnitValidator() {
    if (
      !(
        this.projectDetailForm.controls['propertyLength'].value != null &&
        this.projectDetailForm.controls['propertyBreadth'].value != null &&
        this.projectDetailForm.controls['propertyLength'].value >= 0 &&
        this.projectDetailForm.controls['propertyBreadth'].value >= 0
      )
    ) {
      this.projectDetailForm.controls['areaUnit'].setValidators([
        Validators.required,
      ]);
    } else {
      this.projectDetailForm.controls['areaUnit'].clearValidators();
    }
    this.projectDetailForm.controls['areaUnit'].updateValueAndValidity();
  }

  updateBrokerageUnit(value: any) {
    const dropdownControl = this.projectDetailForm.get('BrokerageAmountUnit');
    if (!value) {
      dropdownControl.setValue(null);
    } else if (value < 100 && value) {
      dropdownControl.setValue('%');
    } else if (value >= 100) {
      const currency = this.brokerageUnits[0] ||
        this.projectDetailForm.get('currency')?.value ||
        this.defaultCurrency
      dropdownControl.setValue(currency);
    }
  }

  lengthAndBreadthChanged(): void {
    const length = this.projectDetailForm.controls.propertyLength.value;
    const breadth = this.projectDetailForm.controls.propertyBreadth.value;
    if (length >= 0 && breadth >= 0) {
      this.projectDetailForm.patchValue({
        area: Number((length * breadth).toFixed(2)),
      });
    }
  }

  calculateDateRange(value: string): void {
    const currentDate = new Date(this.currentDate);
    let startDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth() + 1,
      1
    );
    let endDate: Date;

    if (value === '6 Months') {
      endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 6, 0);
    } else if (value === '1 Year') {
      endDate = new Date(startDate.getFullYear() + 1, startDate.getMonth(), 0);
    } else if (value === '2 Years') {
      endDate = new Date(startDate.getFullYear() + 2, startDate.getMonth(), 0);
    }

    this.projectDetailForm.controls['globalDate'].setValue(endDate);
  }
  removeLocation(type: any) {
    switch (type) {
      case 'location':
        this.projectDetailForm.value.locationId = null;
        this.projectDetailForm.value.enquiredLocality = null;
        this.projectDetailForm.value.enquiredCity = null;
        this.projectDetailForm.value.enquiredState = null;
        break;
      case 'changeLocation':
        this.projectDetailForm.patchValue({
          enquiredLocality: null,
          enquiredCity: null,
          enquiredState: null,
        });
        break;
      case 'changeLocality':
        this.projectDetailForm.patchValue({
          locationId: null,
        });
        break;
    }
  }

  getSelectedCountryCodeBuilderNo(): any {
    return this.builderNumber?.selectedCountry;
  }

  getSelectedCountryCodePointNo(): any {
    return this.pointOfContact?.selectedCountry;
  }

  contactNumberValidator(numType: string): ValidatorFn {
    let defaultCountry: CountryCode = 'IN';
    return (control: AbstractControl): ValidationErrors | null => {
      if (numType == 'builder') {
        const input = document.querySelector(
          '.builderNumber > div > input'
        ) as HTMLInputElement;

        if (!input?.value?.length) {
          return null;
        }
        defaultCountry = this.getSelectedCountryCodeBuilderNo()?.dialCode;
      } else if (numType == 'point') {
        const input = document.querySelector(
          '.pointOfContact > div > input'
        ) as HTMLInputElement;

        if (!input?.value?.length) {
          return null;
        }
        defaultCountry = this.getSelectedCountryCodePointNo()?.dialCode;
      }
      try {
        const validNumber = isPossiblePhoneNumber(
          (numType == 'builder'
            ? this.builderNumber?.value
            : this.pointOfContact?.value) || control?.value,
          defaultCountry
        );
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }

  decrement(property: string) {
    if (this.projectDetailForm.get(property).value > 0) {
      let propName = this.projectDetailForm.get(property).value;
      this.projectDetailForm.get(property)?.setValue(--propName);
    }
  }

  increment(property: string) {
    let propName = this.projectDetailForm.get(property).value;
    this.projectDetailForm.get(property)?.setValue(++propName);
  }

  manageProject() {
    this.router.navigate(['/projects/manage-projects']);
  }

  onSubmit() {
    // this.scrollToInvalidField();
    if (this.projectDetailForm.value.reraNumberList?.length >= 10) {
      this.projectDetailForm.controls['reraNumberList'].setErrors({
        invalidLength: true,
      });
      this._notificationService.warn(`Please Press Enter To Add RERA Numbers.`);
      // this.scrollToInvalidField();
      return;
    }
    if (this.checkDuplicacy) {
      this.projectDetailForm.controls['name'].setErrors({
        invalidLength: true,
      });
      // this.scrollToInvalidField();
      return;
    }
    if (this.isGeoFenceEnabled) {
      if (!this.isManualLocation) {
        toggleValidation(VALIDATION_SET, this.projectDetailForm, 'locationId', [
          Validators.required,
        ]);
        toggleValidation(
          VALIDATION_CLEAR,
          this.projectDetailForm,
          'locality'
        );
      } else {
        toggleValidation(
          VALIDATION_SET,
          this.projectDetailForm,
          'locality',
          [Validators.required]
        );
        toggleValidation(VALIDATION_CLEAR, this.projectDetailForm, 'locationId');
      }
    }
    if (!this.projectDetailForm.valid) {
      this._notificationService.warn(
        `Form is not valid. Please fill all the required fields.`
      );
      validateAllFormFields(this.projectDetailForm);
      return;
    }
    const projectData = this.projectDetailForm.value;
    const payload: any = {
      name: projectData.name,
      projectTypeId: projectData?.projectSubType,
      status: ProjectStatus[projectData.status],
      area: projectData.area,
      areaUnitId: projectData.carpetAreaUnitId,
      certificates: projectData.certificates,
      facings: this.facingPayload,
      description: projectData.projectDescription,
      totalFloor:
        projectData.projectType !== 'Agricultural'
          ? projectData.totalFloors
          : 0,
      totalBlocks:
        projectData.projectType !== 'Agricultural'
          ? projectData.totalBlocks
          : 0,
      totalFlats:
        projectData.projectType === 'Residential' ? projectData.totalFlats : 0,
      reraNumbers: projectData.reraNumber,
      minimumPrice: projectData.minimumPrice,
      maximumPrice: projectData.maximumPrice,
      brochures: this.selectedProject?.brochures,
      builderDetail:
        !projectData.builderName &&
          !projectData.builderNumber &&
          !projectData.pointOfContact
          ? null
          : {
            name: projectData.builderName ? projectData.builderName : null,
            contactNo: projectData.builderNumber?.toString()
              ? projectData.builderNumber?.toString()
              : null,
            pointOfContact: projectData.pointOfContact?.toString()
              ? projectData.pointOfContact?.toString()
              : null,
          },
      associateBank: projectData?.bankList,
      monetaryInfo: this.selectedProject?.monetaryInfo?.id
        ? {
          id: this.selectedProject.monetaryInfo?.id,
          brokerage: projectData.BrokerageAmount,
          brokerageCurrency: projectData.BrokerageAmountUnit,
          currency: projectData.currency || this.defaultCurrency,
        }
        : !projectData.BrokerageAmount &&
          !projectData.BrokerageAmountUnit &&
          !projectData.currency
          ? null
          : {
            brokerage: projectData.BrokerageAmount,
            brokerageCurrency: projectData.BrokerageAmountUnit,
            currency: projectData.currency || this.defaultCurrency,
          },
      startDate: setTimeZoneDate(
        projectData.startDate,
        this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
      ),
      endDate: setTimeZoneDate(
        projectData.endDate,
        this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
      ),
      possessionDate:
        projectData?.globalRange === 'Custom Date' ||
          projectData?.globalRange === '6 Months' ||
          projectData?.globalRange === '1 Year' ||
          projectData?.globalRange === '2 Years'
          ? projectData?.globalDate
            ? setTimeZoneDate(
              projectData.globalDate,
              this.userBasicDetails?.timeZoneInfo?.baseUTcOffset
            )
            : null
          : null,
      possesionType: projectData?.globalRange
        ? PossessionType[projectData?.globalRange]
        : 0,
      id: this.selectedDataId,
      notes: projectData?.notes,
    };
    if (
      !this.isManualLocation &&
      this.projectDetailForm.value?.locationId?.placeId
    ) {
      payload.address = {
        placeId: this.projectDetailForm.value?.locationId?.placeId,
      };
    } else {
      payload.address = {
        subLocality:
          this.projectDetailForm.value?.locality ??
          this.projectDetailForm.value?.locality,
        city:
          this.projectDetailForm.value?.city ??
          this.projectDetailForm.value?.city,
        state:
          this.projectDetailForm.value?.state ??
          this.projectDetailForm.value?.state,
        country: this.projectDetailForm.value?.country,
        postalCode: projectData.pinCode ? projectData.pinCode.toString() : null,
      };
    }
    this.sharedDataService.sendBasicDetailCurrency(
      payload?.monetaryInfo?.currency
    );
    if (this.budgetValidation) {
      return;
    }
    if (this.selectedDataId) {
      this._store.dispatch(new UpdateProject(this.selectedDataId, payload));
      this.sharedDataService.setProjectTitleId(this.selectedDataId);
    } else {
      this._store.dispatch(new AddProject(payload));
    }
    this.selectedProject = null;
    this.modalRef.hide();
  }

  // get currentDate(): Date {
  //   const currentDate = new Date();
  //   currentDate.setHours(0, 0, 0, 0);
  //   return currentDate;
  // }

  addInputField() {
    if (
      typeof this.newUrl === 'string' &&
      this.newUrl.trim() !== '' &&
      this.newUrl.trim().length >= 10
    ) {
      this.links = [...this.links, this.newUrl.trim()];
      this.projectDetailForm.get('reraNumber')?.setValue(this.links);
      this.newUrl = '';
    }
  }

  removeInputField(link: string) {
    const index = this.links.indexOf(link);
    this.links = [...this.links];
    if (index !== -1) {
      this.links.splice(index, 1);
      this.projectDetailForm.get('reraNumber').setValue(this.links);
    }
  }

  applyDate() {
    if (
      this.projectDetailForm.controls['globalRange'].value !== 'Custom Date'
    ) {
      this.filters.showGlobalFilter = false;
    } else if (
      this.projectDetailForm.controls['globalRange'].value === 'Custom Date' &&
      this.projectDetailForm.get('globalDate').value
    ) {
      this.filters.showGlobalFilter = false;
    } else {
      this.projectDetailForm.controls['globalDate'].markAsTouched();
    }
  }

  changeDateFormat(value: any) {
    const prevDate = String(value)?.includes('00.000Z')
      ? value
      : getISODateFormat(value);

    const date = new Date(prevDate);
    const year = date.getFullYear();
    const month = ('0' + (date.getMonth() + 1)).slice(-2);
    const day = ('0' + date.getDate()).slice(-2);
    const formattedDate = `${day}-${month}-${year}`;
    return formattedDate;
  }

  // stop(event: Event) {
  //   event.stopPropagation();
  // }

  // scrollToInvalidField() {
  //   const firstInvalidField = this.findFirstInvalidField();
  //   if (firstInvalidField) {
  //     firstInvalidField.nativeElement.scrollIntoView({
  //       behavior: 'smooth',
  //       block: 'start',
  //     });
  //   }
  // }

  // findFirstInvalidField(): ElementRef<HTMLInputElement> | null {
  //   for (const controlName in this.projectDetailForm.controls) {
  //     const control = this.projectDetailForm.get(controlName);
  //     if (control?.invalid) {
  //       if (
  //         controlName === 'name' ||
  //         controlName === 'projectType' ||
  //         controlName === 'projectSubType' ||
  //         controlName === 'BrokerageAmountUnit' ||
  //         controlName === 'BrokerageAmount'
  //       ) {
  //         return this.basicDetailsForm as ElementRef<HTMLInputElement>;
  //       } else {
  //         return this.reraNumberField as ElementRef<HTMLInputElement>;
  //       }
  //     }
  //   }
  //   return null;
  // }

  // errMessage() {
  //   this._notificationService.warn(`Please select any project type.`);
  // }

  closeModal() {
    if (
      this.projectDetailForm.controls['globalRange'].value === 'Custom Date' &&
      !this.selectedMonthAndYear
    ) {
      this.isValidPossDate = true;
      return;
    }
    this.isOpenPossessionModal = false;
  }

  monthChanged(event: Date): void {
    const selectedMonth = event.getMonth();
    const selectedYear = event.getFullYear();
    const lastDateOfMonth = new Date(selectedYear, selectedMonth + 1, 0);
    this.selectedMonthAndYear = lastDateOfMonth;
    this.projectDetailForm.controls['globalDate'].setValue(
      this.selectedMonthAndYear
    );
    this.isValidPossDate = false;
    this.currentMonth = this.selectedMonthAndYear;
    this.selectedMonth = this.selectedMonthAndYear.toLocaleString('default', {
      month: 'short',
    });
    this.selectedYear = this.selectedMonthAndYear.getFullYear().toString();
    this.selectedPossession = `${this.selectedMonth} ${this.selectedYear}`;
    this.dt5.close();
  }

  convertDateToMonth(data: any) {
    this.selectedMonthAndYear = data;
    this.selectedMonth = MONTHS[parseInt(data?.slice(5, 7), 10) - 1];
    this.selectedYear = parseInt(data?.slice(0, 4), 10);
    this.selectedPossession = this.selectedMonth
      ? this.selectedMonth + ' ' + this.selectedYear
      : null;
  }

  searchLocation() {
    if (this.searchQuery) {
      const geocoder = new google.maps.Geocoder();
      geocoder.geocode({ address: this.searchQuery }, (results, status) => {
        if (status === 'OK' && results.length > 0) {
          const { lat, lng } = results[0].geometry.location;
          this.mapCenter = { lat: lat(), lng: lng() };
          this.selectedAddress = results[0].formatted_address;
          this.projectDetailForm.controls['longitude'].setValue(
            this.mapCenter?.lat.toString()
          );
          this.projectDetailForm.controls['latitude'].setValue(
            this.mapCenter?.lng.toString()
          );
        }
      });
    }
  }

  callSearchQuery(data: any, value: any) {
    if (data === 'state') {
      this.locationArr[0] = value;
    } else if (data === 'city') {
      this.locationArr[1] = value;
    } else if (data === 'pinCode') {
      this.locationArr[2] = value;
    } else if (data === 'locality') {
      this.locationArr[3] = value;
    }
    this.searchQuery =
      this.locationArr[0] +
      ' ' +
      this.locationArr[1] +
      ' ' +
      this.locationArr[2] +
      ' ' +
      this.locationArr[3];
  }

  selectFacing(data: any, facing: any) {
    const index = this.facingArray.indexOf(facing.value);
    if (data && index === -1) {
      this.facingArray.push(facing.value);
      this.facingPayload.push(
        FACING.findIndex((item) => item.value === facing.value) + 1
      );
    } else if (!data && index !== -1) {
      this.facingArray.splice(index, 1);
      this.facingPayload.splice(index, 1);
    }
  }

  addManualLocation() {
    this.isManualLocation = !this.isManualLocation;
    const addressFields = ['locality', 'city', 'state', 'country'];
    if (this.isManualLocation) {
      addressFields.forEach((field) => {
        toggleValidation(VALIDATION_SET, this.projectDetailForm, field, [
          Validators.required,
        ]);
      });
    } else {
      addressFields.forEach((field) => {
        toggleValidation(VALIDATION_CLEAR, this.projectDetailForm, field);
      });
      this.projectDetailForm.patchValue({
        locality: null,
        city: null,
        state: null,
        country: null,
        pinCode: null,
      });
    }
  }
  patchUnits() {
    const defaultUnits = {
      carpetAreaUnitId: this.projectDetailForm?.get('carpetAreaUnitId')?.value ?? this.globalSettingsDetails?.defaultValues?.masterAreaUnit,
    };
    this.projectDetailForm?.patchValue(defaultUnits);
  }

  openPreview(isForNotes: boolean): void {
    this.isForNotes = isForNotes
    this.modalRef = this.modalService.show(this.previewModal, {
      class: 'modal-600 modal-dialog-centered ip-modal-unset',
    });
  }

  getPreviewContent() {
    if (this.isForNotes) {
      return this.projectDetailForm.controls['notes'].value
    } else {
      return this.projectDetailForm.controls['projectDescription'].value ||
        this.selectedProject.description
    }
  }
  ngOnDestroy() {
    this._store.dispatch(new ClearFiltersPayload());
    this._store.dispatch(new HasProjectName(null));
    this.stopper.next();
    this.stopper.complete();
  }
}
