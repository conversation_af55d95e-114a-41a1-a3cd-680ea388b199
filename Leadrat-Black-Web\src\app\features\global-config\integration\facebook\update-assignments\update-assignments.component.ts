import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { NgxMatIntlTelInputComponent } from 'ngx-mat-intl-tel-input';
import { takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import { updateIntegrationAssignment } from 'src/app/reducers/automation/automation.actions';
import { getIntegrationAssignment } from 'src/app/reducers/automation/automation.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchAgencyNameList } from 'src/app/reducers/Integration/integration.actions';
import { getAgencyNameList, getAgencyNameListIsLoading } from 'src/app/reducers/Integration/integration.reducer';
import { FetchCampaignList, FetchChannelPartnerList, FetchPropertyList } from 'src/app/reducers/lead/lead.actions';
import { getCampaignList, getCampaignListIsLoading, getChannelPartnerList, getChannelPartnerListIsLoading, getPropertyList, getPropertyListIsLoading } from 'src/app/reducers/lead/lead.reducer';
import { FetchProjectIdWithName } from 'src/app/reducers/project/project.action';
import {
  getProjectsIDWithName,
  getProjectsIDWithNameIsLoading,
} from 'src/app/reducers/project/project.reducer';
import { FetchAllLocations } from 'src/app/reducers/site/site.actions';
import {
  getAllLocations,
  getAllLocationsIsLoading,
} from 'src/app/reducers/site/site.reducer';

@Component({
  selector: 'update-assignments',
  templateUrl: './update-assignments.component.html',
})
export class UpdateAssignmentsComponent implements OnInit {
  placesList: any;
  allLocationsIsLoading: boolean;
  allProjectList: any;
  projectListIsLoading: boolean;
  preferredCountries: any[] = [];
  updateForm: FormGroup;
  globalSettingsData: any;
  selectedAccountId: any;
  source: any;
  isAdAccount: any;
  selectedAccountName: any;
  selectedAdName: string;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('contactNoInput') contactNoInput!: NgxMatIntlTelInputComponent;
  agencyNameList: any;
  agencyListIsLoading: boolean;
  campaignList: any;
  campaignListIsLoading: boolean;
  channelPartnerList: any;
  channelPartnerListIsLoading: boolean;
  propertyList: any;
  propertyListIsLoading: boolean;

  constructor(
    private store: Store<AppState>,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private cdr: ChangeDetectorRef,
    private fb: FormBuilder
  ) {
    this.updateForm = this.fb.group({
      project: [null],
      property: [null],
      location: [null],
      countryCode: [null],
      agencyName: [null],
      campaign: [null],
      channelPartner: [null],
    });
  }
  ngOnInit() {
    this.store.dispatch(new FetchProjectIdWithName());
    this.store.dispatch(new FetchAllLocations());
    this.store.dispatch(new FetchAgencyNameList());
    this.store.dispatch(new FetchCampaignList());
    this.store.dispatch(new FetchChannelPartnerList());
    this.store.dispatch(new FetchPropertyList(true));


    const selectAndPipe = (selector: any) =>
      this.store.select(selector).pipe(takeUntil(this.stopper));

    selectAndPipe(getProjectsIDWithName).subscribe((data: any) => {
      this.allProjectList = data
        ?.filter((data: any) => data.name)
        .slice()
        .sort((a: any, b: any) => a.name.localeCompare(b.name));
    });

    this.store
      .select(getProjectsIDWithNameIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.projectListIsLoading = data;
      });

    selectAndPipe(getAgencyNameList).subscribe((data: any) => {
      this.agencyNameList = data
        .filter((data: any) => data)
        .slice()
        .sort((a: any, b: any) => a.localeCompare(b));
    });

    this.store
      .select(getAgencyNameListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.agencyListIsLoading = data;
      });

    selectAndPipe(getCampaignList).subscribe((data: any) => {
      this.campaignList = data
        .filter((data: any) => data)
        .slice()
        .sort((a: any, b: any) => a.localeCompare(b));
    });

    this.store
      .select(getCampaignListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.campaignListIsLoading = data;
      });

    selectAndPipe(getChannelPartnerList).subscribe((data: any) => {
      this.channelPartnerList = data
        .filter((data: any) => data)
        .slice()
        .sort((a: any, b: any) => a.localeCompare(b));
    });

    this.store
      .select(getChannelPartnerListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.channelPartnerListIsLoading = data;
      });

    selectAndPipe(getAllLocations).subscribe((data: any) => {
      this.placesList = data?.items
        ?.slice()
        .sort((a: any, b: any) => a.location.localeCompare(b.location));
    });

    this.store
      .select(getPropertyList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.propertyList = data
          .filter((item: any) => !item?.isArchived && item?.title != null)
          .slice()
          .sort((a: any, b: any) => {
            const titleA = a?.title ?? '';
            const titleB = b?.title ?? '';
            return titleA.localeCompare(titleB);
          });
      });

    this.store
      .select(getPropertyListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.propertyListIsLoading = isLoading;
      });

    this.store
      .select(getAllLocationsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.allLocationsIsLoading = data;
      });

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.globalSettingsData = data;
      });

    this.store
      .select(getIntegrationAssignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (this.contactNoInput) {
          this.updateForm?.patchValue({
            project: (!data?.project?.isDeleted && !data?.project?.isArchived)
              ? data?.project?.id
              : null,
            property: !data.property?.isDeleted && !data.property?.isArchived
              ? data.property?.title
              : null,
            location: data?.location?.id,
            agencyName: !data.agency?.isDeleted
              ? data.agency?.name
              : null,
            campaign: !data.campaign?.isDeleted
              ? data.campaign?.name
              : null,
            channelPartner: !data.channelPartner?.isDeleted
              ? data.channelPartner?.firmName
              : null
          });
          this.updatePreferredCountry(data?.countryCode);
        }
      });
  }

  private updatePreferredCountry(countryCode: string | null) {
    if (!this.contactNoInput) {
      return;
    }

    let matchingCountry: any = null;

    if (countryCode) {
      const countryCodeWithoutPlus = countryCode.replace('+', '');
      matchingCountry = this.contactNoInput?.allCountries?.find(
        (country: any) => country.dialCode === countryCodeWithoutPlus
      );
    }

    if (matchingCountry) {
      this.preferredCountries = [matchingCountry.iso2.toLowerCase()];
    } else if (this.globalSettingsData?.countries?.length) {
      const fallbackCountryCode =
        this.globalSettingsData.countries[0].code.toLowerCase();
      matchingCountry = this.contactNoInput.allCountries.find(
        (country: any) => country.iso2.toLowerCase() === fallbackCountryCode
      );
      this.preferredCountries = [fallbackCountryCode];
    } else {
      this.preferredCountries = ['in'];
      matchingCountry = this.contactNoInput.allCountries.find(
        (country: any) => country.iso2.toLowerCase() === 'in'
      );
    }

    this.contactNoInput.selectedCountry = matchingCountry;
    this.cdr.detectChanges();
  }

  updateProjectAndLocation() {
    let payload: any = {
      id: this.selectedAccountId,
      source: this.source,
      projectId: this.updateForm.value.project,
      property: this.updateForm.value.property,
      locationId: this.updateForm.value.location,
      countryCode: this.contactNoInput?.selectedCountry?.dialCode
        ? this.contactNoInput.selectedCountry.dialCode
        : null,
      agency: this.updateForm.value.agencyName,
      camapign: this.updateForm.value.campaign,
      channelPartner: this.updateForm.value.channelPartner,
    };
    this.store.dispatch(new updateIntegrationAssignment(payload));
    this.closeModal();
  }

  closeModal() {
    this.modalService.hide();
    this.isAdAccount = false;
  }
}
