{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nimport { EmptyError } from '../util/EmptyError';\nexport function single(predicate) {\n  return source => source.lift(new SingleOperator(predicate, source));\n}\n\nclass SingleOperator {\n  constructor(predicate, source) {\n    this.predicate = predicate;\n    this.source = source;\n  }\n\n  call(subscriber, source) {\n    return source.subscribe(new SingleSubscriber(subscriber, this.predicate, this.source));\n  }\n\n}\n\nclass SingleSubscriber extends Subscriber {\n  constructor(destination, predicate, source) {\n    super(destination);\n    this.predicate = predicate;\n    this.source = source;\n    this.seenValue = false;\n    this.index = 0;\n  }\n\n  applySingleValue(value) {\n    if (this.seenValue) {\n      this.destination.error('Sequence contains more than one element');\n    } else {\n      this.seenValue = true;\n      this.singleValue = value;\n    }\n  }\n\n  _next(value) {\n    const index = this.index++;\n\n    if (this.predicate) {\n      this.tryNext(value, index);\n    } else {\n      this.applySingleValue(value);\n    }\n  }\n\n  tryNext(value, index) {\n    try {\n      if (this.predicate(value, index, this.source)) {\n        this.applySingleValue(value);\n      }\n    } catch (err) {\n      this.destination.error(err);\n    }\n  }\n\n  _complete() {\n    const destination = this.destination;\n\n    if (this.index > 0) {\n      destination.next(this.seenValue ? this.singleValue : undefined);\n      destination.complete();\n    } else {\n      destination.error(new EmptyError());\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Subscriber", "EmptyError", "single", "predicate", "source", "lift", "SingleOperator", "constructor", "call", "subscriber", "subscribe", "SingleSubscriber", "destination", "seenValue", "index", "applySingleValue", "value", "error", "singleValue", "_next", "tryNext", "err", "_complete", "next", "undefined", "complete"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/@angular-slider/ngx-slider/node_modules/rxjs/_esm2015/internal/operators/single.js"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { EmptyError } from '../util/EmptyError';\nexport function single(predicate) {\n    return (source) => source.lift(new SingleOperator(predicate, source));\n}\nclass SingleOperator {\n    constructor(predicate, source) {\n        this.predicate = predicate;\n        this.source = source;\n    }\n    call(subscriber, source) {\n        return source.subscribe(new SingleSubscriber(subscriber, this.predicate, this.source));\n    }\n}\nclass SingleSubscriber extends Subscriber {\n    constructor(destination, predicate, source) {\n        super(destination);\n        this.predicate = predicate;\n        this.source = source;\n        this.seenValue = false;\n        this.index = 0;\n    }\n    applySingleValue(value) {\n        if (this.seenValue) {\n            this.destination.error('Sequence contains more than one element');\n        }\n        else {\n            this.seenValue = true;\n            this.singleValue = value;\n        }\n    }\n    _next(value) {\n        const index = this.index++;\n        if (this.predicate) {\n            this.tryNext(value, index);\n        }\n        else {\n            this.applySingleValue(value);\n        }\n    }\n    tryNext(value, index) {\n        try {\n            if (this.predicate(value, index, this.source)) {\n                this.applySingleValue(value);\n            }\n        }\n        catch (err) {\n            this.destination.error(err);\n        }\n    }\n    _complete() {\n        const destination = this.destination;\n        if (this.index > 0) {\n            destination.next(this.seenValue ? this.singleValue : undefined);\n            destination.complete();\n        }\n        else {\n            destination.error(new EmptyError);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,OAAO,SAASC,MAAT,CAAgBC,SAAhB,EAA2B;EAC9B,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAY,IAAIC,cAAJ,CAAmBH,SAAnB,EAA8BC,MAA9B,CAAZ,CAAnB;AACH;;AACD,MAAME,cAAN,CAAqB;EACjBC,WAAW,CAACJ,SAAD,EAAYC,MAAZ,EAAoB;IAC3B,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,MAAL,GAAcA,MAAd;EACH;;EACDI,IAAI,CAACC,UAAD,EAAaL,MAAb,EAAqB;IACrB,OAAOA,MAAM,CAACM,SAAP,CAAiB,IAAIC,gBAAJ,CAAqBF,UAArB,EAAiC,KAAKN,SAAtC,EAAiD,KAAKC,MAAtD,CAAjB,CAAP;EACH;;AAPgB;;AASrB,MAAMO,gBAAN,SAA+BX,UAA/B,CAA0C;EACtCO,WAAW,CAACK,WAAD,EAAcT,SAAd,EAAyBC,MAAzB,EAAiC;IACxC,MAAMQ,WAAN;IACA,KAAKT,SAAL,GAAiBA,SAAjB;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKS,SAAL,GAAiB,KAAjB;IACA,KAAKC,KAAL,GAAa,CAAb;EACH;;EACDC,gBAAgB,CAACC,KAAD,EAAQ;IACpB,IAAI,KAAKH,SAAT,EAAoB;MAChB,KAAKD,WAAL,CAAiBK,KAAjB,CAAuB,yCAAvB;IACH,CAFD,MAGK;MACD,KAAKJ,SAAL,GAAiB,IAAjB;MACA,KAAKK,WAAL,GAAmBF,KAAnB;IACH;EACJ;;EACDG,KAAK,CAACH,KAAD,EAAQ;IACT,MAAMF,KAAK,GAAG,KAAKA,KAAL,EAAd;;IACA,IAAI,KAAKX,SAAT,EAAoB;MAChB,KAAKiB,OAAL,CAAaJ,KAAb,EAAoBF,KAApB;IACH,CAFD,MAGK;MACD,KAAKC,gBAAL,CAAsBC,KAAtB;IACH;EACJ;;EACDI,OAAO,CAACJ,KAAD,EAAQF,KAAR,EAAe;IAClB,IAAI;MACA,IAAI,KAAKX,SAAL,CAAea,KAAf,EAAsBF,KAAtB,EAA6B,KAAKV,MAAlC,CAAJ,EAA+C;QAC3C,KAAKW,gBAAL,CAAsBC,KAAtB;MACH;IACJ,CAJD,CAKA,OAAOK,GAAP,EAAY;MACR,KAAKT,WAAL,CAAiBK,KAAjB,CAAuBI,GAAvB;IACH;EACJ;;EACDC,SAAS,GAAG;IACR,MAAMV,WAAW,GAAG,KAAKA,WAAzB;;IACA,IAAI,KAAKE,KAAL,GAAa,CAAjB,EAAoB;MAChBF,WAAW,CAACW,IAAZ,CAAiB,KAAKV,SAAL,GAAiB,KAAKK,WAAtB,GAAoCM,SAArD;MACAZ,WAAW,CAACa,QAAZ;IACH,CAHD,MAIK;MACDb,WAAW,CAACK,KAAZ,CAAkB,IAAIhB,UAAJ,EAAlB;IACH;EACJ;;AA7CqC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}