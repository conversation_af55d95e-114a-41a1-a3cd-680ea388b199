{"ast": null, "code": "/**\n * The base implementation of `_.isNaN` without support for number objects.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is `NaN`, else `false`.\n */\nfunction baseIsNaN(value) {\n  return value !== value;\n}\n\nexport default baseIsNaN;", "map": {"version": 3, "names": ["baseIsNaN", "value"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/lodash-es/_baseIsNaN.js"], "sourcesContent": ["/**\n * The base implementation of `_.isNaN` without support for number objects.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is `NaN`, else `false`.\n */\nfunction baseIsNaN(value) {\n  return value !== value;\n}\n\nexport default baseIsNaN;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAT,CAAmBC,KAAnB,EAA0B;EACxB,OAAOA,KAAK,KAAKA,KAAjB;AACD;;AAED,eAAeD,SAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}