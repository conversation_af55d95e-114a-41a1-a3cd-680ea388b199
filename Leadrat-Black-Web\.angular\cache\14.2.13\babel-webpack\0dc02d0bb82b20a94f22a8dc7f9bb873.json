{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter, TemplateRef } from '@angular/core';\nimport { Subject, firstValueFrom, map, skipWhile, switchMap, take, takeUntil } from 'rxjs';\nimport * as moment from 'moment';\nimport { PAGE_SIZE, REPORTS_DATE_TYPE, REPORT_FILTERS_KEY_LABEL, SHOW_ENTRIES, USER_VISIBILITY } from 'src/app/app.constants';\nimport { IntegrationSource, LeadSource, ReportDateType } from 'src/app/app.enum';\nimport { assignToSort, changeCalendar, getPages, getSystemTimeOffset, getSystemTimeZoneId, getTimeZoneDate, getTotalCountForReports, onPickerOpened, patchTimeZoneDate, setTimeZoneDate, snakeToCamel } from 'src/app/core/utils/common.util';\nimport { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';\nimport { getAllSources, getAllSourcesLoading, getGlobalAnonymousIsLoading, getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';\nimport { FetchLeadCities, FetchLeadStates, FetchProjectList, FetchSubSourceList } from 'src/app/reducers/lead/lead.actions';\nimport { getIsLeadCustomStatusEnabled, getLeadCities, getLeadCitiesIsLoading, getLeadCountries, getLeadCountriesIsLoading, getLeadStates, getLeadStatesIsLoading, getProjectList, getProjectListIsLoading, getSubSourceList, getSubSourceListIsLoading } from 'src/app/reducers/lead/lead.reducer';\nimport { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';\nimport { FetchReportsCustomUser, FetchReportsUser, FetchUserExportSuccess, UpdateUserFilterPayload } from 'src/app/reducers/reports/reports.actions';\nimport { getReportsCustomUsersList, getReportsCustomUsersListIsLoading, getReportsCustomUsersListTotalCount, getReportsUsersList, getReportsUsersListIsLoading, getUserFiltersPayload } from 'src/app/reducers/reports/reports.reducer';\nimport { getCustomStatusList, getCustomStatusListIsLoading } from 'src/app/reducers/status/status.reducer';\nimport { FetchOnlyReporteesWithInactive, FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';\nimport { getOnlyReporteesWithInactive, getOnlyReporteesWithInactiveIsLoading, getUserBasicDetails, getUsersListForReassignment, getUsersListForReassignmentIsLoading } from 'src/app/reducers/teams/teams.reducer';\nimport { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/shared/grid-options.service\";\nimport * as i2 from \"@ngrx/store\";\nimport * as i3 from \"src/app/services/shared/header-title.service\";\nimport * as i4 from \"@angular/platform-browser\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"ngx-bootstrap/modal\";\nimport * as i7 from \"src/app/services/shared/share-data.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@ng-select/ng-select\";\nimport * as i10 from \"@danielmoncada/angular-datetime-picker\";\nimport * as i11 from \"src/app/shared/components/pagination/pagination.component\";\nimport * as i12 from \"src/app/shared/directives/image.directive\";\nimport * as i13 from \"src/app/shared/directives/dropdownPanel-Resize.directive\";\nimport * as i14 from \"../../../../shared/components/application-loader/application-loader.component\";\nimport * as i15 from \"ag-grid-angular\";\nimport * as i16 from \"@angular/forms\";\nimport * as i17 from \"../../reports-graph/reports-graph.component\";\nimport * as i18 from \"@ngx-translate/core\";\nconst _c0 = [\"reportsGraph\"];\n\nfunction StatusReportComponent_div_0_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function StatusReportComponent_div_0_ng_container_4_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const visibilityImage_r15 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.currentVisibility(visibilityImage_r15.userStatus, true));\n    });\n    i0.ɵɵelementStart(2, \"div\", 45)(3, \"a\");\n    i0.ɵɵelement(4, \"img\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 47);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const visibilityImage_r15 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"title\", (ctx_r3.usersData == null ? null : ctx_r3.usersData[visibilityImage_r15 == null ? null : visibilityImage_r15.visibility]) ? ctx_r3.usersData == null ? null : ctx_r3.usersData[visibilityImage_r15 == null ? null : visibilityImage_r15.visibility] : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r3.appliedFilter.userStatus == visibilityImage_r15.userStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"type\", \"leadrat\")(\"appImage\", ctx_r3.s3BucketUrl + visibilityImage_r15.image);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", ctx_r3.appliedFilter.userStatus == visibilityImage_r15.userStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(visibilityImage_r15.name);\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_template_6_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 82);\n    i0.ɵɵlistener(\"click\", function StatusReportComponent_div_0_ng_template_6_ng_template_15_Template_span_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r33);\n      const item_r30 = restoredCtx.item;\n      const clear_r31 = restoredCtx.clear;\n      return i0.ɵɵresetView(clear_r31(item_r30));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"span\", 83);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r30 = ctx.item;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r30.firstName + \" \" + item_r30.lastName, \"\");\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_template_6_ng_template_16_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 89);\n    i0.ɵɵtext(1, \"( Disabled )\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_template_6_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"div\", 85);\n    i0.ɵɵelement(2, \"input\", 86)(3, \"span\", 12);\n    i0.ɵɵelementStart(4, \"span\", 87);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, StatusReportComponent_div_0_ng_template_6_ng_template_16_span_6_Template, 2, 0, \"span\", 88);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r34 = ctx.item;\n    const item$_r35 = ctx.item$;\n    const index_r36 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"item-\", index_r36, \"\")(\"automate-id\", \"item-\", index_r36, \"\");\n    i0.ɵɵproperty(\"checked\", item$_r35.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", item_r34.firstName, \" \", item_r34.lastName, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r34.isActive);\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_template_6_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵelement(1, \"input\", 86)(2, \"span\", 12);\n    i0.ɵɵelementStart(3, \"span\", 87);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const item_r38 = ctx.item;\n    const item$_r39 = ctx.item$;\n    const index_r40 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"item-\", index_r40, \"\")(\"automate-id\", \"item-\", index_r40, \"\");\n    i0.ɵɵproperty(\"checked\", item$_r39.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r38 == null ? null : item_r38.displayName, \"\");\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_template_6_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵelement(1, \"input\", 86)(2, \"span\", 12);\n    i0.ɵɵelementStart(3, \"span\", 87);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const item_r41 = ctx.item;\n    const item$_r42 = ctx.item$;\n    const index_r43 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"item-\", index_r43, \"\")(\"automate-id\", \"item-\", index_r43, \"\");\n    i0.ɵɵproperty(\"checked\", item$_r42.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r41, \"\");\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_template_6_ng_template_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵelement(1, \"input\", 86)(2, \"span\", 12);\n    i0.ɵɵelementStart(3, \"span\", 87);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const item_r44 = ctx.item;\n    const item$_r45 = ctx.item$;\n    const index_r46 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"item-\", index_r46, \"\")(\"automate-id\", \"item-\", index_r46, \"\");\n    i0.ɵɵproperty(\"checked\", item$_r45.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r44, \"\");\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_template_6_ng_template_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵelement(1, \"input\", 86)(2, \"span\", 12);\n    i0.ɵɵelementStart(3, \"span\", 87);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const item_r47 = ctx.item;\n    const item$_r48 = ctx.item$;\n    const index_r49 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"item-\", index_r49, \"\")(\"automate-id\", \"item-\", index_r49, \"\");\n    i0.ɵɵproperty(\"checked\", item$_r48.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r47, \"\");\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_template_6_ng_template_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵelement(1, \"input\", 86)(2, \"span\", 12);\n    i0.ɵɵelementStart(3, \"span\", 87);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const item_r50 = ctx.item;\n    const item$_r51 = ctx.item$;\n    const index_r52 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"item-\", index_r52, \"\")(\"automate-id\", \"item-\", index_r52, \"\");\n    i0.ɵɵproperty(\"checked\", item$_r51.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r50, \"\");\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_template_6_ng_option_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 90);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const dType_r53 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", dType_r53);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(dType_r53);\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_template_6_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 91);\n    i0.ɵɵlistener(\"click\", function StatusReportComponent_div_0_ng_template_6_div_70_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r54 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r54.onResetDateFilter());\n    });\n    i0.ɵɵelement(1, \"span\", 92);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_template_6_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"div\", 94)(2, \"label\", 10)(3, \"input\", 54);\n    i0.ɵɵlistener(\"ngModelChange\", function StatusReportComponent_div_0_ng_template_6_div_71_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r56 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r56.appliedFilter.ShouldShowAll = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"span\", 12);\n    i0.ɵɵtext(5, \"Show all users \");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r29.appliedFilter.ShouldShowAll);\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    \"blinking pe-none\": a0\n  };\n};\n\nfunction StatusReportComponent_div_0_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49)(2, \"div\", 50)(3, \"div\", 51)(4, \"div\", 52)(5, \"div\", 53);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"label\", 10)(9, \"input\", 54);\n    i0.ɵɵlistener(\"ngModelChange\", function StatusReportComponent_div_0_ng_template_6_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r58 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r58.appliedFilter.withTeam = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"span\", 12);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 55)(14, \"ng-select\", 56);\n    i0.ɵɵlistener(\"ngModelChange\", function StatusReportComponent_div_0_ng_template_6_Template_ng_select_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r60 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r60.appliedFilter.users = $event);\n    });\n    i0.ɵɵtemplate(15, StatusReportComponent_div_0_ng_template_6_ng_template_15_Template, 3, 1, \"ng-template\", 57);\n    i0.ɵɵtemplate(16, StatusReportComponent_div_0_ng_template_6_ng_template_16_Template, 7, 6, \"ng-template\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 58)(18, \"div\", 53);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 59)(22, \"ng-select\", 60);\n    i0.ɵɵlistener(\"ngModelChange\", function StatusReportComponent_div_0_ng_template_6_Template_ng_select_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r61 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r61.appliedFilter.sources = $event);\n    })(\"change\", function StatusReportComponent_div_0_ng_template_6_Template_ng_select_change_22_listener() {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r62 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r62.updateSubSource());\n    });\n    i0.ɵɵpipe(23, \"translate\");\n    i0.ɵɵtemplate(24, StatusReportComponent_div_0_ng_template_6_ng_template_24_Template, 5, 4, \"ng-template\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 58)(26, \"div\", 53);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 59)(30, \"ng-select\", 61);\n    i0.ɵɵlistener(\"ngModelChange\", function StatusReportComponent_div_0_ng_template_6_Template_ng_select_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r63 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r63.appliedFilter.subSources = $event);\n    });\n    i0.ɵɵpipe(31, \"translate\");\n    i0.ɵɵtemplate(32, StatusReportComponent_div_0_ng_template_6_ng_template_32_Template, 5, 4, \"ng-template\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 58)(34, \"div\", 53);\n    i0.ɵɵtext(35);\n    i0.ɵɵpipe(36, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 62)(38, \"ng-select\", 63);\n    i0.ɵɵlistener(\"ngModelChange\", function StatusReportComponent_div_0_ng_template_6_Template_ng_select_ngModelChange_38_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r64 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r64.appliedFilter.projects = $event);\n    });\n    i0.ɵɵpipe(39, \"translate\");\n    i0.ɵɵtemplate(40, StatusReportComponent_div_0_ng_template_6_ng_template_40_Template, 5, 4, \"ng-template\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 58)(42, \"div\", 53);\n    i0.ɵɵtext(43, \"State\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 59)(45, \"ng-select\", 64);\n    i0.ɵɵlistener(\"ngModelChange\", function StatusReportComponent_div_0_ng_template_6_Template_ng_select_ngModelChange_45_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r65 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r65.appliedFilter.states = $event);\n    });\n    i0.ɵɵpipe(46, \"translate\");\n    i0.ɵɵtemplate(47, StatusReportComponent_div_0_ng_template_6_ng_template_47_Template, 5, 4, \"ng-template\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(48, \"div\", 58)(49, \"div\", 53);\n    i0.ɵɵtext(50, \"City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"div\", 59)(52, \"ng-select\", 64);\n    i0.ɵɵlistener(\"ngModelChange\", function StatusReportComponent_div_0_ng_template_6_Template_ng_select_ngModelChange_52_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r66 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r66.appliedFilter.cities = $event);\n    });\n    i0.ɵɵpipe(53, \"translate\");\n    i0.ɵɵtemplate(54, StatusReportComponent_div_0_ng_template_6_ng_template_54_Template, 5, 4, \"ng-template\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(55, \"div\", 58)(56, \"div\", 53);\n    i0.ɵɵtext(57);\n    i0.ɵɵpipe(58, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"div\", 65)(60, \"div\", 66)(61, \"div\", 67)(62, \"ng-select\", 68);\n    i0.ɵɵlistener(\"ngModelChange\", function StatusReportComponent_div_0_ng_template_6_Template_ng_select_ngModelChange_62_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r67 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r67.appliedFilter.dateType = $event);\n    });\n    i0.ɵɵtemplate(63, StatusReportComponent_div_0_ng_template_6_ng_option_63_Template, 2, 2, \"ng-option\", 69);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"div\", 70)(65, \"div\", 71);\n    i0.ɵɵelement(66, \"span\", 72);\n    i0.ɵɵelementStart(67, \"input\", 73);\n    i0.ɵɵlistener(\"ngModelChange\", function StatusReportComponent_div_0_ng_template_6_Template_input_ngModelChange_67_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r68 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r68.appliedFilter.date = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"owl-date-time\", 74, 75);\n    i0.ɵɵlistener(\"afterPickerOpen\", function StatusReportComponent_div_0_ng_template_6_Template_owl_date_time_afterPickerOpen_68_listener() {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r69 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r69.onPickerOpened(ctx_r69.currentDate));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(70, StatusReportComponent_div_0_ng_template_6_div_70_Template, 2, 0, \"div\", 76);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(71, StatusReportComponent_div_0_ng_template_6_div_71_Template, 6, 1, \"div\", 77);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(72, \"div\", 78)(73, \"u\", 79);\n    i0.ɵɵlistener(\"click\", function StatusReportComponent_div_0_ng_template_6_Template_u_click_73_listener() {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r70 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r70.modalService.hide());\n    });\n    i0.ɵɵtext(74);\n    i0.ɵɵpipe(75, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function StatusReportComponent_div_0_ng_template_6_Template_button_click_76_listener() {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r71 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r71.reset());\n    });\n    i0.ɵɵtext(77);\n    i0.ɵɵpipe(78, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(79, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function StatusReportComponent_div_0_ng_template_6_Template_button_click_79_listener() {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r72 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r72.applyAdvancedFilter());\n    });\n    i0.ɵɵtext(80);\n    i0.ɵɵpipe(81, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const _r27 = i0.ɵɵreference(69);\n\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 65, \"USER.user\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.appliedFilter.withTeam);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(12, 67, \"DASHBOARD.with-team\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r5.canViewAllUsers ? ctx_r5.allUsers : ctx_r5.onlyReportees)(\"ngClass\", i0.ɵɵpureFunction1(93, _c1, ctx_r5.canViewAllUsers ? ctx_r5.isAllUsersLoading : ctx_r5.isOnlyReporteesLoading))(\"multiple\", true)(\"closeOnSelect\", false)(\"ngModel\", ctx_r5.appliedFilter.users);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(20, 69, \"LEADS.source\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(23, 71, \"GLOBAL.select\"));\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r5.leadSources)(\"multiple\", true)(\"closeOnSelect\", false)(\"ngClass\", i0.ɵɵpureFunction1(95, _c1, ctx_r5.isSourcesLoading))(\"ngModel\", ctx_r5.appliedFilter.sources);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(28, 73, \"LEADS.sub-source\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(31, 75, \"GLOBAL.select\"));\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r5.subSourceList)(\"ngClass\", i0.ɵɵpureFunction1(97, _c1, ctx_r5.allSubSourceListIsLoading))(\"multiple\", true)(\"closeOnSelect\", false)(\"ngModel\", ctx_r5.appliedFilter.subSources);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(36, 77, \"SIDEBAR.project\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(39, 79, \"GLOBAL.select\"));\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r5.projectList)(\"ngClass\", i0.ɵɵpureFunction1(99, _c1, ctx_r5.isProjectListLoading))(\"multiple\", true)(\"closeOnSelect\", false)(\"ngModel\", ctx_r5.appliedFilter.projects);\n    i0.ɵɵadvance(7);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(46, 81, \"GLOBAL.select\"));\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r5.states)(\"ngClass\", i0.ɵɵpureFunction1(101, _c1, ctx_r5.statesIsLoading))(\"multiple\", true)(\"closeOnSelect\", false)(\"ngModel\", ctx_r5.appliedFilter.states);\n    i0.ɵɵadvance(7);\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(53, 83, \"GLOBAL.select\"));\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r5.cities)(\"ngClass\", i0.ɵɵpureFunction1(103, _c1, ctx_r5.citiesIsLoading))(\"multiple\", true)(\"closeOnSelect\", false)(\"ngModel\", ctx_r5.appliedFilter.cities);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(58, 85, \"REPORTS.date-filters\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"searchable\", false)(\"ngModel\", ctx_r5.appliedFilter.dateType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.dateTypeList);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"owlDateTimeTrigger\", _r27);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"max\", ctx_r5.appliedFilter.dateType === \"Modified Date\" || ctx_r5.appliedFilter.dateType === \"Created Date\" ? ctx_r5.maxDate : \"\")(\"owlDateTimeTrigger\", _r27)(\"owlDateTime\", _r27)(\"selectMode\", \"range\")(\"ngModel\", ctx_r5.appliedFilter.date)(\"disabled\", !ctx_r5.appliedFilter.dateType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"pickerType\", \"calendar\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.appliedFilter == null ? null : ctx_r5.appliedFilter.date == null ? null : ctx_r5.appliedFilter.date[0]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isCustomStatusEnabled);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(75, 87, \"BUTTONS.cancel\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(78, 89, \"GLOBAL.reset\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(81, 91, \"GLOBAL.search\"));\n  }\n}\n\nfunction StatusReportComponent_div_0_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r76 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵlistener(\"click\", function StatusReportComponent_div_0_div_24_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r75 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r75.exportLeadReport());\n    });\n    i0.ɵɵelementStart(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 99);\n    i0.ɵɵtext(5, \"(tabular)\");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"REPORTS.export\"));\n  }\n}\n\nfunction StatusReportComponent_div_0_div_24_div_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 99);\n    i0.ɵɵtext(5, \"(visual)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"REPORTS.export\"));\n  }\n}\n\nfunction StatusReportComponent_div_0_div_24_div_2_ng_template_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"div\", 102);\n    i0.ɵɵelementContainerEnd();\n  }\n}\n\nconst _c2 = function () {\n  return [1, 2, 3];\n};\n\nfunction StatusReportComponent_div_0_div_24_div_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵtemplate(1, StatusReportComponent_div_0_div_24_div_2_ng_template_2_ng_container_1_Template, 2, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c2));\n  }\n}\n\nfunction StatusReportComponent_div_0_div_24_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r83 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵlistener(\"click\", function StatusReportComponent_div_0_div_24_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r83);\n      const ctx_r82 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(!ctx_r82.isExporting && ctx_r82.exportGraphAsPDF());\n    });\n    i0.ɵɵtemplate(1, StatusReportComponent_div_0_div_24_div_2_ng_container_1_Template, 6, 3, \"ng-container\", 43);\n    i0.ɵɵtemplate(2, StatusReportComponent_div_0_div_24_div_2_ng_template_2_Template, 2, 2, \"ng-template\", null, 100, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r78 = i0.ɵɵreference(3);\n\n    const ctx_r74 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"pe-none\", ctx_r74.isExporting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r74.isExporting)(\"ngIfElse\", _r78);\n  }\n}\n\nconst _c3 = function (a0) {\n  return {\n    \"pe-none\": a0\n  };\n};\n\nfunction StatusReportComponent_div_0_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95);\n    i0.ɵɵtemplate(1, StatusReportComponent_div_0_div_24_div_1_Template, 6, 3, \"div\", 96);\n    i0.ɵɵtemplate(2, StatusReportComponent_div_0_div_24_div_2_Template, 4, 4, \"div\", 97);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c3, ctx_r6.currentView === \"graph\" && !ctx_r6.isGraphExportEnabled()));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.currentView === \"table\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.currentView === \"graph\");\n  }\n}\n\nfunction StatusReportComponent_div_0_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 103);\n  }\n}\n\nfunction StatusReportComponent_div_0_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 104);\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵelement(1, \"input\", 86)(2, \"span\", 12);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r84 = ctx.item;\n    const item$_r85 = ctx.item$;\n    const index_r86 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"item-\", index_r86, \"\")(\"automate-id\", \"item-\", index_r86, \"\");\n    i0.ɵɵproperty(\"checked\", item$_r85.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r84.label);\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_option_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const pageSize_r87 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", pageSize_r87);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", pageSize_r87, \"\");\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_container_57_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r94 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 112);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 113);\n    i0.ɵɵlistener(\"click\", function StatusReportComponent_div_0_ng_container_57_div_3_div_1_Template_span_click_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r94);\n      const value_r91 = restoredCtx.$implicit;\n      const filter_r89 = i0.ɵɵnextContext().$implicit;\n      const ctx_r92 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r92.onRemoveFilter(filter_r89.key, value_r91));\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const value_r91 = ctx.$implicit;\n    const filter_r89 = i0.ɵɵnextContext().$implicit;\n    const ctx_r90 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r90.reportFiltersKeyLabel[filter_r89.key] || filter_r89.key, \": \", filter_r89.key === \"users\" ? ctx_r90.getUserName(value_r91) : value_r91, \" \");\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_container_57_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtemplate(1, StatusReportComponent_div_0_ng_container_57_div_3_div_1_Template, 3, 2, \"div\", 111);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const filter_r89 = ctx.$implicit;\n    const ctx_r88 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r88.getArrayOfFilters(filter_r89.key, filter_r89.value));\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_container_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r97 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 106)(2, \"drag-scroll\", 107);\n    i0.ɵɵtemplate(3, StatusReportComponent_div_0_ng_container_57_div_3_Template, 2, 1, \"div\", 108);\n    i0.ɵɵpipe(4, \"keyvalue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 109);\n    i0.ɵɵlistener(\"click\", function StatusReportComponent_div_0_ng_container_57_Template_div_click_5_listener() {\n      i0.ɵɵrestoreView(_r97);\n      const ctx_r96 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r96.reset());\n    });\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(4, 3, ctx_r11.appliedFilter));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(7, 5, \"BUTTONS.clear\"), \" \", i0.ɵɵpipeBind1(8, 7, \"GLOBAL.all\"), \" \");\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_template_58_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r99 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate7(\"\", i0.ɵɵpipeBind1(2, 7, \"GLOBAL.showing\"), \" \", ctx_r99.currOffset * ctx_r99.pageSize + 1, \" \", i0.ɵɵpipeBind1(3, 9, \"GLOBAL.to-small\"), \" \", (ctx_r99.rowData == null ? null : ctx_r99.rowData.length) > 1 ? ctx_r99.currOffset * ctx_r99.pageSize + (ctx_r99.rowData == null ? null : ctx_r99.rowData.length) - 1 : ctx_r99.currOffset * ctx_r99.pageSize + (ctx_r99.rowData == null ? null : ctx_r99.rowData.length), \" \", i0.ɵɵpipeBind1(4, 11, \"GLOBAL.of-small\"), \" \", ctx_r99.userTotalCount, \" \", i0.ɵɵpipeBind1(5, 13, \"GLOBAL.entries-small\"), \"\");\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_template_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r102 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 114)(2, \"ag-grid-angular\", 115, 116);\n    i0.ɵɵlistener(\"gridReady\", function StatusReportComponent_div_0_ng_template_58_Template_ag_grid_angular_gridReady_2_listener($event) {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r101 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r101.onGridReady($event));\n    })(\"cellClickedEvent\", function StatusReportComponent_div_0_ng_template_58_Template_ag_grid_angular_cellClickedEvent_2_listener($event) {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r103 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r103.onCellClicked($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 117);\n    i0.ɵɵtemplate(5, StatusReportComponent_div_0_ng_template_58_div_5_Template, 6, 15, \"div\", 118);\n    i0.ɵɵelementStart(6, \"pagination\", 119);\n    i0.ɵɵlistener(\"pageChange\", function StatusReportComponent_div_0_ng_template_58_Template_pagination_pageChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r104 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r104.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\")(8, \"report-graph\", 120, 121);\n    i0.ɵɵlistener(\"exportStarted\", function StatusReportComponent_div_0_ng_template_58_Template_report_graph_exportStarted_8_listener() {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r105 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r105.isExporting = true);\n    })(\"exportFinished\", function StatusReportComponent_div_0_ng_template_58_Template_report_graph_exportFinished_8_listener() {\n      i0.ɵɵrestoreView(_r102);\n      const ctx_r106 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r106.isExporting = false);\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"display\", ctx_r13.currentView === \"table\" ? \"block\" : \"none\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pagination\", true)(\"paginationPageSize\", ctx_r13.pageSize + 1)(\"gridOptions\", ctx_r13.gridOptions)(\"rowData\", ctx_r13.rowData)(\"suppressPaginationPanel\", true)(\"alwaysShowHorizontalScroll\", true)(\"alwaysShowVerticalScroll\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.userTotalCount);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"offset\", ctx_r13.currOffset)(\"limit\", 1)(\"range\", 1)(\"size\", ctx_r13.getPages(ctx_r13.userTotalCount, ctx_r13.pageSize));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"display\", ctx_r13.currentView === \"graph\" ? \"block\" : \"none\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"payload\", ctx_r13.filtersPayload)(\"rowData\", ctx_r13.rowData)(\"gridOptions\", ctx_r13.gridOptions)(\"filteredColumnDefsCache\", ctx_r13.filteredColumnDefsCache);\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_container_60_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 125);\n    i0.ɵɵelementContainer(1, 126);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵnextContext(3);\n\n    const _r1 = i0.ɵɵreference(2);\n\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r1);\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_container_60_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵelement(1, \"img\", 128);\n    i0.ɵɵelementStart(2, \"div\", 129);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 1, \"PROFILE.no-data-found\"));\n  }\n}\n\nfunction StatusReportComponent_div_0_ng_container_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, StatusReportComponent_div_0_ng_container_60_div_1_Template, 2, 1, \"div\", 123);\n    i0.ɵɵtemplate(2, StatusReportComponent_div_0_ng_container_60_div_2_Template, 5, 3, \"div\", 124);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.isCustomStatusEnabled ? ctx_r14.isCustomStatusReportLoading : ctx_r14.isStatusReportLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r14.isCustomStatusEnabled ? !ctx_r14.isCustomStatusReportLoading : !ctx_r14.isStatusReportLoading) && !(ctx_r14.rowData == null ? null : ctx_r14.rowData.length));\n  }\n}\n\nfunction StatusReportComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r110 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 2)(2, \"div\", 3)(3, \"ul\", 4);\n    i0.ɵɵtemplate(4, StatusReportComponent_div_0_ng_container_4_Template, 7, 8, \"ng-container\", 5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 6);\n    i0.ɵɵtemplate(6, StatusReportComponent_div_0_ng_template_6_Template, 82, 105, \"ng-template\", null, 7, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(8, \"div\", 8)(9, \"div\", 9);\n    i0.ɵɵelement(10, \"div\");\n    i0.ɵɵelementStart(11, \"label\", 10)(12, \"input\", 11);\n    i0.ɵɵlistener(\"ngModelChange\", function StatusReportComponent_div_0_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r110);\n      const ctx_r109 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r109.canShowPercentage = $event);\n    })(\"change\", function StatusReportComponent_div_0_Template_input_change_12_listener() {\n      i0.ɵɵrestoreView(_r110);\n      const ctx_r111 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r111.refresh());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"span\", 12);\n    i0.ɵɵtext(14, \"Show Percentage \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 13)(16, \"div\", 14);\n    i0.ɵɵelementContainerStart(17);\n    i0.ɵɵelementStart(18, \"div\", 15);\n    i0.ɵɵelement(19, \"span\", 16);\n    i0.ɵɵelementStart(20, \"input\", 17);\n    i0.ɵɵlistener(\"keydown\", function StatusReportComponent_div_0_Template_input_keydown_20_listener($event) {\n      i0.ɵɵrestoreView(_r110);\n      const ctx_r112 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r112.onSearch($event));\n    })(\"input\", function StatusReportComponent_div_0_Template_input_input_20_listener($event) {\n      i0.ɵɵrestoreView(_r110);\n      const ctx_r113 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r113.isEmptyInput($event));\n    })(\"ngModelChange\", function StatusReportComponent_div_0_Template_input_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r110);\n      const ctx_r114 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r114.searchTerm = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"small\", 18);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(24, StatusReportComponent_div_0_div_24_Template, 3, 5, \"div\", 19);\n    i0.ɵɵelementStart(25, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function StatusReportComponent_div_0_Template_div_click_25_listener() {\n      i0.ɵɵrestoreView(_r110);\n      const ctx_r115 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r115.toggleView());\n    });\n    i0.ɵɵtemplate(26, StatusReportComponent_div_0_span_26_Template, 1, 0, \"span\", 21);\n    i0.ɵɵtemplate(27, StatusReportComponent_div_0_span_27_Template, 1, 0, \"span\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 23)(29, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function StatusReportComponent_div_0_Template_div_click_29_listener() {\n      i0.ɵɵrestoreView(_r110);\n\n      const _r4 = i0.ɵɵreference(7);\n\n      const ctx_r116 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r116.openAdvFiltersModal(_r4));\n    });\n    i0.ɵɵelement(30, \"div\", 25);\n    i0.ɵɵelementStart(31, \"span\", 26);\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 27)(35, \"span\", 28)(36, \"span\", 29);\n    i0.ɵɵtext(37, \"Manage \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \"Columns\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 30)(40, \"ng-select\", 31);\n    i0.ɵɵlistener(\"change\", function StatusReportComponent_div_0_Template_ng_select_change_40_listener($event) {\n      i0.ɵɵrestoreView(_r110);\n      const ctx_r117 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r117.onColumnsSelected($event));\n    });\n    i0.ɵɵtemplate(41, StatusReportComponent_div_0_ng_template_41_Template, 4, 4, \"ng-template\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(42, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function StatusReportComponent_div_0_Template_div_click_42_listener() {\n      i0.ɵɵrestoreView(_r110);\n      const ctx_r118 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r118.onSetColumnDefault());\n    });\n    i0.ɵɵelementStart(43, \"span\", 34);\n    i0.ɵɵtext(44);\n    i0.ɵɵpipe(45, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(46, \"span\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 36)(48, \"span\", 37)(49, \"span\", 38);\n    i0.ɵɵtext(50);\n    i0.ɵɵpipe(51, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"ng-select\", 39);\n    i0.ɵɵlistener(\"ngModelChange\", function StatusReportComponent_div_0_Template_ng_select_ngModelChange_54_listener($event) {\n      i0.ɵɵrestoreView(_r110);\n      const ctx_r119 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r119.selectedPageSize = $event);\n    })(\"change\", function StatusReportComponent_div_0_Template_ng_select_change_54_listener() {\n      i0.ɵɵrestoreView(_r110);\n      const ctx_r120 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r120.assignCount());\n    });\n    i0.ɵɵtemplate(55, StatusReportComponent_div_0_ng_option_55_Template, 2, 2, \"ng-option\", 40);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(56, \"div\", 41);\n    i0.ɵɵtemplate(57, StatusReportComponent_div_0_ng_container_57_Template, 9, 9, \"ng-container\", 0);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(58, StatusReportComponent_div_0_ng_template_58_Template, 10, 20, \"ng-template\", null, 42, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(60, StatusReportComponent_div_0_ng_container_60_Template, 3, 2, \"ng-container\", 43);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const _r12 = i0.ɵɵreference(59);\n\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.showLeftNav ? \"left-230\" : \"left-125\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.visibilityList);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.canShowPercentage);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.searchTerm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", i0.ɵɵpipeBind1(23, 27, \"LEADS.lead-search-prompt\"), \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.canExportAllUsers || ctx_r0.canExportReportees);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentView === \"graph\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentView === \"table\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(33, 29, \"PROPERTY.advanced-filters\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"items\", ctx_r0.columns)(\"multiple\", true)(\"searchable\", false)(\"closeOnSelect\", false)(\"ngModel\", ctx_r0.defaultColumns);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(45, 31, \"GLOBAL.default\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(51, 33, \"GLOBAL.show\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(53, 35, \"GLOBAL.entries\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"placeholder\", ctx_r0.pageSize)(\"ngModel\", ctx_r0.selectedPageSize)(\"searchable\", false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.showEntriesSize);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.showLeftNav ? \"w-100-190\" : \"w-100-90\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showFilters);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r0.rowData == null ? null : ctx_r0.rowData.length) || (ctx_r0.isCustomStatusEnabled ? ctx_r0.isCustomStatusReportLoading : ctx_r0.isStatusReportLoading))(\"ngIfElse\", _r12);\n  }\n}\n\nfunction StatusReportComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 130);\n    i0.ɵɵelement(1, \"application-loader\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nexport class StatusReportComponent {\n  constructor(gridOptionsService, _store, headerTitle, metaTitle, router, modalService, modalRef, shareDataService) {\n    this.gridOptionsService = gridOptionsService;\n    this._store = _store;\n    this.headerTitle = headerTitle;\n    this.metaTitle = metaTitle;\n    this.router = router;\n    this.modalService = modalService;\n    this.modalRef = modalRef;\n    this.shareDataService = shareDataService;\n    this.stopper = new EventEmitter();\n    this.searchTermSubject = new Subject();\n    this.leadSources = [];\n    this.columnDropDown = [];\n    this.showEntriesSize = SHOW_ENTRIES;\n    this.pageSize = PAGE_SIZE;\n    this.currOffset = 0;\n    this.currentView = 'table';\n    this.rowData = [];\n    this.filteredColumnDefsCache = [];\n    this.canExportAllUsers = false;\n    this.canViewAllUsers = false;\n    this.canViewReportees = false;\n    this.canExportReportees = false;\n    this.getPages = getPages;\n    this.dateTypeList = REPORTS_DATE_TYPE.slice(0, 4);\n    this.visibilityList = USER_VISIBILITY.slice(0, 3);\n    this.allUsers = [];\n    this.onlyReportees = [];\n    this.users = [];\n    this.reportees = [];\n    this.showLeftNav = true;\n    this.isStatusReportLoading = true;\n    this.isAllUsersLoading = true;\n    this.isOnlyReporteesLoading = true;\n    this.allSubSourceListIsLoading = true;\n    this.isProjectListLoading = true;\n    this.customStatusList = [];\n    this.isCustomStatusListLoading = true;\n    this.isSourcesLoading = true;\n    this.showFilters = false;\n    this.moment = moment;\n    this.reportFiltersKeyLabel = REPORT_FILTERS_KEY_LABEL;\n    this.citiesIsLoading = true;\n    this.statesIsLoading = true;\n    this.countryIsLoading = true;\n    this.isCustomStatusEnabled = false;\n    this.isGlobalSettingsLoading = true;\n    this.isCustomStatusReportLoading = true;\n    this.snakeToCamel = snakeToCamel;\n    this.currentDate = new Date();\n    this.toDate = new Date();\n    this.fromDate = new Date();\n    this.onPickerOpened = onPickerOpened;\n    this.s3BucketUrl = environment.s3ImageBucketURL;\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this.globalSettingsData = yield firstValueFrom(_this._store.select(getGlobalSettingsAnonymous).pipe(skipWhile(data => !Object.keys(data).length)));\n\n      _this.headerTitle.setTitle('Leads - Status Report');\n\n      _this.metaTitle.setTitle('CRM | Reports');\n\n      _this.gridOptions = _this.gridOptionsService.getGridSettings(_this);\n\n      _this._store.select(getUserBasicDetails).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        var _a, _b;\n\n        _this.userData = data;\n        _this.currentDate = changeCalendar((_b = (_a = _this.userData) === null || _a === void 0 ? void 0 : _a.timeZoneInfo) === null || _b === void 0 ? void 0 : _b.baseUTcOffset);\n      });\n\n      _this._store.select(getAllSources).pipe(takeUntil(_this.stopper)).subscribe(leadSource => {\n        if (leadSource) {\n          const enabledSources = leadSource.filter(source => source.isEnabled).sort((a, b) => a === null || a === void 0 ? void 0 : a.displayName.localeCompare(b === null || b === void 0 ? void 0 : b.displayName));\n          _this.leadSources = [...enabledSources];\n        } else {\n          _this.leadSources = [];\n        }\n\n        _this.updateSubSource();\n      });\n\n      _this._store.select(getAllSourcesLoading).pipe(takeUntil(_this.stopper)).subscribe(loading => {\n        _this.isSourcesLoading = loading;\n      });\n\n      yield _this._store.select(getGlobalAnonymousIsLoading).pipe(skipWhile(isLoading => {\n        return isLoading;\n      }), take(1)).toPromise();\n      _this.isCustomStatusEnabled = yield _this._store.select(getIsLeadCustomStatusEnabled).pipe(map(data => data), take(1)).toPromise();\n\n      _this._store.select(getUserFiltersPayload).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z;\n\n        _this.filtersPayload = Object.assign(Object.assign({}, data), {\n          isNavigatedFromReports: true\n        });\n        _this.pageSize = (_a = _this.filtersPayload) === null || _a === void 0 ? void 0 : _a.pageSize;\n        const userStatus = ((_b = _this.filtersPayload) === null || _b === void 0 ? void 0 : _b.userStatus) === undefined ? 1 : (_c = _this.filtersPayload) === null || _c === void 0 ? void 0 : _c.userStatus;\n        _this.appliedFilter = Object.assign(Object.assign({}, _this.appliedFilter), {\n          pageNumber: (_d = _this.filtersPayload) === null || _d === void 0 ? void 0 : _d.pageNumber,\n          pageSize: (_e = _this.filtersPayload) === null || _e === void 0 ? void 0 : _e.pageSize,\n          userStatus: userStatus,\n          visibility: (_f = _this.filtersPayload) === null || _f === void 0 ? void 0 : _f.userStatus,\n          dateType: ReportDateType[Number((_g = _this.filtersPayload) === null || _g === void 0 ? void 0 : _g.dateType)],\n          date: [patchTimeZoneDate((_h = _this.filtersPayload) === null || _h === void 0 ? void 0 : _h.fromDate, (_k = (_j = _this.userData) === null || _j === void 0 ? void 0 : _j.timeZoneInfo) === null || _k === void 0 ? void 0 : _k.baseUTcOffset), patchTimeZoneDate((_l = _this.filtersPayload) === null || _l === void 0 ? void 0 : _l.toDate, (_o = (_m = _this.userData) === null || _m === void 0 ? void 0 : _m.timeZoneInfo) === null || _o === void 0 ? void 0 : _o.baseUTcOffset)],\n          withTeam: (_p = _this.filtersPayload) === null || _p === void 0 ? void 0 : _p.IsWithTeam,\n          users: (_q = _this.filtersPayload) === null || _q === void 0 ? void 0 : _q.UserIds,\n          search: (_r = _this.filtersPayload) === null || _r === void 0 ? void 0 : _r.SearchText,\n          sources: (_s = _this.filtersPayload) === null || _s === void 0 ? void 0 : _s.Sources,\n          subSources: (_t = _this.filtersPayload) === null || _t === void 0 ? void 0 : _t.SubSources,\n          projects: (_u = _this.filtersPayload) === null || _u === void 0 ? void 0 : _u.Projects,\n          cities: (_v = _this.filtersPayload) === null || _v === void 0 ? void 0 : _v.Cities,\n          states: (_w = _this.filtersPayload) === null || _w === void 0 ? void 0 : _w.States,\n          ShouldShowAll: (_y = (_x = _this.filtersPayload) === null || _x === void 0 ? void 0 : _x.ShouldShowAll) !== null && _y !== void 0 ? _y : true,\n          shouldShowPercentage: (_z = _this.filtersPayload) === null || _z === void 0 ? void 0 : _z.ShouldShowPercentage\n        });\n      });\n\n      _this._store.select(getUsersListForReassignment).pipe(takeUntil(_this.stopper), switchMap(data => {\n        const usersData = data === null || data === void 0 ? void 0 : data.map(user => {\n          user = Object.assign(Object.assign({}, user), {\n            fullName: user.firstName + ' ' + user.lastName\n          });\n          return user;\n        });\n        _this.users = usersData;\n        _this.allUsers = usersData;\n        _this.allUsers = assignToSort(_this.allUsers, '');\n        return _this._store.select(getUsersListForReassignmentIsLoading).pipe(takeUntil(_this.stopper));\n      })).subscribe(isLoading => {\n        _this.isAllUsersLoading = isLoading;\n\n        if (!isLoading) {\n          _this.currentVisibility(1, false);\n        }\n      });\n\n      _this._store.select(getOnlyReporteesWithInactive).pipe(takeUntil(_this.stopper), switchMap(data => {\n        const usersData = data === null || data === void 0 ? void 0 : data.map(user => {\n          user = Object.assign(Object.assign({}, user), {\n            fullName: user.firstName + ' ' + user.lastName\n          });\n          return user;\n        });\n        _this.reportees = usersData;\n        _this.onlyReportees = usersData;\n        _this.onlyReportees = assignToSort(_this.onlyReportees, '');\n        return _this._store.select(getOnlyReporteesWithInactiveIsLoading).pipe(takeUntil(_this.stopper));\n      })).subscribe(isLoading => {\n        _this.isOnlyReporteesLoading = isLoading;\n\n        if (!isLoading) {\n          _this.currentVisibility(1, false);\n        }\n      });\n\n      _this._store.select(getProjectList).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        _this.projectList = data.slice().sort((a, b) => a.localeCompare(b));\n      });\n\n      _this._store.select(getProjectListIsLoading).pipe(takeUntil(_this.stopper)).subscribe(isLoading => {\n        _this.isProjectListLoading = isLoading;\n      });\n\n      _this._store.select(getReportsUsersListIsLoading).pipe(takeUntil(_this.stopper)).subscribe(isLoading => {\n        _this.isStatusReportLoading = isLoading;\n      });\n\n      _this._store.select(getReportsCustomUsersListIsLoading).pipe(takeUntil(_this.stopper)).subscribe(isLoading => {\n        _this.isCustomStatusReportLoading = isLoading;\n      });\n\n      _this._store.select(getPermissions).pipe(takeUntil(_this.stopper)).subscribe(permissions => {\n        if (!(permissions === null || permissions === void 0 ? void 0 : permissions.length)) return;\n        const permissionsSet = new Set(permissions);\n        _this.canExportAllUsers = permissionsSet.has('Permissions.Reports.ExportAllUsers');\n        _this.canViewAllUsers = permissionsSet.has('Permissions.Reports.ViewAllUsers');\n        _this.canExportReportees = permissionsSet.has('Permissions.Reports.ExportReportees');\n        _this.canViewReportees = permissionsSet.has('Permissions.Reports.ViewReportees');\n\n        if (_this.canViewAllUsers) {\n          _this._store.dispatch(new FetchUsersListForReassignment());\n        } else if (_this.canViewReportees) {\n          _this._store.dispatch(new FetchOnlyReporteesWithInactive());\n        }\n      });\n\n      _this._store.select(getSubSourceList).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        _this.allSubSourceList = data;\n        _this.subSourceList = Object.values(data).flat().filter(data => data).slice().sort((a, b) => a.localeCompare(b));\n\n        _this.updateSubSource();\n      });\n\n      _this._store.select(getSubSourceListIsLoading).pipe(takeUntil(_this.stopper)).subscribe(isLoading => {\n        _this.allSubSourceListIsLoading = isLoading;\n      });\n\n      _this.shareDataService.showLeftNav$.subscribe(show => {\n        _this.showLeftNav = show;\n      });\n\n      _this.initializeGridSettings();\n\n      _this.initializeGraphData();\n\n      if (_this.isCustomStatusEnabled) {\n        _this._store.select(getReportsCustomUsersList).pipe(takeUntil(_this.stopper)).subscribe(data => {\n          var _a, _b;\n\n          _this.rowData = data.map(row => {\n            var _a;\n\n            let statuses = {};\n            (_a = row === null || row === void 0 ? void 0 : row.status) === null || _a === void 0 ? void 0 : _a.forEach(status => {\n              statuses[status === null || status === void 0 ? void 0 : status.statusDisplayName] = (status === null || status === void 0 ? void 0 : status.count) || 0;\n              statuses[(status === null || status === void 0 ? void 0 : status.statusDisplayName) + '__percentage__'] = `${status === null || status === void 0 ? void 0 : status.percentage}` || '';\n            });\n            return Object.assign(Object.assign({}, row), {\n              statuses\n            });\n          });\n          let totalRow = {\n            userName: 'Total',\n            projectTitle: 'Total',\n            statuses: {}\n          };\n\n          _this.rowData.forEach(row => {\n            var _a, _b;\n\n            for (let key in row === null || row === void 0 ? void 0 : row.statuses) {\n              if (!((_a = totalRow === null || totalRow === void 0 ? void 0 : totalRow.statuses) === null || _a === void 0 ? void 0 : _a[key])) {\n                totalRow.statuses[key] = 0;\n              }\n\n              if (!key.includes('__percentage__')) totalRow.statuses[key] += ((_b = row === null || row === void 0 ? void 0 : row.statuses) === null || _b === void 0 ? void 0 : _b[key]) || 0;\n            }\n          });\n\n          if (((_a = _this.rowData) === null || _a === void 0 ? void 0 : _a.length) > 1) {\n            (_b = _this.rowData) === null || _b === void 0 ? void 0 : _b.push(totalRow);\n          }\n        });\n\n        _this._store.select(getReportsCustomUsersListTotalCount).pipe(takeUntil(_this.stopper)).subscribe(data => {\n          _this.userTotalCount = data;\n        });\n      } else _this._store.select(getReportsUsersList).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        _this.rowData = getTotalCountForReports(data.items);\n        _this.userTotalCount = data.totalCount;\n      });\n\n      if (_this.isCustomStatusEnabled) _this.fetchCustomStatuses();\n      _this.selectedPageSize = 50;\n\n      _this.searchTermSubject.subscribe(() => {\n        _this.appliedFilter.pageNumber = 1;\n\n        _this.filterFunction();\n      });\n\n      _this._store.select(getLeadCities).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        _this.cities = data.filter(data => data).slice().sort((a, b) => a.localeCompare(b));\n      });\n\n      _this._store.select(getLeadCitiesIsLoading).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        _this.citiesIsLoading = data;\n      });\n\n      _this._store.select(getLeadStates).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        _this.states = data.filter(data => data).slice().sort((a, b) => a.localeCompare(b));\n      });\n\n      _this._store.select(getLeadStatesIsLoading).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        _this.statesIsLoading = data;\n      });\n\n      _this._store.select(getLeadCountries).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        _this.countryList = data.filter(data => data).slice().sort((a, b) => a.localeCompare(b));\n      });\n\n      _this._store.select(getLeadCountriesIsLoading).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        _this.countryIsLoading = data;\n      });\n    })();\n  }\n\n  fetchCustomStatuses() {\n    this._store.select(getCustomStatusList).pipe(takeUntil(this.stopper)).subscribe(customStatus => {\n      this.customStatusList = customStatus;\n    });\n\n    this._store.select(getCustomStatusListIsLoading).pipe(takeUntil(this.stopper)).subscribe(isLoading => {\n      this.isCustomStatusListLoading = isLoading;\n      if (!isLoading) this.initializeGridSettings();\n      this.initializeGraphData();\n    });\n  }\n\n  initializeGraphData() {\n    var _a, _b;\n\n    this.filteredColumnDefsCache = (_b = (_a = this.gridOptions) === null || _a === void 0 ? void 0 : _a.columnDefs) === null || _b === void 0 ? void 0 : _b.filter(col => col.field !== 'User Name' && col.field !== 'General Manager' && col.field != 'Reporting Manager');\n  }\n\n  initializeGridSettings() {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n\n    this.gridOptions = this.gridOptionsService.getGridSettings(this);\n    const nameAndLeads = [{\n      headerName: 'User Name',\n      field: 'User Name',\n      pinned: window.innerWidth > 480 ? 'left' : null,\n      lockPinned: true,\n      cellClass: 'lock-pinned',\n      valueGetter: params => {\n        var _a;\n\n        return [(_a = params.data) === null || _a === void 0 ? void 0 : _a.userName];\n      },\n      minWidth: 180,\n      cellRenderer: params => {\n        return `<div class=\"py-16 align-center text-truncate\"><p>${params.value[0]}\n            </p></div>`;\n      }\n    }, {\n      headerName: 'General Manager',\n      field: 'General Manager',\n      hide: false,\n      cellClass: 'lock-pinned',\n      valueGetter: params => {\n        var _a;\n\n        return [(_a = params.data) === null || _a === void 0 ? void 0 : _a.generalManager];\n      },\n      minWidth: 180,\n      cellRenderer: params => {\n        return `<div class=\"py-16 align-center text-truncate\"><p>${params.value[0] || ''}\n            </p></div>`;\n      }\n    }, {\n      headerName: 'Reporting Manager',\n      field: 'Reporting Manager',\n      hide: false,\n      cellClass: 'lock-pinned',\n      valueGetter: params => {\n        var _a;\n\n        return [(_a = params.data) === null || _a === void 0 ? void 0 : _a.reportingManager];\n      },\n      minWidth: 180,\n      cellRenderer: params => {\n        return `<div class=\"py-16 align-center text-truncate\"><p>${params.value[0] || ''}\n            </p></div>`;\n      }\n    }, {\n      headerName: 'All Leads',\n      field: 'All Leads',\n      filter: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d, _e, _f, _g, _h;\n\n        return [this.isCustomStatusEnabled ? (_b = (_a = params === null || params === void 0 ? void 0 : params.data) === null || _a === void 0 ? void 0 : _a.statuses) === null || _b === void 0 ? void 0 : _b.AllCount : (_c = params.data) === null || _c === void 0 ? void 0 : _c.allCount, this.isCustomStatusEnabled ? (_e = (_d = params === null || params === void 0 ? void 0 : params.data) === null || _d === void 0 ? void 0 : _d.statuses) === null || _e === void 0 ? void 0 : _e.ActiveCount : (_f = params.data) === null || _f === void 0 ? void 0 : _f.activeCount, (_g = params === null || params === void 0 ? void 0 : params.data) === null || _g === void 0 ? void 0 : _g.userId, (_h = params === null || params === void 0 ? void 0 : params.data) === null || _h === void 0 ? void 0 : _h.projectTitle];\n      },\n      valueLabels: ['All Leads', 'Active Leads'],\n      minWidth: 120,\n      cellRenderer: params => {\n        var _a, _b, _c, _d;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return !this.isCustomStatusEnabled ? `${((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[3]) == 'Total' || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`}\n          <p class=\"text-truncate\"><span class=\"text-dark-gray\">active: </span>\n          <span class=\"fw-600\">${params.value[1] && ((_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[3]) != 'Total' ? `<a>${params.value[1]}</a>` : params.value[1] ? params.value[1] : '--'}<span>\n          </p>` : `${((_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[3]) == 'Total' || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` : `<p><span >${params.value[0] ? params.value[0] : '--'}</span></p>`}\n          <p class=\"text-truncate\"><span class=\"text-dark-gray\">active: </span>\n          <span class=\"fw-600\">${params.value[1] && ((_d = params === null || params === void 0 ? void 0 : params.value) === null || _d === void 0 ? void 0 : _d[3]) != 'Total' ? `<span>${params.value[1]}</span>` : params.value[1] ? params.value[1] : '--'}<span>\n          </p>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n\n        if (event.data.projectTitle == 'Total') {\n          return;\n        } else if (event.event.target.innerText == event.value[0]) {\n          if (isCtrlClick) {\n            this.getDataInNewTab('All Leads', params);\n            return;\n          }\n\n          this.getDataFromCell('All Leads', event);\n        } else if (event.event.target.innerText == event.value[1]) {\n          if (isCtrlClick) {\n            this.getDataInNewTab('Active Leads', params);\n            return;\n          }\n\n          this.getDataFromCell('Active Leads', event);\n        }\n      }\n    }];\n    const newAndPending = [{\n      headerName: 'New',\n      field: 'New',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d;\n\n        return [(_a = params.data) === null || _a === void 0 ? void 0 : _a.newCount, (_b = params === null || params === void 0 ? void 0 : params.data) === null || _b === void 0 ? void 0 : _b.userId, (_c = params === null || params === void 0 ? void 0 : params.data) === null || _c === void 0 ? void 0 : _c.projectTitle, this.canShowPercentage ? (_d = params.data) === null || _d === void 0 ? void 0 : _d.newCountPercentage : ''];\n      },\n      minWidth: 70,\n      cellRenderer: params => {\n        var _a, _b, _c, _d, _e;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return ((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[2]) == 'Total' || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` : !this.isCustomStatusEnabled ? `<p><a>${params.value[0] ? params.value[0] + (((_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[3]) ? ` (${(_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[3]})` : '') : '--'}</a></p>` : `<p><span>${params.value[0] ? params.value[0] + (((_d = params === null || params === void 0 ? void 0 : params.value) === null || _d === void 0 ? void 0 : _d[3]) ? ` (${(_e = params === null || params === void 0 ? void 0 : params.value) === null || _e === void 0 ? void 0 : _e[3]})` : '') : '--'}</span></p>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total') {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            this.getDataInNewTab('New', params);\n            return;\n          }\n\n          this.getDataFromCell('New', event);\n        }\n      }\n    }, {\n      headerName: 'Pending',\n      field: 'Pending',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d;\n\n        return [(_a = params.data) === null || _a === void 0 ? void 0 : _a.pendingCount, (_b = params === null || params === void 0 ? void 0 : params.data) === null || _b === void 0 ? void 0 : _b.userId, (_c = params === null || params === void 0 ? void 0 : params.data) === null || _c === void 0 ? void 0 : _c.projectTitle, this.canShowPercentage ? (_d = params.data) === null || _d === void 0 ? void 0 : _d.pendingCountPercentage : ''];\n      },\n      minWidth: 80,\n      cellRenderer: params => {\n        var _a, _b, _c, _d, _e;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return ((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[2]) == 'Total' || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` : !this.isCustomStatusEnabled ? `<p><a>${params.value[0] ? params.value[0] + (((_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[3]) ? ` (${(_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[3]})` : '') : '--'}</a></p>` : `<p><span>${params.value[0] ? params.value[0] + (((_d = params === null || params === void 0 ? void 0 : params.value) === null || _d === void 0 ? void 0 : _d[3]) ? ` (${(_e = params === null || params === void 0 ? void 0 : params.value) === null || _e === void 0 ? void 0 : _e[3]})` : '') : '--'}</span></p>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total') {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            this.getDataInNewTab('Pending', params);\n            return;\n          }\n\n          this.getDataFromCell('Pending', event);\n        }\n      }\n    }];\n    const overdue = [{\n      headerName: 'Overdue',\n      field: 'Overdue',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d, _e, _f, _g, _h;\n\n        return [this.isCustomStatusEnabled ? (_b = (_a = params === null || params === void 0 ? void 0 : params.data) === null || _a === void 0 ? void 0 : _a.statuses) === null || _b === void 0 ? void 0 : _b.OverdueCount : (_c = params.data) === null || _c === void 0 ? void 0 : _c.overdueCount, (_d = params === null || params === void 0 ? void 0 : params.data) === null || _d === void 0 ? void 0 : _d.userId, (_e = params === null || params === void 0 ? void 0 : params.data) === null || _e === void 0 ? void 0 : _e.projectTitle, this.canShowPercentage ? this.isCustomStatusEnabled ? (_g = (_f = params === null || params === void 0 ? void 0 : params.data) === null || _f === void 0 ? void 0 : _f.statuses) === null || _g === void 0 ? void 0 : _g.OverdueCount__percentage__ : (_h = params.data) === null || _h === void 0 ? void 0 : _h.overdueCount__percentage__ : ''];\n      },\n      minWidth: 110,\n      cellRenderer: params => {\n        var _a, _b, _c, _d, _e;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return ((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[2]) == 'Total' || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` : !this.isCustomStatusEnabled ? `<p><a>${params.value[0] ? params.value[0] + (((_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[3]) ? ` (${(_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[3]})` : '') : '--'}</a></p>` : `<p><span>${params.value[0] ? params.value[0] + (((_d = params === null || params === void 0 ? void 0 : params.value) === null || _d === void 0 ? void 0 : _d[3]) ? ` (${(_e = params === null || params === void 0 ? void 0 : params.value) === null || _e === void 0 ? void 0 : _e[3]})` : '') : '--'}</span></p>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total') {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            this.getDataInNewTab('Overdue', params);\n            return;\n          }\n\n          this.getDataFromCell('Overdue', event);\n        }\n      }\n    }];\n    const callbackAndMS = [{\n      headerName: 'Callback',\n      field: 'Callback',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d;\n\n        return [(_a = params.data) === null || _a === void 0 ? void 0 : _a.callbackCount, (_b = params === null || params === void 0 ? void 0 : params.data) === null || _b === void 0 ? void 0 : _b.userId, (_c = params === null || params === void 0 ? void 0 : params.data) === null || _c === void 0 ? void 0 : _c.projectTitle, this.canShowPercentage ? (_d = params.data) === null || _d === void 0 ? void 0 : _d.callbackCountPercentage : ''];\n      },\n      minWidth: 110,\n      cellRenderer: params => {\n        var _a;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return ((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[2]) == 'Total' || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` : !this.isCustomStatusEnabled ? `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>` : `<p><span>${params.value[0] ? params.value[0] : '--'}</span></p>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total') {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            this.getDataInNewTab('Callback', params);\n            return;\n          }\n\n          this.getDataFromCell('Callback', event);\n        }\n      }\n    }, {\n      headerName: 'Meeting Scheduled',\n      field: 'Meeting Scheduled',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d;\n\n        return [(_a = params.data) === null || _a === void 0 ? void 0 : _a.meetingScheduledCount, (_b = params === null || params === void 0 ? void 0 : params.data) === null || _b === void 0 ? void 0 : _b.userId, (_c = params === null || params === void 0 ? void 0 : params.data) === null || _c === void 0 ? void 0 : _c.projectTitle, this.canShowPercentage ? (_d = params.data) === null || _d === void 0 ? void 0 : _d.meetingScheduledCountPercentage : ''];\n      },\n      minWidth: 140,\n      cellRenderer: params => {\n        var _a, _b, _c, _d;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return !this.isCustomStatusEnabled ? `<a ><p>${params.value[0] ? params.value[0] + (((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[3]) ? ` (${(_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[3]})` : '') : '--'}</p></a>` : `<span><p>${params.value[0] ? params.value[0] + (((_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[3]) ? ` (${(_d = params === null || params === void 0 ? void 0 : params.value) === null || _d === void 0 ? void 0 : _d[3]})` : '') : '--'}</p></span>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total') {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            this.getDataInNewTab('Meeting Scheduled', params);\n            return;\n          }\n\n          this.getDataFromCell('Meeting Scheduled', event);\n        }\n      }\n    }];\n    const meetingDoneAndNotDone = [{\n      headerName: 'Meeting Done',\n      field: 'Meeting Done',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;\n\n        return [this.isCustomStatusEnabled ? (_b = (_a = params === null || params === void 0 ? void 0 : params.data) === null || _a === void 0 ? void 0 : _a.statuses) === null || _b === void 0 ? void 0 : _b.MeetingDoneCount : (_c = params.data) === null || _c === void 0 ? void 0 : _c.meetingDoneCount, this.isCustomStatusEnabled ? (_e = (_d = params === null || params === void 0 ? void 0 : params.data) === null || _d === void 0 ? void 0 : _d.statuses) === null || _e === void 0 ? void 0 : _e.MeetingDoneUniqueCount : (_f = params.data) === null || _f === void 0 ? void 0 : _f.meetingDoneUniqueCount, (_g = params === null || params === void 0 ? void 0 : params.data) === null || _g === void 0 ? void 0 : _g.userId, (_h = params === null || params === void 0 ? void 0 : params.data) === null || _h === void 0 ? void 0 : _h.projectTitle, this.canShowPercentage ? this.isCustomStatusEnabled ? (_k = (_j = params === null || params === void 0 ? void 0 : params.data) === null || _j === void 0 ? void 0 : _j.statuses) === null || _k === void 0 ? void 0 : _k.MeetingDoneUniqueCount__percentage__ : (_l = params.data) === null || _l === void 0 ? void 0 : _l.meetingDoneUniqueCount__percentage__ : ''];\n      },\n      minWidth: 120,\n      valueLabels: ['Meeting Done', 'Meeting Done (Unquie Count)'],\n      cellRenderer: params => {\n        var _a, _b, _c, _d;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return !this.isCustomStatusEnabled ? `<a ><p>${params.value[0] ? params.value[0] : '--'}</p>\n          <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1] ? params.value[1] + (((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[4]) ? ` (${(_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[4]})` : '') : '--'}<span></p></a>` : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>\n            <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1] ? params.value[1] + (((_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[4]) ? ` (${(_d = params === null || params === void 0 ? void 0 : params.value) === null || _d === void 0 ? void 0 : _d[4]})` : '') : '--'}<span></p></span>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total') {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            this.getMeetingCountnewTab('All Leads', params, 'Meeting Done');\n            return;\n          }\n\n          this.getMeetingCount('All Leads', event, 'Meeting Done');\n        }\n      }\n    }, {\n      headerName: 'Meeting Not Done',\n      field: 'Meeting Not Done',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;\n\n        return [this.isCustomStatusEnabled ? (_b = (_a = params === null || params === void 0 ? void 0 : params.data) === null || _a === void 0 ? void 0 : _a.statuses) === null || _b === void 0 ? void 0 : _b.MeetingNotDoneCount : (_c = params.data) === null || _c === void 0 ? void 0 : _c.meetingNotDoneCount, this.isCustomStatusEnabled ? (_e = (_d = params === null || params === void 0 ? void 0 : params.data) === null || _d === void 0 ? void 0 : _d.statuses) === null || _e === void 0 ? void 0 : _e.MeetingNotDoneUniqueCount : (_f = params.data) === null || _f === void 0 ? void 0 : _f.meetingNotDoneUniqueCount, (_g = params === null || params === void 0 ? void 0 : params.data) === null || _g === void 0 ? void 0 : _g.userId, (_h = params === null || params === void 0 ? void 0 : params.data) === null || _h === void 0 ? void 0 : _h.projectTitle, this.canShowPercentage ? this.isCustomStatusEnabled ? (_k = (_j = params === null || params === void 0 ? void 0 : params.data) === null || _j === void 0 ? void 0 : _j.statuses) === null || _k === void 0 ? void 0 : _k.MeetingNotDoneUniqueCount__percentage__ : (_l = params.data) === null || _l === void 0 ? void 0 : _l.meetingNotDoneUniqueCount__percentage__ : ''];\n      },\n      minWidth: 150,\n      valueLabels: ['Meeting Not Done', 'Meeting Not Done (Unquie Count)'],\n      cellRenderer: params => {\n        var _a, _b, _c, _d;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return !this.isCustomStatusEnabled ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>\n          <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1] ? params.value[1] + (((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[4]) ? ` (${(_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[4]})` : '') : '--'}<span></p></a>` : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>\n            <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1] ? params.value[1] + (((_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[4]) ? ` (${(_d = params === null || params === void 0 ? void 0 : params.value) === null || _d === void 0 ? void 0 : _d[4]})` : '') : '--'}<span></p></span>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total') {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            this.getMeetingCountnewTab('All Leads', params, 'Meeting Not Done');\n            return;\n          }\n\n          this.getMeetingCount('All Leads', event, 'Meeting Not Done');\n        }\n      }\n    }];\n    const svs = [{\n      headerName: ((_a = this.globalSettingsData) === null || _a === void 0 ? void 0 : _a.shouldRenameSiteVisitColumn) ? 'Referral Scheduled' : 'Site Visit Scheduled',\n      field: ((_b = this.globalSettingsData) === null || _b === void 0 ? void 0 : _b.shouldRenameSiteVisitColumn) ? 'Referral Scheduled' : 'Site Visit Scheduled',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d;\n\n        return [(_a = params.data) === null || _a === void 0 ? void 0 : _a.siteVisitScheduledCount, (_b = params === null || params === void 0 ? void 0 : params.data) === null || _b === void 0 ? void 0 : _b.userId, (_c = params === null || params === void 0 ? void 0 : params.data) === null || _c === void 0 ? void 0 : _c.projectTitle, this.canShowPercentage ? (_d = params.data) === null || _d === void 0 ? void 0 : _d.siteVisitScheduledCountPercentage : ''];\n      },\n      valueLabels: [((_c = this.globalSettingsData) === null || _c === void 0 ? void 0 : _c.shouldRenameSiteVisitColumn) ? 'Referral Scheduled' : 'Site Visit Scheduled', (((_d = this.globalSettingsData) === null || _d === void 0 ? void 0 : _d.shouldRenameSiteVisitColumn) ? 'Referral Scheduled' : 'Site Visit Scheduled') + ' (unique count)'],\n      minWidth: 150,\n      cellRenderer: params => {\n        var _a, _b, _c, _d;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return !this.isCustomStatusEnabled ? `<a ><p>${params.value[0] ? params.value[0] + (((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[3]) ? ` (${(_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[3]})` : '') : '--'}</p></a>` : `<span><p>${params.value[0] ? params.value[0] + (((_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[3]) ? ` (${(_d = params === null || params === void 0 ? void 0 : params.value) === null || _d === void 0 ? void 0 : _d[3]})` : '') : '--'}</p></span>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a, _b, _c;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total') {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            this.getDataInNewTab(!((_b = this.globalSettingsData) === null || _b === void 0 ? void 0 : _b.shouldRenameSiteVisitColumn) ? 'Site Visit Scheduled' : 'Referral Scheduled', params);\n            return;\n          }\n\n          this.getDataFromCell(!((_c = this.globalSettingsData) === null || _c === void 0 ? void 0 : _c.shouldRenameSiteVisitColumn) ? 'Site Visit Scheduled' : 'Referral Scheduled', event);\n        }\n      }\n    }];\n    const siteVisitDoneAndNotDone = [{\n      headerName: !((_e = this.globalSettingsData) === null || _e === void 0 ? void 0 : _e.shouldRenameSiteVisitColumn) ? 'Site Visit Done' : 'Referral Taken',\n      field: !((_f = this.globalSettingsData) === null || _f === void 0 ? void 0 : _f.shouldRenameSiteVisitColumn) ? 'Site Visit Done' : 'Referral Taken',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;\n\n        return [this.isCustomStatusEnabled ? (_b = (_a = params === null || params === void 0 ? void 0 : params.data) === null || _a === void 0 ? void 0 : _a.statuses) === null || _b === void 0 ? void 0 : _b.SiteVisitDoneCount : (_c = params.data) === null || _c === void 0 ? void 0 : _c.siteVisitDoneCount, this.isCustomStatusEnabled ? (_e = (_d = params === null || params === void 0 ? void 0 : params.data) === null || _d === void 0 ? void 0 : _d.statuses) === null || _e === void 0 ? void 0 : _e.SiteVisitDoneUniqueCount : (_f = params.data) === null || _f === void 0 ? void 0 : _f.siteVisitDoneUniqueCount, (_g = params === null || params === void 0 ? void 0 : params.data) === null || _g === void 0 ? void 0 : _g.userId, (_h = params === null || params === void 0 ? void 0 : params.data) === null || _h === void 0 ? void 0 : _h.projectTitle, this.canShowPercentage ? this.isCustomStatusEnabled ? (_k = (_j = params === null || params === void 0 ? void 0 : params.data) === null || _j === void 0 ? void 0 : _j.statuses) === null || _k === void 0 ? void 0 : _k.SiteVisitDoneUniqueCount__percentage__ : (_l = params.data) === null || _l === void 0 ? void 0 : _l.siteVisitDoneUniqueCount__percentage__ : ''];\n      },\n      minWidth: 120,\n      valueLabels: [!((_g = this.globalSettingsData) === null || _g === void 0 ? void 0 : _g.shouldRenameSiteVisitColumn) ? 'Site Visit Done' : 'Referral Taken', (!((_h = this.globalSettingsData) === null || _h === void 0 ? void 0 : _h.shouldRenameSiteVisitColumn) ? 'Site Visit Done' : 'Referral Taken') + ' (unique count)'],\n      cellRenderer: params => {\n        var _a, _b, _c, _d;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return !this.isCustomStatusEnabled ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>\n          <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1] ? params.value[1] + (((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[4]) ? ` (${(_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[4]})` : '') : '--'}<span></p></a>` : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>\n            <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1] ? params.value[1] + (((_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[4]) ? ` (${(_d = params === null || params === void 0 ? void 0 : params.value) === null || _d === void 0 ? void 0 : _d[4]})` : '') : '--'}<span></p></span>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total') {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            this.getMeetingCountnewTab('All Leads', params, 'Site Visit Done');\n            return;\n          }\n\n          this.getMeetingCount('All Leads', event, 'Site Visit Done');\n        }\n      }\n    }, {\n      headerName: !((_j = this.globalSettingsData) === null || _j === void 0 ? void 0 : _j.shouldRenameSiteVisitColumn) ? 'Site Visit Not Done' : 'Referral Not Taken',\n      field: !((_k = this.globalSettingsData) === null || _k === void 0 ? void 0 : _k.shouldRenameSiteVisitColumn) ? 'Site Visit Not Done' : 'Referral Not Taken',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;\n\n        return [this.isCustomStatusEnabled ? (_b = (_a = params === null || params === void 0 ? void 0 : params.data) === null || _a === void 0 ? void 0 : _a.statuses) === null || _b === void 0 ? void 0 : _b.SiteVisitNotDoneCount : (_c = params.data) === null || _c === void 0 ? void 0 : _c.siteVisitNotDoneCount, this.isCustomStatusEnabled ? (_e = (_d = params === null || params === void 0 ? void 0 : params.data) === null || _d === void 0 ? void 0 : _d.statuses) === null || _e === void 0 ? void 0 : _e.SiteVisitNotDoneUniqueCount : (_f = params.data) === null || _f === void 0 ? void 0 : _f.siteVisitNotDoneUniqueCount, (_g = params === null || params === void 0 ? void 0 : params.data) === null || _g === void 0 ? void 0 : _g.userId, (_h = params === null || params === void 0 ? void 0 : params.data) === null || _h === void 0 ? void 0 : _h.projectTitle, this.canShowPercentage ? this.isCustomStatusEnabled ? (_k = (_j = params === null || params === void 0 ? void 0 : params.data) === null || _j === void 0 ? void 0 : _j.statuses) === null || _k === void 0 ? void 0 : _k.SiteVisitNotDoneUniqueCount__percentage__ : (_l = params.data) === null || _l === void 0 ? void 0 : _l.siteVisitNotDoneUniqueCount__percentage__ : ''];\n      },\n      valueLabels: [!((_l = this.globalSettingsData) === null || _l === void 0 ? void 0 : _l.shouldRenameSiteVisitColumn) ? 'Site Visit Not Done' : 'Referral Not Taken', (!((_m = this.globalSettingsData) === null || _m === void 0 ? void 0 : _m.shouldRenameSiteVisitColumn) ? 'Site Visit Not Done' : 'Referral Not Taken') + ' (unique count)'],\n      minWidth: 150,\n      cellRenderer: params => {\n        var _a, _b, _c, _d;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return !this.isCustomStatusEnabled ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>\n          <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1] ? params.value[1] + (((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[4]) ? ` (${(_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[4]})` : '') : '--'}<span></p></a>` : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>\n            <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1] ? params.value[1] + (((_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[4]) ? ` (${(_d = params === null || params === void 0 ? void 0 : params.value) === null || _d === void 0 ? void 0 : _d[4]})` : '') : '--'}<span></p></span>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total') {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            this.getMeetingCountnewTab('All Leads', params, 'Site Visit Not Done');\n            return;\n          }\n\n          this.getMeetingCount('All Leads', event, 'Site Visit Not Done');\n        }\n      }\n    }];\n    const others = [{\n      headerName: 'Booked',\n      field: 'Booked',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d;\n\n        return [(_a = params.data) === null || _a === void 0 ? void 0 : _a.bookedCount, (_b = params === null || params === void 0 ? void 0 : params.data) === null || _b === void 0 ? void 0 : _b.userId, (_c = params === null || params === void 0 ? void 0 : params.data) === null || _c === void 0 ? void 0 : _c.projectTitle, this.canShowPercentage ? (_d = params.data) === null || _d === void 0 ? void 0 : _d.bookedCountPercentage : ''];\n      },\n      minWidth: 110,\n      cellRenderer: params => {\n        var _a, _b, _c, _d, _e;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return ((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[2]) == 'Total' || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` : !this.isCustomStatusEnabled ? `<p><a>${params.value[0] ? params.value[0] + (((_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[3]) ? ` (${(_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[3]})` : '') : '--'}</a></p>` : `<p><span>${params.value[0] ? params.value[0] + (((_d = params === null || params === void 0 ? void 0 : params.value) === null || _d === void 0 ? void 0 : _d[3]) ? ` (${(_e = params === null || params === void 0 ? void 0 : params.value) === null || _e === void 0 ? void 0 : _e[3]})` : '') : '--'}</span></p>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total' || event.value[0] == 0) {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            this.getDataInNewTab('Booked', params);\n            return;\n          }\n\n          this.getDataFromCell('Booked', event);\n        }\n      }\n    }, {\n      headerName: 'Invoiced',\n      field: 'Invoiced',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d;\n\n        return [(_a = params.data) === null || _a === void 0 ? void 0 : _a.invoicedLeadsCount, (_b = params === null || params === void 0 ? void 0 : params.data) === null || _b === void 0 ? void 0 : _b.userId, (_c = params === null || params === void 0 ? void 0 : params.data) === null || _c === void 0 ? void 0 : _c.projectTitle, this.canShowPercentage ? (_d = params.data) === null || _d === void 0 ? void 0 : _d.invoicedLeadsCountPercentage : ''];\n      },\n      minWidth: 110,\n      cellRenderer: params => {\n        var _a, _b, _c;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return ((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[2]) == 'Total' || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` : `<p><a>${params.value[0] ? params.value[0] + (((_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[3]) ? ` (${(_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[3]})` : '') : '--'}</a></p>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total' || event.value[0] == 0) {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            this.getDataInNewTab('Invoiced', params);\n            return;\n          }\n\n          this.getDataFromCell('Invoiced', event);\n        }\n      }\n    }, {\n      headerName: 'Booking Cancel',\n      field: 'Booking Cancel',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d;\n\n        return [(_a = params.data) === null || _a === void 0 ? void 0 : _a.bookingCancelCount, (_b = params === null || params === void 0 ? void 0 : params.data) === null || _b === void 0 ? void 0 : _b.userId, (_c = params === null || params === void 0 ? void 0 : params.data) === null || _c === void 0 ? void 0 : _c.projectTitle, this.canShowPercentage ? (_d = params === null || params === void 0 ? void 0 : params.data) === null || _d === void 0 ? void 0 : _d.bookingCancelCountPercentage : ''];\n      },\n      minWidth: 120,\n      cellRenderer: params => {\n        var _a, _b, _c, _d, _e;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return ((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[2]) == 'Total' || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` : !this.isCustomStatusEnabled ? `<p><a>${params.value[0] ? params.value[0] + (((_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[3]) ? ` (${(_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[3]})` : '') : '--'}</a></p>` : `<p><span>${params.value[0] ? params.value[0] + (((_d = params === null || params === void 0 ? void 0 : params.value) === null || _d === void 0 ? void 0 : _d[3]) ? ` (${(_e = params === null || params === void 0 ? void 0 : params.value) === null || _e === void 0 ? void 0 : _e[3]})` : '') : '--'}</span></p>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total' || event.value[0] == 0) {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            this.getDataInNewTab('Booking Cancel', params);\n            return;\n          }\n\n          this.getDataFromCell('Booking Cancel', event);\n        }\n      }\n    }, {\n      headerName: 'Not Interested',\n      field: 'Not Interested',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d;\n\n        return [(_a = params.data) === null || _a === void 0 ? void 0 : _a.notInterestedCount, (_b = params === null || params === void 0 ? void 0 : params.data) === null || _b === void 0 ? void 0 : _b.userId, (_c = params === null || params === void 0 ? void 0 : params.data) === null || _c === void 0 ? void 0 : _c.projectTitle, this.canShowPercentage ? (_d = params.data) === null || _d === void 0 ? void 0 : _d.notInterestedCountPercentage : ''];\n      },\n      minWidth: 120,\n      cellRenderer: params => {\n        var _a, _b, _c, _d, _e;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return ((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[2]) == 'Total' || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` : !this.isCustomStatusEnabled ? `<p><a>${params.value[0] ? params.value[0] + (((_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[3]) ? ` (${(_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[3]})` : '') : '--'}</a></p>` : `<p><span>${params.value[0] ? params.value[0] + (((_d = params === null || params === void 0 ? void 0 : params.value) === null || _d === void 0 ? void 0 : _d[3]) ? ` (${(_e = params === null || params === void 0 ? void 0 : params.value) === null || _e === void 0 ? void 0 : _e[3]})` : '') : '--'}</span></p>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total') {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            this.getDataInNewTab('Not Interested', params);\n            return;\n          }\n\n          this.getDataFromCell('Not Interested', event);\n        }\n      }\n    }, {\n      headerName: 'Dropped',\n      field: 'Dropped',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d;\n\n        return [(_a = params.data) === null || _a === void 0 ? void 0 : _a.droppedCount, (_b = params === null || params === void 0 ? void 0 : params.data) === null || _b === void 0 ? void 0 : _b.userId, (_c = params === null || params === void 0 ? void 0 : params.data) === null || _c === void 0 ? void 0 : _c.projectTitle, this.canShowPercentage ? (_d = params.data) === null || _d === void 0 ? void 0 : _d.droppedCountPercentage : ''];\n      },\n      minWidth: 80,\n      cellRenderer: params => {\n        var _a, _b, _c, _d, _e;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return ((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[2]) == 'Total' || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` : !this.isCustomStatusEnabled ? `<p><a>${params.value[0] ? params.value[0] + (((_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[3]) ? ` (${(_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[3]})` : '') : '--'}</a></p>` : `<p><span>${params.value[0] ? params.value[0] + (((_d = params === null || params === void 0 ? void 0 : params.value) === null || _d === void 0 ? void 0 : _d[3]) ? ` (${(_e = params === null || params === void 0 ? void 0 : params.value) === null || _e === void 0 ? void 0 : _e[3]})` : '') : '--'}</span></p>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total') {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            this.getDataInNewTab('Dropped', params);\n            return;\n          }\n\n          this.getDataFromCell('Dropped', event);\n        }\n      }\n    }, {\n      headerName: 'Expression Of Interest',\n      field: 'Expression Of Interest',\n      filter: false,\n      hide: false,\n      valueGetter: params => {\n        var _a, _b, _c, _d;\n\n        return [(_a = params.data) === null || _a === void 0 ? void 0 : _a.expressionOfInterestLeadCount, (_b = params === null || params === void 0 ? void 0 : params.data) === null || _b === void 0 ? void 0 : _b.userId, (_c = params === null || params === void 0 ? void 0 : params.data) === null || _c === void 0 ? void 0 : _c.projectTitle, this.canShowPercentage ? (_d = params.data) === null || _d === void 0 ? void 0 : _d.expressionOfInterestLeadCountPercentage : ''];\n      },\n      minWidth: 160,\n      cellRenderer: params => {\n        var _a, _b;\n\n        const filters = Object.assign({}, this.filtersPayload);\n        if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n        return `<p>${params.value[0] ? params.value[0] + (((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[3]) ? ` (${(_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[3]})` : '') : '--'}</p>`;\n      },\n      cellClass: 'cursor-pointer',\n      onCellClicked: event => {\n        var _a, _b;\n\n        const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n        const params = {\n          value: event === null || event === void 0 ? void 0 : event.value,\n          data: event === null || event === void 0 ? void 0 : event.data\n        };\n\n        if (event.data.projectTitle == 'Total') {\n          return;\n        } else if (event.value[0] != 0) {\n          if (isCtrlClick) {\n            const filters = Object.assign({}, this.filtersPayload);\n            if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n            window === null || window === void 0 ? void 0 : window.open(`leads/manage-leads?leadReportGetData=true&assignTo=${(_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[0]}&data=${encodeURIComponent(JSON.stringify(params === null || params === void 0 ? void 0 : params.data))}&operation=Dropped&filtersPayload=${encodeURIComponent(JSON.stringify(filters))}`, '_blank');\n            return;\n          }\n\n          this.getDataFromCell('Expression Of Interest', event);\n        }\n      }\n    }];\n    this.gridOptions.columnDefs = this.isCustomStatusEnabled ? [...nameAndLeads, // ...newAndPending,\n    ...overdue, // ...callbackAndMS,\n    ...meetingDoneAndNotDone, // ...svs,\n    ...siteVisitDoneAndNotDone // ...others\n    ] : [...nameAndLeads, ...newAndPending, ...overdue, ...callbackAndMS, ...meetingDoneAndNotDone, ...svs, ...siteVisitDoneAndNotDone, ...others];\n    if (this.isCustomStatusEnabled) this.customStatusList.forEach(customStatus => {\n      var _a, _b;\n\n      let col = {\n        headerName: customStatus === null || customStatus === void 0 ? void 0 : customStatus.displayName,\n        field: customStatus === null || customStatus === void 0 ? void 0 : customStatus.displayName,\n        filter: false,\n        hide: false,\n        valueGetter: params => {\n          var _a, _b, _c, _d, _e, _f;\n\n          return [(_b = (_a = params === null || params === void 0 ? void 0 : params.data) === null || _a === void 0 ? void 0 : _a.statuses) === null || _b === void 0 ? void 0 : _b[customStatus === null || customStatus === void 0 ? void 0 : customStatus.displayName], (_c = params === null || params === void 0 ? void 0 : params.data) === null || _c === void 0 ? void 0 : _c.userId, (_d = params === null || params === void 0 ? void 0 : params.data) === null || _d === void 0 ? void 0 : _d.projectTitle, this.canShowPercentage ? (_f = (_e = params === null || params === void 0 ? void 0 : params.data) === null || _e === void 0 ? void 0 : _e.statuses) === null || _f === void 0 ? void 0 : _f[(customStatus === null || customStatus === void 0 ? void 0 : customStatus.displayName) + '__percentage__'] : ''];\n        },\n        minWidth: 110,\n        cellRenderer: params => {\n          var _a, _b, _c, _d, _e;\n\n          const filters = Object.assign({}, this.filtersPayload);\n          if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n          return ((_a = params === null || params === void 0 ? void 0 : params.value) === null || _a === void 0 ? void 0 : _a[2]) == 'Total' || params.value[0] == 0 ? `<p>${params.value[0] ? params.value[0] : '--'}</p>` : !this.isCustomStatusEnabled ? `<p><a>${params.value[0] ? params.value[0] + (((_b = params === null || params === void 0 ? void 0 : params.value) === null || _b === void 0 ? void 0 : _b[3]) ? ` (${(_c = params === null || params === void 0 ? void 0 : params.value) === null || _c === void 0 ? void 0 : _c[3]})` : '') : '--'}</a></p>` : `<p><span>${params.value[0] ? params.value[0] + (((_d = params === null || params === void 0 ? void 0 : params.value) === null || _d === void 0 ? void 0 : _d[3]) ? ` (${(_e = params === null || params === void 0 ? void 0 : params.value) === null || _e === void 0 ? void 0 : _e[3]})` : '') : '--'}</span></p>`;\n        },\n        cellClass: 'cursor-pointer',\n        onCellClicked: event => {\n          var _a;\n\n          const isCtrlClick = (_a = event === null || event === void 0 ? void 0 : event.event) === null || _a === void 0 ? void 0 : _a.ctrlKey;\n          const params = {\n            value: event === null || event === void 0 ? void 0 : event.value,\n            data: event === null || event === void 0 ? void 0 : event.data\n          };\n\n          if (event.data.projectTitle == 'Total') {\n            return;\n          } else if (event.value[0] != 0) {\n            if (isCtrlClick) {\n              this.getDataInNewTab(customStatus === null || customStatus === void 0 ? void 0 : customStatus.displayName, params);\n              return;\n            }\n\n            this.getDataFromCell(customStatus === null || customStatus === void 0 ? void 0 : customStatus.displayName, event);\n          }\n        }\n      };\n      (_b = (_a = this.gridOptions) === null || _a === void 0 ? void 0 : _a.columnDefs) === null || _b === void 0 ? void 0 : _b.push(col);\n    });\n    this.gridOptions.columnDefs.forEach((item, index) => {\n      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {\n        this.columnDropDown.push({\n          field: item.field,\n          hide: item.hide\n        });\n      }\n    });\n    this.gridOptions.context = {\n      componentParent: this\n    };\n  }\n\n  getMeetingCount(operation, event, meetingStatus) {\n    const filters = Object.assign({}, this.filtersPayload);\n\n    if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) {\n      filters.IsWithTeam = false;\n    } // if (this.isCustomStatusEnabled) {\n    //   return\n    // }\n\n\n    this.router.navigate(['leads/manage-leads']);\n    let visitMeeting = [];\n    visitMeeting.push(meetingStatus);\n    this.gridOptionsService.data = event.data;\n    this.gridOptionsService.dateType = this.appliedFilter.dateType;\n    this.gridOptionsService.status = operation;\n    this.gridOptionsService.payload = this.filtersPayload;\n    this.gridOptionsService.meetingStatus = visitMeeting;\n  }\n\n  getMeetingCountnewTab(operation, params, meetingStatus) {\n    const filters = Object.assign({}, this.filtersPayload);\n\n    if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) {\n      filters.IsWithTeam = false;\n    }\n\n    window === null || window === void 0 ? void 0 : window.open(`leads/manage-leads?leadReportGetMeetingCount=true&data=${encodeURIComponent(JSON.stringify(params === null || params === void 0 ? void 0 : params.data))}&operation=${operation}&meetingStatus=${meetingStatus}&filtersPayload=${encodeURIComponent(JSON.stringify(filters))}`, '_blank');\n  }\n\n  onGridReady(params) {\n    this.gridApi = params.api;\n    this.gridColumnApi = params.columnApi;\n    this.toggleColumns(params);\n    this.gridOptions.api = params.api;\n  }\n\n  getDataFromCell(operation, event) {\n    // if (this.isCustomStatusEnabled) {\n    //   return\n    // }\n    this.router.navigate(['leads/manage-leads']);\n    this.gridOptionsService.meetingStatus = undefined;\n    this.gridOptionsService.dateType = this.appliedFilter.dateType;\n    this.gridOptionsService.data = event.data;\n    this.gridOptionsService.status = operation;\n    const filters = Object.assign({}, this.filtersPayload);\n    if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) filters.IsWithTeam = false;\n    this.gridOptionsService.payload = filters;\n  }\n\n  getDataInNewTab(operation, params) {\n    //     if (this.isCustomStatusEnabled) {\n    //   return\n    // }\n    const filters = Object.assign({}, this.filtersPayload);\n\n    if (filters === null || filters === void 0 ? void 0 : filters.IsWithTeam) {\n      filters.IsWithTeam = false;\n    }\n\n    window === null || window === void 0 ? void 0 : window.open(`leads/manage-leads?leadReportGetData=true&data=${encodeURIComponent(JSON.stringify(params === null || params === void 0 ? void 0 : params.data))}&operation=${operation}&filtersPayload=${encodeURIComponent(JSON.stringify(filters))}`, '_blank');\n  }\n\n  onPageChange(e) {\n    this.currOffset = e;\n    this.filtersPayload = Object.assign(Object.assign({}, this.filtersPayload), {\n      pageSize: this.pageSize,\n      pageNumber: e + 1\n    });\n    this.gridApi.paginationGoToPage(e);\n\n    this._store.dispatch(new UpdateUserFilterPayload(this.filtersPayload));\n\n    this.isCustomStatusEnabled ? this._store.dispatch(new FetchReportsCustomUser()) : this._store.dispatch(new FetchReportsUser());\n  }\n\n  onResetDateFilter() {\n    this.appliedFilter = Object.assign(Object.assign({}, this.appliedFilter), {\n      dateType: null,\n      date: ''\n    });\n    this.filterFunction();\n  }\n\n  getArrayOfFilters(key, values) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n\n    const allowedKeys = ['subSources', 'projects', 'agencyNames', 'cities', 'states'];\n    if (['pageSize', 'pageNumber', 'visibility', 'withTeam', 'isGM', 'userStatus', 'search', 'ShouldShowAll'].includes(key) || (values === null || values === void 0 ? void 0 : values.length) === 0) return [];else if (key === 'date' && values.length === 2) {\n      if (key === 'date' && values[0] !== null) {\n        this.toDate = setTimeZoneDate(new Date(values[0]), (_b = (_a = this.userData) === null || _a === void 0 ? void 0 : _a.timeZoneInfo) === null || _b === void 0 ? void 0 : _b.baseUTcOffset);\n        this.fromDate = setTimeZoneDate(new Date(values[1]), (_d = (_c = this.userData) === null || _c === void 0 ? void 0 : _c.timeZoneInfo) === null || _d === void 0 ? void 0 : _d.baseUTcOffset);\n        const formattedToDate = getTimeZoneDate(this.toDate, (_f = (_e = this.userData) === null || _e === void 0 ? void 0 : _e.timeZoneInfo) === null || _f === void 0 ? void 0 : _f.baseUTcOffset, 'dayMonthYear');\n        const formattedFromDate = getTimeZoneDate(this.fromDate, (_h = (_g = this.userData) === null || _g === void 0 ? void 0 : _g.timeZoneInfo) === null || _h === void 0 ? void 0 : _h.baseUTcOffset, 'dayMonthYear');\n        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;\n        return [dateRangeString];\n      } else {\n        return null;\n      }\n    } else if (allowedKeys.includes(key)) {\n      return values;\n    }\n    return (_j = values === null || values === void 0 ? void 0 : values.toString()) === null || _j === void 0 ? void 0 : _j.split(',');\n  }\n\n  applyAdvancedFilter() {\n    this.filterFunction();\n    this.modalService.hide();\n  }\n\n  onRemoveFilter(key, value) {\n    var _a;\n\n    if (['dateType', 'date'].includes(key)) {\n      delete this.appliedFilter[key];\n      const dependentKey = key === 'date' ? 'dateType' : 'date';\n\n      if (this.appliedFilter[dependentKey]) {\n        delete this.appliedFilter[dependentKey];\n      }\n    } else {\n      this.appliedFilter[key] = (_a = this.appliedFilter[key]) === null || _a === void 0 ? void 0 : _a.filter((item, index) => {\n        var _a;\n\n        const matchIndex = (_a = this.appliedFilter[key]) === null || _a === void 0 ? void 0 : _a.indexOf(value);\n        return index !== matchIndex;\n      });\n    }\n\n    this.filterFunction();\n  }\n\n  openAdvFiltersModal(advFilters) {\n    this._store.dispatch(new FetchProjectList());\n\n    this._store.dispatch(new FetchSubSourceList());\n\n    this._store.dispatch(new FetchLeadCities());\n\n    this._store.dispatch(new FetchLeadStates()); // this._store.dispatch(new FetchLeadCountries)\n\n\n    this._store.dispatch(new FetchAllSources());\n\n    let initialState = {\n      class: 'ip-modal-unset  top-full-modal'\n    };\n    this.modalService.show(advFilters, initialState);\n  }\n\n  getUserName(id) {\n    var _a;\n\n    let userName = '';\n    (_a = this.allUsers) === null || _a === void 0 ? void 0 : _a.forEach(user => {\n      if (id === user.id) userName = `${user.fullName}`;\n    });\n    return userName;\n  }\n\n  toggleColumns(params) {\n    var _a, _b, _c, _d, _e, _f;\n\n    this.columns = (_b = (_a = params === null || params === void 0 ? void 0 : params.columnApi) === null || _a === void 0 ? void 0 : _a.getColumns()) === null || _b === void 0 ? void 0 : _b.map(column => {\n      var _a;\n\n      return {\n        label: (_a = column === null || column === void 0 ? void 0 : column.getColDef()) === null || _a === void 0 ? void 0 : _a.headerName,\n        value: column\n      };\n    }); // .slice(4, this.columns?.length), this.columns[1], this.columns[2]]\n\n    this.columns = [...this.columns.slice(1, (_c = this.columns) === null || _c === void 0 ? void 0 : _c.length)].sort((a, b) => {\n      var _a;\n\n      return (_a = a === null || a === void 0 ? void 0 : a.label) === null || _a === void 0 ? void 0 : _a.localeCompare(b === null || b === void 0 ? void 0 : b.label);\n    });\n    this.defaultColumns = (_d = this.columns) === null || _d === void 0 ? void 0 : _d.filter(col => {\n      var _a, _b;\n\n      return ((_b = (_a = col === null || col === void 0 ? void 0 : col.value) === null || _a === void 0 ? void 0 : _a.getColDef()) === null || _b === void 0 ? void 0 : _b.hide) !== true;\n    });\n    let columnState = JSON.parse(localStorage.getItem('myDataColumnState'));\n\n    if (columnState) {\n      this.gridColumnApi.applyColumnState({\n        state: columnState,\n        applyOrder: true\n      });\n    }\n\n    let columnData = (_e = localStorage.getItem('status-reports-columns')) === null || _e === void 0 ? void 0 : _e.split(',');\n\n    if (columnData === null || columnData === void 0 ? void 0 : columnData.length) {\n      let visibleColumns = (_f = this.columns) === null || _f === void 0 ? void 0 : _f.filter(col => columnData === null || columnData === void 0 ? void 0 : columnData.includes(col.label));\n      this.defaultColumns = visibleColumns;\n      this.onColumnsSelected(visibleColumns);\n    }\n  }\n\n  onColumnsSelected(columns) {\n    var _a, _b, _c;\n\n    let colData = columns === null || columns === void 0 ? void 0 : columns.map(column => column.label);\n    localStorage.setItem('status-reports-columns', colData === null || colData === void 0 ? void 0 : colData.toString());\n    const cols = columns === null || columns === void 0 ? void 0 : columns.map(col => col.value);\n    (_a = this.gridColumnApi) === null || _a === void 0 ? void 0 : _a.setColumnsVisible(cols, true);\n    const nonSelectedCols = (_b = this.columns) === null || _b === void 0 ? void 0 : _b.filter(col => {\n      return !cols.includes(col.value);\n    });\n    (_c = this.gridColumnApi) === null || _c === void 0 ? void 0 : _c.setColumnsVisible(nonSelectedCols.map(col => col.value), false);\n    var columnState = this.gridColumnApi.getColumnState();\n    if (columnState && columnState[0]) columnState[0].pinned = 'left';\n    localStorage.setItem('myDataColumnState', JSON.stringify(columnState));\n    this.gridColumnApi.applyColumnState({\n      state: columnState,\n      applyOrder: true\n    });\n  }\n\n  onSetColumnDefault() {\n    this.defaultColumns = this.columns.filter(col => col.value.getColDef().hide !== true);\n    this.onColumnsSelected(this.defaultColumns);\n  }\n\n  currentVisibility(visibility, isTopLevelFilter) {\n    var _a, _b, _c, _d;\n\n    this.appliedFilter.userStatus = visibility;\n    this.appliedFilter.pageNumber = 1;\n\n    if (isTopLevelFilter) {\n      this.appliedFilter.users = null;\n    }\n\n    this.filterFunction();\n\n    if (this.canViewAllUsers) {\n      switch (visibility) {\n        case 1:\n          this.allUsers = (_a = this.users) === null || _a === void 0 ? void 0 : _a.filter(user => user.isActive);\n          break;\n\n        case 2:\n          this.allUsers = (_b = this.users) === null || _b === void 0 ? void 0 : _b.filter(user => !user.isActive);\n          break;\n\n        case null:\n          this.allUsers = this.users;\n          break;\n      }\n\n      this.allUsers = assignToSort(this.allUsers, '');\n    } else {\n      switch (visibility) {\n        case 1:\n          this.onlyReportees = (_c = this.reportees) === null || _c === void 0 ? void 0 : _c.filter(user => user.isActive);\n          break;\n\n        case 2:\n          this.onlyReportees = (_d = this.reportees) === null || _d === void 0 ? void 0 : _d.filter(user => !user.isActive);\n          break;\n\n        case null:\n          this.onlyReportees = this.reportees;\n          break;\n      }\n\n      this.onlyReportees = assignToSort(this.onlyReportees, '');\n    }\n  }\n\n  assignCount() {\n    this.pageSize = this.selectedPageSize;\n    this.filtersPayload = Object.assign(Object.assign({}, this.filtersPayload), {\n      pageSize: this.pageSize,\n      pageNumber: 1\n    });\n\n    this._store.dispatch(new UpdateUserFilterPayload(this.filtersPayload));\n\n    this.isCustomStatusEnabled ? this._store.dispatch(new FetchReportsCustomUser()) : this._store.dispatch(new FetchReportsUser());\n    this.currOffset = 0;\n  }\n\n  filterFunction() {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z;\n\n    this.appliedFilter.pageNumber = 1;\n\n    if (((_b = (_a = this.appliedFilter) === null || _a === void 0 ? void 0 : _a.dateType) === null || _b === void 0 ? void 0 : _b.length) || ((_e = (_d = (_c = this.appliedFilter) === null || _c === void 0 ? void 0 : _c.date) === null || _d === void 0 ? void 0 : _d[0]) === null || _e === void 0 ? void 0 : _e.length) || ((_f = this.appliedFilter.users) === null || _f === void 0 ? void 0 : _f.length) || ((_g = this.appliedFilter.projects) === null || _g === void 0 ? void 0 : _g.length) || ((_h = this.appliedFilter.subSources) === null || _h === void 0 ? void 0 : _h.length) || ((_j = this.appliedFilter.sources) === null || _j === void 0 ? void 0 : _j.length) || ((_k = this.appliedFilter.cities) === null || _k === void 0 ? void 0 : _k.length) || ((_l = this.appliedFilter.Countries) === null || _l === void 0 ? void 0 : _l.length) || ((_m = this.appliedFilter.states) === null || _m === void 0 ? void 0 : _m.length)) {\n      this.showFilters = true;\n    } else {\n      this.showFilters = false;\n    }\n\n    this.filtersPayload = Object.assign(Object.assign({}, this.filtersPayload), {\n      pageNumber: (_o = this.appliedFilter) === null || _o === void 0 ? void 0 : _o.pageNumber,\n      pageSize: this.pageSize,\n      userStatus: this.appliedFilter.userStatus,\n      dateType: ReportDateType[this.appliedFilter.dateType],\n      fromDate: setTimeZoneDate((_q = (_p = this.appliedFilter) === null || _p === void 0 ? void 0 : _p.date) === null || _q === void 0 ? void 0 : _q[0], (_s = (_r = this.userData) === null || _r === void 0 ? void 0 : _r.timeZoneInfo) === null || _s === void 0 ? void 0 : _s.baseUTcOffset),\n      toDate: setTimeZoneDate((_t = this.appliedFilter.date) === null || _t === void 0 ? void 0 : _t[1], (_v = (_u = this.userData) === null || _u === void 0 ? void 0 : _u.timeZoneInfo) === null || _v === void 0 ? void 0 : _v.baseUTcOffset),\n      IsWithTeam: this.appliedFilter.withTeam,\n      UserIds: this.appliedFilter.users,\n      SearchText: this.searchTerm,\n      Sources: this.appliedFilter.sources,\n      SubSources: this.appliedFilter.subSources,\n      Projects: this.appliedFilter.projects,\n      ReportPermission: this.canViewAllUsers ? 0 : 1,\n      ExportPermission: this.canExportAllUsers ? 0 : 1,\n      Cities: (_w = this.appliedFilter) === null || _w === void 0 ? void 0 : _w.cities,\n      States: (_x = this.appliedFilter) === null || _x === void 0 ? void 0 : _x.states,\n      // Countries: this.appliedFilter?.Countries,\n      ShouldShowAll: (_z = (_y = this.appliedFilter) === null || _y === void 0 ? void 0 : _y.ShouldShowAll) !== null && _z !== void 0 ? _z : true,\n      ShouldShowPercentage: this.canShowPercentage\n    });\n\n    this._store.dispatch(new UpdateUserFilterPayload(this.filtersPayload));\n\n    this.isCustomStatusEnabled ? this._store.dispatch(new FetchReportsCustomUser()) : this._store.dispatch(new FetchReportsUser());\n    this.currOffset = 0;\n  }\n\n  reset() {\n    this.appliedFilter = {\n      pageNumber: 1,\n      pageSize: this.pageSize\n    };\n    this.filterFunction();\n  }\n\n  updateSubSource() {\n    var _a, _b, _c, _d;\n\n    if ((_b = (_a = this.appliedFilter) === null || _a === void 0 ? void 0 : _a.sources) === null || _b === void 0 ? void 0 : _b.length) {\n      this.subSourceList = [];\n      (_c = this.appliedFilter) === null || _c === void 0 ? void 0 : _c.sources.forEach(i => {\n        var _a, _b;\n\n        const source = LeadSource[i];\n        const leadSource = IntegrationSource[source];\n\n        if (leadSource === '99 Acres') {\n          this.subSourceList.push.apply(this.subSourceList, this.allSubSourceList['NinetyNineAcres'] || []);\n        } else {\n          const formattedKey = leadSource === null || leadSource === void 0 ? void 0 : leadSource.replace(/\\s+/g, '');\n\n          if (Array.isArray((_a = this.allSubSourceList) === null || _a === void 0 ? void 0 : _a[formattedKey])) {\n            this.subSourceList.push.apply(this.subSourceList, ((_b = this.allSubSourceList) === null || _b === void 0 ? void 0 : _b[formattedKey]) || []);\n          } else {\n            this.subSourceList.push.apply(this.subSourceList, this.allSubSourceList[leadSource] || []);\n          }\n        }\n      });\n    } else {\n      let subSourceList = ((_d = this.leadSources) === null || _d === void 0 ? void 0 : _d.flatMap(lead => {\n        var _a, _b, _c;\n\n        if ((lead === null || lead === void 0 ? void 0 : lead.displayName) === '99 Acres') {\n          return ((_a = this.allSubSourceList) === null || _a === void 0 ? void 0 : _a['NinetyNineAcres']) || [];\n        }\n\n        const formattedKey = (_b = lead === null || lead === void 0 ? void 0 : lead.displayName) === null || _b === void 0 ? void 0 : _b.replace(/\\s+/g, '');\n        let match = (_c = this.allSubSourceList) === null || _c === void 0 ? void 0 : _c[formattedKey];\n\n        if (!match) {\n          match = this.allSubSourceList[lead === null || lead === void 0 ? void 0 : lead.displayName];\n        }\n\n        if (!match && (formattedKey === null || formattedKey === void 0 ? void 0 : formattedKey.toLowerCase()) === '99acres') {\n          match = this.allSubSourceList['NinetyNineAcres'];\n        }\n\n        return Array.isArray(match) ? match : [];\n      })) || [];\n      this.subSourceList = subSourceList;\n    }\n  }\n\n  onSelectSource(source) {\n    if (source) {\n      this.updateSubSources(source.displayName);\n    } else {\n      this.updateSubSources(null);\n    }\n  }\n\n  updateSubSources(sourceName) {\n    var _a, _b, _c;\n\n    if (sourceName) {\n      if (sourceName === '99 Acres') {\n        this.subSourceList = this.allSubSourceList['NinetyNineAcres'] || [];\n      } else {\n        const formattedKey = sourceName.replace(/\\s+/g, '');\n\n        if (Array.isArray((_a = this.allSubSourceList) === null || _a === void 0 ? void 0 : _a[formattedKey])) {\n          this.subSourceList = ((_b = this.allSubSourceList) === null || _b === void 0 ? void 0 : _b[formattedKey]) || [];\n        } else {\n          this.subSourceList = this.allSubSourceList[sourceName] || [];\n        }\n      }\n    } else {\n      let subSourceList = ((_c = this.leadSources) === null || _c === void 0 ? void 0 : _c.flatMap(lead => {\n        var _a, _b, _c;\n\n        if ((lead === null || lead === void 0 ? void 0 : lead.displayName) === '99 Acres') {\n          return ((_a = this.allSubSourceList) === null || _a === void 0 ? void 0 : _a['NinetyNineAcres']) || [];\n        }\n\n        const formattedKey = (_b = lead === null || lead === void 0 ? void 0 : lead.displayName) === null || _b === void 0 ? void 0 : _b.replace(/\\s+/g, '');\n        let match = (_c = this.allSubSourceList) === null || _c === void 0 ? void 0 : _c[formattedKey];\n\n        if (!match) {\n          match = this.allSubSourceList[lead === null || lead === void 0 ? void 0 : lead.displayName];\n        }\n\n        if (!match && (formattedKey === null || formattedKey === void 0 ? void 0 : formattedKey.toLowerCase()) === '99acres') {\n          match = this.allSubSourceList['NinetyNineAcres'];\n        }\n\n        return Array.isArray(match) ? match : [];\n      })) || [];\n      this.subSourceList = subSourceList;\n    }\n  }\n\n  refresh() {\n    var _a;\n\n    (_a = this.gridOptions.api) === null || _a === void 0 ? void 0 : _a.refreshCells();\n  }\n\n  exportLeadReport() {\n    var _a, _b, _c, _d, _e;\n\n    this._store.dispatch(new FetchUserExportSuccess(''));\n\n    this.filterFunction();\n    let initialState = {\n      payload: Object.assign(Object.assign({}, this.filtersPayload), {\n        selectedColumns: (_a = this.gridColumnApi.getColumnState().filter(col => !(col === null || col === void 0 ? void 0 : col.hide)).map(col => col === null || col === void 0 ? void 0 : col.colId)) === null || _a === void 0 ? void 0 : _a.map(col => {\n          if (col === 'Referral Taken') {\n            return 'Site Visit Done';\n          } else if (col === 'Referral Not Taken') {\n            return 'Site Visit Not Done';\n          } else if (col === 'Referral Scheduled') {\n            return 'Site Visit Scheduled';\n          }\n\n          return col;\n        }),\n        timeZoneId: ((_c = (_b = this.userData) === null || _b === void 0 ? void 0 : _b.timeZoneInfo) === null || _c === void 0 ? void 0 : _c.timeZoneId) || getSystemTimeZoneId(),\n        baseUTcOffset: ((_e = (_d = this.userData) === null || _d === void 0 ? void 0 : _d.timeZoneInfo) === null || _e === void 0 ? void 0 : _e.baseUTcOffset) || getSystemTimeOffset()\n      }),\n      class: 'modal-400 modal-dialog-centered ph-modal-unset'\n    };\n    this.modalService.show(ExportMailComponent, Object.assign({}, {\n      class: 'modal-400 modal-dialog-centered ph-modal-unset',\n      initialState\n    }));\n  }\n\n  onSearch($event) {\n    if ($event.key === 'Enter') {\n      if (!this.searchTerm) {\n        return;\n      }\n\n      this.searchTermSubject.next(this.searchTerm);\n    }\n  }\n\n  isEmptyInput($event) {\n    if (this.searchTerm === '' || this.searchTerm === null) {\n      this.searchTermSubject.next('');\n    }\n  }\n\n  toggleView() {\n    this.currentView = this.currentView === 'graph' ? 'table' : 'graph';\n  }\n\n  exportGraphAsPDF() {\n    if (this.reportsGraph && this.isGraphExportEnabled()) {\n      this.reportsGraph.exportGraph();\n    }\n  }\n\n  isGraphExportEnabled() {\n    var _a, _b;\n\n    return this.currentView === 'graph' && ((_a = this.reportsGraph) === null || _a === void 0 ? void 0 : _a.isChartReady) && !((_b = this.reportsGraph) === null || _b === void 0 ? void 0 : _b.showSelectionMessage);\n  }\n\n  ngOnDestroy() {\n    this.stopper.next();\n    this.stopper.complete();\n  }\n\n}\n\nStatusReportComponent.ɵfac = function StatusReportComponent_Factory(t) {\n  return new (t || StatusReportComponent)(i0.ɵɵdirectiveInject(i1.GridOptionsService), i0.ɵɵdirectiveInject(i2.Store), i0.ɵɵdirectiveInject(i3.HeaderTitleService), i0.ɵɵdirectiveInject(i4.Title), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i6.BsModalService), i0.ɵɵdirectiveInject(i6.BsModalRef), i0.ɵɵdirectiveInject(i7.ShareDataService));\n};\n\nStatusReportComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: StatusReportComponent,\n  selectors: [[\"status-report\"]],\n  viewQuery: function StatusReportComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.reportsGraph = _t.first);\n    }\n  },\n  decls: 3,\n  vars: 1,\n  consts: [[4, \"ngIf\"], [\"reportsLoader\", \"\"], [1, \"d-flex\", \"bg-white\", \"py-12\", \"px-16\", \"border-top\", \"border-bottom\", \"w-100\", \"position-fixed\", \"z-index-2\"], [1, \"align-center\", \"ml-8\", \"z-index-1021\", \"tb-px-0\", \"tb-pb-0\", \"tb-br-top-unset\", \"tb-left-110\", \"left-230\", 3, \"ngClass\"], [1, \"align-center\", \"top-nav-bar\", \"text-nowrap\"], [4, \"ngFor\", \"ngForOf\"], [1, \"mx-24\", \"pt-40\"], [\"AdvancedFilters\", \"\"], [1, \"mt-20\"], [1, \"justify-between\", \"align-end\"], [1, \"checkbox-container\", \"mb-4\"], [\"type\", \"checkbox\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [1, \"checkmark\"], [1, \"align-center\", \"bg-white\", \"w-100\", \"border-gray\", \"tb-align-center-unset\", \"tb-flex-col\"], [1, \"align-center\", \"border-end\", \"flex-grow-1\", \"no-validation\"], [1, \"align-center\", \"w-100\", \"px-10\", \"py-12\"], [1, \"search\", \"icon\", \"ic-search\", \"ic-sm\", \"ic-slate-90\", \"mr-12\", \"ph-mr-4\"], [\"placeholder\", \"Search by User\", \"autocomplete\", \"off\", \"name\", \"search\", 1, \"border-0\", \"outline-0\", \"w-100\", 3, \"ngModel\", \"keydown\", \"input\", \"ngModelChange\"], [1, \"text-muted\", \"text-nowrap\", \"ph-d-none\", \"pr-6\"], [\"class\", \"bg-accent-green text-white border-end\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"bg-coal\", \"text-white\", \"px-20\", \"py-12\", \"h-100\", \"align-center\", \"cursor-pointer\", \"border-end\", \"ph-d-none\", 3, \"click\"], [\"class\", \"text-nowrap ic-cube ic-white ic-large m-2\", 4, \"ngIf\"], [\"class\", \"text-nowrap ic-chart-pie ic-white ic-large m-2\", 4, \"ngIf\"], [1, \"d-flex\", \"tb-br-top\"], [1, \"px-10\", \"align-center\", \"cursor-pointer\", \"border-end\", \"tb-flex-grow-1\", 3, \"click\"], [1, \"icon\", \"ic-filter-solid\", \"ic-xxs\", \"ic-black\", \"mr-10\"], [1, \"fw-600\", \"ph-d-none\"], [1, \"align-center\", \"position-relative\", \"cursor-pointer\", \"d-flex\", \"border-end\"], [1, \"position-absolute\", \"left-15\", \"z-index-2\", \"fw-600\", \"text-sm\"], [1, \"ph-d-none\"], [1, \"show-hide-gray\", \"w-140\", \"ph-w-110px\"], [\"ResizableDropdown\", \"\", 1, \"bg-white\", 3, \"virtualScroll\", \"items\", \"multiple\", \"searchable\", \"closeOnSelect\", \"ngModel\", \"change\"], [\"ng-option-tmp\", \"\"], [1, \"bg-coal\", \"text-white\", \"px-10\", \"py-12\", \"ip-w-30px\", \"align-center\", \"cursor-pointer\", 3, \"click\"], [1, \"ip-d-none\"], [1, \"ic-refresh\", \"d-none\", \"ip-d-block\"], [1, \"show-dropdown-white\", \"align-center\", \"position-relative\"], [1, \"fw-600\", \"position-absolute\", \"left-5\", \"z-index-2\"], [1, \"tb-d-none\"], [\"bindValue\", \"id\", \"ResizableDropdown\", \"\", 1, \"w-150\", \"tb-w-120px\", 3, \"virtualScroll\", \"placeholder\", \"ngModel\", \"searchable\", \"ngModelChange\", \"change\"], [\"name\", \"showEntriesSize\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"px-4\", \"py-12\", \"tb-w-100-34\", 3, \"ngClass\"], [\"statusData\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"cursor-pointer\", 3, \"title\", \"click\"], [1, \"align-center\", \"ph-mb-4\"], [\"alt\", \"muso\", \"width\", \"22\", \"height\", \"22\", 3, \"type\", \"appImage\"], [1, \"text-large\", \"ml-8\", \"mr-16\"], [1, \"lead-adv-filter\", \"p-30\", \"bg-white\", \"brbl-15\", \"brbr-15\"], [1, \"adv-filter\"], [1, \"d-flex\", \"w-100\", \"flex-wrap\", \"ng-select-sm\"], [1, \"flex-column\", \"w-25\", \"tb-w-33\", \"ip-w-50\", \"ph-w-100\"], [1, \"justify-between\", \"align-end\", \"mr-20\"], [1, \"field-label\"], [\"type\", \"checkbox\", 3, \"ngModel\", \"ngModelChange\"], [1, \"position-relative\", \"mr-20\", \"ph-mr-0\"], [\"ResizableDropdown\", \"\", \"name\", \"user\", \"placeholder\", \"ex. Manasa Pampana\", \"bindLabel\", \"fullName\", \"bindValue\", \"id\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"multiple\", \"closeOnSelect\", \"ngModel\", \"ngModelChange\"], [\"ng-label-tmp\", \"\"], [1, \"w-25\", \"tb-w-33\", \"ip-w-50\", \"ph-w-100\"], [1, \"mr-20\", \"ph-mr-0\"], [\"ResizableDropdown\", \"\", \"bindLabel\", \"displayName\", \"bindValue\", \"displayName\", 3, \"virtualScroll\", \"items\", \"multiple\", \"closeOnSelect\", \"ngClass\", \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [\"ResizableDropdown\", \"\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"multiple\", \"closeOnSelect\", \"placeholder\", \"ngModel\", \"ngModelChange\"], [1, \"mr-20\", \"mt-4\", \"ph-mr-0\"], [\"ResizableDropdown\", \"\", \"bindLabel\", \"id\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"multiple\", \"closeOnSelect\", \"placeholder\", \"ngModel\", \"ngModelChange\"], [\"ResizableDropdown\", \"\", \"bindLabel\", \"item\", \"bindValue\", \"item\", 3, \"virtualScroll\", \"items\", \"ngClass\", \"multiple\", \"closeOnSelect\", \"placeholder\", \"ngModel\", \"ngModelChange\"], [1, \"w-100\"], [1, \"mr-20\", \"ph-mr-0\", \"align-center\"], [1, \"w-33\"], [\"placeholder\", \"All\", \"ResizableDropdown\", \"\", 1, \"mr-10\", \"ng-select-w-171\", 3, \"virtualScroll\", \"searchable\", \"ngModel\", \"ngModelChange\"], [\"name\", \"dateType\", \"ngDefaultControl\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-67\", \"align-center\", \"position-relative\", \"filters-grid\", \"clear-padding\"], [\"id\", \"reportsAppointmentDate\", \"data-automate-id\", \"reportsAppointmentDate\", 1, \"date-picker\", \"border\", \"pt-8\", \"pb-6\", \"br-4\", \"align-center\", \"w-100\"], [1, \"ic-appointment\", \"icon\", \"ic-xxs\", \"ic-black\", 3, \"owlDateTimeTrigger\"], [\"type\", \"text\", \"readonly\", \"\", \"placeholder\", \"ex. 5-03-2025 - 14-03-2025\", 1, \"pl-20\", \"text-large\", \"w-100\", 3, \"max\", \"owlDateTimeTrigger\", \"owlDateTime\", \"selectMode\", \"ngModel\", \"disabled\", \"ngModelChange\"], [3, \"pickerType\", \"afterPickerOpen\"], [\"dt1\", \"\"], [\"class\", \"right-4 align-center cursor-pointer position-absolute\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"w-25 tb-w-33 ip-w-50 ph-w-100 align-end\", 4, \"ngIf\"], [1, \"flex-end\", \"mt-10\", \"tb-mr-20\", \"ph-mr-0\"], [1, \"mr-20\", \"fw-semi-bold\", \"text-mud\", \"cursor-pointer\", 3, \"click\"], [1, \"btn-gray\", 3, \"click\"], [1, \"btn-coal\", \"ml-20\", 3, \"click\"], [1, \"ic-cancel\", \"ic-dark\", \"icon\", \"ic-x-xs\", \"mr-4\", 3, \"click\"], [1, \"ng-value-label\"], [1, \"flex-between\"], [1, \"checkbox-container\"], [\"type\", \"checkbox\", 3, \"id\", \"automate-id\", \"checked\"], [1, \"text-truncate-1\", \"break-all\"], [\"class\", \"text-disabled\", 4, \"ngIf\"], [1, \"text-disabled\"], [\"name\", \"dateType\", \"ngDefaultControl\", \"\", 3, \"value\"], [1, \"right-4\", \"align-center\", \"cursor-pointer\", \"position-absolute\", 3, \"click\"], [1, \"ic-refresh\", \"ic-coal\"], [1, \"w-25\", \"tb-w-33\", \"ip-w-50\", \"ph-w-100\", \"align-end\"], [1, \"ml-1\", \"pt-3\"], [1, \"bg-accent-green\", \"text-white\", \"border-end\", 3, \"ngClass\"], [\"class\", \"px-20 py-5 h-100 align-center cursor-pointer flex-col\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"px-20 py-5 h-100 align-center cursor-pointer flex-col\", 3, \"pe-none\", \"click\", 4, \"ngIf\"], [1, \"px-20\", \"py-5\", \"h-100\", \"align-center\", \"cursor-pointer\", \"flex-col\", 3, \"click\"], [1, \"mt-2\", \"fw-300\", \"text-xs\"], [\"buttonDots\", \"\"], [1, \"container\", \"flex-center\", \"py-12\"], [1, \"dot-falling\", \"dot-white\"], [1, \"text-nowrap\", \"ic-cube\", \"ic-white\", \"ic-large\", \"m-2\"], [1, \"text-nowrap\", \"ic-chart-pie\", \"ic-white\", \"ic-large\", \"m-2\"], [\"name\", \"showEntriesSize\", 3, \"value\"], [1, \"bg-secondary\", \"flex-between\"], [1, \"br-4\", \"overflow-auto\", \"d-flex\", \"scroll-hide\", \"w-100\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [1, \"px-8\", \"py-4\", \"bg-slate-120\", \"m-4\", \"br-4\", \"text-mud\", \"text-center\", \"fw-semi-bold\", \"text-nowrap\", \"cursor-pointer\", 3, \"click\"], [1, \"d-flex\"], [\"class\", \"px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap\", 4, \"ngFor\", \"ngForOf\"], [1, \"px-8\", \"py-4\", \"bg-slate-120\", \"m-4\", \"br-4\", \"text-mud\", \"text-center\", \"fw-semi-bold\", \"text-nowrap\"], [1, \"icon\", \"ic-cancel\", \"ic-dark\", \"ic-x-xs\", \"cursor-pointer\", \"text-light-slate\", \"ml-4\", 3, \"click\"], [1, \"reports\", \"pinned-grid\"], [1, \"ag-theme-alpine\", 3, \"pagination\", \"paginationPageSize\", \"gridOptions\", \"rowData\", \"suppressPaginationPanel\", \"alwaysShowHorizontalScroll\", \"alwaysShowVerticalScroll\", \"gridReady\", \"cellClickedEvent\"], [\"agGrid\", \"\"], [1, \"my-20\", \"flex-end\"], [\"class\", \"mr-10\", 4, \"ngIf\"], [3, \"offset\", \"limit\", \"range\", \"size\", \"pageChange\"], [\"xAxisData\", \"firstName lastName\", \"reportType\", \"status-report\", 3, \"payload\", \"rowData\", \"gridOptions\", \"filteredColumnDefsCache\", \"exportStarted\", \"exportFinished\"], [\"reportsGraph\", \"\"], [1, \"mr-10\"], [\"class\", \"flex-center w-100 h-100-337 min-h-250\", 4, \"ngIf\"], [\"class\", \"flex-center-col h-100-337\", 4, \"ngIf\"], [1, \"flex-center\", \"w-100\", \"h-100-337\", \"min-h-250\"], [3, \"ngTemplateOutlet\"], [1, \"flex-center-col\", \"h-100-337\"], [\"src\", \"assets/images/layered-cards.svg\", \"alt\", \"No Data Found\"], [1, \"header-3\", \"fw-600\", \"text-center\"], [1, \"flex-center\", \"h-100\"]],\n  template: function StatusReportComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, StatusReportComponent_div_0_Template, 61, 37, \"div\", 0);\n      i0.ɵɵtemplate(1, StatusReportComponent_ng_template_1_Template, 2, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.canViewAllUsers || ctx.canViewReportees);\n    }\n  },\n  dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i8.NgTemplateOutlet, i9.NgSelectComponent, i9.NgOptionComponent, i9.NgOptionTemplateDirective, i9.NgLabelTemplateDirective, i10.OwlDateTimeTriggerDirective, i10.OwlDateTimeInputDirective, i10.OwlDateTimeComponent, i11.PaginationComponent, i12.AppImageURLDirective, i13.ResizableDropdownDirective, i14.ApplicationLoaderComponent, i15.AgGridAngular, i16.DefaultValueAccessor, i16.CheckboxControlValueAccessor, i16.NgControlStatus, i16.NgModel, i17.ReportGraphComponent, i8.KeyValuePipe, i18.TranslatePipe],\n  encapsulation: 2\n});", "map": {"version": 3, "mappings": ";AAAA,SAEEA,YAFF,EAKEC,WALF,QAOO,eAPP;AAYA,SAASC,OAAT,EAAkBC,cAAlB,EAAkCC,GAAlC,EAAuCC,SAAvC,EAAkDC,SAAlD,EAA6DC,IAA7D,EAAmEC,SAAnE,QAAoF,MAApF;AAEA,OAAO,KAAKC,MAAZ,MAAwB,QAAxB;AAEA,SACEC,SADF,EAEEC,iBAFF,EAGEC,wBAHF,EAIEC,YAJF,EAKEC,eALF,QAMO,uBANP;AAOA,SACEC,iBADF,EAEEC,UAFF,EAGEC,cAHF,QAIO,kBAJP;AAOA,SACEC,YADF,EAEEC,cAFF,EAGEC,QAHF,EAIEC,mBAJF,EAKEC,mBALF,EAMEC,eANF,EAOEC,uBAPF,EAQEC,cARF,EASEC,iBATF,EAUEC,eAVF,EAWEC,YAXF,QAYO,gCAZP;AAaA,SAASC,eAAT,QAAgC,0DAAhC;AACA,SAASC,aAAT,EAAwBC,oBAAxB,EAA8CC,2BAA9C,EAA2EC,0BAA3E,QAA6G,0DAA7G;AACA,SACEC,eADF,EAGEC,eAHF,EAIEC,gBAJF,EAKEC,kBALF,QAMO,oCANP;AAOA,SACEC,4BADF,EAEEC,aAFF,EAGEC,sBAHF,EAIEC,gBAJF,EAKEC,yBALF,EAMEC,aANF,EAOEC,sBAPF,EAQEC,cARF,EASEC,uBATF,EAUEC,gBAVF,EAWEC,yBAXF,QAYO,oCAZP;AAaA,SAASC,cAAT,QAA+B,mDAA/B;AACA,SACEC,sBADF,EAEEC,gBAFF,EAGEC,sBAHF,EAIEC,uBAJF,QAKO,0CALP;AAMA,SACEC,yBADF,EAEEC,kCAFF,EAGEC,mCAHF,EAIEC,mBAJF,EAKEC,4BALF,EAMEC,qBANF,QAOO,0CAPP;AAQA,SAEEC,mBAFF,EAGEC,4BAHF,QAIO,wCAJP;AAKA,SACEC,8BADF,EAEEC,6BAFF,QAGO,sCAHP;AAIA,SACEC,4BADF,EAEEC,qCAFF,EAGEC,mBAHF,EAIEC,2BAJF,EAKEC,oCALF,QAMO,sCANP;AAOA,SAASC,mBAAT,QAAoC,6DAApC;AACA,SAASC,WAAT,QAA4B,8BAA5B;;;;;;;;;;;;;;;;;;;;;;;;;;IC5FQC;IACEA;IACEA;MAAA;MAAA;MAAA;MAAA,OAASA,yEAA8C,IAA9C,EAAT;IAA4D,CAA5D;IACAA,gCAAkC,CAAlC,EAAkC,GAAlC;IAC6EA;IACYA;IACvFA;IACgCA;IAA0BA;IAGhEA;;;;;;IATOA;IAAAA;IAGEA;IAAAA;IAA6EA;IAAAA,iCAAkB,UAAlB,EAAkBC,8CAAlB;IAE1ED;IAAAA;IAC0BA;IAAAA;;;;;;;;IA4B1BA;IAAkDA;MAAA;MAAA;MAAA;MAAA,OAASA,mCAAT;IAAoB,CAApB;IAAsBA;IACxEA;IAA8BA;IACbA;;;;;IADaA;IAAAA;;;;;;IAU5BA;IAAmDA;IAAYA;;;;;;IANjEA,gCAA0B,CAA1B,EAA0B,KAA1B,EAA0B,EAA1B;IACkCA,6BACiC,CADjC,EACiC,MADjC,EACiC,EADjC;IAEHA;IAAyCA;IACjDA;IAErBA;IACFA;;;;;;;IANyDA;IAAAA,wDAAmB,aAAnB,EAAmB,OAAnB,EAAmBE,SAAnB,EAAmB,EAAnB;IACjBF;IACgCA;IAAAA;IAGzCA;IAAAA;;;;;;IAc/BA;IAAgCA,6BACiC,CADjC,EACiC,MADjC,EACiC,EADjC;IAEHA;IAAyCA;IAAqBA;;;;;;;IAFpCA;IAAAA,wDAAmB,aAAnB,EAAmB,OAAnB,EAAmBG,SAAnB,EAAmB,EAAnB;IACjBH;IACgCA;IAAAA;;;;;;IActEA;IAAgCA,6BACiC,CADjC,EACiC,MADjC,EACiC,EADjC;IAEHA;IAAyCA;IAAQA;;;;;;;IAFvBA;IAAAA,wDAAmB,aAAnB,EAAmB,OAAnB,EAAmBI,SAAnB,EAAmB,EAAnB;IACjBJ;IACgCA;IAAAA;;;;;;IAatEA;IAAgCA,6BACiC,CADjC,EACiC,MADjC,EACiC,EADjC;IAEHA;IAAyCA;IAAQA;;;;;;;IAFvBA;IAAAA,wDAAmB,aAAnB,EAAmB,OAAnB,EAAmBK,SAAnB,EAAmB,EAAnB;IACjBL;IACgCA;IAAAA;;;;;;IA8BtEA;IAAgCA,6BACiC,CADjC,EACiC,MADjC,EACiC,EADjC;IAEHA;IAAyCA;IAAQA;;;;;;;IAFvBA;IAAAA,wDAAmB,aAAnB,EAAmB,OAAnB,EAAmBM,SAAnB,EAAmB,EAAnB;IACjBN;IACgCA;IAAAA;;;;;;IActEA;IAAgCA,6BACiC,CADjC,EACiC,MADjC,EACiC,EADjC;IAEHA;IAAyCA;IAAQA;;;;;;;IAFvBA;IAAAA,wDAAmB,aAAnB,EAAmB,OAAnB,EAAmBO,SAAnB,EAAmB,EAAnB;IACjBP;IACgCA;IAAAA;;;;;;IAapEA;IACkBA;IAASA;;;;;IAAzBA;IAAgBA;IAAAA;;;;;;;;IAgBpBA;IACEA;MAAAA;MAAA;MAAA,OAASA,2CAAT;IAA4B,CAA5B;IACAA;IACFA;;;;;;;;IAKRA,gCAAmF,CAAnF,EAAmF,KAAnF,EAAmF,EAAnF,EAAmF,CAAnF,EAAmF,OAAnF,EAAmF,EAAnF,EAAmF,CAAnF,EAAmF,OAAnF,EAAmF,EAAnF;IAG6BA;MAAAA;MAAA;MAAA,OAAaA,4DAAb;IACrC,CADqC;IAAvBA;IACAA;IAA+BA;IACjCA;;;;;IAFyBA;IAAAA;;;;;;;;;;;;;;IApKnCA,gCAA2D,CAA3D,EAA2D,KAA3D,EAA2D,EAA3D,EAA2D,CAA3D,EAA2D,KAA3D,EAA2D,EAA3D,EAA2D,CAA3D,EAA2D,KAA3D,EAA2D,EAA3D,EAA2D,CAA3D,EAA2D,KAA3D,EAA2D,EAA3D,EAA2D,CAA3D,EAA2D,KAA3D,EAA2D,EAA3D;IAKmCA;;IAA4BA;IACrDA,kCAAuC,CAAvC,EAAuC,OAAvC,EAAuC,EAAvC;IACyBA;MAAAA;MAAA;MAAA,OAAaA,uDAAb;IACrC,CADqC;IAAvBA;IACAA;IAA+BA;;IACjCA;IAEFA,iCAA6C,EAA7C,EAA6C,WAA7C,EAA6C,EAA7C;IAIIA;MAAAA;MAAA;MAAA,OAAaA,oDAAb;IAAwC,CAAxC;IACAA;IAKAA;IAUFA;IAGJA,iCAA2C,EAA3C,EAA2C,KAA3C,EAA2C,EAA3C;IAC2BA;;IAAgCA;IACzDA,iCAA2B,EAA3B,EAA2B,WAA3B,EAA2B,EAA3B;IAGkDA;MAAAA;MAAA;MAAA,OAAaA,sDAAb;IAC3D,CAD2D,EAAmC,QAAnC,EAAmC;MAAAA;MAAA;MAAA,OACvEA,yCADuE;IACtD,CADmB;;IAE9CA;IAMFA;IAGJA,iCAA2C,EAA3C,EAA2C,KAA3C,EAA2C,EAA3C;IAC2BA;;IAAkCA;IAC3DA,iCAA2B,EAA3B,EAA2B,WAA3B,EAA2B,EAA3B;IAIIA;MAAAA;MAAA;MAAA,OAAaA,yDAAb;IACd,CADc;;IACAA;IAMFA;IAGJA,iCAA2C,EAA3C,EAA2C,KAA3C,EAA2C,EAA3C;IAC2BA;;IAAmCA;IAC5DA,iCAAiC,EAAjC,EAAiC,WAAjC,EAAiC,EAAjC;IAGiEA;MAAAA;MAAA;MAAA,OAAaA,uDAAb;IAC3E,CAD2E;;IAC7DA;IAMFA;IAmBJA,iCAA2C,EAA3C,EAA2C,KAA3C,EAA2C,EAA3C;IAC2BA;IAAKA;IAC9BA,iCAA2B,EAA3B,EAA2B,WAA3B,EAA2B,EAA3B;IAIIA;MAAAA;MAAA;MAAA,OAAaA,qDAAb;IACd,CADc;;IACAA;IAMFA;IAGJA,iCAA2C,EAA3C,EAA2C,KAA3C,EAA2C,EAA3C;IAC2BA;IAAIA;IAC7BA,iCAA2B,EAA3B,EAA2B,WAA3B,EAA2B,EAA3B;IAIIA;MAAAA;MAAA;MAAA,OAAaA,qDAAb;IACd,CADc;;IACAA;IAMFA;IAGJA,iCAA2C,EAA3C,EAA2C,KAA3C,EAA2C,EAA3C;IAC2BA;;IAAwCA;IACjEA,iCAAmB,EAAnB,EAAmB,KAAnB,EAAmB,EAAnB,EAAmB,EAAnB,EAAmB,KAAnB,EAAmB,EAAnB,EAAmB,EAAnB,EAAmB,WAAnB,EAAmB,EAAnB;IAI0BA;MAAAA;MAAA;MAAA,OAAaA,uDAAb;IACpC,CADoC;IAClBA;IAEFA;IAEFA,iCAA4E,EAA5E,EAA4E,KAA5E,EAA4E,EAA5E;IAGIA;IACAA;IAIEA;MAAAA;MAAA;MAAA,OAAiBA,mDAAjB;IAA4C,CAA5C;IAJFA;IAMAA;IACEA;MAAAA;MAAA;MAAA,OAAmBA,2DAAnB;IAA8C,CAA9C;IAAgDA;IAEpDA;IAIFA;IAINA;IAQFA;IAEFA,iCAA6C,EAA7C,EAA6C,GAA7C,EAA6C,EAA7C;IACwDA;MAAAA;MAAA;MAAA,OAASA,2CAAT;IAA4B,CAA5B;IAA8BA;;IACtEA;IACdA;IAAyBA;MAAAA;MAAA;MAAA,OAASA,+BAAT;IAAgB,CAAhB;IAAkBA;;IAAgCA;IAC3EA;IAA+BA;MAAAA;MAAA;MAAA,OAASA,6CAAT;IAA8B,CAA9B;IAAgCA;;IAAiCA;;;;;;;IA1KjEA;IAAAA;IAEAA;IAAAA;IACQA;IAAAA;IAItBA;IAAAA,qCAAsB,OAAtB,EAAsBQ,+DAAtB,EAAsB,SAAtB,EAAsBR,8GAAtB,EAAsB,UAAtB,EAAsB,IAAtB,EAAsB,eAAtB,EAAsB,KAAtB,EAAsB,SAAtB,EAAsBQ,0BAAtB;IAuBYR;IAAAA;IAIrBA;IAAAA;IAFSA,qCAAsB,OAAtB,EAAsBQ,kBAAtB,EAAsB,UAAtB,EAAsB,IAAtB,EAAsB,eAAtB,EAAsB,KAAtB,EAAsB,SAAtB,EAAsBR,oDAAtB,EAAsB,SAAtB,EAAsBQ,4BAAtB;IAcYR;IAAAA;IAIHA;IAAAA;IAFTA,qCAAsB,OAAtB,EAAsBQ,oBAAtB,EAAsB,SAAtB,EAAsBR,6DAAtB,EAAsB,UAAtB,EAAsB,IAAtB,EAAsB,eAAtB,EAAsB,KAAtB,EAAsB,SAAtB,EAAsBQ,+BAAtB;IAcYR;IAAAA;IAIrBA;IAAAA;IAFSA,qCAAsB,OAAtB,EAAsBQ,kBAAtB,EAAsB,SAAtB,EAAsBR,wDAAtB,EAAsB,UAAtB,EAAsB,IAAtB,EAAsB,eAAtB,EAAsB,KAAtB,EAAsB,SAAtB,EAAsBQ,6BAAtB;IAiCTR;IAAAA;IAFSA,qCAAsB,OAAtB,EAAsBQ,aAAtB,EAAsB,SAAtB,EAAsBR,oDAAtB,EAAsB,UAAtB,EAAsB,IAAtB,EAAsB,eAAtB,EAAsB,KAAtB,EAAsB,SAAtB,EAAsBQ,2BAAtB;IAkBTR;IAAAA;IAFSA,qCAAsB,OAAtB,EAAsBQ,aAAtB,EAAsB,SAAtB,EAAsBR,oDAAtB,EAAsB,UAAtB,EAAsB,IAAtB,EAAsB,eAAtB,EAAsB,KAAtB,EAAsB,SAAtB,EAAsBQ,2BAAtB;IAcYR;IAAAA;IAIRA;IAAAA,qCAAsB,YAAtB,EAAsB,KAAtB,EAAsB,SAAtB,EAAsBQ,6BAAtB;IAEqDR;IAAAA;IAOZA;IAAAA;IAGhDA;IAAAA,kJAA8G,oBAA9G,EAA8GS,IAA9G,EAA8G,aAA9G,EAA8GA,IAA9G,EAA8G,YAA9G,EAA8G,OAA9G,EAA8G,SAA9G,EAA8GD,yBAA9G,EAA8G,UAA9G,EAA8G,8BAA9G;IAIaR;IAAAA;IAGXA;IAAAA;IAQwCA;IAAAA;IAW4BA;IAAAA;IAEzCA;IAAAA;IACoBA;IAAAA;;;;;;;;IAwB7DA;IACmCA;MAAAA;MAAA;MAAA,OAASA,0CAAT;IAA2B,CAA3B;IACjCA;IAAKA;;IAAkCA;IACvCA;IAAiCA;IAASA;;;;IADrCA;IAAAA;;;;;;IAKLA;IACEA;IAAKA;;IAAkCA;IACvCA;IAAiCA;IAAQA;IAC3CA;;;;IAFOA;IAAAA;;;;;;IAKHA;IACEA;IACFA;;;;;;;;;;IAHFA;IACEA;IAGFA;;;;IAHgCA;IAAAA;;;;;;;;IARpCA;IACmCA;MAAAA;MAAA;MAAA,OAASA,uCAAgBU,0BAAhB,CAAT;IAA2C,CAA3C;IACjCV;IAIAA;IAOFA;;;;;;;IAZgFA;IAC/DA;IAAAA,4CAAoB,UAApB,EAAoBW,IAApB;;;;;;;;;;;;IATnBX;IAEEA;IAKAA;IAcFA;;;;;IApBEA;IAEIA;IAAAA;IAKAA;IAAAA;;;;;;IAgBJA;;;;;;IACDA;;;;;;IAkBOA;IAAgCA,6BACiC,CADjC,EACiC,MADjC,EACiC,EADjC;IAEHA;IAAcA;;;;;;;IAFYA;IAAAA,wDAAmB,aAAnB,EAAmB,OAAnB,EAAmBY,SAAnB,EAAmB,EAAnB;IACjBZ;IACTA;IAAAA;;;;;;IAiBjCA;IACEA;IAAYA;;;;;IAD6DA;IACzEA;IAAAA;;;;;;;;IAUAA;IAEEA;IAGAA;IACEA;MAAA;MAAA;MAAA;MAAA;MAAA,OAASA,iEAAT;IAA0C,CAA1C;IAA4CA;;;;;;;IAJ9CA;IAAAA;;;;;;IAHJA;IACEA;IAQFA;;;;;;IAPsBA;IAAAA;;;;;;;;IAL5BA;IACEA,iCAAuC,CAAvC,EAAuC,aAAvC,EAAuC,GAAvC;IAEIA;;IAUFA;IACAA;IACEA;MAAAA;MAAA;MAAA,OAASA,+BAAT;IAAgB,CAAhB;IAAkBA;;;IACpBA;IAEJA;;;;;IAf6CA;IAAAA;IAYrBA;IAAAA;;;;;;IAepBA;IAA0CA;;;;;IAGuDA;;;;;IAHvDA;IAAAA;;;;;;;;IAT9CA,4BAAkE,CAAlE,EAAkE,KAAlE,EAAkE,GAAlE,EAAkE,CAAlE,EAAkE,iBAAlE,EAAkE,GAAlE,EAAkE,GAAlE;IAI4EA;MAAAA;MAAA;MAAA,OAAaA,4CAAb;IAAgC,CAAhC,EAAiC,kBAAjC,EAAiC;MAAAA;MAAA;MAAA,OACnFA,8CADmF;IAC9D,CAD6B;IAExEA;IAEFA;IACEA;IAIAA;IACEA;MAAAA;MAAA;MAAA,OAAcA,6CAAd;IAAkC,CAAlC;IACFA;IAGJA,4BAAkE,CAAlE,EAAkE,cAAlE,EAAkE,GAAlE,EAAkE,GAAlE;IAEmFA;MAAAA;MAAA;MAAA,6CAA+B,IAA/B;IAAmC,CAAnC,EAAoC,gBAApC,EAAoC;MAAAA;MAAA;MAAA,6CAAiC,KAAjC;IAAsC,CAA1E;IAA4EA;;;;;IApB1JA;IAEgDA;IAAAA,kCAAmB,oBAAnB,EAAmBa,oBAAnB,EAAmB,aAAnB,EAAmBA,mBAAnB,EAAmB,SAAnB,EAAmBA,eAAnB,EAAmB,yBAAnB,EAAmB,IAAnB,EAAmB,4BAAnB,EAAmB,IAAnB,EAAmB,0BAAnB,EAAmB,IAAnB;IAO7Bb;IAAAA;IAIRA;IAAAA,4CAAmB,OAAnB,EAAmB,CAAnB,EAAmB,OAAnB,EAAmB,CAAnB,EAAmB,MAAnB,EAAmBa,0DAAnB;IAKXb;IAAAA;IACYA;IAAAA,iDAA0B,SAA1B,EAA0Ba,eAA1B,EAA0B,aAA1B,EAA0BA,mBAA1B,EAA0B,yBAA1B,EAA0BA,+BAA1B;;;;;;IAMjBb;IAEEA;IACFA;;;;;;;;IADgBA;IAAAA;;;;;;IAEhBA;IAEEA;IACAA;IAAyCA;;IAAwCA;;;;IAAxCA;IAAAA;;;;;;IAT7CA;IAEEA;IAIAA;IAKFA;;;;;IATQA;IAAAA;IAIAA;IAAAA;;;;;;;;IAnVdA,4BAAiD,CAAjD,EAAiD,KAAjD,EAAiD,CAAjD,EAAiD,CAAjD,EAAiD,KAAjD,EAAiD,CAAjD,EAAiD,CAAjD,EAAiD,IAAjD,EAAiD,CAAjD;IAKQA;IAWFA;IAKJA;IACEA;IAoLAA,+BAAmB,CAAnB,EAAmB,KAAnB,EAAmB,CAAnB;IAEIA;IACAA,mCAAuC,EAAvC,EAAuC,OAAvC,EAAuC,EAAvC;IACyBA;MAAAA;MAAA;MAAA;IAAA,GAA+B,QAA/B,EAA+B;MAAAA;MAAA;MAAA,OAAWA,kCAAX;IAAoB,CAAnD;IAAvBA;IACAA;IAA+BA;IACjCA;IAEFA,iCAAuF,EAAvF,EAAuF,KAAvF,EAAuF,EAAvF;IAEIA;IACEA;IACEA;IACAA;IAAoCA;MAAAA;MAAA;MAAA,OAAWA,yCAAX;IAA2B,CAA3B,EAA4B,OAA5B,EAA4B;MAAAA;MAAA;MAAA,OAAUA,6CAAV;IAA8B,CAA1D,EAA4B,eAA5B,EAA4B;MAAAA;MAAA;MAAA;IAAA,CAA5B;IAApCA;IAGFA;IAAqDA;;IAA8CA;IACrGA;IACAA;IAsBAA;IACEA;MAAAA;MAAA;MAAA,OAASA,qCAAT;IAAqB,CAArB;IACAA;IACDA;IAEDA;IAEFA,iCAA8B,EAA9B,EAA8B,KAA9B,EAA8B,EAA9B;IAEIA;MAAAA;;MAAA;;MAAA;MAAA,OAASA,iDAAT;IAA6C,CAA7C;IACAA;IACAA;IAA+BA;;IAA2CA;IAE5EA,iCAA6E,EAA7E,EAA6E,MAA7E,EAA6E,EAA7E,EAA6E,EAA7E,EAA6E,MAA7E,EAA6E,EAA7E;IAC2FA;IACvFA;IAAOA;IAAOA;IAChBA,iCAA6C,EAA7C,EAA6C,WAA7C,EAA6C,EAA7C;IAGIA;MAAAA;MAAA;MAAA,OAAUA,kDAAV;IAAmC,CAAnC;IACAA;IAKFA;IAGJA;IACEA;MAAAA;MAAA;MAAA,OAASA,6CAAT;IAA6B,CAA7B;IACAA;IAAwBA;;IAAkCA;IAAQA;IAEpEA;IACAA,iCAAgE,EAAhE,EAAgE,MAAhE,EAAgE,EAAhE,EAAgE,EAAhE,EAAgE,MAAhE,EAAgE,EAAhE;IACkFA;;IAE1EA;IAAQA;;IACAA;IACdA;IACoBA;MAAAA;MAAA;MAAA;IAAA,GAA8B,QAA9B,EAA8B;MAAAA;MAAA;MAAA,OAAWA,sCAAX;IAAwB,CAAtD;IAClBA;IAEFA;IAINA;IACEA;IAmBFA;IACAA;IAwBAA;IAYFA;;;;;;;IAtVEA;IAAAA;IAE4CA;IAAAA;IAyMjBA;IAAAA;IAUgBA;IAAAA;IAEgBA;IAAAA;IAEjDA;IAAAA;IAwBqDA;IAAAA;IACIA;IAAAA;IAQ9BA;IAAAA;IAMlBA;IAAAA,qCAAsB,OAAtB,EAAsBc,cAAtB,EAAsB,UAAtB,EAAsB,IAAtB,EAAsB,YAAtB,EAAsB,KAAtB,EAAsB,eAAtB,EAAsB,KAAtB,EAAsB,SAAtB,EAAsBA,qBAAtB;IAaWd;IAAAA;IAIwDA;IAAAA;IAElEA;IAAAA;IAEHA;IAAAA,qCAAsB,aAAtB,EAAsBc,eAAtB,EAAsB,SAAtB,EAAsBA,uBAAtB,EAAsB,YAAtB,EAAsB,KAAtB;IAE8Cd;IAAAA;IAMlBA;IAAAA;IAC5BA;IAAAA;IA6CdA;IAAAA,sLAAyG,UAAzG,EAAyGe,IAAzG;;;;;;IAePf;IACEA;IACFA;;;;ADrPF,OAAM,MAAOgB,qBAAP,CAA4B;EAsEhCC,YACUC,kBADV,EAEUC,MAFV,EAGUC,WAHV,EAIUC,SAJV,EAKUC,MALV,EAMUC,YANV,EAOUC,QAPV,EAQUC,gBARV,EAQ4C;IAPlC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IA7EF,eAA8B,IAAIhG,YAAJ,EAA9B;IACD,yBAAoB,IAAIE,OAAJ,EAApB;IACP,mBAA0B,EAA1B;IACA,sBAAqD,EAArD;IAIA,uBAAiCW,YAAjC;IACA,gBAAmBH,SAAnB;IAEA,kBAAqB,CAArB;IAEA,mBAAiC,OAAjC;IAEA,eAAsB,EAAtB;IAGA,+BAAiC,EAAjC;IACA,yBAA6B,KAA7B;IACA,uBAA2B,KAA3B;IACA,wBAA4B,KAA5B;IACA,0BAA8B,KAA9B;IAEA,gBAAWU,QAAX;IACA,oBAA8BT,iBAAiB,CAACsF,KAAlB,CAAwB,CAAxB,EAA2B,CAA3B,CAA9B;IACA,sBAAgCnF,eAAe,CAACmF,KAAhB,CAAsB,CAAtB,EAAyB,CAAzB,CAAhC;IAGA,gBAAuB,EAAvB;IACA,qBAA4B,EAA5B;IACA,aAAoB,EAApB;IACA,iBAAwB,EAAxB;IACA,mBAAuB,IAAvB;IACA,6BAAiC,IAAjC;IACA,yBAA6B,IAA7B;IACA,8BAAkC,IAAlC;IACA,iCAAqC,IAArC;IACA,4BAAgC,IAAhC;IACA,wBAAmC,EAAnC;IACA,iCAAqC,IAArC;IACA,wBAA4B,IAA5B;IACA,mBAAuB,KAAvB;IACA,cAASxF,MAAT;IACA,6BAAwBG,wBAAxB;IAEA,uBAA2B,IAA3B;IAEA,uBAA2B,IAA3B;IAKA,wBAA4B,IAA5B;IAGA,6BAAiC,KAAjC;IACA,+BAAmC,IAAnC;IACA,mCAAuC,IAAvC;IACA,oBAAegB,YAAf;IACA,mBAAoB,IAAIsE,IAAJ,EAApB;IACA,cAAc,IAAIA,IAAJ,EAAd;IACA,gBAAgB,IAAIA,IAAJ,EAAhB;IACA,sBAAiBzE,cAAjB;IAEA,mBAAsB6C,WAAW,CAAC6B,gBAAlC;EAcK;;EACCC,QAAQ;IAAA;;IAAA;MACZ,KAAI,CAACC,kBAAL,SAAgClG,cAAc,CAC5C,KAAI,CAACuF,MAAL,CACGY,MADH,CACUrE,0BADV,EAEGsE,IAFH,CAEQlG,SAAS,CAAEmG,IAAD,IAAU,CAACC,MAAM,CAACC,IAAP,CAAYF,IAAZ,EAAkBG,MAA9B,CAFjB,CAD4C,CAA9C;;MAKA,KAAI,CAAChB,WAAL,CAAiBiB,QAAjB,CAA0B,uBAA1B;;MACA,KAAI,CAAChB,SAAL,CAAegB,QAAf,CAAwB,eAAxB;;MACA,KAAI,CAACC,WAAL,GAAmB,KAAI,CAACpB,kBAAL,CAAwBqB,eAAxB,CAAwC,KAAxC,CAAnB;;MACA,KAAI,CAACpB,MAAL,CACGY,MADH,CACUpC,mBADV,EAEGqC,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcR,IAAD,IAAc;;;QACvB,KAAI,CAACS,QAAL,GAAgBT,IAAhB;QACA,KAAI,CAACU,WAAL,GAAmB/F,cAAc,CAC/B,iBAAI,CAAC8F,QAAL,MAAa,IAAb,IAAaE,aAAb,GAAa,MAAb,GAAaA,GAAEC,YAAf,MAA2B,IAA3B,IAA2BC,aAA3B,GAA2B,MAA3B,GAA2BA,GAAEC,aADE,CAAjC;MAGD,CARH;;MAUA,KAAI,CAAC5B,MAAL,CACGY,MADH,CACUxE,aADV,EAEGyE,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcO,UAAD,IAAoB;QAC7B,IAAIA,UAAJ,EAAgB;UACd,MAAMC,cAAc,GAAGD,UAAU,CAC9BE,MADoB,CACZC,MAAD,IAAiBA,MAAM,CAACC,SADX,EAEpBC,IAFoB,CAEf,CAACC,CAAD,EAASC,CAAT,KAAoBD,CAAC,SAAD,KAAC,WAAD,GAAC,MAAD,IAAC,CAAEE,WAAH,CAAeC,aAAf,CAA6BF,CAAC,SAAD,KAAC,WAAD,GAAC,MAAD,IAAC,CAAEC,WAAhC,CAFL,CAAvB;UAGA,KAAI,CAACE,WAAL,GAAmB,CAAC,GAAGT,cAAJ,CAAnB;QACD,CALD,MAKO;UACL,KAAI,CAACS,WAAL,GAAmB,EAAnB;QACD;;QACD,KAAI,CAACC,eAAL;MACD,CAbH;;MAeA,KAAI,CAACxC,MAAL,CACGY,MADH,CACUvE,oBADV,EAEGwE,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcmB,OAAD,IAAqB;QAC9B,KAAI,CAACC,gBAAL,GAAwBD,OAAxB;MACD,CALH;;MAOA,MAAM,KAAI,CAACzC,MAAL,CACHY,MADG,CACItE,2BADJ,EAEHuE,IAFG,CAGFlG,SAAS,CAAEgI,SAAD,IAAuB;QAC/B,OAAOA,SAAP;MACD,CAFQ,CAHP,EAMF9H,IAAI,CAAC,CAAD,CANF,EAQH+H,SARG,EAAN;MASA,KAAI,CAACC,qBAAL,SAAmC,KAAI,CAAC7C,MAAL,CAChCY,MADgC,CACzBhE,4BADyB,EAEhCiE,IAFgC,CAG/BnG,GAAG,CAAEoG,IAAD,IAAeA,IAAhB,CAH4B,EAI/BjG,IAAI,CAAC,CAAD,CAJ2B,EAMhC+H,SANgC,EAAnC;;MAQA,KAAI,CAAC5C,MAAL,CACGY,MADH,CACU3C,qBADV,EAEG4C,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcR,IAAD,IAAc;;;QACvB,KAAI,CAACgC,cAAL,GAAmB/B,gCAAQD,IAAR,GAAY;UAAEiC,sBAAsB,EAAE;QAA1B,CAAZ,CAAnB;QACA,KAAI,CAACC,QAAL,GAAgB,WAAI,CAACF,cAAL,MAAmB,IAAnB,IAAmBrB,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEuB,QAArC;QACA,MAAMC,UAAU,GACd,YAAI,CAACH,cAAL,MAAmB,IAAnB,IAAmBnB,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEsB,UAArB,MAAoCC,SAApC,GACI,CADJ,GAEI,WAAI,CAACJ,cAAL,MAAmB,IAAnB,IAAmBK,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEF,UAH3B;QAIA,KAAI,CAACG,aAAL,GAAkBrC,gCACb,KAAI,CAACqC,aADQ,GACK;UACrBC,UAAU,EAAE,WAAI,CAACP,cAAL,MAAmB,IAAnB,IAAmBQ,aAAnB,GAAmB,MAAnB,GAAmBA,GAAED,UADZ;UAErBL,QAAQ,EAAE,WAAI,CAACF,cAAL,MAAmB,IAAnB,IAAmBS,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEP,QAFV;UAGrBC,UAAU,EAAEA,UAHS;UAIrBO,UAAU,EAAE,WAAI,CAACV,cAAL,MAAmB,IAAnB,IAAmBW,aAAnB,GAAmB,MAAnB,GAAmBA,GAAER,UAJZ;UAKrBS,QAAQ,EAAEnI,cAAc,CAACoI,MAAM,CAAC,WAAI,CAACb,cAAL,MAAmB,IAAnB,IAAmBc,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEF,QAAtB,CAAP,CALH;UAMrBG,IAAI,EAAE,CACJ7H,iBAAiB,CACf,WAAI,CAAC8G,cAAL,MAAmB,IAAnB,IAAmBgB,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,QADN,EAEf,iBAAI,CAACxC,QAAL,MAAa,IAAb,IAAayC,aAAb,GAAa,MAAb,GAAaA,GAAEtC,YAAf,MAA2B,IAA3B,IAA2BuC,aAA3B,GAA2B,MAA3B,GAA2BA,GAAErC,aAFd,CADb,EAKJ5F,iBAAiB,CACf,WAAI,CAAC8G,cAAL,MAAmB,IAAnB,IAAmBoB,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,MADN,EAEf,iBAAI,CAAC5C,QAAL,MAAa,IAAb,IAAa6C,aAAb,GAAa,MAAb,GAAaA,GAAE1C,YAAf,MAA2B,IAA3B,IAA2B2C,aAA3B,GAA2B,MAA3B,GAA2BA,GAAEzC,aAFd,CALb,CANe;UAgBrB0C,QAAQ,EAAE,WAAI,CAACxB,cAAL,MAAmB,IAAnB,IAAmByB,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,UAhBV;UAiBrBC,KAAK,EAAE,WAAI,CAAC3B,cAAL,MAAmB,IAAnB,IAAmB4B,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,OAjBP;UAkBrBC,MAAM,EAAE,WAAI,CAAC9B,cAAL,MAAmB,IAAnB,IAAmB+B,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,UAlBR;UAmBrBC,OAAO,EAAE,WAAI,CAACjC,cAAL,MAAmB,IAAnB,IAAmBkC,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,OAnBT;UAoBrBC,UAAU,EAAE,WAAI,CAACpC,cAAL,MAAmB,IAAnB,IAAmBqC,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,UApBZ;UAqBrBC,QAAQ,EAAE,WAAI,CAACvC,cAAL,MAAmB,IAAnB,IAAmBwC,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,QArBV;UAsBrBC,MAAM,EAAE,WAAI,CAAC1C,cAAL,MAAmB,IAAnB,IAAmB2C,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,MAtBR;UAuBrBC,MAAM,EAAE,WAAI,CAAC7C,cAAL,MAAmB,IAAnB,IAAmB8C,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,MAvBR;UAwBrBC,aAAa,EAAE,iBAAI,CAAChD,cAAL,MAAmB,IAAnB,IAAmBiD,aAAnB,GAAmB,MAAnB,GAAmBA,GAAED,aAArB,MAAkC,IAAlC,IAAkCE,aAAlC,GAAkCA,EAAlC,GAAsC,IAxBhC;UAyBrBC,oBAAoB,EAAE,WAAI,CAACnD,cAAL,MAAmB,IAAnB,IAAmBoD,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC;QAzBtB,CADL,CAAlB;MA4BD,CAtCH;;MAuCA,KAAI,CAACnG,MAAL,CACGY,MADH,CACUnC,2BADV,EAEGoC,IAFH,CAGI/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAHb,EAIIzG,SAAS,CAAEkG,IAAD,IAAc;QACtB,MAAMsF,SAAS,GAAGtF,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEpG,GAAN,CAAW2L,IAAD,IAAc;UACxCA,IAAI,mCACCA,IADD,GACK;YACPC,QAAQ,EAAED,IAAI,CAACE,SAAL,GAAiB,GAAjB,GAAuBF,IAAI,CAACG;UAD/B,CADL,CAAJ;UAIA,OAAOH,IAAP;QACD,CANiB,CAAlB;QAOA,KAAI,CAAC5B,KAAL,GAAa2B,SAAb;QACA,KAAI,CAACK,QAAL,GAAgBL,SAAhB;QACA,KAAI,CAACK,QAAL,GAAgBjL,YAAY,CAAC,KAAI,CAACiL,QAAN,EAAgB,EAAhB,CAA5B;QACA,OAAO,KAAI,CAACzG,MAAL,CACJY,MADI,CACGlC,oCADH,EAEJmC,IAFI,CAEC/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFV,CAAP;MAGD,CAdQ,CAJb,EAoBGC,SApBH,CAoBcqB,SAAD,IAAuB;QAChC,KAAI,CAAC+D,iBAAL,GAAyB/D,SAAzB;;QACA,IAAI,CAACA,SAAL,EAAgB;UACd,KAAI,CAACgE,iBAAL,CAAuB,CAAvB,EAA0B,KAA1B;QACD;MACF,CAzBH;;MA2BA,KAAI,CAAC3G,MAAL,CACGY,MADH,CACUtC,4BADV,EAEGuC,IAFH,CAGI/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAHb,EAIIzG,SAAS,CAAEkG,IAAD,IAAc;QACtB,MAAMsF,SAAS,GAAGtF,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEpG,GAAN,CAAW2L,IAAD,IAAc;UACxCA,IAAI,mCACCA,IADD,GACK;YACPC,QAAQ,EAAED,IAAI,CAACE,SAAL,GAAiB,GAAjB,GAAuBF,IAAI,CAACG;UAD/B,CADL,CAAJ;UAIA,OAAOH,IAAP;QACD,CANiB,CAAlB;QAOA,KAAI,CAACO,SAAL,GAAiBR,SAAjB;QACA,KAAI,CAACS,aAAL,GAAqBT,SAArB;QACA,KAAI,CAACS,aAAL,GAAqBrL,YAAY,CAAC,KAAI,CAACqL,aAAN,EAAqB,EAArB,CAAjC;QACA,OAAO,KAAI,CAAC7G,MAAL,CACJY,MADI,CACGrC,qCADH,EAEJsC,IAFI,CAEC/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFV,CAAP;MAGD,CAdQ,CAJb,EAoBGC,SApBH,CAoBcqB,SAAD,IAAuB;QAChC,KAAI,CAACmE,sBAAL,GAA8BnE,SAA9B;;QACA,IAAI,CAACA,SAAL,EAAgB;UACd,KAAI,CAACgE,iBAAL,CAAuB,CAAvB,EAA0B,KAA1B;QACD;MACF,CAzBH;;MA0BA,KAAI,CAAC3G,MAAL,CACGY,MADH,CACUzD,cADV,EAEG0D,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcR,IAAD,IAAc;QACvB,KAAI,CAACiG,WAAL,GAAmBjG,IAAI,CACpBP,KADgB,GAEhB2B,IAFgB,CAEX,CAACC,CAAD,EAASC,CAAT,KAAoBD,CAAC,CAACG,aAAF,CAAgBF,CAAhB,CAFT,CAAnB;MAGD,CAPH;;MAQA,KAAI,CAACpC,MAAL,CACGY,MADH,CACUxD,uBADV,EAEGyD,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcqB,SAAD,IAAuB;QAChC,KAAI,CAACqE,oBAAL,GAA4BrE,SAA5B;MACD,CALH;;MAMA,KAAI,CAAC3C,MAAL,CACGY,MADH,CACU5C,4BADV,EAEG6C,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcqB,SAAD,IAAmB;QAC5B,KAAI,CAACsE,qBAAL,GAA6BtE,SAA7B;MACD,CALH;;MAMA,KAAI,CAAC3C,MAAL,CACGY,MADH,CACU/C,kCADV,EAEGgD,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcqB,SAAD,IAAmB;QAC5B,KAAI,CAACuE,2BAAL,GAAmCvE,SAAnC;MACD,CALH;;MAMA,KAAI,CAAC3C,MAAL,CACGY,MADH,CACUrD,cADV,EAEGsD,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGc6F,WAAD,IAAqB;QAC9B,IAAI,EAACA,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAElG,MAAd,CAAJ,EAA0B;QAC1B,MAAMmG,cAAc,GAAG,IAAIC,GAAJ,CAAQF,WAAR,CAAvB;QACA,KAAI,CAACG,iBAAL,GAAyBF,cAAc,CAACG,GAAf,CACvB,oCADuB,CAAzB;QAGA,KAAI,CAACC,eAAL,GAAuBJ,cAAc,CAACG,GAAf,CACrB,kCADqB,CAAvB;QAGA,KAAI,CAACE,kBAAL,GAA0BL,cAAc,CAACG,GAAf,CACxB,qCADwB,CAA1B;QAGA,KAAI,CAACG,gBAAL,GAAwBN,cAAc,CAACG,GAAf,CACtB,mCADsB,CAAxB;;QAGA,IAAI,KAAI,CAACC,eAAT,EAA0B;UACxB,KAAI,CAACxH,MAAL,CAAY2H,QAAZ,CAAqB,IAAItJ,6BAAJ,EAArB;QACD,CAFD,MAEO,IAAI,KAAI,CAACqJ,gBAAT,EAA2B;UAChC,KAAI,CAAC1H,MAAL,CAAY2H,QAAZ,CAAqB,IAAIvJ,8BAAJ,EAArB;QACD;MACF,CAvBH;;MAwBA,KAAI,CAAC4B,MAAL,CACGY,MADH,CACUvD,gBADV,EAEGwD,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcR,IAAD,IAAc;QACvB,KAAI,CAAC8G,gBAAL,GAAwB9G,IAAxB;QACA,KAAI,CAAC+G,aAAL,GAAqB9G,MAAM,CAAC+G,MAAP,CAAchH,IAAd,EAClBiH,IADkB,GAElBhG,MAFkB,CAEVjB,IAAD,IAAeA,IAFJ,EAGlBP,KAHkB,GAIlB2B,IAJkB,CAIb,CAACC,CAAD,EAASC,CAAT,KAAoBD,CAAC,CAACG,aAAF,CAAgBF,CAAhB,CAJP,CAArB;;QAKA,KAAI,CAACI,eAAL;MACD,CAXH;;MAYA,KAAI,CAACxC,MAAL,CACGY,MADH,CACUtD,yBADV,EAEGuD,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcqB,SAAD,IAAuB;QAChC,KAAI,CAACqF,yBAAL,GAAiCrF,SAAjC;MACD,CALH;;MAMA,KAAI,CAACrC,gBAAL,CAAsB2H,YAAtB,CAAmC3G,SAAnC,CAA8C4G,IAAD,IAAS;QACpD,KAAI,CAACC,WAAL,GAAmBD,IAAnB;MACD,CAFD;;MAGA,KAAI,CAACE,sBAAL;;MACA,KAAI,CAACC,mBAAL;;MAEA,IAAI,KAAI,CAACxF,qBAAT,EAAgC;QAC9B,KAAI,CAAC7C,MAAL,CACGY,MADH,CACUhD,yBADV,EAEGiD,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcR,IAAD,IAAc;;;UACvB,KAAI,CAACwH,OAAL,GAAexH,IAAI,CAACpG,GAAL,CAAU6N,GAAD,IAAa;;;YACnC,IAAIC,QAAQ,GAAQ,EAApB;YACA,SAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEC,MAAL,MAAW,IAAX,IAAWhH,aAAX,GAAW,MAAX,GAAWA,GAAEiH,OAAF,CAAWD,MAAD,IAAgB;cACnCD,QAAQ,CAACC,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEE,iBAAT,CAAR,GAAsC,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEC,KAAR,KAAiB,CAAvD;cACAJ,QAAQ,CAAC,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEG,iBAAR,IAA4B,gBAA7B,CAAR,GACE,GAAGF,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEI,UAAU,EAArB,IAA2B,EAD7B;YAED,CAJU,CAAX;YAKA,uCACKN,GADL,GACQ;cACNC;YADM,CADR;UAID,CAXc,CAAf;UAYA,IAAIM,QAAQ,GAAQ;YAClBC,QAAQ,EAAE,OADQ;YAElBC,YAAY,EAAE,OAFI;YAGlBR,QAAQ,EAAE;UAHQ,CAApB;;UAKA,KAAI,CAACF,OAAL,CAAaI,OAAb,CAAsBH,GAAD,IAAa;;;YAChC,KAAK,IAAIU,GAAT,IAAgBV,GAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEC,QAArB,EAA+B;cAC7B,IAAI,EAAC,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEA,QAAV,MAAkB,IAAlB,IAAkB/G,aAAlB,GAAkB,MAAlB,GAAkBA,GAAGwH,GAAH,CAAnB,CAAJ,EAAgC;gBAC9BH,QAAQ,CAACN,QAAT,CAAkBS,GAAlB,IAAyB,CAAzB;cACD;;cACD,IAAI,CAACA,GAAG,CAACC,QAAJ,CAAa,gBAAb,CAAL,EACEJ,QAAQ,CAACN,QAAT,CAAkBS,GAAlB,KAA0B,UAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAET,QAAL,MAAa,IAAb,IAAa7G,aAAb,GAAa,MAAb,GAAaA,GAAGsH,GAAH,CAAb,KAAwB,CAAlD;YACH;UACF,CARD;;UASA,IAAI,YAAI,CAACX,OAAL,MAAY,IAAZ,IAAY7G,aAAZ,GAAY,MAAZ,GAAYA,GAAER,MAAd,IAAuB,CAA3B,EAA8B;YAC5B,WAAI,CAACqH,OAAL,MAAY,IAAZ,IAAY3G,aAAZ,GAAY,MAAZ,GAAYA,GAAEwH,IAAF,CAAOL,QAAP,CAAZ;UACD;QACF,CAjCH;;QAkCA,KAAI,CAAC9I,MAAL,CACGY,MADH,CACU9C,mCADV,EAEG+C,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcR,IAAD,IAAc;UACvB,KAAI,CAACsI,cAAL,GAAsBtI,IAAtB;QACD,CALH;MAMD,CAzCD,MA0CE,KAAI,CAACd,MAAL,CACGY,MADH,CACU7C,mBADV,EAEG8C,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcR,IAAD,IAAc;QACvB,KAAI,CAACwH,OAAL,GAAexM,uBAAuB,CAACgF,IAAI,CAACuI,KAAN,CAAtC;QACA,KAAI,CAACD,cAAL,GAAsBtI,IAAI,CAACwI,UAA3B;MACD,CANH;;MAQF,IAAI,KAAI,CAACzG,qBAAT,EAAgC,KAAI,CAAC0G,mBAAL;MAChC,KAAI,CAACC,gBAAL,GAAwB,EAAxB;;MACA,KAAI,CAACC,iBAAL,CAAuBnI,SAAvB,CAAiC,MAAK;QACpC,KAAI,CAAC8B,aAAL,CAAmBC,UAAnB,GAAgC,CAAhC;;QACA,KAAI,CAACqG,cAAL;MACD,CAHD;;MAKA,KAAI,CAAC1J,MAAL,CACGY,MADH,CACU/D,aADV,EAEGgE,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcR,IAAD,IAAc;QACvB,KAAI,CAAC0E,MAAL,GAAc1E,IAAI,CACfiB,MADW,CACHjB,IAAD,IAAeA,IADX,EAEXP,KAFW,GAGX2B,IAHW,CAGN,CAACC,CAAD,EAASC,CAAT,KAAoBD,CAAC,CAACG,aAAF,CAAgBF,CAAhB,CAHd,CAAd;MAID,CARH;;MASA,KAAI,CAACpC,MAAL,CACGY,MADH,CACU9D,sBADV,EAEG+D,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcR,IAAD,IAAkB;QAC3B,KAAI,CAAC6I,eAAL,GAAuB7I,IAAvB;MACD,CALH;;MAMA,KAAI,CAACd,MAAL,CACGY,MADH,CACU3D,aADV,EAEG4D,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcR,IAAD,IAAc;QACvB,KAAI,CAAC6E,MAAL,GAAc7E,IAAI,CACfiB,MADW,CACHjB,IAAD,IAAeA,IADX,EAEXP,KAFW,GAGX2B,IAHW,CAGN,CAACC,CAAD,EAASC,CAAT,KAAoBD,CAAC,CAACG,aAAF,CAAgBF,CAAhB,CAHd,CAAd;MAID,CARH;;MASA,KAAI,CAACpC,MAAL,CACGY,MADH,CACU1D,sBADV,EAEG2D,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcR,IAAD,IAAkB;QAC3B,KAAI,CAAC8I,eAAL,GAAuB9I,IAAvB;MACD,CALH;;MAOA,KAAI,CAACd,MAAL,CACGY,MADH,CACU7D,gBADV,EAEG8D,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcR,IAAD,IAAc;QACvB,KAAI,CAAC+I,WAAL,GAAmB/I,IAAI,CACpBiB,MADgB,CACRjB,IAAD,IAAeA,IADN,EAEhBP,KAFgB,GAGhB2B,IAHgB,CAGX,CAACC,CAAD,EAASC,CAAT,KAAoBD,CAAC,CAACG,aAAF,CAAgBF,CAAhB,CAHT,CAAnB;MAID,CARH;;MASA,KAAI,CAACpC,MAAL,CACGY,MADH,CACU5D,yBADV,EAEG6D,IAFH,CAEQ/F,SAAS,CAAC,KAAI,CAACuG,OAAN,CAFjB,EAGGC,SAHH,CAGcR,IAAD,IAAkB;QAC3B,KAAI,CAACgJ,gBAAL,GAAwBhJ,IAAxB;MACD,CALH;IAjUY;EAuUb;;EAEDyI,mBAAmB;IACjB,KAAKvJ,MAAL,CACGY,MADH,CACU1C,mBADV,EAEG2C,IAFH,CAEQ/F,SAAS,CAAC,KAAKuG,OAAN,CAFjB,EAGGC,SAHH,CAGcyI,YAAD,IAAsB;MAC/B,KAAKC,gBAAL,GAAwBD,YAAxB;IACD,CALH;;IAOA,KAAK/J,MAAL,CACGY,MADH,CACUzC,4BADV,EAEG0C,IAFH,CAEQ/F,SAAS,CAAC,KAAKuG,OAAN,CAFjB,EAGGC,SAHH,CAGcqB,SAAD,IAAmB;MAC5B,KAAKsH,yBAAL,GAAiCtH,SAAjC;MACA,IAAI,CAACA,SAAL,EAAgB,KAAKyF,sBAAL;MAChB,KAAKC,mBAAL;IACD,CAPH;EAQD;;EAEDA,mBAAmB;;;IACjB,KAAK6B,uBAAL,GAA+B,iBAAK/I,WAAL,MAAgB,IAAhB,IAAgBM,aAAhB,GAAgB,MAAhB,GAAgBA,GAAE0I,UAAlB,MAA4B,IAA5B,IAA4BxI,aAA5B,GAA4B,MAA5B,GAA4BA,GAAEI,MAAF,CACxDqI,GAAD,IAAcA,GAAG,CAACC,KAAJ,KAAc,WAAd,IAA6BD,GAAG,CAACC,KAAJ,KAAc,iBAA3C,IAAgED,GAAG,CAACC,KAAJ,IAAa,mBADlC,CAA3D;EAGD;;EAEDjC,sBAAsB;;;IACpB,KAAKjH,WAAL,GAAmB,KAAKpB,kBAAL,CAAwBqB,eAAxB,CAAwC,IAAxC,CAAnB;IACA,MAAMkJ,YAAY,GAAG,CACnB;MACEC,UAAU,EAAE,WADd;MAEEF,KAAK,EAAE,WAFT;MAGEG,MAAM,EAAEC,MAAM,CAACC,UAAP,GAAoB,GAApB,GAA0B,MAA1B,GAAmC,IAH7C;MAIEC,UAAU,EAAE,IAJd;MAKEC,SAAS,EAAE,aALb;MAMEC,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,YAAM,CAAChK,IAAP,MAAW,IAAX,IAAWW,aAAX,GAAW,MAAX,GAAWA,GAAEsH,QADe;MAE7B,CARH;MASEgC,QAAQ,EAAE,GATZ;MAUEC,YAAY,EAAGF,MAAD,IAAgB;QAC5B,OAAO,oDAAoDA,MAAM,CAACG,KAAP,CAAa,CAAb,CAAe;uBAA1E;MAED;IAbH,CADmB,EAgBnB;MACEV,UAAU,EAAE,iBADd;MAEEF,KAAK,EAAE,iBAFT;MAGEa,IAAI,EAAE,KAHR;MAIEN,SAAS,EAAE,aAJb;MAKEC,WAAW,EAAGC,MAAD,IAAgB;QAAA;;QAAC,QAAC,YAAM,CAAChK,IAAP,MAAW,IAAX,IAAWW,aAAX,GAAW,MAAX,GAAWA,GAAE0J,cAAd;MAA6B,CAL7D;MAMEJ,QAAQ,EAAE,GANZ;MAOEC,YAAY,EAAGF,MAAD,IAAgB;QAC5B,OAAO,oDAAoDA,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,EAC5E;uBADF;MAGD;IAXH,CAhBmB,EA6BnB;MACEV,UAAU,EAAE,mBADd;MAEEF,KAAK,EAAE,mBAFT;MAGEa,IAAI,EAAE,KAHR;MAIEN,SAAS,EAAE,aAJb;MAKEC,WAAW,EAAGC,MAAD,IAAgB;QAAA;;QAAC,QAAC,YAAM,CAAChK,IAAP,MAAW,IAAX,IAAWW,aAAX,GAAW,MAAX,GAAWA,GAAE2J,gBAAd;MAA+B,CAL/D;MAMEL,QAAQ,EAAE,GANZ;MAOEC,YAAY,EAAGF,MAAD,IAAgB;QAC5B,OAAO,oDAAoDA,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,EAC5E;uBADF;MAGD;IAXH,CA7BmB,EA0CnB;MACEV,UAAU,EAAE,WADd;MAEEF,KAAK,EAAE,WAFT;MAGEtI,MAAM,EAAE,KAHV;MAIE8I,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,KAAKjI,qBAAL,GACI,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAYW,aAAZ,GAAY,MAAZ,GAAYA,GAAE+G,QAAd,MAAsB,IAAtB,IAAsB7G,aAAtB,GAAsB,MAAtB,GAAsBA,GAAE0J,QAD5B,GAEI,YAAM,CAACvK,IAAP,MAAW,IAAX,IAAWqC,aAAX,GAAW,MAAX,GAAWA,GAAEmI,QAHW,EAI5B,KAAKzI,qBAAL,GACI,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAYwC,aAAZ,GAAY,MAAZ,GAAYA,GAAEkF,QAAd,MAAsB,IAAtB,IAAsBjF,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEgI,WAD5B,GAEI,YAAM,CAACzK,IAAP,MAAW,IAAX,IAAW2C,aAAX,GAAW,MAAX,GAAWA,GAAE+H,WANW,EAO5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE1K,IAAR,MAAY,IAAZ,IAAY8C,aAAZ,GAAY,MAAZ,GAAYA,GAAE6H,MAPc,EAQ5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYgD,aAAZ,GAAY,MAAZ,GAAYA,GAAEkF,YARc;MAS7B,CAbH;MAcE0C,WAAW,EAAE,CAAC,WAAD,EAAc,cAAd,CAdf;MAeEX,QAAQ,EAAE,GAfZ;MAgBEC,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,CAAC,KAAK3B,qBAAN,GACH,GAAG,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEoI,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,KAAsB,OAAtB,IAAiCqJ,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,CAApD,GACD,MAAMH,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI,MAD7C,GAED,SAASH,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI,UACrD;;iCAEqBH,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,KAAsB,OAAzC,GACjB,MAAMmJ,MAAM,CAACG,KAAP,CAAa,CAAb,CAAe,MADJ,GAEjBH,MAAM,CAACG,KAAP,CAAa,CAAb,IACEH,MAAM,CAACG,KAAP,CAAa,CAAb,CADF,GAEE,IACN;eAXK,GAaH,GAAG,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,KAAsB,OAAtB,IAAiC2H,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,CAApD,GACD,MAAMH,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI,MAD7C,GAED,aAAaH,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IACnD,aACF;;iCAEqBH,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa3H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,KAAsB,OAAzC,GACjB,SAASwH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAe,SADP,GAEjBH,MAAM,CAACG,KAAP,CAAa,CAAb,IACEH,MAAM,CAACG,KAAP,CAAa,CAAb,CADF,GAEE,IACN;eAxBF;MA0BD,CA7CH;MA8CEL,SAAS,EAAE,gBA9Cb;MA+CEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;QACA,MAAM6K,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;;QACzB,IAAIqH,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA/B,EAAwC;UACtC;QACD,CAFD,MAEO,IAAI6C,KAAK,CAACA,KAAN,CAAYG,MAAZ,CAAmBC,SAAnB,IAAgCJ,KAAK,CAACZ,KAAN,CAAY,CAAZ,CAApC,EAAoD;UACzD,IAAIa,WAAJ,EAAiB;YACf,KAAKI,eAAL,CAAqB,WAArB,EAAkCpB,MAAlC;YACA;UACD;;UACD,KAAKqB,eAAL,CAAqB,WAArB,EAAkCN,KAAlC;QACD,CANM,MAMA,IAAIA,KAAK,CAACA,KAAN,CAAYG,MAAZ,CAAmBC,SAAnB,IAAgCJ,KAAK,CAACZ,KAAN,CAAY,CAAZ,CAApC,EAAoD;UACzD,IAAIa,WAAJ,EAAiB;YACf,KAAKI,eAAL,CAAqB,cAArB,EAAqCpB,MAArC;YACA;UACD;;UACD,KAAKqB,eAAL,CAAqB,cAArB,EAAqCN,KAArC;QACD;MACF;IAnEH,CA1CmB,CAArB;IAgHA,MAAMO,aAAa,GAAQ,CACzB;MACE7B,UAAU,EAAE,KADd;MAEEF,KAAK,EAAE,KAFT;MAGEtI,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,YAAM,CAAChK,IAAP,MAAW,IAAX,IAAWW,aAAX,GAAW,MAAX,GAAWA,GAAE4K,QADe,EAE5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEvL,IAAR,MAAY,IAAZ,IAAYa,aAAZ,GAAY,MAAZ,GAAYA,GAAE8J,MAFc,EAG5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYqC,aAAZ,GAAY,MAAZ,GAAYA,GAAE6F,YAHc,EAI5B,KAAKsD,iBAAL,GAAyB,YAAM,CAACxL,IAAP,MAAW,IAAX,IAAWwC,aAAX,GAAW,MAAX,GAAWA,GAAEiJ,kBAAtC,GAA2D,EAJ/B;MAK7B,CAVH;MAWExB,QAAQ,EAAE,EAXZ;MAYEC,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyG,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,KAAsB,OAAtB,IAAiCqJ,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,CAApD,GACH,MAAMH,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI,MAD3C,GAEH,CAAC,KAAKpI,qBAAN,GACE,SAASiI,MAAM,CAACG,KAAP,CAAa,CAAb,IACPH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEsJ,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADO,GAGP,IACJ,UALA,GAME,YAAY2H,MAAM,CAACG,KAAP,CAAa,CAAb,IACVH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa3H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE2H,KAAR,MAAa,IAAb,IAAa1H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADU,GAGV,IACJ,aAZJ;MAaD,CA5BH;MA6BEqH,SAAS,EAAE,gBA7Bb;MA8BEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA/B,EAAwC;UACtC;QACD,CAFD,MAEO,IAAI6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,KAAKI,eAAL,CAAqB,KAArB,EAA4BpB,MAA5B;YACA;UACD;;UACD,KAAKqB,eAAL,CAAqB,KAArB,EAA4BN,KAA5B;QACD;MACF;IA1CH,CADyB,EA6CzB;MACEtB,UAAU,EAAE,SADd;MAEEF,KAAK,EAAE,SAFT;MAGEtI,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,YAAM,CAAChK,IAAP,MAAW,IAAX,IAAWW,aAAX,GAAW,MAAX,GAAWA,GAAE+K,YADe,EAE5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE1L,IAAR,MAAY,IAAZ,IAAYa,aAAZ,GAAY,MAAZ,GAAYA,GAAE8J,MAFc,EAG5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYqC,aAAZ,GAAY,MAAZ,GAAYA,GAAE6F,YAHc,EAI5B,KAAKsD,iBAAL,GAAyB,YAAM,CAACxL,IAAP,MAAW,IAAX,IAAWwC,aAAX,GAAW,MAAX,GAAWA,GAAEmJ,sBAAtC,GAA+D,EAJnC;MAK7B,CAVH;MAWE1B,QAAQ,EAAE,EAXZ;MAYEC,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyG,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,KAAsB,OAAtB,IAAiCqJ,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,CAApD,GACH,MAAMH,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI,MAD3C,GAEH,CAAC,KAAKpI,qBAAN,GACE,SAASiI,MAAM,CAACG,KAAP,CAAa,CAAb,IACPH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEsJ,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADO,GAGP,IACJ,UALA,GAME,YAAY2H,MAAM,CAACG,KAAP,CAAa,CAAb,IACVH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa3H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE2H,KAAR,MAAa,IAAb,IAAa1H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADU,GAGV,IACJ,aAZJ;MAaD,CA5BH;MA6BEqH,SAAS,EAAE,gBA7Bb;MA8BEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA/B,EAAwC;UACtC;QACD,CAFD,MAEO,IAAI6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,KAAKI,eAAL,CAAqB,SAArB,EAAgCpB,MAAhC;YACA;UACD;;UACD,KAAKqB,eAAL,CAAqB,SAArB,EAAgCN,KAAhC;QACD;MACF;IA1CH,CA7CyB,CAA3B;IA0FA,MAAMa,OAAO,GAAQ,CACnB;MACEnC,UAAU,EAAE,SADd;MAEEF,KAAK,EAAE,SAFT;MAGEtI,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,KAAKjI,qBAAL,GACI,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAYW,aAAZ,GAAY,MAAZ,GAAYA,GAAE+G,QAAd,MAAsB,IAAtB,IAAsB7G,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEgL,YAD5B,GAEI,YAAM,CAAC7L,IAAP,MAAW,IAAX,IAAWqC,aAAX,GAAW,MAAX,GAAWA,GAAEyJ,YAHW,EAI5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE9L,IAAR,MAAY,IAAZ,IAAYwC,aAAZ,GAAY,MAAZ,GAAYA,GAAEmI,MAJc,EAK5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYyC,aAAZ,GAAY,MAAZ,GAAYA,GAAEyF,YALc,EAM5B,KAAKsD,iBAAL,GACI,KAAKzJ,qBAAL,GACE,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAY2C,aAAZ,GAAY,MAAZ,GAAYA,GAAE+E,QAAd,MAAsB,IAAtB,IAAsB5E,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEiJ,0BAD1B,GAEE,YAAM,CAAC/L,IAAP,MAAW,IAAX,IAAWgD,aAAX,GAAW,MAAX,GAAWA,GAAEgJ,0BAHnB,GAII,EAVwB;MAW7B,CAhBH;MAiBE/B,QAAQ,EAAE,GAjBZ;MAkBEC,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyG,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,KAAsB,OAAtB,IAAiCqJ,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,CAApD,GACH,MAAMH,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI,MAD3C,GAEH,CAAC,KAAKpI,qBAAN,GACE,SAASiI,MAAM,CAACG,KAAP,CAAa,CAAb,IACPH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEsJ,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADO,GAGP,IACJ,UALA,GAME,YAAY2H,MAAM,CAACG,KAAP,CAAa,CAAb,IACVH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa3H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE2H,KAAR,MAAa,IAAb,IAAa1H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADU,GAGV,IACJ,aAZJ;MAaD,CAlCH;MAmCEqH,SAAS,EAAE,gBAnCb;MAoCEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA/B,EAAwC;UACtC;QACD,CAFD,MAEO,IAAI6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,KAAKI,eAAL,CAAqB,SAArB,EAAgCpB,MAAhC;YACA;UACD;;UACD,KAAKqB,eAAL,CAAqB,SAArB,EAAgCN,KAAhC;QACD;MACF;IAhDH,CADmB,CAArB;IAoDA,MAAMkB,aAAa,GAAQ,CACzB;MACExC,UAAU,EAAE,UADd;MAEEF,KAAK,EAAE,UAFT;MAGEtI,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,YAAM,CAAChK,IAAP,MAAW,IAAX,IAAWW,aAAX,GAAW,MAAX,GAAWA,GAAEuL,aADe,EAE5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAElM,IAAR,MAAY,IAAZ,IAAYa,aAAZ,GAAY,MAAZ,GAAYA,GAAE8J,MAFc,EAG5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYqC,aAAZ,GAAY,MAAZ,GAAYA,GAAE6F,YAHc,EAI5B,KAAKsD,iBAAL,GAAyB,YAAM,CAACxL,IAAP,MAAW,IAAX,IAAWwC,aAAX,GAAW,MAAX,GAAWA,GAAE2J,uBAAtC,GAAgE,EAJpC;MAK7B,CAVH;MAWElC,QAAQ,EAAE,GAXZ;MAYEC,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyG,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,KAAsB,OAAtB,IAAiCqJ,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,CAApD,GACH,MAAMH,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI,MAD3C,GAEH,CAAC,KAAKpI,qBAAN,GACE,SAASiI,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI,UADnD,GAEE,YAAYH,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI,aAJ1D;MAKD,CApBH;MAqBEL,SAAS,EAAE,gBArBb;MAsBEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA/B,EAAwC;UACtC;QACD,CAFD,MAEO,IAAI6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,KAAKI,eAAL,CAAqB,UAArB,EAAiCpB,MAAjC;YACA;UACD;;UACD,KAAKqB,eAAL,CAAqB,UAArB,EAAiCN,KAAjC;QACD;MACF;IAlCH,CADyB,EAqCzB;MACEtB,UAAU,EAAE,mBADd;MAEEF,KAAK,EAAE,mBAFT;MAGEtI,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,YAAM,CAAChK,IAAP,MAAW,IAAX,IAAWW,aAAX,GAAW,MAAX,GAAWA,GAAEyL,qBADe,EAE5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEpM,IAAR,MAAY,IAAZ,IAAYa,aAAZ,GAAY,MAAZ,GAAYA,GAAE8J,MAFc,EAG5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYqC,aAAZ,GAAY,MAAZ,GAAYA,GAAE6F,YAHc,EAI5B,KAAKsD,iBAAL,GACI,YAAM,CAACxL,IAAP,MAAW,IAAX,IAAWwC,aAAX,GAAW,MAAX,GAAWA,GAAE6J,+BADjB,GAEI,EANwB;MAO7B,CAZH;MAaEpC,QAAQ,EAAE,GAbZ;MAcEC,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,CAAC,KAAK3B,qBAAN,GACH,UAAUiI,MAAM,CAACG,KAAP,CAAa,CAAb,IACRH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEwJ,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADQ,GAGR,IACJ,UALK,GAMH,YAAYmJ,MAAM,CAACG,KAAP,CAAa,CAAb,IACVH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE8H,KAAR,MAAa,IAAb,IAAa3H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADU,GAGV,IACJ,aAVF;MAWD,CA5BH;MA6BEsH,SAAS,EAAE,gBA7Bb;MA8BEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA/B,EAAwC;UACtC;QACD,CAFD,MAEO,IAAI6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,KAAKI,eAAL,CAAqB,mBAArB,EAA0CpB,MAA1C;YACA;UACD;;UACD,KAAKqB,eAAL,CAAqB,mBAArB,EAA0CN,KAA1C;QACD;MACF;IA1CH,CArCyB,CAA3B;IAkFA,MAAMuB,qBAAqB,GAAQ,CACjC;MACE7C,UAAU,EAAE,cADd;MAEEF,KAAK,EAAE,cAFT;MAGEtI,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,KAAKjI,qBAAL,GACI,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAYW,aAAZ,GAAY,MAAZ,GAAYA,GAAE+G,QAAd,MAAsB,IAAtB,IAAsB7G,aAAtB,GAAsB,MAAtB,GAAsBA,GAAE0L,gBAD5B,GAEI,YAAM,CAACvM,IAAP,MAAW,IAAX,IAAWqC,aAAX,GAAW,MAAX,GAAWA,GAAEmK,gBAHW,EAI5B,KAAKzK,qBAAL,GACI,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAYwC,aAAZ,GAAY,MAAZ,GAAYA,GAAEkF,QAAd,MAAsB,IAAtB,IAAsBjF,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEgK,sBAD5B,GAEI,YAAM,CAACzM,IAAP,MAAW,IAAX,IAAW2C,aAAX,GAAW,MAAX,GAAWA,GAAE+J,sBANW,EAO5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE1M,IAAR,MAAY,IAAZ,IAAY8C,aAAZ,GAAY,MAAZ,GAAYA,GAAE6H,MAPc,EAQ5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYgD,aAAZ,GAAY,MAAZ,GAAYA,GAAEkF,YARc,EAS5B,KAAKsD,iBAAL,GACI,KAAKzJ,qBAAL,GACE,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAYkD,aAAZ,GAAY,MAAZ,GAAYA,GAAEwE,QAAd,MAAsB,IAAtB,IAAsBvE,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEwJ,oCAD1B,GAEE,YAAM,CAAC3M,IAAP,MAAW,IAAX,IAAWoD,aAAX,GAAW,MAAX,GAAWA,GAAEwJ,oCAHnB,GAII,EAbwB;MAc7B,CAnBH;MAoBE3C,QAAQ,EAAE,GApBZ;MAqBEW,WAAW,EAAE,CAAC,cAAD,EAAiB,6BAAjB,CArBf;MAsBEV,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,CAAC,KAAK3B,qBAAN,GACH,UAAUiI,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI;sGACsCH,MAAM,CAACG,KAAP,CAAa,CAAb,IACtFH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEwJ,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADsF,GAGtF,IACJ,gBANK,GAOH,YAAYmJ,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI;wGACsCH,MAAM,CAACG,KAAP,CAAa,CAAb,IACxFH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE8H,KAAR,MAAa,IAAb,IAAa3H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADwF,GAGxF,IACJ,mBAZF;MAaD,CAtCH;MAuCEsH,SAAS,EAAE,gBAvCb;MAwCEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA/B,EAAwC;UACtC;QACD,CAFD,MAEO,IAAI6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,KAAK6B,qBAAL,CAA2B,WAA3B,EAAwC7C,MAAxC,EAAgD,cAAhD;YACA;UACD;;UACD,KAAK8C,eAAL,CAAqB,WAArB,EAAkC/B,KAAlC,EAAyC,cAAzC;QACD;MACF;IApDH,CADiC,EAuDjC;MACEtB,UAAU,EAAE,kBADd;MAEEF,KAAK,EAAE,kBAFT;MAGEtI,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,KAAKjI,qBAAL,GACI,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAYW,aAAZ,GAAY,MAAZ,GAAYA,GAAE+G,QAAd,MAAsB,IAAtB,IAAsB7G,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEkM,mBAD5B,GAEI,YAAM,CAAC/M,IAAP,MAAW,IAAX,IAAWqC,aAAX,GAAW,MAAX,GAAWA,GAAE2K,mBAHW,EAI5B,KAAKjL,qBAAL,GACI,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAYwC,aAAZ,GAAY,MAAZ,GAAYA,GAAEkF,QAAd,MAAsB,IAAtB,IAAsBjF,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEwK,yBAD5B,GAEI,YAAM,CAACjN,IAAP,MAAW,IAAX,IAAW2C,aAAX,GAAW,MAAX,GAAWA,GAAEuK,yBANW,EAO5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAElN,IAAR,MAAY,IAAZ,IAAY8C,aAAZ,GAAY,MAAZ,GAAYA,GAAE6H,MAPc,EAQ5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYgD,aAAZ,GAAY,MAAZ,GAAYA,GAAEkF,YARc,EAS5B,KAAKsD,iBAAL,GACI,KAAKzJ,qBAAL,GACE,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAYkD,aAAZ,GAAY,MAAZ,GAAYA,GAAEwE,QAAd,MAAsB,IAAtB,IAAsBvE,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEgK,uCAD1B,GAEE,YAAM,CAACnN,IAAP,MAAW,IAAX,IAAWoD,aAAX,GAAW,MAAX,GAAWA,GAAEgK,uCAHnB,GAII,EAbwB;MAc7B,CAnBH;MAoBEnD,QAAQ,EAAE,GApBZ;MAqBEW,WAAW,EAAE,CAAC,kBAAD,EAAqB,iCAArB,CArBf;MAsBEV,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,CAAC,KAAK3B,qBAAN,GACH,SAASiI,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI;sGACuCH,MAAM,CAACG,KAAP,CAAa,CAAb,IACtFH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEwJ,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADsF,GAGtF,IACJ,gBANK,GAOH,YAAYmJ,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI;wGACsCH,MAAM,CAACG,KAAP,CAAa,CAAb,IACxFH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE8H,KAAR,MAAa,IAAb,IAAa3H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADwF,GAGxF,IACJ,mBAZF;MAaD,CAtCH;MAuCEsH,SAAS,EAAE,gBAvCb;MAwCEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA/B,EAAwC;UACtC;QACD,CAFD,MAEO,IAAI6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,KAAK6B,qBAAL,CACE,WADF,EAEE7C,MAFF,EAGE,kBAHF;YAKA;UACD;;UACD,KAAK8C,eAAL,CAAqB,WAArB,EAAkC/B,KAAlC,EAAyC,kBAAzC;QACD;MACF;IAxDH,CAvDiC,CAAnC;IAkHA,MAAMsC,GAAG,GAAQ,CACf;MACE5D,UAAU,EAAE,YAAK5J,kBAAL,MAAuB,IAAvB,IAAuBc,aAAvB,GAAuB,MAAvB,GAAuBA,GAAE2M,2BAAzB,IAAuD,oBAAvD,GAA8E,sBAD5F;MAEE/D,KAAK,EAAE,YAAK1J,kBAAL,MAAuB,IAAvB,IAAuBgB,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEyM,2BAAzB,IAAuD,oBAAvD,GAA8E,sBAFvF;MAGErM,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,YAAM,CAAChK,IAAP,MAAW,IAAX,IAAWW,aAAX,GAAW,MAAX,GAAWA,GAAE4M,uBADe,EAE5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEvN,IAAR,MAAY,IAAZ,IAAYa,aAAZ,GAAY,MAAZ,GAAYA,GAAE8J,MAFc,EAG5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYqC,aAAZ,GAAY,MAAZ,GAAYA,GAAE6F,YAHc,EAI5B,KAAKsD,iBAAL,GACI,YAAM,CAACxL,IAAP,MAAW,IAAX,IAAWwC,aAAX,GAAW,MAAX,GAAWA,GAAEgL,iCADjB,GAEI,EANwB;MAO7B,CAZH;MAaE5C,WAAW,EAAE,CACX,YAAK/K,kBAAL,MAAuB,IAAvB,IAAuBwC,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEiL,2BAAzB,IAAuD,oBAAvD,GAA8E,sBADnE,EAEX,CAAC,YAAKzN,kBAAL,MAAuB,IAAvB,IAAuB2C,aAAvB,GAAuB,MAAvB,GAAuBA,GAAE8K,2BAAzB,IAAuD,oBAAvD,GAA8E,sBAA/E,IAAyG,iBAF9F,CAbf;MAiBErD,QAAQ,EAAE,GAjBZ;MAkBEC,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,CAAC,KAAK3B,qBAAN,GACH,UAAUiI,MAAM,CAACG,KAAP,CAAa,CAAb,IACRH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEwJ,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADQ,GAGR,IACJ,UALK,GAMH,YAAYmJ,MAAM,CAACG,KAAP,CAAa,CAAb,IACVH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE8H,KAAR,MAAa,IAAb,IAAa3H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADU,GAGV,IACJ,aAVF;MAWD,CAhCH;MAiCEsH,SAAS,EAAE,gBAjCb;MAkCEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA/B,EAAwC;UACtC;QACD,CAFD,MAEO,IAAI6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,KAAKI,eAAL,CAAqB,EAAC,WAAKvL,kBAAL,MAAuB,IAAvB,IAAuBgB,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEyM,2BAA1B,IAAwD,sBAAxD,GAAiF,oBAAtG,EAA4HtD,MAA5H;YACA;UACD;;UACD,KAAKqB,eAAL,CAAqB,EAAC,WAAKxL,kBAAL,MAAuB,IAAvB,IAAuBwC,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEiL,2BAA1B,IAAwD,sBAAxD,GAAiF,oBAAtG,EAA4HvC,KAA5H;QACD;MACF;IA9CH,CADe,CAAjB;IAkDA,MAAM0C,uBAAuB,GAAQ,CACnC;MACEhE,UAAU,EAAE,EAAC,WAAK5J,kBAAL,MAAuB,IAAvB,IAAuB4C,aAAvB,GAAuB,MAAvB,GAAuBA,GAAE6K,2BAA1B,IAAwD,iBAAxD,GAA4E,gBAD1F;MAEE/D,KAAK,EAAE,EAAC,WAAK1J,kBAAL,MAAuB,IAAvB,IAAuB8C,aAAvB,GAAuB,MAAvB,GAAuBA,GAAE2K,2BAA1B,IAAwD,iBAAxD,GAA4E,gBAFrF;MAGErM,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,KAAKjI,qBAAL,GACI,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAYW,aAAZ,GAAY,MAAZ,GAAYA,GAAE+G,QAAd,MAAsB,IAAtB,IAAsB7G,aAAtB,GAAsB,MAAtB,GAAsBA,GAAE6M,kBAD5B,GAEI,YAAM,CAAC1N,IAAP,MAAW,IAAX,IAAWqC,aAAX,GAAW,MAAX,GAAWA,GAAEsL,kBAHW,EAI5B,KAAK5L,qBAAL,GACI,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAYwC,aAAZ,GAAY,MAAZ,GAAYA,GAAEkF,QAAd,MAAsB,IAAtB,IAAsBjF,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEmL,wBAD5B,GAEI,YAAM,CAAC5N,IAAP,MAAW,IAAX,IAAW2C,aAAX,GAAW,MAAX,GAAWA,GAAEkL,wBANW,EAO5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE7N,IAAR,MAAY,IAAZ,IAAY8C,aAAZ,GAAY,MAAZ,GAAYA,GAAE6H,MAPc,EAQ5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYgD,aAAZ,GAAY,MAAZ,GAAYA,GAAEkF,YARc,EAS5B,KAAKsD,iBAAL,GACI,KAAKzJ,qBAAL,GACE,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAYkD,aAAZ,GAAY,MAAZ,GAAYA,GAAEwE,QAAd,MAAsB,IAAtB,IAAsBvE,aAAtB,GAAsB,MAAtB,GAAsBA,GAAE2K,sCAD1B,GAEE,YAAM,CAAC9N,IAAP,MAAW,IAAX,IAAWoD,aAAX,GAAW,MAAX,GAAWA,GAAE2K,sCAHnB,GAII,EAbwB;MAc7B,CAnBH;MAoBE9D,QAAQ,EAAE,GApBZ;MAqBEW,WAAW,EAAE,CACX,EAAC,WAAK/K,kBAAL,MAAuB,IAAvB,IAAuBiD,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEwK,2BAA1B,IAAwD,iBAAxD,GAA4E,gBADjE,EAEX,CAAC,EAAC,WAAKzN,kBAAL,MAAuB,IAAvB,IAAuBmD,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEsK,2BAA1B,IAAwD,iBAAxD,GAA4E,gBAA7E,IAAiG,iBAFtF,CArBf;MAyBEpD,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,CAAC,KAAK3B,qBAAN,GACH,SAASiI,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI;sGACuCH,MAAM,CAACG,KAAP,CAAa,CAAb,IACtFH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEwJ,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADsF,GAGtF,IACJ,gBANK,GAOH,YAAYmJ,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI;wGACsCH,MAAM,CAACG,KAAP,CAAa,CAAb,IACxFH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE8H,KAAR,MAAa,IAAb,IAAa3H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADwF,GAGxF,IACJ,mBAZF;MAaD,CAzCH;MA0CEsH,SAAS,EAAE,gBA1Cb;MA2CEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA/B,EAAwC;UACtC;QACD,CAFD,MAEO,IAAI6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,KAAK6B,qBAAL,CACE,WADF,EAEE7C,MAFF,EAGE,iBAHF;YAKA;UACD;;UACD,KAAK8C,eAAL,CAAqB,WAArB,EAAkC/B,KAAlC,EAAyC,iBAAzC;QACD;MACF;IA3DH,CADmC,EA8DnC;MACEtB,UAAU,EAAE,EAAC,WAAK5J,kBAAL,MAAuB,IAAvB,IAAuBqD,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEoK,2BAA1B,IAAwD,qBAAxD,GAAgF,oBAD9F;MAEE/D,KAAK,EAAE,EAAC,WAAK1J,kBAAL,MAAuB,IAAvB,IAAuBsD,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEmK,2BAA1B,IAAwD,qBAAxD,GAAgF,oBAFzF;MAGErM,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,KAAKjI,qBAAL,GACI,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAYW,aAAZ,GAAY,MAAZ,GAAYA,GAAE+G,QAAd,MAAsB,IAAtB,IAAsB7G,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEmN,qBAD5B,GAEI,YAAM,CAAChO,IAAP,MAAW,IAAX,IAAWqC,aAAX,GAAW,MAAX,GAAWA,GAAE4L,qBAHW,EAI5B,KAAKlM,qBAAL,GACI,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAYwC,aAAZ,GAAY,MAAZ,GAAYA,GAAEkF,QAAd,MAAsB,IAAtB,IAAsBjF,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEyL,2BAD5B,GAEI,YAAM,CAAClO,IAAP,MAAW,IAAX,IAAW2C,aAAX,GAAW,MAAX,GAAWA,GAAEwL,2BANW,EAO5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEnO,IAAR,MAAY,IAAZ,IAAY8C,aAAZ,GAAY,MAAZ,GAAYA,GAAE6H,MAPc,EAQ5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYgD,aAAZ,GAAY,MAAZ,GAAYA,GAAEkF,YARc,EAS5B,KAAKsD,iBAAL,GACI,KAAKzJ,qBAAL,GACE,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/B,IAAR,MAAY,IAAZ,IAAYkD,aAAZ,GAAY,MAAZ,GAAYA,GAAEwE,QAAd,MAAsB,IAAtB,IAAsBvE,aAAtB,GAAsB,MAAtB,GAAsBA,GACpBiL,yCAFJ,GAGE,YAAM,CAACpO,IAAP,MAAW,IAAX,IAAWoD,aAAX,GAAW,MAAX,GAAWA,GAAEiL,yCAJnB,GAKI,EAdwB;MAe7B,CApBH;MAqBEzD,WAAW,EAAE,CACX,EAAC,WAAK/K,kBAAL,MAAuB,IAAvB,IAAuBuD,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEkK,2BAA1B,IAAwD,qBAAxD,GAAgF,oBADrE,EAEX,CAAC,EAAC,WAAKzN,kBAAL,MAAuB,IAAvB,IAAuByD,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEgK,2BAA1B,IAAwD,qBAAxD,GAAgF,oBAAjF,IAAyG,iBAF9F,CArBf;MAyBErD,QAAQ,EAAE,GAzBZ;MA0BEC,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,CAAC,KAAK3B,qBAAN,GACH,SAASiI,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI;sGACuCH,MAAM,CAACG,KAAP,CAAa,CAAb,IACtFH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEwJ,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADsF,GAGtF,IACJ,gBANK,GAOH,YAAYmJ,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI;wGACsCH,MAAM,CAACG,KAAP,CAAa,CAAb,IACxFH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE8H,KAAR,MAAa,IAAb,IAAa3H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADwF,GAGxF,IACJ,mBAZF;MAaD,CA1CH;MA2CEsH,SAAS,EAAE,gBA3Cb;MA4CEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA/B,EAAwC;UACtC;QACD,CAFD,MAEO,IAAI6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,KAAK6B,qBAAL,CACE,WADF,EAEE7C,MAFF,EAGE,qBAHF;YAKA;UACD;;UACD,KAAK8C,eAAL,CAAqB,WAArB,EAAkC/B,KAAlC,EAAyC,qBAAzC;QACD;MACF;IA5DH,CA9DmC,CAArC;IA6HA,MAAMuD,MAAM,GAAQ,CAClB;MACE7E,UAAU,EAAE,QADd;MAEEF,KAAK,EAAE,QAFT;MAGEtI,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,YAAM,CAAChK,IAAP,MAAW,IAAX,IAAWW,aAAX,GAAW,MAAX,GAAWA,GAAE4N,WADe,EAE5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEvO,IAAR,MAAY,IAAZ,IAAYa,aAAZ,GAAY,MAAZ,GAAYA,GAAE8J,MAFc,EAG5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYqC,aAAZ,GAAY,MAAZ,GAAYA,GAAE6F,YAHc,EAI5B,KAAKsD,iBAAL,GAAyB,YAAM,CAACxL,IAAP,MAAW,IAAX,IAAWwC,aAAX,GAAW,MAAX,GAAWA,GAAEgM,qBAAtC,GAA8D,EAJlC;MAK7B,CAVH;MAWEvE,QAAQ,EAAE,GAXZ;MAYEC,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyG,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,KAAsB,OAAtB,IAAiCqJ,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,CAApD,GACH,MAAMH,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI,MAD3C,GAEH,CAAC,KAAKpI,qBAAN,GACE,SAASiI,MAAM,CAACG,KAAP,CAAa,CAAb,IACPH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEsJ,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADO,GAGP,IACJ,UALA,GAME,YAAY2H,MAAM,CAACG,KAAP,CAAa,CAAb,IACVH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa3H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE2H,KAAR,MAAa,IAAb,IAAa1H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADU,GAGV,IACJ,aAZJ;MAaD,CA5BH;MA6BEqH,SAAS,EAAE,gBA7Bb;MA8BEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA3B,IAAsC6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAA5D,EAA+D;UAC7D;QACD,CAFD,MAEO,IAAIY,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,KAAKI,eAAL,CAAqB,QAArB,EAA+BpB,MAA/B;YACA;UACD;;UACD,KAAKqB,eAAL,CAAqB,QAArB,EAA+BN,KAA/B;QACD;MACF;IA1CH,CADkB,EA6ClB;MACEtB,UAAU,EAAE,UADd;MAEEF,KAAK,EAAE,UAFT;MAGEtI,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,YAAM,CAAChK,IAAP,MAAW,IAAX,IAAWW,aAAX,GAAW,MAAX,GAAWA,GAAE8N,kBADe,EAE5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEzO,IAAR,MAAY,IAAZ,IAAYa,aAAZ,GAAY,MAAZ,GAAYA,GAAE8J,MAFc,EAG5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYqC,aAAZ,GAAY,MAAZ,GAAYA,GAAE6F,YAHc,EAI5B,KAAKsD,iBAAL,GACI,YAAM,CAACxL,IAAP,MAAW,IAAX,IAAWwC,aAAX,GAAW,MAAX,GAAWA,GAAEkM,4BADjB,GAEI,EANwB;MAO7B,CAZH;MAaEzE,QAAQ,EAAE,GAbZ;MAcEC,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyG,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,KAAsB,OAAtB,IAAiCqJ,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,CAApD,GACH,MAAMH,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI,MAD3C,GAEH,SAASH,MAAM,CAACG,KAAP,CAAa,CAAb,IACPH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEsJ,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADO,GAGP,IACJ,UANF;MAOD,CAxBH;MAyBEyH,SAAS,EAAE,gBAzBb;MA0BEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA3B,IAAsC6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAA5D,EAA+D;UAC7D;QACD,CAFD,MAEO,IAAIY,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,KAAKI,eAAL,CAAqB,UAArB,EAAiCpB,MAAjC;YACA;UACD;;UACD,KAAKqB,eAAL,CAAqB,UAArB,EAAiCN,KAAjC;QACD;MACF;IAtCH,CA7CkB,EAqFlB;MACEtB,UAAU,EAAE,gBADd;MAEEF,KAAK,EAAE,gBAFT;MAGEtI,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,YAAM,CAAChK,IAAP,MAAW,IAAX,IAAWW,aAAX,GAAW,MAAX,GAAWA,GAAEgO,kBADe,EAE5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3O,IAAR,MAAY,IAAZ,IAAYa,aAAZ,GAAY,MAAZ,GAAYA,GAAE8J,MAFc,EAG5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYqC,aAAZ,GAAY,MAAZ,GAAYA,GAAE6F,YAHc,EAI5B,KAAKsD,iBAAL,GACI,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAExL,IAAR,MAAY,IAAZ,IAAYwC,aAAZ,GAAY,MAAZ,GAAYA,GAAEoM,4BADlB,GAEI,EANwB;MAO7B,CAZH;MAaE3E,QAAQ,EAAE,GAbZ;MAcEC,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyG,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,KAAsB,OAAtB,IAAiCqJ,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,CAApD,GACH,MAAMH,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI,MAD3C,GAEH,CAAC,KAAKpI,qBAAN,GACE,SAASiI,MAAM,CAACG,KAAP,CAAa,CAAb,IACPH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEsJ,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADO,GAGP,IACJ,UALA,GAME,YAAY2H,MAAM,CAACG,KAAP,CAAa,CAAb,IACVH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa3H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE2H,KAAR,MAAa,IAAb,IAAa1H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADU,GAGV,IACJ,aAZJ;MAaD,CA9BH;MA+BEqH,SAAS,EAAE,gBA/Bb;MAgCEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA3B,IAAsC6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAA5D,EAA+D;UAC7D;QACD,CAFD,MAEO,IAAIY,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,KAAKI,eAAL,CAAqB,gBAArB,EAAuCpB,MAAvC;YACA;UACD;;UACD,KAAKqB,eAAL,CAAqB,gBAArB,EAAuCN,KAAvC;QACD;MACF;IA5CH,CArFkB,EAmIlB;MACEtB,UAAU,EAAE,gBADd;MAEEF,KAAK,EAAE,gBAFT;MAGEtI,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,YAAM,CAAChK,IAAP,MAAW,IAAX,IAAWW,aAAX,GAAW,MAAX,GAAWA,GAAEkO,kBADe,EAE5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE7O,IAAR,MAAY,IAAZ,IAAYa,aAAZ,GAAY,MAAZ,GAAYA,GAAE8J,MAFc,EAG5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYqC,aAAZ,GAAY,MAAZ,GAAYA,GAAE6F,YAHc,EAI5B,KAAKsD,iBAAL,GACI,YAAM,CAACxL,IAAP,MAAW,IAAX,IAAWwC,aAAX,GAAW,MAAX,GAAWA,GAAEsM,4BADjB,GAEI,EANwB;MAO7B,CAZH;MAaE7E,QAAQ,EAAE,GAbZ;MAcEC,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyG,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,KAAsB,OAAtB,IAAiCqJ,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,CAApD,GACH,MAAMH,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI,MAD3C,GAEH,CAAC,KAAKpI,qBAAN,GACE,SAASiI,MAAM,CAACG,KAAP,CAAa,CAAb,IACPH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEsJ,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADO,GAGP,IACJ,UALA,GAME,YAAY2H,MAAM,CAACG,KAAP,CAAa,CAAb,IACVH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa3H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE2H,KAAR,MAAa,IAAb,IAAa1H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADU,GAGV,IACJ,aAZJ;MAaD,CA9BH;MA+BEqH,SAAS,EAAE,gBA/Bb;MAgCEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA/B,EAAwC;UACtC;QACD,CAFD,MAEO,IAAI6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,KAAKI,eAAL,CAAqB,gBAArB,EAAuCpB,MAAvC;YACA;UACD;;UACD,KAAKqB,eAAL,CAAqB,gBAArB,EAAuCN,KAAvC;QACD;MACF;IA5CH,CAnIkB,EAiLlB;MACEtB,UAAU,EAAE,SADd;MAEEF,KAAK,EAAE,SAFT;MAGEtI,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,YAAM,CAAChK,IAAP,MAAW,IAAX,IAAWW,aAAX,GAAW,MAAX,GAAWA,GAAEoO,YADe,EAE5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE/O,IAAR,MAAY,IAAZ,IAAYa,aAAZ,GAAY,MAAZ,GAAYA,GAAE8J,MAFc,EAG5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYqC,aAAZ,GAAY,MAAZ,GAAYA,GAAE6F,YAHc,EAI5B,KAAKsD,iBAAL,GAAyB,YAAM,CAACxL,IAAP,MAAW,IAAX,IAAWwC,aAAX,GAAW,MAAX,GAAWA,GAAEwM,sBAAtC,GAA+D,EAJnC;MAK7B,CAVH;MAWE/E,QAAQ,EAAE,EAXZ;MAYEC,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyG,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,KAAsB,OAAtB,IAAiCqJ,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,CAApD,GACH,MAAMH,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI,MAD3C,GAEH,CAAC,KAAKpI,qBAAN,GACE,SAASiI,MAAM,CAACG,KAAP,CAAa,CAAb,IACPH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEsJ,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADO,GAGP,IACJ,UALA,GAME,YAAY2H,MAAM,CAACG,KAAP,CAAa,CAAb,IACVH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa3H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE2H,KAAR,MAAa,IAAb,IAAa1H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADU,GAGV,IACJ,aAZJ;MAaD,CA5BH;MA6BEqH,SAAS,EAAE,gBA7Bb;MA8BEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA/B,EAAwC;UACtC;QACD,CAFD,MAEO,IAAI6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,KAAKI,eAAL,CAAqB,SAArB,EAAgCpB,MAAhC;YACA;UACD;;UACD,KAAKqB,eAAL,CAAqB,SAArB,EAAgCN,KAAhC;QACD;MACF;IA1CH,CAjLkB,EA6NlB;MACEtB,UAAU,EAAE,wBADd;MAEEF,KAAK,EAAE,wBAFT;MAGEtI,MAAM,EAAE,KAHV;MAIEmJ,IAAI,EAAE,KAJR;MAKEL,WAAW,EAAGC,MAAD,IAAgB;;;QAAC,QAC5B,YAAM,CAAChK,IAAP,MAAW,IAAX,IAAWW,aAAX,GAAW,MAAX,GAAWA,GAAEsO,6BADe,EAE5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEjP,IAAR,MAAY,IAAZ,IAAYa,aAAZ,GAAY,MAAZ,GAAYA,GAAE8J,MAFc,EAG5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYqC,aAAZ,GAAY,MAAZ,GAAYA,GAAE6F,YAHc,EAI5B,KAAKsD,iBAAL,GACI,YAAM,CAACxL,IAAP,MAAW,IAAX,IAAWwC,aAAX,GAAW,MAAX,GAAWA,GAAE0M,uCADjB,GAEI,EANwB;MAO7B,CAZH;MAaEjF,QAAQ,EAAE,GAbZ;MAcEC,YAAY,EAAGF,MAAD,IAAgB;;;QAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;QACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;QACzB,OAAO,MAAMsG,MAAM,CAACG,KAAP,CAAa,CAAb,IACTH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEwJ,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADS,GAGT,IACF,MAJF;MAKD,CAtBH;MAuBEiJ,SAAS,EAAE,gBAvBb;MAwBEgB,aAAa,EAAGC,KAAD,IAAe;;;QAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;QACA,MAAMjB,MAAM,GAAG;UAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;UAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;QAApC,CAAf;;QACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA/B,EAAwC;UACtC;QACD,CAFD,MAEO,IAAI6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;UAC9B,IAAIa,WAAJ,EAAiB;YACf,MAAMH,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;YACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;YACzBiG,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEwF,IAAR,CACE,sDAAsD,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEhF,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CACnE,SAASuO,kBAAkB,CACzBC,IAAI,CAACC,SAAL,CAAetF,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEhK,IAAvB,CADyB,CAE1B,qCAAqCoP,kBAAkB,CACtDC,IAAI,CAACC,SAAL,CAAezE,OAAf,CADsD,CAEvD,EANH,EAOE,QAPF;YASA;UACD;;UACD,KAAKQ,eAAL,CAAqB,wBAArB,EAA+CN,KAA/C;QACD;MACF;IA9CH,CA7NkB,CAApB;IA8QA,KAAK1K,WAAL,CAAiBgJ,UAAjB,GAA8B,KAAKtH,qBAAL,GAC1B,CACA,GAAGyH,YADH,EAEA;IACA,GAAGoC,OAHH,EAIA;IACA,GAAGU,qBALH,EAMA;IACA,GAAGmB,uBAPH,CAQA;IARA,CAD0B,GAW1B,CACA,GAAGjE,YADH,EAEA,GAAG8B,aAFH,EAGA,GAAGM,OAHH,EAIA,GAAGK,aAJH,EAKA,GAAGK,qBALH,EAMA,GAAGe,GANH,EAOA,GAAGI,uBAPH,EAQA,GAAGa,MARH,CAXJ;IAqBA,IAAI,KAAKvM,qBAAT,EACE,KAAKmH,gBAAL,CAAsBtB,OAAtB,CAA+BqB,YAAD,IAA+B;;;MAC3D,IAAIK,GAAG,GAAQ;QACbG,UAAU,EAAER,YAAY,SAAZ,gBAAY,WAAZ,GAAY,MAAZ,eAAY,CAAE1H,WADb;QAEbgI,KAAK,EAAEN,YAAY,SAAZ,gBAAY,WAAZ,GAAY,MAAZ,eAAY,CAAE1H,WAFR;QAGbN,MAAM,EAAE,KAHK;QAIbmJ,IAAI,EAAE,KAJO;QAKbL,WAAW,EAAGC,MAAD,IAAgB;;;UAAC,QAC5B,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEhK,IAAR,MAAY,IAAZ,IAAYW,aAAZ,GAAY,MAAZ,GAAYA,GAAE+G,QAAd,MAAsB,IAAtB,IAAsB7G,aAAtB,GAAsB,MAAtB,GAAsBA,GAAGoI,YAAY,SAAZ,gBAAY,WAAZ,GAAY,MAAZ,eAAY,CAAE1H,WAAjB,CADM,EAE5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEvB,IAAR,MAAY,IAAZ,IAAYqC,aAAZ,GAAY,MAAZ,GAAYA,GAAEsI,MAFc,EAG5B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE3K,IAAR,MAAY,IAAZ,IAAYwC,aAAZ,GAAY,MAAZ,GAAYA,GAAE0F,YAHc,EAI5B,KAAKsD,iBAAL,GACI,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAExL,IAAR,MAAY,IAAZ,IAAYyC,aAAZ,GAAY,MAAZ,GAAYA,GAAEiF,QAAd,MAAsB,IAAtB,IAAsB/E,aAAtB,GAAsB,MAAtB,GAAsBA,GACxB,aAAY,SAAZ,gBAAY,WAAZ,GAAY,MAAZ,eAAY,CAAEpB,WAAd,IAA4B,gBADJ,CAD1B,GAII,EARwB;QAS7B,CAdY;QAeb0I,QAAQ,EAAE,GAfG;QAgBbC,YAAY,EAAGF,MAAD,IAAgB;;;UAC5B,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;UACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;UACzB,OAAO,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEyG,KAAR,MAAa,IAAb,IAAaxJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,KAAsB,OAAtB,IAAiCqJ,MAAM,CAACG,KAAP,CAAa,CAAb,KAAmB,CAApD,GACH,MAAMH,MAAM,CAACG,KAAP,CAAa,CAAb,IAAkBH,MAAM,CAACG,KAAP,CAAa,CAAb,CAAlB,GAAoC,IAAI,MAD3C,GAEH,CAAC,KAAKpI,qBAAN,GACE,SAASiI,MAAM,CAACG,KAAP,CAAa,CAAb,IACPH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAatJ,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEsJ,KAAR,MAAa,IAAb,IAAa9H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADO,GAGP,IACJ,UALA,GAME,YAAY2H,MAAM,CAACG,KAAP,CAAa,CAAb,IACVH,MAAM,CAACG,KAAP,CAAa,CAAb,KACD,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEA,KAAR,MAAa,IAAb,IAAa3H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAb,IAAqB,KAAK,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAE2H,KAAR,MAAa,IAAb,IAAa1H,aAAb,GAAa,MAAb,GAAaA,GAAG,CAAH,CAAK,GAA5C,GAAkD,EADjD,CADU,GAGV,IACJ,aAZJ;QAaD,CAhCY;QAiCbqH,SAAS,EAAE,gBAjCE;QAkCbgB,aAAa,EAAGC,KAAD,IAAe;;;UAC5B,MAAMC,WAAW,GAAG,WAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAED,KAAP,MAAY,IAAZ,IAAYpK,aAAZ,GAAY,MAAZ,GAAYA,GAAEsK,OAAlC;UACA,MAAMjB,MAAM,GAAG;YAAEG,KAAK,EAAEY,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEZ,KAAhB;YAAuBnK,IAAI,EAAE+K,KAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAE/K;UAApC,CAAf;;UACA,IAAI+K,KAAK,CAAC/K,IAAN,CAAWkI,YAAX,IAA2B,OAA/B,EAAwC;YACtC;UACD,CAFD,MAEO,IAAI6C,KAAK,CAACZ,KAAN,CAAY,CAAZ,KAAkB,CAAtB,EAAyB;YAC9B,IAAIa,WAAJ,EAAiB;cACf,KAAKI,eAAL,CAAqBnC,YAAY,SAAZ,gBAAY,WAAZ,GAAY,MAAZ,eAAY,CAAE1H,WAAnC,EAAgDyI,MAAhD;cACA;YACD;;YACD,KAAKqB,eAAL,CAAqBpC,YAAY,SAAZ,gBAAY,WAAZ,GAAY,MAAZ,eAAY,CAAE1H,WAAnC,EAAgDwJ,KAAhD;UACD;QACF;MA9CY,CAAf;MAgDA,iBAAK1K,WAAL,MAAgB,IAAhB,IAAgBM,aAAhB,GAAgB,MAAhB,GAAgBA,GAAE0I,UAAlB,MAA4B,IAA5B,IAA4BxI,aAA5B,GAA4B,MAA5B,GAA4BA,GAAEwH,IAAF,CAAOiB,GAAP,CAA5B;IACD,CAlDD;IAoDF,KAAKjJ,WAAL,CAAiBgJ,UAAjB,CAA4BzB,OAA5B,CAAoC,CAAC2H,IAAD,EAAYC,KAAZ,KAA6B;MAC/D,IAAIA,KAAK,IAAI,CAAT,IAAcA,KAAK,IAAI,KAAKnP,WAAL,CAAiBgJ,UAAjB,CAA4BlJ,MAA5B,GAAqC,CAAhE,EAAmE;QACjE,KAAKsP,cAAL,CAAoBpH,IAApB,CAAyB;UAAEkB,KAAK,EAAEgG,IAAI,CAAChG,KAAd;UAAqBa,IAAI,EAAEmF,IAAI,CAACnF;QAAhC,CAAzB;MACD;IACF,CAJD;IAKA,KAAK/J,WAAL,CAAiBqP,OAAjB,GAA2B;MACzBC,eAAe,EAAE;IADQ,CAA3B;EAGD;;EAED7C,eAAe,CAAC8C,SAAD,EAAoB7E,KAApB,EAAgC8E,aAAhC,EAAqD;IAClE,MAAMhF,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;;IACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyB;MACvBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;IACD,CAJiE,CAKlE;IACA;IACA;;;IACA,KAAKrE,MAAL,CAAYyQ,QAAZ,CAAqB,CAAC,oBAAD,CAArB;IACA,IAAIC,YAAY,GAAQ,EAAxB;IACAA,YAAY,CAAC1H,IAAb,CAAkBwH,aAAlB;IACA,KAAK5Q,kBAAL,CAAwBe,IAAxB,GAA+B+K,KAAK,CAAC/K,IAArC;IACA,KAAKf,kBAAL,CAAwB2D,QAAxB,GAAmC,KAAKN,aAAL,CAAmBM,QAAtD;IACA,KAAK3D,kBAAL,CAAwB0I,MAAxB,GAAiCiI,SAAjC;IACA,KAAK3Q,kBAAL,CAAwB+Q,OAAxB,GAAkC,KAAKhO,cAAvC;IACA,KAAK/C,kBAAL,CAAwB4Q,aAAxB,GAAwCE,YAAxC;EACD;;EAEDlD,qBAAqB,CAAC+C,SAAD,EAAoB5F,MAApB,EAAiC6F,aAAjC,EAAsD;IACzE,MAAMhF,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;;IACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyB;MACvBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;IACD;;IACDiG,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEwF,IAAR,CACE,0DAA0DC,kBAAkB,CAC1EC,IAAI,CAACC,SAAL,CAAetF,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEhK,IAAvB,CAD0E,CAE3E,cAAc4P,SAAS,kBAAkBC,aAAa,mBAAmBT,kBAAkB,CAC1FC,IAAI,CAACC,SAAL,CAAezE,OAAf,CAD0F,CAE3F,EALH,EAME,QANF;EAQD;;EAEDoF,WAAW,CAACjG,MAAD,EAAY;IACrB,KAAKkG,OAAL,GAAelG,MAAM,CAACmG,GAAtB;IACA,KAAKC,aAAL,GAAqBpG,MAAM,CAACqG,SAA5B;IACA,KAAKC,aAAL,CAAmBtG,MAAnB;IACA,KAAK3J,WAAL,CAAiB8P,GAAjB,GAAuBnG,MAAM,CAACmG,GAA9B;EACD;;EAED9E,eAAe,CAACuE,SAAD,EAAoB7E,KAApB,EAA8B;IAC3C;IACA;IACA;IACA,KAAK1L,MAAL,CAAYyQ,QAAZ,CAAqB,CAAC,oBAAD,CAArB;IACA,KAAK7Q,kBAAL,CAAwB4Q,aAAxB,GAAwCzN,SAAxC;IACA,KAAKnD,kBAAL,CAAwB2D,QAAxB,GAAmC,KAAKN,aAAL,CAAmBM,QAAtD;IACA,KAAK3D,kBAAL,CAAwBe,IAAxB,GAA+B+K,KAAK,CAAC/K,IAArC;IACA,KAAKf,kBAAL,CAAwB0I,MAAxB,GAAiCiI,SAAjC;IACA,MAAM/E,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;IACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;IACzB,KAAKzE,kBAAL,CAAwB+Q,OAAxB,GAAkCnF,OAAlC;EACD;;EAEDO,eAAe,CAACwE,SAAD,EAAoB5F,MAApB,EAA+B;IAC5C;IACA;IACA;IACA,MAAMa,OAAO,qBAAQ,KAAK7I,cAAb,CAAb;;IACA,IAAI6I,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEnH,UAAb,EAAyB;MACvBmH,OAAO,CAACnH,UAAR,GAAqB,KAArB;IACD;;IACDiG,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEwF,IAAR,CACE,kDAAkDC,kBAAkB,CAClEC,IAAI,CAACC,SAAL,CAAetF,MAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEhK,IAAvB,CADkE,CAEnE,cAAc4P,SAAS,mBAAmBR,kBAAkB,CAC3DC,IAAI,CAACC,SAAL,CAAezE,OAAf,CAD2D,CAE5D,EALH,EAME,QANF;EAQD;;EAED0F,YAAY,CAACC,CAAD,EAAO;IACjB,KAAKC,UAAL,GAAkBD,CAAlB;IACA,KAAKxO,cAAL,GAAmB/B,gCACd,KAAK+B,cADS,GACK;MACtBE,QAAQ,EAAE,KAAKA,QADO;MAEtBK,UAAU,EAAEiO,CAAC,GAAG;IAFM,CADL,CAAnB;IAKA,KAAKN,OAAL,CAAaQ,kBAAb,CAAgCF,CAAhC;;IACA,KAAKtR,MAAL,CAAY2H,QAAZ,CAAqB,IAAIhK,uBAAJ,CAA4B,KAAKmF,cAAjC,CAArB;;IACA,KAAKD,qBAAL,GACI,KAAK7C,MAAL,CAAY2H,QAAZ,CAAqB,IAAInK,sBAAJ,EAArB,CADJ,GAEI,KAAKwC,MAAL,CAAY2H,QAAZ,CAAqB,IAAIlK,gBAAJ,EAArB,CAFJ;EAGD;;EAEDgU,iBAAiB;IACf,KAAKrO,aAAL,GAAkBrC,gCACb,KAAKqC,aADQ,GACK;MACrBM,QAAQ,EAAE,IADW;MAErBG,IAAI,EAAE;IAFe,CADL,CAAlB;IAKA,KAAK6F,cAAL;EACD;;EAEDgI,iBAAiB,CAACzI,GAAD,EAAcnB,MAAd,EAA4B;;;IAC3C,MAAM6J,WAAW,GAAG,CAClB,YADkB,EAElB,UAFkB,EAGlB,aAHkB,EAIlB,QAJkB,EAKlB,QALkB,CAApB;IAQA,IACE,CACE,UADF,EAEE,YAFF,EAGE,YAHF,EAIE,UAJF,EAKE,MALF,EAME,YANF,EAOE,QAPF,EAQE,eARF,EASEzI,QATF,CASWD,GATX,KAUA,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEhI,MAAR,MAAmB,CAXrB,EAaE,OAAO,EAAP,CAbF,KAcK,IAAIgI,GAAG,KAAK,MAAR,IAAkBnB,MAAM,CAAC7G,MAAP,KAAkB,CAAxC,EAA2C;MAC9C,IAAIgI,GAAG,KAAK,MAAR,IAAkBnB,MAAM,CAAC,CAAD,CAAN,KAAc,IAApC,EAA0C;QACxC,KAAK3D,MAAL,GAAclI,eAAe,CAC3B,IAAIuE,IAAJ,CAASsH,MAAM,CAAC,CAAD,CAAf,CAD2B,EAE3B,iBAAKvG,QAAL,MAAa,IAAb,IAAaE,aAAb,GAAa,MAAb,GAAaA,GAAEC,YAAf,MAA2B,IAA3B,IAA2BC,aAA3B,GAA2B,MAA3B,GAA2BA,GAAEC,aAFF,CAA7B;QAIA,KAAKmC,QAAL,GAAgB9H,eAAe,CAC7B,IAAIuE,IAAJ,CAASsH,MAAM,CAAC,CAAD,CAAf,CAD6B,EAE7B,iBAAKvG,QAAL,MAAa,IAAb,IAAa4B,aAAb,GAAa,MAAb,GAAaA,GAAEzB,YAAf,MAA2B,IAA3B,IAA2B4B,aAA3B,GAA2B,MAA3B,GAA2BA,GAAE1B,aAFA,CAA/B;QAIA,MAAMgQ,eAAe,GAAG/V,eAAe,CACrC,KAAKsI,MADgC,EAErC,iBAAK5C,QAAL,MAAa,IAAb,IAAagC,aAAb,GAAa,MAAb,GAAaA,GAAE7B,YAAf,MAA2B,IAA3B,IAA2B+B,aAA3B,GAA2B,MAA3B,GAA2BA,GAAE7B,aAFQ,EAGrC,cAHqC,CAAvC;QAKA,MAAMiQ,iBAAiB,GAAGhW,eAAe,CACvC,KAAKkI,QADkC,EAEvC,iBAAKxC,QAAL,MAAa,IAAb,IAAaqC,aAAb,GAAa,MAAb,GAAaA,GAAElC,YAAf,MAA2B,IAA3B,IAA2BoC,aAA3B,GAA2B,MAA3B,GAA2BA,GAAElC,aAFU,EAGvC,cAHuC,CAAzC;QAKA,MAAMkQ,eAAe,GAAG,GAAGF,eAAe,OAAOC,iBAAiB,EAAlE;QACA,OAAO,CAACC,eAAD,CAAP;MACD,CArBD,MAqBO;QACL,OAAO,IAAP;MACD;IACF,CAzBI,MAyBE,IAAIH,WAAW,CAACzI,QAAZ,CAAqBD,GAArB,CAAJ,EAA+B;MACpC,OAAOnB,MAAP;IACD;IACD,OAAO,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEiK,QAAR,QAAkB,IAAlB,IAAkB/N,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEgO,KAAF,CAAQ,GAAR,CAAzB;EACD;;EAEDC,mBAAmB;IACjB,KAAKvI,cAAL;IACA,KAAKtJ,YAAL,CAAkB8K,IAAlB;EACD;;EAEDgH,cAAc,CAACjJ,GAAD,EAAcgC,KAAd,EAA2B;;;IACvC,IAAI,CAAC,UAAD,EAAa,MAAb,EAAqB/B,QAArB,CAA8BD,GAA9B,CAAJ,EAAwC;MACtC,OAAO,KAAK7F,aAAL,CAAmB6F,GAAnB,CAAP;MACA,MAAMkJ,YAAY,GAAGlJ,GAAG,KAAK,MAAR,GAAiB,UAAjB,GAA8B,MAAnD;;MACA,IAAI,KAAK7F,aAAL,CAAmB+O,YAAnB,CAAJ,EAAsC;QACpC,OAAO,KAAK/O,aAAL,CAAmB+O,YAAnB,CAAP;MACD;IACF,CAND,MAMO;MACL,KAAK/O,aAAL,CAAmB6F,GAAnB,IAA0B,WAAK7F,aAAL,CAAmB6F,GAAnB,OAAuB,IAAvB,IAAuBxH,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEM,MAAF,CAC/C,CAACsO,IAAD,EAAYC,KAAZ,KAA6B;;;QAC3B,MAAM8B,UAAU,GAAG,WAAKhP,aAAL,CAAmB6F,GAAnB,OAAuB,IAAvB,IAAuBxH,aAAvB,GAAuB,MAAvB,GAAuBA,GAAE4Q,OAAF,CAAUpH,KAAV,CAA1C;QACA,OAAOqF,KAAK,KAAK8B,UAAjB;MACD,CAJ8C,CAAjD;IAMD;;IACD,KAAK1I,cAAL;EACD;;EAED4I,mBAAmB,CAACC,UAAD,EAA6B;IAC9C,KAAKvS,MAAL,CAAY2H,QAAZ,CAAqB,IAAIjL,gBAAJ,EAArB;;IACA,KAAKsD,MAAL,CAAY2H,QAAZ,CAAqB,IAAIhL,kBAAJ,EAArB;;IACA,KAAKqD,MAAL,CAAY2H,QAAZ,CAAqB,IAAInL,eAAJ,EAArB;;IACA,KAAKwD,MAAL,CAAY2H,QAAZ,CAAqB,IAAIlL,eAAJ,EAArB,EAJ8C,CAK9C;;;IACA,KAAKuD,MAAL,CAAY2H,QAAZ,CAAqB,IAAIxL,eAAJ,EAArB;;IACA,IAAIqW,YAAY,GAAQ;MACtBC,KAAK,EAAE;IADe,CAAxB;IAGA,KAAKrS,YAAL,CAAkB8H,IAAlB,CAAuBqK,UAAvB,EAAmCC,YAAnC;EACD;;EAEDE,WAAW,CAACC,EAAD,EAAW;;;IACpB,IAAI5J,QAAQ,GAAG,EAAf;IACA,WAAKtC,QAAL,MAAa,IAAb,IAAahF,aAAb,GAAa,MAAb,GAAaA,GAAEiH,OAAF,CAAWrC,IAAD,IAAc;MACnC,IAAIsM,EAAE,KAAKtM,IAAI,CAACsM,EAAhB,EAAoB5J,QAAQ,GAAG,GAAG1C,IAAI,CAACC,QAAQ,EAA3B;IACrB,CAFY,CAAb;IAGA,OAAOyC,QAAP;EACD;;EACDqI,aAAa,CAACtG,MAAD,EAAY;;;IACvB,KAAK8H,OAAL,GAAe,kBAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEzB,SAAR,MAAiB,IAAjB,IAAiB1P,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEoR,UAAF,EAAjB,MAA+B,IAA/B,IAA+BlR,aAA/B,GAA+B,MAA/B,GAA+BA,GAAEjH,GAAF,CAAOoY,MAAD,IAAgB;;;MAClE,OAAO;QACLC,KAAK,EAAE,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEC,SAAR,QAAmB,IAAnB,IAAmBvR,aAAnB,GAAmB,MAAnB,GAAmBA,GAAE8I,UADvB;QAELU,KAAK,EAAE6H;MAFF,CAAP;IAID,CAL6C,CAA9C,CADuB,CAQvB;;IACA,KAAKF,OAAL,GAAe,CAAC,GAAG,KAAKA,OAAL,CAAarS,KAAb,CAAmB,CAAnB,EAAsB,WAAKqS,OAAL,MAAY,IAAZ,IAAYzP,aAAZ,GAAY,MAAZ,GAAYA,GAAElC,MAApC,CAAJ,EAAiDiB,IAAjD,CACb,CAACC,CAAD,EAASC,CAAT,KAAmB;MAAA;;MAAC,cAAC,SAAD,KAAC,WAAD,GAAC,MAAD,IAAC,CAAE2Q,KAAH,MAAQ,IAAR,IAAQtR,aAAR,GAAQ,MAAR,GAAQA,GAAEa,aAAF,CAAgBF,CAAC,SAAD,KAAC,WAAD,GAAC,MAAD,IAAC,CAAE2Q,KAAnB,CAAR;IAAiC,CADxC,CAAf;IAGA,KAAKE,cAAL,GAAsB,WAAKL,OAAL,MAAY,IAAZ,IAAYtP,aAAZ,GAAY,MAAZ,GAAYA,GAAEvB,MAAF,CAC/BqI,GAAD,IAAQ;MAAA;;MAAC,uBAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEa,KAAL,MAAU,IAAV,IAAUxJ,aAAV,GAAU,MAAV,GAAUA,GAAEuR,SAAF,EAAV,MAAuB,IAAvB,IAAuBrR,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEuJ,IAAzB,MAAkC,IAAlC;IAAsC,CADf,CAAlC;IAIA,IAAIgI,WAAW,GAAG/C,IAAI,CAACgD,KAAL,CAAWC,YAAY,CAACC,OAAb,CAAqB,mBAArB,CAAX,CAAlB;;IACA,IAAIH,WAAJ,EAAiB;MACf,KAAKhC,aAAL,CAAmBoC,gBAAnB,CAAoC;QAClCC,KAAK,EAAEL,WAD2B;QAElCM,UAAU,EAAE;MAFsB,CAApC;IAID;;IAED,IAAIC,UAAU,GAAG,kBAAY,CAACJ,OAAb,CAAqB,wBAArB,OAA8C,IAA9C,IAA8C9P,aAA9C,GAA8C,MAA9C,GAA8CA,GAAEyO,KAAF,CAAQ,GAAR,CAA/D;;IAEA,IAAIyB,UAAU,SAAV,cAAU,WAAV,GAAU,MAAV,aAAU,CAAExS,MAAhB,EAAwB;MACtB,IAAIyS,cAAc,GAAG,WAAKd,OAAL,MAAY,IAAZ,IAAYnP,aAAZ,GAAY,MAAZ,GAAYA,GAAE1B,MAAF,CAAUqI,GAAD,IACxCqJ,UAAU,SAAV,cAAU,WAAV,GAAU,MAAV,aAAU,CAAEvK,QAAZ,CAAqBkB,GAAG,CAAC2I,KAAzB,CAD+B,CAAjC;MAGA,KAAKE,cAAL,GAAsBS,cAAtB;MACA,KAAKC,iBAAL,CAAuBD,cAAvB;IACD;EACF;;EAEDC,iBAAiB,CAACf,OAAD,EAAa;;;IAC5B,IAAIgB,OAAO,GAAGhB,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAElY,GAAT,CAAcoY,MAAD,IAAiBA,MAAM,CAACC,KAArC,CAAd;IACAK,YAAY,CAACS,OAAb,CAAqB,wBAArB,EAA+CD,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAE7B,QAAT,EAA/C;IACA,MAAM+B,IAAI,GAAGlB,OAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAElY,GAAT,CAAc0P,GAAD,IAAcA,GAAG,CAACa,KAA/B,CAAb;IACA,WAAKiG,aAAL,MAAkB,IAAlB,IAAkBzP,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEsS,iBAAF,CAAoBD,IAApB,EAA0B,IAA1B,CAAlB;IACA,MAAME,eAAe,GAAG,WAAKpB,OAAL,MAAY,IAAZ,IAAYjR,aAAZ,GAAY,MAAZ,GAAYA,GAAEI,MAAF,CAAUqI,GAAD,IAAa;MACxD,OAAO,CAAC0J,IAAI,CAAC5K,QAAL,CAAckB,GAAG,CAACa,KAAlB,CAAR;IACD,CAFmC,CAApC;IAGA,WAAKiG,aAAL,MAAkB,IAAlB,IAAkB/N,aAAlB,GAAkB,MAAlB,GAAkBA,GAAE4Q,iBAAF,CAChBC,eAAe,CAACtZ,GAAhB,CAAqB0P,GAAD,IAASA,GAAG,CAACa,KAAjC,CADgB,EAEhB,KAFgB,CAAlB;IAIA,IAAIiI,WAAW,GAAQ,KAAKhC,aAAL,CAAmB+C,cAAnB,EAAvB;IACA,IAAIf,WAAW,IAAIA,WAAW,CAAC,CAAD,CAA9B,EAAmCA,WAAW,CAAC,CAAD,CAAX,CAAe1I,MAAf,GAAwB,MAAxB;IACnC4I,YAAY,CAACS,OAAb,CAAqB,mBAArB,EAA0C1D,IAAI,CAACC,SAAL,CAAe8C,WAAf,CAA1C;IACA,KAAKhC,aAAL,CAAmBoC,gBAAnB,CAAoC;MAClCC,KAAK,EAAEL,WAD2B;MAElCM,UAAU,EAAE;IAFsB,CAApC;EAID;;EAEDU,kBAAkB;IAChB,KAAKjB,cAAL,GAAsB,KAAKL,OAAL,CAAa7Q,MAAb,CACnBqI,GAAD,IAASA,GAAG,CAACa,KAAJ,CAAU+H,SAAV,GAAsB9H,IAAtB,KAA+B,IADpB,CAAtB;IAGA,KAAKyI,iBAAL,CAAuB,KAAKV,cAA5B;EACD;;EAEDtM,iBAAiB,CAACnD,UAAD,EAAkB2Q,gBAAlB,EAAuC;;;IACtD,KAAK/Q,aAAL,CAAmBH,UAAnB,GAAgCO,UAAhC;IACA,KAAKJ,aAAL,CAAmBC,UAAnB,GAAgC,CAAhC;;IACA,IAAI8Q,gBAAJ,EAAsB;MACpB,KAAK/Q,aAAL,CAAmBqB,KAAnB,GAA2B,IAA3B;IACD;;IACD,KAAKiF,cAAL;;IAEA,IAAI,KAAKlC,eAAT,EAA0B;MACxB,QAAQhE,UAAR;QACE,KAAK,CAAL;UACE,KAAKiD,QAAL,GAAgB,WAAKhC,KAAL,MAAU,IAAV,IAAUhD,aAAV,GAAU,MAAV,GAAUA,GAAEM,MAAF,CAAUsE,IAAD,IAAeA,IAAI,CAAC+N,QAA7B,CAA1B;UACA;;QACF,KAAK,CAAL;UACE,KAAK3N,QAAL,GAAgB,WAAKhC,KAAL,MAAU,IAAV,IAAU9C,aAAV,GAAU,MAAV,GAAUA,GAAEI,MAAF,CAAUsE,IAAD,IAAe,CAACA,IAAI,CAAC+N,QAA9B,CAA1B;UACA;;QACF,KAAK,IAAL;UACE,KAAK3N,QAAL,GAAgB,KAAKhC,KAArB;UACA;MATJ;;MAWA,KAAKgC,QAAL,GAAgBjL,YAAY,CAAC,KAAKiL,QAAN,EAAgB,EAAhB,CAA5B;IACD,CAbD,MAaO;MACL,QAAQjD,UAAR;QACE,KAAK,CAAL;UACE,KAAKqD,aAAL,GAAqB,WAAKD,SAAL,MAAc,IAAd,IAAczD,aAAd,GAAc,MAAd,GAAcA,GAAEpB,MAAF,CAChCsE,IAAD,IAAeA,IAAI,CAAC+N,QADa,CAAnC;UAGA;;QACF,KAAK,CAAL;UACE,KAAKvN,aAAL,GAAqB,WAAKD,SAAL,MAAc,IAAd,IAActD,aAAd,GAAc,MAAd,GAAcA,GAAEvB,MAAF,CAChCsE,IAAD,IAAe,CAACA,IAAI,CAAC+N,QADY,CAAnC;UAGA;;QACF,KAAK,IAAL;UACE,KAAKvN,aAAL,GAAqB,KAAKD,SAA1B;UACA;MAbJ;;MAeA,KAAKC,aAAL,GAAqBrL,YAAY,CAAC,KAAKqL,aAAN,EAAqB,EAArB,CAAjC;IACD;EACF;;EAEDwN,WAAW;IACT,KAAKrR,QAAL,GAAgB,KAAKwG,gBAArB;IACA,KAAK1G,cAAL,GAAmB/B,gCACd,KAAK+B,cADS,GACK;MACtBE,QAAQ,EAAE,KAAKA,QADO;MAEtBK,UAAU,EAAE;IAFU,CADL,CAAnB;;IAKA,KAAKrD,MAAL,CAAY2H,QAAZ,CAAqB,IAAIhK,uBAAJ,CAA4B,KAAKmF,cAAjC,CAArB;;IACA,KAAKD,qBAAL,GACI,KAAK7C,MAAL,CAAY2H,QAAZ,CAAqB,IAAInK,sBAAJ,EAArB,CADJ,GAEI,KAAKwC,MAAL,CAAY2H,QAAZ,CAAqB,IAAIlK,gBAAJ,EAArB,CAFJ;IAGA,KAAK8T,UAAL,GAAkB,CAAlB;EACD;;EAED7H,cAAc;;;IACZ,KAAKtG,aAAL,CAAmBC,UAAnB,GAAgC,CAAhC;;IACA,IACE,kBAAKD,aAAL,MAAkB,IAAlB,IAAkB3B,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEiC,QAApB,MAA4B,IAA5B,IAA4B/B,aAA5B,GAA4B,MAA5B,GAA4BA,GAAEV,MAA9B,MACA,uBAAKmC,aAAL,MAAkB,IAAlB,IAAkBD,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEU,IAApB,MAAwB,IAAxB,IAAwBP,aAAxB,GAAwB,MAAxB,GAAwBA,GAAG,CAAH,CAAxB,MAA6B,IAA7B,IAA6BC,aAA7B,GAA6B,MAA7B,GAA6BA,GAAEtC,MAD/B,MAEA,WAAKmC,aAAL,CAAmBqB,KAAnB,MAAwB,IAAxB,IAAwBhB,aAAxB,GAAwB,MAAxB,GAAwBA,GAAExC,MAF1B,MAGA,WAAKmC,aAAL,CAAmBiC,QAAnB,MAA2B,IAA3B,IAA2BzB,aAA3B,GAA2B,MAA3B,GAA2BA,GAAE3C,MAH7B,MAIA,WAAKmC,aAAL,CAAmB8B,UAAnB,MAA6B,IAA7B,IAA6BpB,aAA7B,GAA6B,MAA7B,GAA6BA,GAAE7C,MAJ/B,MAKA,WAAKmC,aAAL,CAAmB2B,OAAnB,MAA0B,IAA1B,IAA0Bf,aAA1B,GAA0B,MAA1B,GAA0BA,GAAE/C,MAL5B,MAMA,WAAKmC,aAAL,CAAmBoC,MAAnB,MAAyB,IAAzB,IAAyBvB,aAAzB,GAAyB,MAAzB,GAAyBA,GAAEhD,MAN3B,MAOA,WAAKmC,aAAL,CAAmBkR,SAAnB,MAA4B,IAA5B,IAA4BpQ,aAA5B,GAA4B,MAA5B,GAA4BA,GAAEjD,MAP9B,MAQA,WAAKmC,aAAL,CAAmBuC,MAAnB,MAAyB,IAAzB,IAAyBvB,aAAzB,GAAyB,MAAzB,GAAyBA,GAAEnD,MAR3B,CADF,EAUE;MACA,KAAKsT,WAAL,GAAmB,IAAnB;IACD,CAZD,MAYO;MACL,KAAKA,WAAL,GAAmB,KAAnB;IACD;;IACD,KAAKzR,cAAL,GAAmB/B,gCACd,KAAK+B,cADS,GACK;MACtBO,UAAU,EAAE,WAAKD,aAAL,MAAkB,IAAlB,IAAkBiB,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEhB,UADV;MAEtBL,QAAQ,EAAE,KAAKA,QAFO;MAGtBC,UAAU,EAAE,KAAKG,aAAL,CAAmBH,UAHT;MAItBS,QAAQ,EAAEnI,cAAc,CAAC,KAAK6H,aAAL,CAAmBM,QAApB,CAJF;MAKtBK,QAAQ,EAAE9H,eAAe,CACvB,iBAAKmH,aAAL,MAAkB,IAAlB,IAAkBmB,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEV,IAApB,MAAwB,IAAxB,IAAwBa,aAAxB,GAAwB,MAAxB,GAAwBA,GAAG,CAAH,CADD,EAEvB,iBAAKnD,QAAL,MAAa,IAAb,IAAasD,aAAb,GAAa,MAAb,GAAaA,GAAEnD,YAAf,MAA2B,IAA3B,IAA2BsD,aAA3B,GAA2B,MAA3B,GAA2BA,GAAEpD,aAFN,CALH;MAStBuC,MAAM,EAAElI,eAAe,CACrB,WAAKmH,aAAL,CAAmBS,IAAnB,MAAuB,IAAvB,IAAuBsB,aAAvB,GAAuB,MAAvB,GAAuBA,GAAG,CAAH,CADF,EAErB,iBAAK5D,QAAL,MAAa,IAAb,IAAa+D,aAAb,GAAa,MAAb,GAAaA,GAAE5D,YAAf,MAA2B,IAA3B,IAA2B+D,aAA3B,GAA2B,MAA3B,GAA2BA,GAAE7D,aAFR,CATD;MAatB4C,UAAU,EAAE,KAAKpB,aAAL,CAAmBkB,QAbT;MActBK,OAAO,EAAE,KAAKvB,aAAL,CAAmBqB,KAdN;MAetBK,UAAU,EAAE,KAAK0P,UAfK;MAgBtBvP,OAAO,EAAE,KAAK7B,aAAL,CAAmB2B,OAhBN;MAiBtBK,UAAU,EAAE,KAAKhC,aAAL,CAAmB8B,UAjBT;MAkBtBK,QAAQ,EAAE,KAAKnC,aAAL,CAAmBiC,QAlBP;MAmBtBoP,gBAAgB,EAAE,KAAKjN,eAAL,GAAuB,CAAvB,GAA2B,CAnBvB;MAoBtBkN,gBAAgB,EAAE,KAAKpN,iBAAL,GAAyB,CAAzB,GAA6B,CApBzB;MAqBtB5B,MAAM,EAAE,WAAKtC,aAAL,MAAkB,IAAlB,IAAkBwC,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEJ,MArBN;MAsBtBK,MAAM,EAAE,WAAKzC,aAAL,MAAkB,IAAlB,IAAkB2C,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEJ,MAtBN;MAuBtB;MACAG,aAAa,EAAE,iBAAK1C,aAAL,MAAkB,IAAlB,IAAkB4C,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEF,aAApB,MAAiC,IAAjC,IAAiCI,aAAjC,GAAiCA,EAAjC,GAAqC,IAxB9B;MAyBtBC,oBAAoB,EAAE,KAAKmG;IAzBL,CADL,CAAnB;;IA4BA,KAAKtM,MAAL,CAAY2H,QAAZ,CAAqB,IAAIhK,uBAAJ,CAA4B,KAAKmF,cAAjC,CAArB;;IACA,KAAKD,qBAAL,GACI,KAAK7C,MAAL,CAAY2H,QAAZ,CAAqB,IAAInK,sBAAJ,EAArB,CADJ,GAEI,KAAKwC,MAAL,CAAY2H,QAAZ,CAAqB,IAAIlK,gBAAJ,EAArB,CAFJ;IAGA,KAAK8T,UAAL,GAAkB,CAAlB;EACD;;EAEDoD,KAAK;IACH,KAAKvR,aAAL,GAAqB;MACnBC,UAAU,EAAE,CADO;MAEnBL,QAAQ,EAAE,KAAKA;IAFI,CAArB;IAIA,KAAK0G,cAAL;EACD;;EAEDlH,eAAe;;;IACb,IAAI,iBAAKY,aAAL,MAAkB,IAAlB,IAAkB3B,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEsD,OAApB,MAA2B,IAA3B,IAA2BpD,aAA3B,GAA2B,MAA3B,GAA2BA,GAAEV,MAAjC,EAAyC;MACvC,KAAK4G,aAAL,GAAqB,EAArB;MACA,WAAKzE,aAAL,MAAkB,IAAlB,IAAkBD,aAAlB,GAAkB,MAAlB,GAAkBA,GAAE4B,OAAF,CAAU2D,OAAV,CAAmBkM,CAAD,IAAW;;;QAC7C,MAAM5S,MAAM,GAAQ1G,UAAU,CAACsZ,CAAD,CAA9B;QACA,MAAM/S,UAAU,GAAGxG,iBAAiB,CAAC2G,MAAD,CAApC;;QACA,IAAIH,UAAU,KAAK,UAAnB,EAA+B;UAC7B,KAAKgG,aAAL,CAAmBsB,IAAnB,CAAwB0L,KAAxB,CACE,KAAKhN,aADP,EAEE,KAAKD,gBAAL,CAAsB,iBAAtB,KAA4C,EAF9C;QAID,CALD,MAKO;UACL,MAAMkN,YAAY,GAAGjT,UAAU,SAAV,cAAU,WAAV,GAAU,MAAV,aAAU,CAAEkT,OAAZ,CAAoB,MAApB,EAA4B,EAA5B,CAArB;;UACA,IAAIC,KAAK,CAACC,OAAN,CAAc,WAAKrN,gBAAL,MAAqB,IAArB,IAAqBnG,aAArB,GAAqB,MAArB,GAAqBA,GAAGqT,YAAH,CAAnC,CAAJ,EAA0D;YACxD,KAAKjN,aAAL,CAAmBsB,IAAnB,CAAwB0L,KAAxB,CACE,KAAKhN,aADP,EAEE,YAAKD,gBAAL,MAAqB,IAArB,IAAqBjG,aAArB,GAAqB,MAArB,GAAqBA,GAAGmT,YAAH,CAArB,KAAyC,EAF3C;UAID,CALD,MAKO;YACL,KAAKjN,aAAL,CAAmBsB,IAAnB,CAAwB0L,KAAxB,CACE,KAAKhN,aADP,EAEE,KAAKD,gBAAL,CAAsB/F,UAAtB,KAAqC,EAFvC;UAID;QACF;MACF,CAtBiB,CAAlB;IAuBD,CAzBD,MAyBO;MACL,IAAIgG,aAAa,GAAa,YAAKtF,WAAL,MAAgB,IAAhB,IAAgBe,aAAhB,GAAgB,MAAhB,GAAgBA,GAAE4R,OAAF,CAAWC,IAAD,IAAwB;;;QAC9E,IAAI,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE9S,WAAN,MAAsB,UAA1B,EAAsC;UACpC,OAAO,YAAKuF,gBAAL,MAAqB,IAArB,IAAqBnG,aAArB,GAAqB,MAArB,GAAqBA,GAAG,iBAAH,CAArB,KAA8C,EAArD;QACD;;QACD,MAAMqT,YAAY,GAAG,UAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEzS,WAAN,MAAiB,IAAjB,IAAiBV,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEoT,OAAF,CAAU,MAAV,EAAkB,EAAlB,CAAtC;QACA,IAAIK,KAAK,GAAG,WAAKxN,gBAAL,MAAqB,IAArB,IAAqBzE,aAArB,GAAqB,MAArB,GAAqBA,GAAG2R,YAAH,CAAjC;;QACA,IAAI,CAACM,KAAL,EAAY;UACVA,KAAK,GAAG,KAAKxN,gBAAL,CAAsBuN,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE9S,WAA5B,CAAR;QACD;;QACD,IAAI,CAAC+S,KAAD,IAAU,aAAY,SAAZ,gBAAY,WAAZ,GAAY,MAAZ,eAAY,CAAEC,WAAd,QAAgC,SAA9C,EAAyD;UACvDD,KAAK,GAAG,KAAKxN,gBAAL,CAAsB,iBAAtB,CAAR;QACD;;QACD,OAAOoN,KAAK,CAACC,OAAN,CAAcG,KAAd,IAAuBA,KAAvB,GAA+B,EAAtC;MACD,CAb6C,CAAhB,KAaxB,EAbN;MAcA,KAAKvN,aAAL,GAAqBA,aAArB;IACD;EACF;;EAEDyN,cAAc,CAACtT,MAAD,EAAY;IACxB,IAAIA,MAAJ,EAAY;MACV,KAAKuT,gBAAL,CAAsBvT,MAAM,CAACK,WAA7B;IACD,CAFD,MAEO;MACL,KAAKkT,gBAAL,CAAsB,IAAtB;IACD;EACF;;EAEDA,gBAAgB,CAACC,UAAD,EAA0B;;;IACxC,IAAIA,UAAJ,EAAgB;MACd,IAAIA,UAAU,KAAK,UAAnB,EAA+B;QAC7B,KAAK3N,aAAL,GAAqB,KAAKD,gBAAL,CAAsB,iBAAtB,KAA4C,EAAjE;MACD,CAFD,MAEO;QACL,MAAMkN,YAAY,GAAGU,UAAU,CAACT,OAAX,CAAmB,MAAnB,EAA2B,EAA3B,CAArB;;QACA,IAAIC,KAAK,CAACC,OAAN,CAAc,WAAKrN,gBAAL,MAAqB,IAArB,IAAqBnG,aAArB,GAAqB,MAArB,GAAqBA,GAAGqT,YAAH,CAAnC,CAAJ,EAA0D;UACxD,KAAKjN,aAAL,GAAqB,YAAKD,gBAAL,MAAqB,IAArB,IAAqBjG,aAArB,GAAqB,MAArB,GAAqBA,GAAGmT,YAAH,CAArB,KAAyC,EAA9D;QACD,CAFD,MAEO;UACL,KAAKjN,aAAL,GAAqB,KAAKD,gBAAL,CAAsB4N,UAAtB,KAAqC,EAA1D;QACD;MACF;IACF,CAXD,MAWO;MACL,IAAI3N,aAAa,GAAa,YAAKtF,WAAL,MAAgB,IAAhB,IAAgBY,aAAhB,GAAgB,MAAhB,GAAgBA,GAAE+R,OAAF,CAAWC,IAAD,IAAwB;;;QAC9E,IAAI,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE9S,WAAN,MAAsB,UAA1B,EAAsC;UACpC,OAAO,YAAKuF,gBAAL,MAAqB,IAArB,IAAqBnG,aAArB,GAAqB,MAArB,GAAqBA,GAAG,iBAAH,CAArB,KAA8C,EAArD;QACD;;QACD,MAAMqT,YAAY,GAAG,UAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEzS,WAAN,MAAiB,IAAjB,IAAiBV,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEoT,OAAF,CAAU,MAAV,EAAkB,EAAlB,CAAtC;QACA,IAAIK,KAAK,GAAG,WAAKxN,gBAAL,MAAqB,IAArB,IAAqBzE,aAArB,GAAqB,MAArB,GAAqBA,GAAG2R,YAAH,CAAjC;;QACA,IAAI,CAACM,KAAL,EAAY;UACVA,KAAK,GAAG,KAAKxN,gBAAL,CAAsBuN,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE9S,WAA5B,CAAR;QACD;;QACD,IAAI,CAAC+S,KAAD,IAAU,aAAY,SAAZ,gBAAY,WAAZ,GAAY,MAAZ,eAAY,CAAEC,WAAd,QAAgC,SAA9C,EAAyD;UACvDD,KAAK,GAAG,KAAKxN,gBAAL,CAAsB,iBAAtB,CAAR;QACD;;QACD,OAAOoN,KAAK,CAACC,OAAN,CAAcG,KAAd,IAAuBA,KAAvB,GAA+B,EAAtC;MACD,CAb6C,CAAhB,KAaxB,EAbN;MAcA,KAAKvN,aAAL,GAAqBA,aAArB;IACD;EACF;;EAED4N,OAAO;;;IACL,WAAKtU,WAAL,CAAiB8P,GAAjB,MAAoB,IAApB,IAAoBxP,aAApB,GAAoB,MAApB,GAAoBA,GAAEiU,YAAF,EAApB;EACD;;EAEDC,gBAAgB;;;IACd,KAAK3V,MAAL,CAAY2H,QAAZ,CAAqB,IAAIjK,sBAAJ,CAA2B,EAA3B,CAArB;;IACA,KAAKgM,cAAL;IACA,IAAI8I,YAAY,GAAQ;MACtB1B,OAAO,kCACF,KAAKhO,cADH,GACiB;QACtB8S,eAAe,EAAE,WAAK1E,aAAL,CACd+C,cADc,GAEdlS,MAFc,CAENqI,GAAD,IAAc,EAACA,GAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEc,IAAN,CAFP,EAGdxQ,GAHc,CAGT0P,GAAD,IAAcA,GAAG,SAAH,OAAG,WAAH,GAAG,MAAH,MAAG,CAAEyL,KAHT,OAGe,IAHf,IAGepU,aAHf,GAGe,MAHf,GAGeA,GAAE/G,GAAF,CAAO0P,GAAD,IAAa;UAC/C,IAAIA,GAAG,KAAK,gBAAZ,EAA8B;YAC5B,OAAO,iBAAP;UACD,CAFD,MAGK,IAAIA,GAAG,KAAK,oBAAZ,EAAkC;YACrC,OAAO,qBAAP;UACD,CAFI,MAGA,IAAIA,GAAG,KAAK,oBAAZ,EAAkC;YACrC,OAAO,sBAAP;UACD;;UACD,OAAOA,GAAP;QACD,CAX6B,CAJV;QAgBtB0L,UAAU,EACR,kBAAKvU,QAAL,MAAa,IAAb,IAAaI,aAAb,GAAa,MAAb,GAAaA,GAAED,YAAf,MAA2B,IAA3B,IAA2ByB,aAA3B,GAA2B,MAA3B,GAA2BA,GAAE2S,UAA7B,KAA2Cla,mBAAmB,EAjB1C;QAkBtBgG,aAAa,EACX,kBAAKL,QAAL,MAAa,IAAb,IAAa+B,aAAb,GAAa,MAAb,GAAaA,GAAE5B,YAAf,MAA2B,IAA3B,IAA2B6B,aAA3B,GAA2B,MAA3B,GAA2BA,GAAE3B,aAA7B,KAA8CjG,mBAAmB;MAnB7C,CADjB,CADe;MAuBtB8W,KAAK,EAAE;IAvBe,CAAxB;IAyBA,KAAKrS,YAAL,CAAkB8H,IAAlB,CACEvJ,mBADF,EAEEoC,MAAM,CAACgV,MAAP,CACE,EADF,EAEE;MACEtD,KAAK,EAAE,gDADT;MAEED;IAFF,CAFF,CAFF;EAUD;;EAEDwD,QAAQ,CAACC,MAAD,EAAY;IAClB,IAAIA,MAAM,CAAChN,GAAP,KAAe,OAAnB,EAA4B;MAC1B,IAAI,CAAC,KAAKuL,UAAV,EAAsB;QACpB;MACD;;MACD,KAAK/K,iBAAL,CAAuByM,IAAvB,CAA4B,KAAK1B,UAAjC;IACD;EACF;;EAED2B,YAAY,CAACF,MAAD,EAAY;IACtB,IAAI,KAAKzB,UAAL,KAAoB,EAApB,IAA0B,KAAKA,UAAL,KAAoB,IAAlD,EAAwD;MACtD,KAAK/K,iBAAL,CAAuByM,IAAvB,CAA4B,EAA5B;IACD;EACF;;EAEDE,UAAU;IACR,KAAKC,WAAL,GAAmB,KAAKA,WAAL,KAAqB,OAArB,GAA+B,OAA/B,GAAyC,OAA5D;EACD;;EAEDC,gBAAgB;IACd,IAAI,KAAKC,YAAL,IAAqB,KAAKC,oBAAL,EAAzB,EAAsD;MACpD,KAAKD,YAAL,CAAkBE,WAAlB;IACD;EACF;;EAEDD,oBAAoB;;;IAClB,OAAO,KAAKH,WAAL,KAAqB,OAArB,KACL,WAAKE,YAAL,MAAiB,IAAjB,IAAiB9U,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEiV,YADd,KAEL,EAAC,WAAKH,YAAL,MAAiB,IAAjB,IAAiB5U,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEgV,oBAApB,CAFF;EAGD;;EAEDC,WAAW;IACT,KAAKvV,OAAL,CAAa6U,IAAb;IACA,KAAK7U,OAAL,CAAawV,QAAb;EACD;;AA15D+B;;;mBAArBhX,uBAAqBhB;AAAA;;;QAArBgB;EAAqBiX;EAAAC;IAAA;;;;;;;;;;;;;;;MC1GlClY;MA4VAA;;;;MA5VMA", "names": ["EventEmitter", "TemplateRef", "Subject", "firstValueFrom", "map", "<PERSON><PERSON><PERSON><PERSON>", "switchMap", "take", "takeUntil", "moment", "PAGE_SIZE", "REPORTS_DATE_TYPE", "REPORT_FILTERS_KEY_LABEL", "SHOW_ENTRIES", "USER_VISIBILITY", "IntegrationSource", "LeadSource", "ReportDateType", "assignToSort", "changeCalendar", "getPages", "getSystemTimeOffset", "getSystemTimeZoneId", "getTimeZoneDate", "getTotalCountForReports", "onPickerOpened", "patchTimeZoneDate", "setTimeZoneDate", "snakeToCamel", "FetchAllSources", "getAllSources", "getAllSourcesLoading", "getGlobalAnonymousIsLoading", "getGlobalSettingsAnonymous", "FetchLeadCities", "FetchLeadStates", "FetchProjectList", "FetchSubSourceList", "getIsLeadCustomStatusEnabled", "getLeadCities", "getLeadCitiesIsLoading", "getLeadCountries", "getLeadCountriesIsLoading", "getLeadStates", "getLeadStatesIsLoading", "getProjectList", "getProjectListIsLoading", "getSubSourceList", "getSubSourceListIsLoading", "getPermissions", "FetchReportsCustomUser", "FetchReportsUser", "FetchUserExportSuccess", "UpdateUserFilterPayload", "getReportsCustomUsersList", "getReportsCustomUsersListIsLoading", "getReportsCustomUsersListTotalCount", "getReportsUsersList", "getReportsUsersListIsLoading", "getUserFiltersPayload", "getCustomStatusList", "getCustomStatusListIsLoading", "FetchOnlyReporteesWithInactive", "FetchUsersListForReassignment", "getOnlyReporteesWithInactive", "getOnlyReporteesWithInactiveIsLoading", "getUserBasicDetails", "getUsersListForReassignment", "getUsersListForReassignmentIsLoading", "ExportMailComponent", "environment", "i0", "ctx_r3", "index_r36", "index_r40", "index_r43", "index_r46", "index_r49", "index_r52", "ctx_r5", "_r27", "ctx_r82", "_r78", "index_r86", "ctx_r13", "ctx_r0", "_r12", "StatusReportComponent", "constructor", "gridOptionsService", "_store", "headerTitle", "metaTitle", "router", "modalService", "modalRef", "shareDataService", "slice", "Date", "s3ImageBucketURL", "ngOnInit", "globalSettingsData", "select", "pipe", "data", "Object", "keys", "length", "setTitle", "gridOptions", "getGridSettings", "stopper", "subscribe", "userData", "currentDate", "_a", "timeZoneInfo", "_b", "baseUTcOffset", "leadSource", "enabledSources", "filter", "source", "isEnabled", "sort", "a", "b", "displayName", "localeCompare", "leadSources", "updateSubSource", "loading", "isSourcesLoading", "isLoading", "to<PERSON>romise", "isCustomStatusEnabled", "filtersPayload", "isNavigatedFromReports", "pageSize", "userStatus", "undefined", "_c", "appliedFilter", "pageNumber", "_d", "_e", "visibility", "_f", "dateType", "Number", "_g", "date", "_h", "fromDate", "_j", "_k", "_l", "toDate", "_m", "_o", "withTeam", "_p", "IsWithTeam", "users", "_q", "UserIds", "search", "_r", "SearchText", "sources", "_s", "Sources", "subSources", "_t", "SubSources", "projects", "_u", "Projects", "cities", "_v", "Cities", "states", "_w", "States", "ShouldShowAll", "_x", "_y", "shouldShowPercentage", "_z", "ShouldShowPercentage", "usersData", "user", "fullName", "firstName", "lastName", "allUsers", "isAllUsersLoading", "currentVisibility", "reportees", "onlyReportees", "isOnlyReporteesLoading", "projectList", "isProjectListLoading", "isStatusReportLoading", "isCustomStatusReportLoading", "permissions", "permissionsSet", "Set", "canExportAllUsers", "has", "canViewAllUsers", "canExportReportees", "canViewReportees", "dispatch", "allSubSourceList", "subSourceList", "values", "flat", "allSubSourceListIsLoading", "showLeftNav$", "show", "showLeftNav", "initializeGridSettings", "initializeGraphData", "rowData", "row", "statuses", "status", "for<PERSON>ach", "statusDisplayName", "count", "percentage", "totalRow", "userName", "projectTitle", "key", "includes", "push", "userTotalCount", "items", "totalCount", "fetchCustomStatuses", "selectedPageSize", "searchTermSubject", "filterFunction", "citiesIsLoading", "statesIsLoading", "countryList", "countryIsLoading", "customStatus", "customStatusList", "isCustomStatusListLoading", "filteredColumnDefsCache", "columnDefs", "col", "field", "nameAndLeads", "headerName", "pinned", "window", "innerWidth", "lockPinned", "cellClass", "valueGetter", "params", "min<PERSON><PERSON><PERSON>", "cell<PERSON><PERSON><PERSON>", "value", "hide", "<PERSON><PERSON><PERSON><PERSON>", "reportingManager", "AllCount", "allCount", "ActiveCount", "activeCount", "userId", "valueLabels", "filters", "onCellClicked", "event", "isCtrlClick", "ctrl<PERSON>ey", "target", "innerText", "getDataInNewTab", "getDataFromCell", "newAndPending", "newCount", "canShowPercentage", "newCountPercentage", "pendingCount", "pendingCountPercentage", "overdue", "OverdueCount", "overdueCount", "OverdueCount__percentage__", "overdueCount__percentage__", "callbackAndMS", "callbackCount", "callbackCountPercentage", "meetingScheduledCount", "meetingScheduledCountPercentage", "meetingDoneAndNotDone", "MeetingDoneCount", "meetingDoneCount", "MeetingDoneUniqueCount", "meetingDoneUniqueCount", "MeetingDoneUniqueCount__percentage__", "meetingDoneUniqueCount__percentage__", "getMeetingCountnewTab", "getMeetingCount", "MeetingNotDoneCount", "meetingNotDoneCount", "MeetingNotDoneUniqueCount", "meetingNotDoneUniqueCount", "MeetingNotDoneUniqueCount__percentage__", "meetingNotDoneUniqueCount__percentage__", "svs", "shouldRenameSiteVisitColumn", "siteVisitScheduledCount", "siteVisitScheduledCountPercentage", "siteVisitDoneAndNotDone", "SiteVisitDoneCount", "siteVisitDoneCount", "SiteVisitDoneUniqueCount", "siteVisitDoneUniqueCount", "SiteVisitDoneUniqueCount__percentage__", "siteVisitDoneUniqueCount__percentage__", "SiteVisitNotDoneCount", "siteVisitNotDoneCount", "SiteVisitNotDoneUniqueCount", "siteVisitNotDoneUniqueCount", "SiteVisitNotDoneUniqueCount__percentage__", "siteVisitNotDoneUniqueCount__percentage__", "others", "bookedCount", "bookedCountPercentage", "invoicedLeadsCount", "invoicedLeadsCountPercentage", "bookingCancelCount", "bookingCancelCountPercentage", "notInterestedCount", "notInterestedCountPercentage", "droppedCount", "droppedCountPercentage", "expressionOfInterestLeadCount", "expressionOfInterestLeadCountPercentage", "open", "encodeURIComponent", "JSON", "stringify", "item", "index", "columnDropDown", "context", "componentParent", "operation", "meetingStatus", "navigate", "visitMeeting", "payload", "onGridReady", "gridApi", "api", "gridColumnApi", "columnApi", "toggleColumns", "onPageChange", "e", "currOffset", "paginationGoToPage", "onResetDateFilter", "getArrayOfFilters", "<PERSON><PERSON><PERSON><PERSON>", "formattedToDate", "formattedFromDate", "dateRangeString", "toString", "split", "applyAdvancedFilter", "onRemoveFilter", "<PERSON><PERSON><PERSON>", "matchIndex", "indexOf", "openAdvFiltersModal", "advFilters", "initialState", "class", "getUserName", "id", "columns", "getColumns", "column", "label", "getColDef", "defaultColumns", "columnState", "parse", "localStorage", "getItem", "applyColumnState", "state", "applyOrder", "columnData", "visibleColumns", "onColumnsSelected", "colData", "setItem", "cols", "setColumnsVisible", "nonSelectedCols", "getColumnState", "onSetColumnDefault", "isTopLevelFilter", "isActive", "assignCount", "Countries", "showFilters", "searchTerm", "ReportPermission", "ExportPermission", "reset", "i", "apply", "formattedKey", "replace", "Array", "isArray", "flatMap", "lead", "match", "toLowerCase", "onSelectSource", "updateSubSources", "sourceName", "refresh", "refresh<PERSON>ells", "exportLeadReport", "selectedColumns", "colId", "timeZoneId", "assign", "onSearch", "$event", "next", "isEmptyInput", "to<PERSON><PERSON><PERSON><PERSON>", "current<PERSON>iew", "exportGraphAsPDF", "reportsGraph", "isGraphExportEnabled", "exportGraph", "isChartReady", "showSelectionMessage", "ngOnDestroy", "complete", "selectors", "viewQuery"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Desktop\\Translate\\Leadrat-Black-Web\\src\\app\\features\\reports\\leads\\status-report\\status-report.component.ts", "C:\\Users\\<USER>\\Desktop\\Translate\\Leadrat-Black-Web\\src\\app\\features\\reports\\leads\\status-report\\status-report.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  EventEmitter,\r\n  OnDestroy,\r\n  OnInit,\r\n  TemplateRef,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { Router } from '@angular/router';\r\nimport { Store } from '@ngrx/store';\r\nimport { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';\r\nimport { Subject, firstValueFrom, map, skipWhile, switchMap, take, takeUntil } from 'rxjs';\r\n\r\nimport * as moment from 'moment';\r\nimport { AnimationOptions } from 'ngx-lottie';\r\nimport {\r\n  PAGE_SIZE,\r\n  REPORTS_DATE_TYPE,\r\n  REPORT_FILTERS_KEY_LABEL,\r\n  SHOW_ENTRIES,\r\n  USER_VISIBILITY,\r\n} from 'src/app/app.constants';\r\nimport {\r\n  IntegrationSource,\r\n  LeadSource,\r\n  ReportDateType,\r\n} from 'src/app/app.enum';\r\nimport { AppState } from 'src/app/app.reducer';\r\nimport { ReportsFilter } from 'src/app/core/interfaces/reports.interface';\r\nimport {\r\n  assignToSort,\r\n  changeCalendar,\r\n  getPages,\r\n  getSystemTimeOffset,\r\n  getSystemTimeZoneId,\r\n  getTimeZoneDate,\r\n  getTotalCountForReports,\r\n  onPickerOpened,\r\n  patchTimeZoneDate,\r\n  setTimeZoneDate,\r\n  snakeToCamel,\r\n} from 'src/app/core/utils/common.util';\r\nimport { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';\r\nimport { getAllSources, getAllSourcesLoading, getGlobalAnonymousIsLoading, getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';\r\nimport {\r\n  FetchLeadCities,\r\n  FetchLeadCountries,\r\n  FetchLeadStates,\r\n  FetchProjectList,\r\n  FetchSubSourceList,\r\n} from 'src/app/reducers/lead/lead.actions';\r\nimport {\r\n  getIsLeadCustomStatusEnabled,\r\n  getLeadCities,\r\n  getLeadCitiesIsLoading,\r\n  getLeadCountries,\r\n  getLeadCountriesIsLoading,\r\n  getLeadStates,\r\n  getLeadStatesIsLoading,\r\n  getProjectList,\r\n  getProjectListIsLoading,\r\n  getSubSourceList,\r\n  getSubSourceListIsLoading,\r\n} from 'src/app/reducers/lead/lead.reducer';\r\nimport { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';\r\nimport {\r\n  FetchReportsCustomUser,\r\n  FetchReportsUser,\r\n  FetchUserExportSuccess,\r\n  UpdateUserFilterPayload,\r\n} from 'src/app/reducers/reports/reports.actions';\r\nimport {\r\n  getReportsCustomUsersList,\r\n  getReportsCustomUsersListIsLoading,\r\n  getReportsCustomUsersListTotalCount,\r\n  getReportsUsersList,\r\n  getReportsUsersListIsLoading,\r\n  getUserFiltersPayload,\r\n} from 'src/app/reducers/reports/reports.reducer';\r\nimport {\r\n  CustomStatus,\r\n  getCustomStatusList,\r\n  getCustomStatusListIsLoading,\r\n} from 'src/app/reducers/status/status.reducer';\r\nimport {\r\n  FetchOnlyReporteesWithInactive,\r\n  FetchUsersListForReassignment,\r\n} from 'src/app/reducers/teams/teams.actions';\r\nimport {\r\n  getOnlyReporteesWithInactive,\r\n  getOnlyReporteesWithInactiveIsLoading,\r\n  getUserBasicDetails,\r\n  getUsersListForReassignment,\r\n  getUsersListForReassignmentIsLoading,\r\n} from 'src/app/reducers/teams/teams.reducer';\r\nimport { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';\r\nimport { environment } from 'src/environments/environment';\r\nimport { GridOptionsService } from 'src/app/services/shared/grid-options.service';\r\nimport { HeaderTitleService } from 'src/app/services/shared/header-title.service';\r\nimport { ShareDataService } from 'src/app/services/shared/share-data.service';\r\n\r\n@Component({\r\n  selector: 'status-report',\r\n  templateUrl: './status-report.component.html',\r\n})\r\nexport class StatusReportComponent implements OnInit, OnDestroy {\r\n  private stopper: EventEmitter<void> = new EventEmitter<void>();\r\n  public searchTermSubject = new Subject<string>();\r\n  leadSources: Array<any> = [];\r\n  columnDropDown: { field: string; hide: boolean }[] = [];\r\n  gridOptions: any;\r\n  gridApi: any;\r\n  gridColumnApi: any;\r\n  showEntriesSize: Array<number> = SHOW_ENTRIES;\r\n  pageSize: number = PAGE_SIZE;\r\n  selectedPageSize: number;\r\n  currOffset: number = 0;\r\n  searchTerm: string;\r\n  currentView: 'table' | 'graph' = 'table';\r\n  projectList: any;\r\n  rowData: Array<any> = [];\r\n  filtersPayload: ReportsFilter;\r\n  appliedFilter: any;\r\n  filteredColumnDefsCache: any[] = [];\r\n  canExportAllUsers: boolean = false;\r\n  canViewAllUsers: boolean = false;\r\n  canViewReportees: boolean = false;\r\n  canExportReportees: boolean = false;\r\n  userTotalCount: number;\r\n  getPages = getPages;\r\n  dateTypeList: Array<string> = REPORTS_DATE_TYPE.slice(0, 4);\r\n  visibilityList: Array<Object> = USER_VISIBILITY.slice(0, 3);\r\n  subSourceList: any;\r\n  allSubSourceList: any;\r\n  allUsers: Array<any> = [];\r\n  onlyReportees: Array<any> = [];\r\n  users: Array<any> = [];\r\n  reportees: Array<any> = [];\r\n  showLeftNav: boolean = true;\r\n  isStatusReportLoading: boolean = true;\r\n  isAllUsersLoading: boolean = true;\r\n  isOnlyReporteesLoading: boolean = true;\r\n  allSubSourceListIsLoading: boolean = true;\r\n  isProjectListLoading: boolean = true;\r\n  customStatusList: CustomStatus[] = [];\r\n  isCustomStatusListLoading: boolean = true;\r\n  isSourcesLoading: boolean = true;\r\n  showFilters: boolean = false;\r\n  moment = moment;\r\n  reportFiltersKeyLabel = REPORT_FILTERS_KEY_LABEL;\r\n  cities: string[];\r\n  citiesIsLoading: boolean = true;\r\n  states: string[];\r\n  statesIsLoading: boolean = true;\r\n  columns: any[];\r\n  defaultColumns: any[];\r\n  canShowPercentage: boolean;\r\n  countryList: any[];\r\n  countryIsLoading: boolean = true;\r\n\r\n\r\n  isCustomStatusEnabled: boolean = false;\r\n  isGlobalSettingsLoading: boolean = true;\r\n  isCustomStatusReportLoading: boolean = true;\r\n  snakeToCamel = snakeToCamel;\r\n  currentDate: Date = new Date();\r\n  toDate: any = new Date();\r\n  fromDate: any = new Date();\r\n  onPickerOpened = onPickerOpened;\r\n  userData: any;\r\n  s3BucketUrl: string = environment.s3ImageBucketURL;\r\n  globalSettingsData: any;\r\n\r\n  @ViewChild('reportsGraph') reportsGraph: any;\r\n\r\n  constructor(\r\n    private gridOptionsService: GridOptionsService,\r\n    private _store: Store<AppState>,\r\n    private headerTitle: HeaderTitleService,\r\n    private metaTitle: Title,\r\n    private router: Router,\r\n    private modalService: BsModalService,\r\n    private modalRef: BsModalRef,\r\n    private shareDataService: ShareDataService\r\n  ) { }\r\n  async ngOnInit() {\r\n    this.globalSettingsData = await firstValueFrom(\r\n      this._store\r\n        .select(getGlobalSettingsAnonymous)\r\n        .pipe(skipWhile((data) => !Object.keys(data).length))\r\n    );\r\n    this.headerTitle.setTitle('Leads - Status Report');\r\n    this.metaTitle.setTitle('CRM | Reports');\r\n    this.gridOptions = this.gridOptionsService.getGridSettings(this);\r\n    this._store\r\n      .select(getUserBasicDetails)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        this.userData = data;\r\n        this.currentDate = changeCalendar(\r\n          this.userData?.timeZoneInfo?.baseUTcOffset\r\n        );\r\n      });\r\n\r\n    this._store\r\n      .select(getAllSources)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((leadSource: any) => {\r\n        if (leadSource) {\r\n          const enabledSources = leadSource\r\n            .filter((source: any) => source.isEnabled)\r\n            .sort((a: any, b: any) => a?.displayName.localeCompare(b?.displayName));\r\n          this.leadSources = [...enabledSources];\r\n        } else {\r\n          this.leadSources = [];\r\n        }\r\n        this.updateSubSource()\r\n      });\r\n\r\n    this._store\r\n      .select(getAllSourcesLoading)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((loading: boolean) => {\r\n        this.isSourcesLoading = loading;\r\n      });\r\n\r\n    await this._store\r\n      .select(getGlobalAnonymousIsLoading)\r\n      .pipe(\r\n        skipWhile((isLoading: boolean) => {\r\n          return isLoading;\r\n        }),\r\n        take(1)\r\n      )\r\n      .toPromise();\r\n    this.isCustomStatusEnabled = await this._store\r\n      .select(getIsLeadCustomStatusEnabled)\r\n      .pipe(\r\n        map((data: any) => data),\r\n        take(1)\r\n      )\r\n      .toPromise();\r\n\r\n    this._store\r\n      .select(getUserFiltersPayload)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        this.filtersPayload = { ...data, isNavigatedFromReports: true };\r\n        this.pageSize = this.filtersPayload?.pageSize;\r\n        const userStatus =\r\n          this.filtersPayload?.userStatus === undefined\r\n            ? 1\r\n            : this.filtersPayload?.userStatus;\r\n        this.appliedFilter = {\r\n          ...this.appliedFilter,\r\n          pageNumber: this.filtersPayload?.pageNumber,\r\n          pageSize: this.filtersPayload?.pageSize,\r\n          userStatus: userStatus,\r\n          visibility: this.filtersPayload?.userStatus,\r\n          dateType: ReportDateType[Number(this.filtersPayload?.dateType)],\r\n          date: [\r\n            patchTimeZoneDate(\r\n              this.filtersPayload?.fromDate,\r\n              this.userData?.timeZoneInfo?.baseUTcOffset\r\n            ),\r\n            patchTimeZoneDate(\r\n              this.filtersPayload?.toDate,\r\n              this.userData?.timeZoneInfo?.baseUTcOffset\r\n            ),\r\n          ],\r\n          withTeam: this.filtersPayload?.IsWithTeam,\r\n          users: this.filtersPayload?.UserIds,\r\n          search: this.filtersPayload?.SearchText,\r\n          sources: this.filtersPayload?.Sources,\r\n          subSources: this.filtersPayload?.SubSources,\r\n          projects: this.filtersPayload?.Projects,\r\n          cities: this.filtersPayload?.Cities,\r\n          states: this.filtersPayload?.States,\r\n          ShouldShowAll: this.filtersPayload?.ShouldShowAll ?? true,\r\n          shouldShowPercentage: this.filtersPayload?.ShouldShowPercentage,\r\n        };\r\n      });\r\n    this._store\r\n      .select(getUsersListForReassignment)\r\n      .pipe(\r\n        takeUntil(this.stopper),\r\n        switchMap((data: any) => {\r\n          const usersData = data?.map((user: any) => {\r\n            user = {\r\n              ...user,\r\n              fullName: user.firstName + ' ' + user.lastName,\r\n            };\r\n            return user;\r\n          });\r\n          this.users = usersData;\r\n          this.allUsers = usersData;\r\n          this.allUsers = assignToSort(this.allUsers, '');\r\n          return this._store\r\n            .select(getUsersListForReassignmentIsLoading)\r\n            .pipe(takeUntil(this.stopper));\r\n        })\r\n      )\r\n      .subscribe((isLoading: boolean) => {\r\n        this.isAllUsersLoading = isLoading;\r\n        if (!isLoading) {\r\n          this.currentVisibility(1, false);\r\n        }\r\n      });\r\n\r\n    this._store\r\n      .select(getOnlyReporteesWithInactive)\r\n      .pipe(\r\n        takeUntil(this.stopper),\r\n        switchMap((data: any) => {\r\n          const usersData = data?.map((user: any) => {\r\n            user = {\r\n              ...user,\r\n              fullName: user.firstName + ' ' + user.lastName,\r\n            };\r\n            return user;\r\n          });\r\n          this.reportees = usersData;\r\n          this.onlyReportees = usersData;\r\n          this.onlyReportees = assignToSort(this.onlyReportees, '');\r\n          return this._store\r\n            .select(getOnlyReporteesWithInactiveIsLoading)\r\n            .pipe(takeUntil(this.stopper));\r\n        })\r\n      )\r\n      .subscribe((isLoading: boolean) => {\r\n        this.isOnlyReporteesLoading = isLoading;\r\n        if (!isLoading) {\r\n          this.currentVisibility(1, false);\r\n        }\r\n      });\r\n    this._store\r\n      .select(getProjectList)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        this.projectList = data\r\n          .slice()\r\n          .sort((a: any, b: any) => a.localeCompare(b));\r\n      });\r\n    this._store\r\n      .select(getProjectListIsLoading)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((isLoading: boolean) => {\r\n        this.isProjectListLoading = isLoading;\r\n      });\r\n    this._store\r\n      .select(getReportsUsersListIsLoading)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((isLoading: any) => {\r\n        this.isStatusReportLoading = isLoading;\r\n      });\r\n    this._store\r\n      .select(getReportsCustomUsersListIsLoading)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((isLoading: any) => {\r\n        this.isCustomStatusReportLoading = isLoading;\r\n      });\r\n    this._store\r\n      .select(getPermissions)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((permissions: any) => {\r\n        if (!permissions?.length) return;\r\n        const permissionsSet = new Set(permissions);\r\n        this.canExportAllUsers = permissionsSet.has(\r\n          'Permissions.Reports.ExportAllUsers'\r\n        );\r\n        this.canViewAllUsers = permissionsSet.has(\r\n          'Permissions.Reports.ViewAllUsers'\r\n        );\r\n        this.canExportReportees = permissionsSet.has(\r\n          'Permissions.Reports.ExportReportees'\r\n        );\r\n        this.canViewReportees = permissionsSet.has(\r\n          'Permissions.Reports.ViewReportees'\r\n        );\r\n        if (this.canViewAllUsers) {\r\n          this._store.dispatch(new FetchUsersListForReassignment());\r\n        } else if (this.canViewReportees) {\r\n          this._store.dispatch(new FetchOnlyReporteesWithInactive());\r\n        }\r\n      });\r\n    this._store\r\n      .select(getSubSourceList)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        this.allSubSourceList = data;\r\n        this.subSourceList = Object.values(data)\r\n          .flat()\r\n          .filter((data: any) => data)\r\n          .slice()\r\n          .sort((a: any, b: any) => a.localeCompare(b));\r\n        this.updateSubSource()\r\n      });\r\n    this._store\r\n      .select(getSubSourceListIsLoading)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((isLoading: boolean) => {\r\n        this.allSubSourceListIsLoading = isLoading;\r\n      });\r\n    this.shareDataService.showLeftNav$.subscribe((show) => {\r\n      this.showLeftNav = show;\r\n    });\r\n    this.initializeGridSettings();\r\n    this.initializeGraphData();\r\n\r\n    if (this.isCustomStatusEnabled) {\r\n      this._store\r\n        .select(getReportsCustomUsersList)\r\n        .pipe(takeUntil(this.stopper))\r\n        .subscribe((data: any) => {\r\n          this.rowData = data.map((row: any) => {\r\n            let statuses: any = {};\r\n            row?.status?.forEach((status: any) => {\r\n              statuses[status?.statusDisplayName] = status?.count || 0;\r\n              statuses[status?.statusDisplayName + '__percentage__'] =\r\n                `${status?.percentage}` || '';\r\n            });\r\n            return {\r\n              ...row,\r\n              statuses,\r\n            };\r\n          });\r\n          let totalRow: any = {\r\n            userName: 'Total',\r\n            projectTitle: 'Total',\r\n            statuses: {},\r\n          };\r\n          this.rowData.forEach((row: any) => {\r\n            for (let key in row?.statuses) {\r\n              if (!totalRow?.statuses?.[key]) {\r\n                totalRow.statuses[key] = 0;\r\n              }\r\n              if (!key.includes('__percentage__'))\r\n                totalRow.statuses[key] += row?.statuses?.[key] || 0;\r\n            }\r\n          });\r\n          if (this.rowData?.length > 1) {\r\n            this.rowData?.push(totalRow);\r\n          }\r\n        });\r\n      this._store\r\n        .select(getReportsCustomUsersListTotalCount)\r\n        .pipe(takeUntil(this.stopper))\r\n        .subscribe((data: any) => {\r\n          this.userTotalCount = data;\r\n        });\r\n    } else\r\n      this._store\r\n        .select(getReportsUsersList)\r\n        .pipe(takeUntil(this.stopper))\r\n        .subscribe((data: any) => {\r\n          this.rowData = getTotalCountForReports(data.items);\r\n          this.userTotalCount = data.totalCount;\r\n        });\r\n\r\n    if (this.isCustomStatusEnabled) this.fetchCustomStatuses();\r\n    this.selectedPageSize = 50;\r\n    this.searchTermSubject.subscribe(() => {\r\n      this.appliedFilter.pageNumber = 1;\r\n      this.filterFunction();\r\n    });\r\n\r\n    this._store\r\n      .select(getLeadCities)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        this.cities = data\r\n          .filter((data: any) => data)\r\n          .slice()\r\n          .sort((a: any, b: any) => a.localeCompare(b));\r\n      });\r\n    this._store\r\n      .select(getLeadCitiesIsLoading)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: boolean) => {\r\n        this.citiesIsLoading = data;\r\n      });\r\n    this._store\r\n      .select(getLeadStates)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        this.states = data\r\n          .filter((data: any) => data)\r\n          .slice()\r\n          .sort((a: any, b: any) => a.localeCompare(b));\r\n      });\r\n    this._store\r\n      .select(getLeadStatesIsLoading)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: boolean) => {\r\n        this.statesIsLoading = data;\r\n      });\r\n\r\n    this._store\r\n      .select(getLeadCountries)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        this.countryList = data\r\n          .filter((data: any) => data)\r\n          .slice()\r\n          .sort((a: any, b: any) => a.localeCompare(b));\r\n      });\r\n    this._store\r\n      .select(getLeadCountriesIsLoading)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: boolean) => {\r\n        this.countryIsLoading = data;\r\n      });\r\n  }\r\n\r\n  fetchCustomStatuses() {\r\n    this._store\r\n      .select(getCustomStatusList)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((customStatus: any) => {\r\n        this.customStatusList = customStatus;\r\n      });\r\n\r\n    this._store\r\n      .select(getCustomStatusListIsLoading)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((isLoading: any) => {\r\n        this.isCustomStatusListLoading = isLoading;\r\n        if (!isLoading) this.initializeGridSettings();\r\n        this.initializeGraphData();\r\n      });\r\n  }\r\n\r\n  initializeGraphData() {\r\n    this.filteredColumnDefsCache = this.gridOptions?.columnDefs?.filter(\r\n      (col: any) => col.field !== 'User Name' && col.field !== 'General Manager' && col.field != 'Reporting Manager'\r\n    );\r\n  }\r\n\r\n  initializeGridSettings() {\r\n    this.gridOptions = this.gridOptionsService.getGridSettings(this);\r\n    const nameAndLeads = [\r\n      {\r\n        headerName: 'User Name',\r\n        field: 'User Name',\r\n        pinned: window.innerWidth > 480 ? 'left' : null,\r\n        lockPinned: true,\r\n        cellClass: 'lock-pinned',\r\n        valueGetter: (params: any) => [\r\n          params.data?.userName\r\n        ],\r\n        minWidth: 180,\r\n        cellRenderer: (params: any) => {\r\n          return `<div class=\"py-16 align-center text-truncate\"><p>${params.value[0]}\r\n            </p></div>`;\r\n        },\r\n      },\r\n      {\r\n        headerName: 'General Manager',\r\n        field: 'General Manager',\r\n        hide: false,\r\n        cellClass: 'lock-pinned',\r\n        valueGetter: (params: any) => [params.data?.generalManager],\r\n        minWidth: 180,\r\n        cellRenderer: (params: any) => {\r\n          return `<div class=\"py-16 align-center text-truncate\"><p>${params.value[0] || ''\r\n            }\r\n            </p></div>`;\r\n        },\r\n      },\r\n      {\r\n        headerName: 'Reporting Manager',\r\n        field: 'Reporting Manager',\r\n        hide: false,\r\n        cellClass: 'lock-pinned',\r\n        valueGetter: (params: any) => [params.data?.reportingManager],\r\n        minWidth: 180,\r\n        cellRenderer: (params: any) => {\r\n          return `<div class=\"py-16 align-center text-truncate\"><p>${params.value[0] || ''\r\n            }\r\n            </p></div>`;\r\n        },\r\n      },\r\n      {\r\n        headerName: 'All Leads',\r\n        field: 'All Leads',\r\n        filter: false,\r\n        valueGetter: (params: any) => [\r\n          this.isCustomStatusEnabled\r\n            ? params?.data?.statuses?.AllCount\r\n            : params.data?.allCount,\r\n          this.isCustomStatusEnabled\r\n            ? params?.data?.statuses?.ActiveCount\r\n            : params.data?.activeCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n        ],\r\n        valueLabels: ['All Leads', 'Active Leads'],\r\n        minWidth: 120,\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return !this.isCustomStatusEnabled\r\n            ? `${params?.value?.[3] == 'Total' || params.value[0] == 0\r\n              ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`\r\n              : `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`\r\n            }\r\n          <p class=\"text-truncate\"><span class=\"text-dark-gray\">active: </span>\r\n          <span class=\"fw-600\">${params.value[1] && params?.value?.[3] != 'Total'\r\n              ? `<a>${params.value[1]}</a>`\r\n              : params.value[1]\r\n                ? params.value[1]\r\n                : '--'\r\n            }<span>\r\n          </p>`\r\n            : `${params?.value?.[3] == 'Total' || params.value[0] == 0\r\n              ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`\r\n              : `<p><span >${params.value[0] ? params.value[0] : '--'\r\n              }</span></p>`\r\n            }\r\n          <p class=\"text-truncate\"><span class=\"text-dark-gray\">active: </span>\r\n          <span class=\"fw-600\">${params.value[1] && params?.value?.[3] != 'Total'\r\n              ? `<span>${params.value[1]}</span>`\r\n              : params.value[1]\r\n                ? params.value[1]\r\n                : '--'\r\n            }<span>\r\n          </p>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          if (event.data.projectTitle == 'Total') {\r\n            return;\r\n          } else if (event.event.target.innerText == event.value[0]) {\r\n            if (isCtrlClick) {\r\n              this.getDataInNewTab('All Leads', params);\r\n              return;\r\n            }\r\n            this.getDataFromCell('All Leads', event);\r\n          } else if (event.event.target.innerText == event.value[1]) {\r\n            if (isCtrlClick) {\r\n              this.getDataInNewTab('Active Leads', params);\r\n              return;\r\n            }\r\n            this.getDataFromCell('Active Leads', event);\r\n          }\r\n        },\r\n      },\r\n    ];\r\n    const newAndPending: any = [\r\n      {\r\n        headerName: 'New',\r\n        field: 'New',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          params.data?.newCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage ? params.data?.newCountPercentage : '',\r\n        ],\r\n        minWidth: 70,\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return params?.value?.[2] == 'Total' || params.value[0] == 0\r\n            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`\r\n            : !this.isCustomStatusEnabled\r\n              ? `<p><a>${params.value[0]\r\n                ? params.value[0] +\r\n                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                : '--'\r\n              }</a></p>`\r\n              : `<p><span>${params.value[0]\r\n                ? params.value[0] +\r\n                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                : '--'\r\n              }</span></p>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total') {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              this.getDataInNewTab('New', params);\r\n              return;\r\n            }\r\n            this.getDataFromCell('New', event);\r\n          }\r\n        },\r\n      },\r\n      {\r\n        headerName: 'Pending',\r\n        field: 'Pending',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          params.data?.pendingCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage ? params.data?.pendingCountPercentage : '',\r\n        ],\r\n        minWidth: 80,\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return params?.value?.[2] == 'Total' || params.value[0] == 0\r\n            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`\r\n            : !this.isCustomStatusEnabled\r\n              ? `<p><a>${params.value[0]\r\n                ? params.value[0] +\r\n                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                : '--'\r\n              }</a></p>`\r\n              : `<p><span>${params.value[0]\r\n                ? params.value[0] +\r\n                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                : '--'\r\n              }</span></p>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total') {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              this.getDataInNewTab('Pending', params);\r\n              return;\r\n            }\r\n            this.getDataFromCell('Pending', event);\r\n          }\r\n        },\r\n      },\r\n    ];\r\n    const overdue: any = [\r\n      {\r\n        headerName: 'Overdue',\r\n        field: 'Overdue',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          this.isCustomStatusEnabled\r\n            ? params?.data?.statuses?.OverdueCount\r\n            : params.data?.overdueCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage\r\n            ? this.isCustomStatusEnabled\r\n              ? params?.data?.statuses?.OverdueCount__percentage__\r\n              : params.data?.overdueCount__percentage__\r\n            : '',\r\n        ],\r\n        minWidth: 110,\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return params?.value?.[2] == 'Total' || params.value[0] == 0\r\n            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`\r\n            : !this.isCustomStatusEnabled\r\n              ? `<p><a>${params.value[0]\r\n                ? params.value[0] +\r\n                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                : '--'\r\n              }</a></p>`\r\n              : `<p><span>${params.value[0]\r\n                ? params.value[0] +\r\n                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                : '--'\r\n              }</span></p>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total') {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              this.getDataInNewTab('Overdue', params);\r\n              return;\r\n            }\r\n            this.getDataFromCell('Overdue', event);\r\n          }\r\n        },\r\n      },\r\n    ];\r\n    const callbackAndMS: any = [\r\n      {\r\n        headerName: 'Callback',\r\n        field: 'Callback',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          params.data?.callbackCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage ? params.data?.callbackCountPercentage : '',\r\n        ],\r\n        minWidth: 110,\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return params?.value?.[2] == 'Total' || params.value[0] == 0\r\n            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`\r\n            : !this.isCustomStatusEnabled\r\n              ? `<p><a>${params.value[0] ? params.value[0] : '--'}</a></p>`\r\n              : `<p><span>${params.value[0] ? params.value[0] : '--'}</span></p>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total') {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              this.getDataInNewTab('Callback', params);\r\n              return;\r\n            }\r\n            this.getDataFromCell('Callback', event);\r\n          }\r\n        },\r\n      },\r\n      {\r\n        headerName: 'Meeting Scheduled',\r\n        field: 'Meeting Scheduled',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          params.data?.meetingScheduledCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage\r\n            ? params.data?.meetingScheduledCountPercentage\r\n            : '',\r\n        ],\r\n        minWidth: 140,\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return !this.isCustomStatusEnabled\r\n            ? `<a ><p>${params.value[0]\r\n              ? params.value[0] +\r\n              (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n              : '--'\r\n            }</p></a>`\r\n            : `<span><p>${params.value[0]\r\n              ? params.value[0] +\r\n              (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n              : '--'\r\n            }</p></span>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total') {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              this.getDataInNewTab('Meeting Scheduled', params);\r\n              return;\r\n            }\r\n            this.getDataFromCell('Meeting Scheduled', event);\r\n          }\r\n        },\r\n      },\r\n    ];\r\n    const meetingDoneAndNotDone: any = [\r\n      {\r\n        headerName: 'Meeting Done',\r\n        field: 'Meeting Done',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          this.isCustomStatusEnabled\r\n            ? params?.data?.statuses?.MeetingDoneCount\r\n            : params.data?.meetingDoneCount,\r\n          this.isCustomStatusEnabled\r\n            ? params?.data?.statuses?.MeetingDoneUniqueCount\r\n            : params.data?.meetingDoneUniqueCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage\r\n            ? this.isCustomStatusEnabled\r\n              ? params?.data?.statuses?.MeetingDoneUniqueCount__percentage__\r\n              : params.data?.meetingDoneUniqueCount__percentage__\r\n            : '',\r\n        ],\r\n        minWidth: 120,\r\n        valueLabels: ['Meeting Done', 'Meeting Done (Unquie Count)'],\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return !this.isCustomStatusEnabled\r\n            ? `<a ><p>${params.value[0] ? params.value[0] : '--'}</p>\r\n          <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1]\r\n              ? params.value[1] +\r\n              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')\r\n              : '--'\r\n            }<span></p></a>`\r\n            : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>\r\n            <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1]\r\n              ? params.value[1] +\r\n              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')\r\n              : '--'\r\n            }<span></p></span>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total') {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              this.getMeetingCountnewTab('All Leads', params, 'Meeting Done');\r\n              return;\r\n            }\r\n            this.getMeetingCount('All Leads', event, 'Meeting Done');\r\n          }\r\n        },\r\n      },\r\n      {\r\n        headerName: 'Meeting Not Done',\r\n        field: 'Meeting Not Done',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          this.isCustomStatusEnabled\r\n            ? params?.data?.statuses?.MeetingNotDoneCount\r\n            : params.data?.meetingNotDoneCount,\r\n          this.isCustomStatusEnabled\r\n            ? params?.data?.statuses?.MeetingNotDoneUniqueCount\r\n            : params.data?.meetingNotDoneUniqueCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage\r\n            ? this.isCustomStatusEnabled\r\n              ? params?.data?.statuses?.MeetingNotDoneUniqueCount__percentage__\r\n              : params.data?.meetingNotDoneUniqueCount__percentage__\r\n            : '',\r\n        ],\r\n        minWidth: 150,\r\n        valueLabels: ['Meeting Not Done', 'Meeting Not Done (Unquie Count)'],\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return !this.isCustomStatusEnabled\r\n            ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>\r\n          <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1]\r\n              ? params.value[1] +\r\n              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')\r\n              : '--'\r\n            }<span></p></a>`\r\n            : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>\r\n            <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1]\r\n              ? params.value[1] +\r\n              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')\r\n              : '--'\r\n            }<span></p></span>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total') {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              this.getMeetingCountnewTab(\r\n                'All Leads',\r\n                params,\r\n                'Meeting Not Done'\r\n              );\r\n              return;\r\n            }\r\n            this.getMeetingCount('All Leads', event, 'Meeting Not Done');\r\n          }\r\n        },\r\n      },\r\n    ];\r\n    const svs: any = [\r\n      {\r\n        headerName: this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled',\r\n        field: this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          params.data?.siteVisitScheduledCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage\r\n            ? params.data?.siteVisitScheduledCountPercentage\r\n            : '',\r\n        ],\r\n        valueLabels: [\r\n          this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled',\r\n          (this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : 'Site Visit Scheduled') + ' (unique count)'\r\n        ], \r\n        minWidth: 150,\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return !this.isCustomStatusEnabled\r\n            ? `<a ><p>${params.value[0]\r\n              ? params.value[0] +\r\n              (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n              : '--'\r\n            }</p></a>`\r\n            : `<span><p>${params.value[0]\r\n              ? params.value[0] +\r\n              (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n              : '--'\r\n            }</p></span>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total') {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              this.getDataInNewTab(!this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Scheduled' : 'Referral Scheduled', params);\r\n              return;\r\n            }\r\n            this.getDataFromCell(!this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Scheduled' : 'Referral Scheduled', event);\r\n          }\r\n        },\r\n      },\r\n    ];\r\n    const siteVisitDoneAndNotDone: any = [\r\n      {\r\n        headerName: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',\r\n        field: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          this.isCustomStatusEnabled\r\n            ? params?.data?.statuses?.SiteVisitDoneCount\r\n            : params.data?.siteVisitDoneCount,\r\n          this.isCustomStatusEnabled\r\n            ? params?.data?.statuses?.SiteVisitDoneUniqueCount\r\n            : params.data?.siteVisitDoneUniqueCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage\r\n            ? this.isCustomStatusEnabled\r\n              ? params?.data?.statuses?.SiteVisitDoneUniqueCount__percentage__\r\n              : params.data?.siteVisitDoneUniqueCount__percentage__\r\n            : '',\r\n        ],\r\n        minWidth: 120,\r\n        valueLabels: [\r\n          !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',\r\n          (!this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken') + ' (unique count)'\r\n        ],\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return !this.isCustomStatusEnabled\r\n            ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>\r\n          <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1]\r\n              ? params.value[1] +\r\n              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')\r\n              : '--'\r\n            }<span></p></a>`\r\n            : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>\r\n            <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1]\r\n              ? params.value[1] +\r\n              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')\r\n              : '--'\r\n            }<span></p></span>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total') {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              this.getMeetingCountnewTab(\r\n                'All Leads',\r\n                params,\r\n                'Site Visit Done'\r\n              );\r\n              return;\r\n            }\r\n            this.getMeetingCount('All Leads', event, 'Site Visit Done');\r\n          }\r\n        },\r\n      },\r\n      {\r\n        headerName: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Not Done' : 'Referral Not Taken',\r\n        field: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Not Done' : 'Referral Not Taken',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          this.isCustomStatusEnabled\r\n            ? params?.data?.statuses?.SiteVisitNotDoneCount\r\n            : params.data?.siteVisitNotDoneCount,\r\n          this.isCustomStatusEnabled\r\n            ? params?.data?.statuses?.SiteVisitNotDoneUniqueCount\r\n            : params.data?.siteVisitNotDoneUniqueCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage\r\n            ? this.isCustomStatusEnabled\r\n              ? params?.data?.statuses\r\n                ?.SiteVisitNotDoneUniqueCount__percentage__\r\n              : params.data?.siteVisitNotDoneUniqueCount__percentage__\r\n            : '',\r\n        ],\r\n        valueLabels: [\r\n          !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Not Done' : 'Referral Not Taken',\r\n          (!this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Not Done' : 'Referral Not Taken') + ' (unique count)'\r\n        ],\r\n        minWidth: 150,\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return !this.isCustomStatusEnabled\r\n            ? `<a><p>${params.value[0] ? params.value[0] : '--'}</p>\r\n          <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1]\r\n              ? params.value[1] +\r\n              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')\r\n              : '--'\r\n            }<span></p></a>`\r\n            : `<span><p>${params.value[0] ? params.value[0] : '--'}</p>\r\n            <p class=\"text-truncate\"><span class=\"text-dark-gray\">unique: </span><span class=\"fw-600\">${params.value[1]\r\n              ? params.value[1] +\r\n              (params?.value?.[4] ? ` (${params?.value?.[4]})` : '')\r\n              : '--'\r\n            }<span></p></span>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total') {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              this.getMeetingCountnewTab(\r\n                'All Leads',\r\n                params,\r\n                'Site Visit Not Done'\r\n              );\r\n              return;\r\n            }\r\n            this.getMeetingCount('All Leads', event, 'Site Visit Not Done');\r\n          }\r\n        },\r\n      },\r\n    ];\r\n    const others: any = [\r\n      {\r\n        headerName: 'Booked',\r\n        field: 'Booked',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          params.data?.bookedCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage ? params.data?.bookedCountPercentage : '',\r\n        ],\r\n        minWidth: 110,\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return params?.value?.[2] == 'Total' || params.value[0] == 0\r\n            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`\r\n            : !this.isCustomStatusEnabled\r\n              ? `<p><a>${params.value[0]\r\n                ? params.value[0] +\r\n                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                : '--'\r\n              }</a></p>`\r\n              : `<p><span>${params.value[0]\r\n                ? params.value[0] +\r\n                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                : '--'\r\n              }</span></p>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total' || event.value[0] == 0) {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              this.getDataInNewTab('Booked', params);\r\n              return;\r\n            }\r\n            this.getDataFromCell('Booked', event);\r\n          }\r\n        },\r\n      },\r\n      {\r\n        headerName: 'Invoiced',\r\n        field: 'Invoiced',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          params.data?.invoicedLeadsCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage\r\n            ? params.data?.invoicedLeadsCountPercentage\r\n            : '',\r\n        ],\r\n        minWidth: 110,\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return params?.value?.[2] == 'Total' || params.value[0] == 0\r\n            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`\r\n            : `<p><a>${params.value[0]\r\n              ? params.value[0] +\r\n              (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n              : '--'\r\n            }</a></p>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total' || event.value[0] == 0) {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              this.getDataInNewTab('Invoiced', params);\r\n              return;\r\n            }\r\n            this.getDataFromCell('Invoiced', event);\r\n          }\r\n        },\r\n      },\r\n      {\r\n        headerName: 'Booking Cancel',\r\n        field: 'Booking Cancel',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          params.data?.bookingCancelCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage\r\n            ? params?.data?.bookingCancelCountPercentage\r\n            : '',\r\n        ],\r\n        minWidth: 120,\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return params?.value?.[2] == 'Total' || params.value[0] == 0\r\n            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`\r\n            : !this.isCustomStatusEnabled\r\n              ? `<p><a>${params.value[0]\r\n                ? params.value[0] +\r\n                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                : '--'\r\n              }</a></p>`\r\n              : `<p><span>${params.value[0]\r\n                ? params.value[0] +\r\n                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                : '--'\r\n              }</span></p>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total' || event.value[0] == 0) {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              this.getDataInNewTab('Booking Cancel', params);\r\n              return;\r\n            }\r\n            this.getDataFromCell('Booking Cancel', event);\r\n          }\r\n        },\r\n      },\r\n      {\r\n        headerName: 'Not Interested',\r\n        field: 'Not Interested',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          params.data?.notInterestedCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage\r\n            ? params.data?.notInterestedCountPercentage\r\n            : '',\r\n        ],\r\n        minWidth: 120,\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return params?.value?.[2] == 'Total' || params.value[0] == 0\r\n            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`\r\n            : !this.isCustomStatusEnabled\r\n              ? `<p><a>${params.value[0]\r\n                ? params.value[0] +\r\n                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                : '--'\r\n              }</a></p>`\r\n              : `<p><span>${params.value[0]\r\n                ? params.value[0] +\r\n                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                : '--'\r\n              }</span></p>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total') {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              this.getDataInNewTab('Not Interested', params);\r\n              return;\r\n            }\r\n            this.getDataFromCell('Not Interested', event);\r\n          }\r\n        },\r\n      },\r\n      {\r\n        headerName: 'Dropped',\r\n        field: 'Dropped',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          params.data?.droppedCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage ? params.data?.droppedCountPercentage : '',\r\n        ],\r\n        minWidth: 80,\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return params?.value?.[2] == 'Total' || params.value[0] == 0\r\n            ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`\r\n            : !this.isCustomStatusEnabled\r\n              ? `<p><a>${params.value[0]\r\n                ? params.value[0] +\r\n                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                : '--'\r\n              }</a></p>`\r\n              : `<p><span>${params.value[0]\r\n                ? params.value[0] +\r\n                (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                : '--'\r\n              }</span></p>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total') {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              this.getDataInNewTab('Dropped', params);\r\n              return;\r\n            }\r\n            this.getDataFromCell('Dropped', event);\r\n          }\r\n        },\r\n      },\r\n      {\r\n        headerName: 'Expression Of Interest',\r\n        field: 'Expression Of Interest',\r\n        filter: false,\r\n        hide: false,\r\n        valueGetter: (params: any) => [\r\n          params.data?.expressionOfInterestLeadCount,\r\n          params?.data?.userId,\r\n          params?.data?.projectTitle,\r\n          this.canShowPercentage\r\n            ? params.data?.expressionOfInterestLeadCountPercentage\r\n            : '',\r\n        ],\r\n        minWidth: 160,\r\n        cellRenderer: (params: any) => {\r\n          const filters = { ...this.filtersPayload };\r\n          if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n          return `<p>${params.value[0]\r\n            ? params.value[0] +\r\n            (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n            : '--'\r\n            }</p>`;\r\n        },\r\n        cellClass: 'cursor-pointer',\r\n        onCellClicked: (event: any) => {\r\n          const isCtrlClick = event?.event?.ctrlKey;\r\n          const params = { value: event?.value, data: event?.data };\r\n          if (event.data.projectTitle == 'Total') {\r\n            return;\r\n          } else if (event.value[0] != 0) {\r\n            if (isCtrlClick) {\r\n              const filters = { ...this.filtersPayload };\r\n              if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n              window?.open(\r\n                `leads/manage-leads?leadReportGetData=true&assignTo=${params?.value?.[0]\r\n                }&data=${encodeURIComponent(\r\n                  JSON.stringify(params?.data)\r\n                )}&operation=Dropped&filtersPayload=${encodeURIComponent(\r\n                  JSON.stringify(filters)\r\n                )}`,\r\n                '_blank'\r\n              );\r\n              return;\r\n            }\r\n            this.getDataFromCell('Expression Of Interest', event);\r\n          }\r\n        },\r\n      },\r\n    ];\r\n    this.gridOptions.columnDefs = this.isCustomStatusEnabled\r\n      ? [\r\n        ...nameAndLeads,\r\n        // ...newAndPending,\r\n        ...overdue,\r\n        // ...callbackAndMS,\r\n        ...meetingDoneAndNotDone,\r\n        // ...svs,\r\n        ...siteVisitDoneAndNotDone,\r\n        // ...others\r\n      ]\r\n      : [\r\n        ...nameAndLeads,\r\n        ...newAndPending,\r\n        ...overdue,\r\n        ...callbackAndMS,\r\n        ...meetingDoneAndNotDone,\r\n        ...svs,\r\n        ...siteVisitDoneAndNotDone,\r\n        ...others,\r\n      ];\r\n    if (this.isCustomStatusEnabled)\r\n      this.customStatusList.forEach((customStatus: CustomStatus) => {\r\n        let col: any = {\r\n          headerName: customStatus?.displayName,\r\n          field: customStatus?.displayName,\r\n          filter: false,\r\n          hide: false,\r\n          valueGetter: (params: any) => [\r\n            params?.data?.statuses?.[customStatus?.displayName],\r\n            params?.data?.userId,\r\n            params?.data?.projectTitle,\r\n            this.canShowPercentage\r\n              ? params?.data?.statuses?.[\r\n              customStatus?.displayName + '__percentage__'\r\n              ]\r\n              : '',\r\n          ],\r\n          minWidth: 110,\r\n          cellRenderer: (params: any) => {\r\n            const filters = { ...this.filtersPayload };\r\n            if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n            return params?.value?.[2] == 'Total' || params.value[0] == 0\r\n              ? `<p>${params.value[0] ? params.value[0] : '--'}</p>`\r\n              : !this.isCustomStatusEnabled\r\n                ? `<p><a>${params.value[0]\r\n                  ? params.value[0] +\r\n                  (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                  : '--'\r\n                }</a></p>`\r\n                : `<p><span>${params.value[0]\r\n                  ? params.value[0] +\r\n                  (params?.value?.[3] ? ` (${params?.value?.[3]})` : '')\r\n                  : '--'\r\n                }</span></p>`;\r\n          },\r\n          cellClass: 'cursor-pointer',\r\n          onCellClicked: (event: any) => {\r\n            const isCtrlClick = event?.event?.ctrlKey;\r\n            const params = { value: event?.value, data: event?.data };\r\n            if (event.data.projectTitle == 'Total') {\r\n              return;\r\n            } else if (event.value[0] != 0) {\r\n              if (isCtrlClick) {\r\n                this.getDataInNewTab(customStatus?.displayName, params);\r\n                return;\r\n              }\r\n              this.getDataFromCell(customStatus?.displayName, event);\r\n            }\r\n          },\r\n        };\r\n        this.gridOptions?.columnDefs?.push(col);\r\n      });\r\n\r\n    this.gridOptions.columnDefs.forEach((item: any, index: number) => {\r\n      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {\r\n        this.columnDropDown.push({ field: item.field, hide: item.hide });\r\n      }\r\n    });\r\n    this.gridOptions.context = {\r\n      componentParent: this,\r\n    };\r\n  }\r\n\r\n  getMeetingCount(operation: string, event: any, meetingStatus: string) {\r\n    const filters = { ...this.filtersPayload };\r\n    if (filters?.IsWithTeam) {\r\n      filters.IsWithTeam = false;\r\n    }\r\n    // if (this.isCustomStatusEnabled) {\r\n    //   return\r\n    // }\r\n    this.router.navigate(['leads/manage-leads']);\r\n    let visitMeeting: any = [];\r\n    visitMeeting.push(meetingStatus);\r\n    this.gridOptionsService.data = event.data;\r\n    this.gridOptionsService.dateType = this.appliedFilter.dateType;\r\n    this.gridOptionsService.status = operation;\r\n    this.gridOptionsService.payload = this.filtersPayload;\r\n    this.gridOptionsService.meetingStatus = visitMeeting;\r\n  }\r\n\r\n  getMeetingCountnewTab(operation: string, params: any, meetingStatus: string) {\r\n    const filters = { ...this.filtersPayload };\r\n    if (filters?.IsWithTeam) {\r\n      filters.IsWithTeam = false;\r\n    }\r\n    window?.open(\r\n      `leads/manage-leads?leadReportGetMeetingCount=true&data=${encodeURIComponent(\r\n        JSON.stringify(params?.data)\r\n      )}&operation=${operation}&meetingStatus=${meetingStatus}&filtersPayload=${encodeURIComponent(\r\n        JSON.stringify(filters)\r\n      )}`,\r\n      '_blank'\r\n    );\r\n  }\r\n\r\n  onGridReady(params: any) {\r\n    this.gridApi = params.api;\r\n    this.gridColumnApi = params.columnApi;\r\n    this.toggleColumns(params);\r\n    this.gridOptions.api = params.api;\r\n  }\r\n\r\n  getDataFromCell(operation: string, event: any) {\r\n    // if (this.isCustomStatusEnabled) {\r\n    //   return\r\n    // }\r\n    this.router.navigate(['leads/manage-leads']);\r\n    this.gridOptionsService.meetingStatus = undefined;\r\n    this.gridOptionsService.dateType = this.appliedFilter.dateType;\r\n    this.gridOptionsService.data = event.data;\r\n    this.gridOptionsService.status = operation;\r\n    const filters = { ...this.filtersPayload };\r\n    if (filters?.IsWithTeam) filters.IsWithTeam = false;\r\n    this.gridOptionsService.payload = filters;\r\n  }\r\n\r\n  getDataInNewTab(operation: string, params: any) {\r\n    //     if (this.isCustomStatusEnabled) {\r\n    //   return\r\n    // }\r\n    const filters = { ...this.filtersPayload };\r\n    if (filters?.IsWithTeam) {\r\n      filters.IsWithTeam = false;\r\n    }\r\n    window?.open(\r\n      `leads/manage-leads?leadReportGetData=true&data=${encodeURIComponent(\r\n        JSON.stringify(params?.data)\r\n      )}&operation=${operation}&filtersPayload=${encodeURIComponent(\r\n        JSON.stringify(filters)\r\n      )}`,\r\n      '_blank'\r\n    );\r\n  }\r\n\r\n  onPageChange(e: any) {\r\n    this.currOffset = e;\r\n    this.filtersPayload = {\r\n      ...this.filtersPayload,\r\n      pageSize: this.pageSize,\r\n      pageNumber: e + 1,\r\n    };\r\n    this.gridApi.paginationGoToPage(e);\r\n    this._store.dispatch(new UpdateUserFilterPayload(this.filtersPayload));\r\n    this.isCustomStatusEnabled\r\n      ? this._store.dispatch(new FetchReportsCustomUser())\r\n      : this._store.dispatch(new FetchReportsUser());\r\n  }\r\n\r\n  onResetDateFilter() {\r\n    this.appliedFilter = {\r\n      ...this.appliedFilter,\r\n      dateType: null,\r\n      date: '',\r\n    };\r\n    this.filterFunction();\r\n  }\r\n\r\n  getArrayOfFilters(key: string, values: string) {\r\n    const allowedKeys = [\r\n      'subSources',\r\n      'projects',\r\n      'agencyNames',\r\n      'cities',\r\n      'states',\r\n    ];\r\n\r\n    if (\r\n      [\r\n        'pageSize',\r\n        'pageNumber',\r\n        'visibility',\r\n        'withTeam',\r\n        'isGM',\r\n        'userStatus',\r\n        'search',\r\n        'ShouldShowAll',\r\n      ].includes(key) ||\r\n      values?.length === 0\r\n    )\r\n      return [];\r\n    else if (key === 'date' && values.length === 2) {\r\n      if (key === 'date' && values[0] !== null) {\r\n        this.toDate = setTimeZoneDate(\r\n          new Date(values[0]),\r\n          this.userData?.timeZoneInfo?.baseUTcOffset\r\n        );\r\n        this.fromDate = setTimeZoneDate(\r\n          new Date(values[1]),\r\n          this.userData?.timeZoneInfo?.baseUTcOffset\r\n        );\r\n        const formattedToDate = getTimeZoneDate(\r\n          this.toDate,\r\n          this.userData?.timeZoneInfo?.baseUTcOffset,\r\n          'dayMonthYear'\r\n        );\r\n        const formattedFromDate = getTimeZoneDate(\r\n          this.fromDate,\r\n          this.userData?.timeZoneInfo?.baseUTcOffset,\r\n          'dayMonthYear'\r\n        );\r\n        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;\r\n        return [dateRangeString];\r\n      } else {\r\n        return null;\r\n      }\r\n    } else if (allowedKeys.includes(key)) {\r\n      return values;\r\n    }\r\n    return values?.toString()?.split(',');\r\n  }\r\n\r\n  applyAdvancedFilter() {\r\n    this.filterFunction();\r\n    this.modalService.hide();\r\n  }\r\n\r\n  onRemoveFilter(key: string, value: string) {\r\n    if (['dateType', 'date'].includes(key)) {\r\n      delete this.appliedFilter[key];\r\n      const dependentKey = key === 'date' ? 'dateType' : 'date';\r\n      if (this.appliedFilter[dependentKey]) {\r\n        delete this.appliedFilter[dependentKey];\r\n      }\r\n    } else {\r\n      this.appliedFilter[key] = this.appliedFilter[key]?.filter(\r\n        (item: any, index: number) => {\r\n          const matchIndex = this.appliedFilter[key]?.indexOf(value);\r\n          return index !== matchIndex;\r\n        }\r\n      );\r\n    }\r\n    this.filterFunction();\r\n  }\r\n\r\n  openAdvFiltersModal(advFilters: TemplateRef<any>) {\r\n    this._store.dispatch(new FetchProjectList());\r\n    this._store.dispatch(new FetchSubSourceList());\r\n    this._store.dispatch(new FetchLeadCities());\r\n    this._store.dispatch(new FetchLeadStates());\r\n    // this._store.dispatch(new FetchLeadCountries)\r\n    this._store.dispatch(new FetchAllSources());\r\n    let initialState: any = {\r\n      class: 'ip-modal-unset  top-full-modal',\r\n    };\r\n    this.modalService.show(advFilters, initialState);\r\n  }\r\n\r\n  getUserName(id: string) {\r\n    let userName = '';\r\n    this.allUsers?.forEach((user: any) => {\r\n      if (id === user.id) userName = `${user.fullName}`;\r\n    });\r\n    return userName;\r\n  }\r\n  toggleColumns(params: any): void {\r\n    this.columns = params?.columnApi?.getColumns()?.map((column: any) => {\r\n      return {\r\n        label: column?.getColDef()?.headerName,\r\n        value: column,\r\n      };\r\n    });\r\n\r\n    // .slice(4, this.columns?.length), this.columns[1], this.columns[2]]\r\n    this.columns = [...this.columns.slice(1, this.columns?.length)].sort(\r\n      (a: any, b: any) => a?.label?.localeCompare(b?.label)\r\n    );\r\n    this.defaultColumns = this.columns?.filter(\r\n      (col) => col?.value?.getColDef()?.hide !== true\r\n    );\r\n\r\n    let columnState = JSON.parse(localStorage.getItem('myDataColumnState'));\r\n    if (columnState) {\r\n      this.gridColumnApi.applyColumnState({\r\n        state: columnState,\r\n        applyOrder: true,\r\n      });\r\n    }\r\n\r\n    let columnData = localStorage.getItem('status-reports-columns')?.split(',');\r\n\r\n    if (columnData?.length) {\r\n      let visibleColumns = this.columns?.filter((col: any) =>\r\n        columnData?.includes(col.label)\r\n      );\r\n      this.defaultColumns = visibleColumns;\r\n      this.onColumnsSelected(visibleColumns);\r\n    }\r\n  }\r\n\r\n  onColumnsSelected(columns: any): void {\r\n    let colData = columns?.map((column: any) => column.label);\r\n    localStorage.setItem('status-reports-columns', colData?.toString());\r\n    const cols = columns?.map((col: any) => col.value);\r\n    this.gridColumnApi?.setColumnsVisible(cols, true);\r\n    const nonSelectedCols = this.columns?.filter((col: any) => {\r\n      return !cols.includes(col.value);\r\n    });\r\n    this.gridColumnApi?.setColumnsVisible(\r\n      nonSelectedCols.map((col) => col.value),\r\n      false\r\n    );\r\n    var columnState: any = this.gridColumnApi.getColumnState();\r\n    if (columnState && columnState[0]) columnState[0].pinned = 'left';\r\n    localStorage.setItem('myDataColumnState', JSON.stringify(columnState));\r\n    this.gridColumnApi.applyColumnState({\r\n      state: columnState,\r\n      applyOrder: true,\r\n    });\r\n  }\r\n\r\n  onSetColumnDefault() {\r\n    this.defaultColumns = this.columns.filter(\r\n      (col) => col.value.getColDef().hide !== true\r\n    );\r\n    this.onColumnsSelected(this.defaultColumns);\r\n  }\r\n\r\n  currentVisibility(visibility: any, isTopLevelFilter: any) {\r\n    this.appliedFilter.userStatus = visibility;\r\n    this.appliedFilter.pageNumber = 1;\r\n    if (isTopLevelFilter) {\r\n      this.appliedFilter.users = null;\r\n    }\r\n    this.filterFunction();\r\n\r\n    if (this.canViewAllUsers) {\r\n      switch (visibility) {\r\n        case 1:\r\n          this.allUsers = this.users?.filter((user: any) => user.isActive);\r\n          break;\r\n        case 2:\r\n          this.allUsers = this.users?.filter((user: any) => !user.isActive);\r\n          break;\r\n        case null:\r\n          this.allUsers = this.users;\r\n          break;\r\n      }\r\n      this.allUsers = assignToSort(this.allUsers, '');\r\n    } else {\r\n      switch (visibility) {\r\n        case 1:\r\n          this.onlyReportees = this.reportees?.filter(\r\n            (user: any) => user.isActive\r\n          );\r\n          break;\r\n        case 2:\r\n          this.onlyReportees = this.reportees?.filter(\r\n            (user: any) => !user.isActive\r\n          );\r\n          break;\r\n        case null:\r\n          this.onlyReportees = this.reportees;\r\n          break;\r\n      }\r\n      this.onlyReportees = assignToSort(this.onlyReportees, '');\r\n    }\r\n  }\r\n\r\n  assignCount() {\r\n    this.pageSize = this.selectedPageSize;\r\n    this.filtersPayload = {\r\n      ...this.filtersPayload,\r\n      pageSize: this.pageSize,\r\n      pageNumber: 1,\r\n    };\r\n    this._store.dispatch(new UpdateUserFilterPayload(this.filtersPayload));\r\n    this.isCustomStatusEnabled\r\n      ? this._store.dispatch(new FetchReportsCustomUser())\r\n      : this._store.dispatch(new FetchReportsUser());\r\n    this.currOffset = 0;\r\n  }\r\n\r\n  filterFunction() {\r\n    this.appliedFilter.pageNumber = 1;\r\n    if (\r\n      this.appliedFilter?.dateType?.length ||\r\n      this.appliedFilter?.date?.[0]?.length ||\r\n      this.appliedFilter.users?.length ||\r\n      this.appliedFilter.projects?.length ||\r\n      this.appliedFilter.subSources?.length ||\r\n      this.appliedFilter.sources?.length ||\r\n      this.appliedFilter.cities?.length ||\r\n      this.appliedFilter.Countries?.length ||\r\n      this.appliedFilter.states?.length\r\n    ) {\r\n      this.showFilters = true;\r\n    } else {\r\n      this.showFilters = false;\r\n    }\r\n    this.filtersPayload = {\r\n      ...this.filtersPayload,\r\n      pageNumber: this.appliedFilter?.pageNumber,\r\n      pageSize: this.pageSize,\r\n      userStatus: this.appliedFilter.userStatus,\r\n      dateType: ReportDateType[this.appliedFilter.dateType],\r\n      fromDate: setTimeZoneDate(\r\n        this.appliedFilter?.date?.[0],\r\n        this.userData?.timeZoneInfo?.baseUTcOffset\r\n      ),\r\n      toDate: setTimeZoneDate(\r\n        this.appliedFilter.date?.[1],\r\n        this.userData?.timeZoneInfo?.baseUTcOffset\r\n      ),\r\n      IsWithTeam: this.appliedFilter.withTeam,\r\n      UserIds: this.appliedFilter.users,\r\n      SearchText: this.searchTerm,\r\n      Sources: this.appliedFilter.sources,\r\n      SubSources: this.appliedFilter.subSources,\r\n      Projects: this.appliedFilter.projects,\r\n      ReportPermission: this.canViewAllUsers ? 0 : 1,\r\n      ExportPermission: this.canExportAllUsers ? 0 : 1,\r\n      Cities: this.appliedFilter?.cities,\r\n      States: this.appliedFilter?.states,\r\n      // Countries: this.appliedFilter?.Countries,\r\n      ShouldShowAll: this.appliedFilter?.ShouldShowAll ?? true,\r\n      ShouldShowPercentage: this.canShowPercentage,\r\n    };\r\n    this._store.dispatch(new UpdateUserFilterPayload(this.filtersPayload));\r\n    this.isCustomStatusEnabled\r\n      ? this._store.dispatch(new FetchReportsCustomUser())\r\n      : this._store.dispatch(new FetchReportsUser());\r\n    this.currOffset = 0;\r\n  }\r\n\r\n  reset() {\r\n    this.appliedFilter = {\r\n      pageNumber: 1,\r\n      pageSize: this.pageSize,\r\n    };\r\n    this.filterFunction();\r\n  }\r\n\r\n  updateSubSource() {\r\n    if (this.appliedFilter?.sources?.length) {\r\n      this.subSourceList = [];\r\n      this.appliedFilter?.sources.forEach((i: any) => {\r\n        const source: any = LeadSource[i];\r\n        const leadSource = IntegrationSource[source];\r\n        if (leadSource === '99 Acres') {\r\n          this.subSourceList.push.apply(\r\n            this.subSourceList,\r\n            this.allSubSourceList['NinetyNineAcres'] || []\r\n          );\r\n        } else {\r\n          const formattedKey = leadSource?.replace(/\\s+/g, '');\r\n          if (Array.isArray(this.allSubSourceList?.[formattedKey])) {\r\n            this.subSourceList.push.apply(\r\n              this.subSourceList,\r\n              this.allSubSourceList?.[formattedKey] || []\r\n            );\r\n          } else {\r\n            this.subSourceList.push.apply(\r\n              this.subSourceList,\r\n              this.allSubSourceList[leadSource] || []\r\n            );\r\n          }\r\n        }\r\n      });\r\n    } else {\r\n      let subSourceList: string[] = this.leadSources?.flatMap((lead: any): string[] => {\r\n        if (lead?.displayName === '99 Acres') {\r\n          return this.allSubSourceList?.['NinetyNineAcres'] || [];\r\n        }\r\n        const formattedKey = lead?.displayName?.replace(/\\s+/g, '');\r\n        let match = this.allSubSourceList?.[formattedKey];\r\n        if (!match) {\r\n          match = this.allSubSourceList[lead?.displayName];\r\n        }\r\n        if (!match && formattedKey?.toLowerCase() === '99acres') {\r\n          match = this.allSubSourceList['NinetyNineAcres'];\r\n        }\r\n        return Array.isArray(match) ? match : [];\r\n      }) || [];\r\n      this.subSourceList = subSourceList\r\n    }\r\n  }\r\n\r\n  onSelectSource(source: any) {\r\n    if (source) {\r\n      this.updateSubSources(source.displayName);\r\n    } else {\r\n      this.updateSubSources(null);\r\n    }\r\n  }\r\n\r\n  updateSubSources(sourceName: string | null) {\r\n    if (sourceName) {\r\n      if (sourceName === '99 Acres') {\r\n        this.subSourceList = this.allSubSourceList['NinetyNineAcres'] || [];\r\n      } else {\r\n        const formattedKey = sourceName.replace(/\\s+/g, '');\r\n        if (Array.isArray(this.allSubSourceList?.[formattedKey])) {\r\n          this.subSourceList = this.allSubSourceList?.[formattedKey] || [];\r\n        } else {\r\n          this.subSourceList = this.allSubSourceList[sourceName] || [];\r\n        }\r\n      }\r\n    } else {\r\n      let subSourceList: string[] = this.leadSources?.flatMap((lead: any): string[] => {\r\n        if (lead?.displayName === '99 Acres') {\r\n          return this.allSubSourceList?.['NinetyNineAcres'] || [];\r\n        }\r\n        const formattedKey = lead?.displayName?.replace(/\\s+/g, '');\r\n        let match = this.allSubSourceList?.[formattedKey];\r\n        if (!match) {\r\n          match = this.allSubSourceList[lead?.displayName];\r\n        }\r\n        if (!match && formattedKey?.toLowerCase() === '99acres') {\r\n          match = this.allSubSourceList['NinetyNineAcres'];\r\n        }\r\n        return Array.isArray(match) ? match : [];\r\n      }) || [];\r\n      this.subSourceList = subSourceList\r\n    }\r\n  }\r\n\r\n  refresh() {\r\n    this.gridOptions.api?.refreshCells();\r\n  }\r\n\r\n  exportLeadReport() {\r\n    this._store.dispatch(new FetchUserExportSuccess(''));\r\n    this.filterFunction();\r\n    let initialState: any = {\r\n      payload: {\r\n        ...this.filtersPayload,\r\n        selectedColumns: this.gridColumnApi\r\n          .getColumnState()\r\n          .filter((col: any) => !col?.hide)\r\n          .map((col: any) => col?.colId)?.map((col: any) => {\r\n            if (col === 'Referral Taken') {\r\n              return 'Site Visit Done'\r\n            }\r\n            else if (col === 'Referral Not Taken') {\r\n              return 'Site Visit Not Done'\r\n            }\r\n            else if (col === 'Referral Scheduled') {\r\n              return 'Site Visit Scheduled'\r\n            }\r\n            return col\r\n          }),\r\n        timeZoneId:\r\n          this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),\r\n        baseUTcOffset:\r\n          this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),\r\n      },\r\n      class: 'modal-400 modal-dialog-centered ph-modal-unset',\r\n    };\r\n    this.modalService.show(\r\n      ExportMailComponent,\r\n      Object.assign(\r\n        {},\r\n        {\r\n          class: 'modal-400 modal-dialog-centered ph-modal-unset',\r\n          initialState,\r\n        }\r\n      )\r\n    );\r\n  }\r\n\r\n  onSearch($event: any) {\r\n    if ($event.key === 'Enter') {\r\n      if (!this.searchTerm) {\r\n        return;\r\n      }\r\n      this.searchTermSubject.next(this.searchTerm);\r\n    }\r\n  }\r\n\r\n  isEmptyInput($event: any) {\r\n    if (this.searchTerm === '' || this.searchTerm === null) {\r\n      this.searchTermSubject.next('');\r\n    }\r\n  }\r\n\r\n  toggleView() {\r\n    this.currentView = this.currentView === 'graph' ? 'table' : 'graph';\r\n  }\r\n\r\n  exportGraphAsPDF() {\r\n    if (this.reportsGraph && this.isGraphExportEnabled()) {\r\n      this.reportsGraph.exportGraph();\r\n    }\r\n  }\r\n\r\n  isGraphExportEnabled(): boolean {\r\n    return this.currentView === 'graph' &&\r\n      this.reportsGraph?.isChartReady &&\r\n      !this.reportsGraph?.showSelectionMessage;\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.stopper.next();\r\n    this.stopper.complete();\r\n  }\r\n}\r\n", "<div *ngIf=\"canViewAllUsers || canViewReportees\">\r\n  <div class=\"d-flex bg-white py-12 px-16 border-top border-bottom w-100 position-fixed z-index-2\">\r\n    <div class=\"align-center ml-8 z-index-1021 tb-px-0 tb-pb-0 tb-br-top-unset tb-left-110 left-230\"\r\n      [ngClass]=\"showLeftNav ? 'left-230' : 'left-125'\">\r\n      <ul class=\"align-center top-nav-bar text-nowrap\">\r\n        <ng-container *ngFor=\"let visibilityImage of visibilityList;let i=index\">\r\n          <div [title]=\"usersData?.[visibilityImage?.visibility] ? usersData?.[visibilityImage?.visibility] : ''\"\r\n            (click)=\"currentVisibility(visibilityImage.userStatus, true)\" class=\"cursor-pointer\">\r\n            <div class=\"align-center ph-mb-4\">\r\n              <a [class.active]=\"appliedFilter.userStatus == visibilityImage.userStatus\"><img [type]=\"'leadrat'\"\r\n                  [appImage]=\"s3BucketUrl + visibilityImage.image\" alt=\"muso\" width=\"22\" height=\"22\"></a>\r\n              <span [class.active]=\"appliedFilter.userStatus == visibilityImage.userStatus\"\r\n                class=\"text-large ml-8 mr-16\">{{ visibilityImage.name }}</span>\r\n            </div>\r\n          </div>\r\n        </ng-container>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n  <!-- <div class=\"mt-16 fw-600 header-3\">{{ 'GLOBAL.lead' | translate }} {{ 'GLOBAL.status' | translate }} {{\r\n    'SIDEBAR.reports' | translate }}</div> -->\r\n  <div class=\"mx-24 pt-40\">\r\n    <ng-template #AdvancedFilters>\r\n      <div class=\"lead-adv-filter p-30 bg-white brbl-15 brbr-15\">\r\n        <div class=\"adv-filter\">\r\n          <div class=\"d-flex w-100 flex-wrap ng-select-sm\">\r\n            <div class=\"flex-column w-25 tb-w-33 ip-w-50 ph-w-100\">\r\n              <div class=\"justify-between align-end mr-20\">\r\n                <div class=\"field-label\">{{'USER.user' | translate }}</div>\r\n                <label class=\"checkbox-container mb-4\">\r\n                  <input type=\"checkbox\" [(ngModel)]=\"appliedFilter.withTeam\">\r\n                  <span class=\"checkmark\"></span>{{'DASHBOARD.with-team' | translate}}\r\n                </label>\r\n              </div>\r\n              <div class=\"position-relative mr-20 ph-mr-0\">\r\n                <ng-select [virtualScroll]=\"true\" [items]=\"canViewAllUsers ? allUsers : onlyReportees\" ResizableDropdown\r\n                  [ngClass]=\"{'blinking pe-none': (canViewAllUsers ? isAllUsersLoading : isOnlyReporteesLoading )}\"\r\n                  [multiple]=\"true\" [closeOnSelect]=\"false\" name=\"user\" placeholder=\"ex. Manasa Pampana\"\r\n                  [(ngModel)]=\"appliedFilter.users\" bindLabel=\"fullName\" bindValue=\"id\">\r\n                  <ng-template ng-label-tmp let-item=\"item\" let-clear=\"clear\">\r\n                    <span class=\"ic-cancel ic-dark icon ic-x-xs mr-4\" (click)=\"clear(item)\"></span>\r\n                    <span class=\"ng-value-label\"> {{item.firstName + ' ' +\r\n                      item.lastName}}</span>\r\n                  </ng-template>\r\n                  <ng-template ng-option-tmp let-item=\"item\" let-item$=\"item$\" let-index=\"index\">\r\n                    <div class=\"flex-between\">\r\n                      <div class=\"checkbox-container\"><input type=\"checkbox\" id=\"item-{{index}}\"\r\n                          data-automate-id=\"item-{{index}}\" [checked]=\"item$.selected\"><span\r\n                          class=\"checkmark\"></span><span class=\"text-truncate-1 break-all\"> {{item.firstName}}\r\n                          {{item.lastName}}</span>\r\n                      </div>\r\n                      <span class=\"text-disabled\" *ngIf=\"!item.isActive\">( Disabled )</span>\r\n                    </div>\r\n                  </ng-template>\r\n                </ng-select>\r\n              </div>\r\n            </div>\r\n            <div class=\"w-25 tb-w-33 ip-w-50 ph-w-100\">\r\n              <div class=\"field-label\">{{'LEADS.source' | translate }} </div>\r\n              <div class=\"mr-20 ph-mr-0\">\r\n                <ng-select [virtualScroll]=\"true\" [items]=\"leadSources\" [multiple]=\"true\" [closeOnSelect]=\"false\"\r\n                  ResizableDropdown [ngClass]=\"{'blinking pe-none': isSourcesLoading}\"\r\n                  placeholder=\"{{'GLOBAL.select' | translate}}\" [(ngModel)]=\"appliedFilter.sources\"\r\n                  (change)=\"updateSubSource()\" bindLabel=\"displayName\" bindValue=\"displayName\">\r\n                  <ng-template ng-option-tmp let-item=\"item\" let-item$=\"item$\" let-index=\"index\">\r\n                    <div class=\"checkbox-container\"><input type=\"checkbox\" id=\"item-{{index}}\"\r\n                        data-automate-id=\"item-{{index}}\" [checked]=\"item$.selected\"><span\r\n                        class=\"checkmark\"></span><span class=\"text-truncate-1 break-all\"> {{item?.displayName}}</span>\r\n                    </div>\r\n                  </ng-template>\r\n                </ng-select>\r\n              </div>\r\n            </div>\r\n            <div class=\"w-25 tb-w-33 ip-w-50 ph-w-100\">\r\n              <div class=\"field-label\">{{'LEADS.sub-source' | translate}}</div>\r\n              <div class=\"mr-20 ph-mr-0\">\r\n                <ng-select [virtualScroll]=\"true\" [items]=\"subSourceList\"\r\n                  [ngClass]=\"{'blinking pe-none': allSubSourceListIsLoading}\" [multiple]=\"true\" [closeOnSelect]=\"false\"\r\n                  ResizableDropdown placeholder=\"{{'GLOBAL.select' | translate}}\"\r\n                  [(ngModel)]=\"appliedFilter.subSources\">\r\n                  <ng-template ng-option-tmp let-item=\"item\" let-item$=\"item$\" let-index=\"index\">\r\n                    <div class=\"checkbox-container\"><input type=\"checkbox\" id=\"item-{{index}}\"\r\n                        data-automate-id=\"item-{{index}}\" [checked]=\"item$.selected\"><span\r\n                        class=\"checkmark\"></span><span class=\"text-truncate-1 break-all\"> {{item}}</span>\r\n                    </div>\r\n                  </ng-template>\r\n                </ng-select>\r\n              </div>\r\n            </div>\r\n            <div class=\"w-25 tb-w-33 ip-w-50 ph-w-100\">\r\n              <div class=\"field-label\">{{'SIDEBAR.project' | translate }} </div>\r\n              <div class=\"mr-20 mt-4 ph-mr-0 \">\r\n                <ng-select [virtualScroll]=\"true\" [items]=\"projectList\" ResizableDropdown\r\n                  [ngClass]=\"{'blinking pe-none': isProjectListLoading}\" [multiple]=\"true\" [closeOnSelect]=\"false\"\r\n                  placeholder=\"{{'GLOBAL.select' | translate}}\" bindLabel=\"id\" [(ngModel)]=\"appliedFilter.projects\">\r\n                  <ng-template ng-option-tmp let-item=\"item\" let-item$=\"item$\" let-index=\"index\">\r\n                    <div class=\"checkbox-container\"><input type=\"checkbox\" id=\"item-{{index}}\"\r\n                        data-automate-id=\"item-{{index}}\" [checked]=\"item$.selected\"><span\r\n                        class=\"checkmark\"></span><span class=\"text-truncate-1 break-all\"> {{item}}</span>\r\n                    </div>\r\n                  </ng-template>\r\n                </ng-select>\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"w-25 tb-w-33 ip-w-50 ph-w-100\">\r\n              <div class=\"field-label\">Country</div>\r\n              <div class=\"mr-20 ph-mr-0\">\r\n                <ng-select [virtualScroll]=\"true\" [items]=\"countryList\" [ngClass]=\"{'blinking pe-none': countryIsLoading}\"\r\n                  ResizableDropdown [multiple]=\"true\" [closeOnSelect]=\"false\"\r\n                  placeholder=\"{{'GLOBAL.select' | translate}}\" bindLabel=\"item\" bindValue=\"item\"\r\n                  [(ngModel)]=\"appliedFilter.Countries\">\r\n                  <ng-template ng-option-tmp let-item=\"item\" let-item$=\"item$\" let-index=\"index\">\r\n                    <div class=\"checkbox-container\"><input type=\"checkbox\" id=\"item-{{index}}\"\r\n                        data-automate-id=\"item-{{index}}\" [checked]=\"item$.selected\"><span\r\n                        class=\"checkmark\"></span><span class=\"text-truncate-1 break-all\"> {{item}}</span>\r\n                    </div>\r\n                  </ng-template>\r\n                </ng-select>\r\n              </div>\r\n            </div> -->\r\n            <div class=\"w-25 tb-w-33 ip-w-50 ph-w-100\">\r\n              <div class=\"field-label\">State</div>\r\n              <div class=\"mr-20 ph-mr-0\">\r\n                <ng-select [virtualScroll]=\"true\" [items]=\"states\" [ngClass]=\"{'blinking pe-none': statesIsLoading}\"\r\n                  ResizableDropdown [multiple]=\"true\" [closeOnSelect]=\"false\"\r\n                  placeholder=\"{{'GLOBAL.select' | translate}}\" bindLabel=\"item\" bindValue=\"item\"\r\n                  [(ngModel)]=\"appliedFilter.states\">\r\n                  <ng-template ng-option-tmp let-item=\"item\" let-item$=\"item$\" let-index=\"index\">\r\n                    <div class=\"checkbox-container\"><input type=\"checkbox\" id=\"item-{{index}}\"\r\n                        data-automate-id=\"item-{{index}}\" [checked]=\"item$.selected\"><span\r\n                        class=\"checkmark\"></span><span class=\"text-truncate-1 break-all\"> {{item}}</span>\r\n                    </div>\r\n                  </ng-template>\r\n                </ng-select>\r\n              </div>\r\n            </div>\r\n            <div class=\"w-25 tb-w-33 ip-w-50 ph-w-100\">\r\n              <div class=\"field-label\">City</div>\r\n              <div class=\"mr-20 ph-mr-0\">\r\n                <ng-select [virtualScroll]=\"true\" [items]=\"cities\" [ngClass]=\"{'blinking pe-none': citiesIsLoading}\"\r\n                  ResizableDropdown [multiple]=\"true\" [closeOnSelect]=\"false\"\r\n                  placeholder=\"{{'GLOBAL.select' | translate}}\" bindLabel=\"item\" bindValue=\"item\"\r\n                  [(ngModel)]=\"appliedFilter.cities\">\r\n                  <ng-template ng-option-tmp let-item=\"item\" let-item$=\"item$\" let-index=\"index\">\r\n                    <div class=\"checkbox-container\"><input type=\"checkbox\" id=\"item-{{index}}\"\r\n                        data-automate-id=\"item-{{index}}\" [checked]=\"item$.selected\"><span\r\n                        class=\"checkmark\"></span><span class=\"text-truncate-1 break-all\"> {{item}}</span>\r\n                    </div>\r\n                  </ng-template>\r\n                </ng-select>\r\n              </div>\r\n            </div>\r\n            <div class=\"w-25 tb-w-33 ip-w-50 ph-w-100\">\r\n              <div class=\"field-label\">{{'REPORTS.date-filters' | translate }} </div>\r\n              <div class=\"w-100\">\r\n                <div class=\"mr-20 ph-mr-0 align-center\">\r\n                  <div class=\"w-33\">\r\n                    <ng-select [virtualScroll]=\"true\" placeholder=All [searchable]=\"false\" class=\"mr-10 ng-select-w-171\"\r\n                      ResizableDropdown [(ngModel)]=\"appliedFilter.dateType\">\r\n                      <ng-option name=\"dateType\" ngDefaultControl *ngFor=\"let dType of dateTypeList\"\r\n                        [value]=\"dType\">{{dType}}</ng-option>\r\n                    </ng-select>\r\n                  </div>\r\n                  <div class=\"w-67 align-center position-relative filters-grid clear-padding\">\r\n                    <div class=\"date-picker border pt-8 pb-6 br-4 align-center w-100\" id=\"reportsAppointmentDate\"\r\n                      data-automate-id=\"reportsAppointmentDate\">\r\n                      <span class=\"ic-appointment icon ic-xxs ic-black\" [owlDateTimeTrigger]=\"dt1\"></span>\r\n                      <input type=\"text\" readonly placeholder=\"ex. 5-03-2025 - 14-03-2025\"\r\n                        class=\"pl-20 text-large w-100\"\r\n                        [max]=\"appliedFilter.dateType === 'Modified Date' || appliedFilter.dateType === 'Created Date' ? maxDate : ''\"\r\n                        [owlDateTimeTrigger]=\"dt1\" [owlDateTime]=\"dt1\" [selectMode]=\"'range'\"\r\n                        (ngModelChange)=\"appliedFilter.date = $event\" [ngModel]=\"appliedFilter.date\"\r\n                        [disabled]=\"!appliedFilter.dateType\" />\r\n                      <owl-date-time [pickerType]=\"'calendar'\" #dt1\r\n                        (afterPickerOpen)=\"onPickerOpened(currentDate)\"></owl-date-time>\r\n                    </div>\r\n                    <div *ngIf=\"appliedFilter?.date?.[0]\" class=\"right-4 align-center cursor-pointer position-absolute\"\r\n                      (click)=\"onResetDateFilter()\">\r\n                      <span class=\"ic-refresh ic-coal\"></span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"w-25 tb-w-33 ip-w-50 ph-w-100 align-end\" *ngIf=\"isCustomStatusEnabled\">\r\n              <div class=\"ml-1 pt-3\">\r\n                <label class=\"checkbox-container mb-4\">\r\n                  <input type=\"checkbox\" [(ngModel)]=\"appliedFilter.ShouldShowAll\">\r\n                  <span class=\"checkmark\"></span>Show all users\r\n                </label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"flex-end mt-10 tb-mr-20 ph-mr-0\">\r\n          <u class=\"mr-20 fw-semi-bold text-mud cursor-pointer\" (click)=\"modalService.hide()\">{{'BUTTONS.cancel' |\r\n            translate }}</u>\r\n          <button class=\"btn-gray\" (click)=\"reset()\">{{ 'GLOBAL.reset' | translate }}</button>\r\n          <button class=\"btn-coal ml-20\" (click)=\"applyAdvancedFilter()\">{{ 'GLOBAL.search' | translate }}</button>\r\n        </div>\r\n      </div>\r\n    </ng-template>\r\n    <div class=\"mt-20\">\r\n      <div class=\"justify-between align-end\">\r\n        <div></div>\r\n        <label class=\"checkbox-container mb-4\">\r\n          <input type=\"checkbox\" [(ngModel)]=\"canShowPercentage\" (change)=\"refresh()\">\r\n          <span class=\"checkmark\"></span>Show Percentage\r\n        </label>\r\n      </div>\r\n      <div class=\"align-center bg-white w-100 border-gray tb-align-center-unset tb-flex-col\">\r\n        <div class=\"align-center border-end flex-grow-1 no-validation\">\r\n          <ng-container>\r\n            <div class=\"align-center w-100 px-10 py-12\">\r\n              <span class=\"search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4\"> </span>\r\n              <input placeholder=\"Search by User\" (keydown)=\"onSearch($event)\" (input)=\"isEmptyInput($event)\"\r\n                autocomplete=\"off\" name=\"search\" [(ngModel)]=\"searchTerm\" class=\"border-0 outline-0 w-100\">\r\n            </div>\r\n            <small class=\"text-muted text-nowrap ph-d-none pr-6\">({{ 'LEADS.lead-search-prompt' | translate }})</small>\r\n          </ng-container>\r\n          <div *ngIf=\"canExportAllUsers || canExportReportees\" class=\"bg-accent-green text-white border-end\"\r\n            [ngClass]=\"{ 'pe-none': currentView === 'graph' && !isGraphExportEnabled() }\">\r\n            <div class=\"px-20 py-5 h-100 align-center cursor-pointer flex-col\"\r\n               *ngIf=\"currentView === 'table'\" (click)=\"exportLeadReport()\">\r\n              <div>{{ 'REPORTS.export' | translate }}</div>\r\n              <div class=\"mt-2 fw-300 text-xs\">(tabular)</div>\r\n            </div>\r\n            <div class=\"px-20 py-5 h-100 align-center cursor-pointer flex-col\"\r\n               *ngIf=\"currentView === 'graph'\" (click)=\"!isExporting && exportGraphAsPDF()\" [class.pe-none]=\"isExporting\">\r\n              <ng-container *ngIf=\"!isExporting; else buttonDots\">\r\n                <div>{{ 'REPORTS.export' | translate }}</div>\r\n                <div class=\"mt-2 fw-300 text-xs\">(visual)</div>\r\n              </ng-container>\r\n              <ng-template #buttonDots>\r\n                <div class=\"container flex-center py-12\">\r\n                  <ng-container *ngFor=\"let dot of [1,2,3]\">\r\n                    <div class=\"dot-falling dot-white\"></div>\r\n                  </ng-container>\r\n                </div>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n          <div class=\"bg-coal text-white px-20 py-12 h-100 align-center cursor-pointer border-end ph-d-none\"\r\n            (click)=\"toggleView()\">\r\n            <span class=\"text-nowrap ic-cube ic-white ic-large m-2\" *ngIf=\"currentView === 'graph'\"></span>\r\n           <span class=\"text-nowrap ic-chart-pie ic-white ic-large m-2\" *ngIf=\"currentView === 'table'\"></span>\r\n\r\n          </div>\r\n        </div>\r\n        <div class=\"d-flex tb-br-top\">\r\n          <div class=\"px-10 align-center cursor-pointer border-end tb-flex-grow-1\"\r\n            (click)=\"openAdvFiltersModal(AdvancedFilters)\">\r\n            <div class=\"icon ic-filter-solid ic-xxs ic-black mr-10\"></div>\r\n            <span class=\"fw-600 ph-d-none\">{{'PROPERTY.advanced-filters' | translate}}</span>\r\n          </div>\r\n          <div class=\"align-center position-relative cursor-pointer d-flex border-end\">\r\n            <span class=\"position-absolute left-15 z-index-2 fw-600 text-sm\"><span class=\"ph-d-none\">Manage\r\n              </span>Columns</span>\r\n            <div class=\"show-hide-gray w-140 ph-w-110px\">\r\n              <ng-select [virtualScroll]=\"true\" class=\"bg-white\" [items]=\"columns\" [multiple]=\"true\"\r\n                [searchable]=\"false\" ResizableDropdown [closeOnSelect]=\"false\" [ngModel]=\"defaultColumns\"\r\n                (change)=\"onColumnsSelected($event)\">\r\n                <ng-template ng-option-tmp let-item=\"item\" let-item$=\"item$\" let-index=\"index\">\r\n                  <div class=\"checkbox-container\"><input type=\"checkbox\" id=\"item-{{index}}\"\r\n                      data-automate-id=\"item-{{index}}\" [checked]=\"item$.selected\"><span\r\n                      class=\"checkmark\"></span>{{item.label}}</div>\r\n                </ng-template>\r\n              </ng-select>\r\n            </div>\r\n          </div>\r\n          <div class=\"bg-coal text-white px-10 py-12 ip-w-30px  align-center cursor-pointer\"\r\n            (click)=\"onSetColumnDefault()\">\r\n            <span class=\"ip-d-none\">{{ 'GLOBAL.default' | translate }}</span> <span\r\n              class=\"ic-refresh d-none ip-d-block\"></span>\r\n          </div>\r\n          <div class=\"show-dropdown-white align-center position-relative\">\r\n            <span class=\"fw-600 position-absolute left-5 z-index-2\"><span class=\"tb-d-none\">{{ 'GLOBAL.show' |\r\n                translate\r\n                }}</span> {{ 'GLOBAL.entries' |\r\n              translate }}</span>\r\n            <ng-select [virtualScroll]=\"true\" [placeholder]=\"pageSize\" bindValue=\"id\" class=\"w-150 tb-w-120px\"\r\n              ResizableDropdown [(ngModel)]=\"selectedPageSize\" (change)=\"assignCount()\" [searchable]=\"false\">\r\n              <ng-option name=\"showEntriesSize\" *ngFor=\"let pageSize of showEntriesSize\" [value]=\"pageSize\">\r\n                {{pageSize}}</ng-option>\r\n            </ng-select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"bg-white px-4 py-12 tb-w-100-34\" [ngClass]=\"showLeftNav ? 'w-100-190' : 'w-100-90'\">\r\n        <ng-container *ngIf=\"showFilters\">\r\n          <div class=\"bg-secondary flex-between\">\r\n            <drag-scroll class=\"br-4 overflow-auto d-flex scroll-hide w-100\">\r\n              <div class=\"d-flex\" *ngFor=\"let filter of appliedFilter | keyvalue\">\r\n                <div class=\"px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap\"\r\n                  *ngFor=\"let value of getArrayOfFilters(filter.key, filter.value)\">\r\n                  {{reportFiltersKeyLabel[filter.key] || filter.key}}:\r\n                  {{ filter.key === 'users' ? getUserName(value) :\r\n                  value}}\r\n                  <span class=\"icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4\"\r\n                    (click)=\"onRemoveFilter(filter.key, value)\"></span>\r\n                </div>\r\n              </div>\r\n            </drag-scroll>\r\n            <div class=\"px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer\"\r\n              (click)=\"reset()\">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}\r\n            </div>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n      <ng-template #statusData>\r\n        <div [style.display]=\"currentView === 'table' ? 'block' : 'none'\">\r\n          <div class=\"reports pinned-grid\">\r\n            <ag-grid-angular #agGrid class=\"ag-theme-alpine\" [pagination]=\"true\" [paginationPageSize]=\"pageSize + 1\"\r\n              [gridOptions]=\"gridOptions\" [rowData]=\"rowData\" [suppressPaginationPanel]=\"true\"\r\n              [alwaysShowHorizontalScroll]=\"true\" [alwaysShowVerticalScroll]=\"true\" (gridReady)=\"onGridReady($event)\"\r\n              (cellClickedEvent)=\"onCellClicked($event)\">\r\n            </ag-grid-angular>\r\n          </div>\r\n          <div class=\"my-20 flex-end\">\r\n            <div class=\"mr-10\" *ngIf=\"userTotalCount\">{{ 'GLOBAL.showing' | translate }} {{(currOffset * pageSize) + 1}}\r\n              {{ 'GLOBAL.to-small' | translate }} {{rowData?.length > 1 ? currOffset*pageSize + rowData?.length - 1 :\r\n              currOffset*pageSize + rowData?.length}}\r\n              {{ 'GLOBAL.of-small' | translate }} {{userTotalCount}} {{ 'GLOBAL.entries-small' | translate }}</div>\r\n            <pagination [offset]=currOffset [limit]=\"1\" [range]=\"1\" [size]='getPages(userTotalCount,pageSize)'\r\n              (pageChange)=\"onPageChange($event)\">\r\n            </pagination>\r\n          </div>\r\n        </div>\r\n        <div [style.display]=\"currentView === 'graph' ? 'block' : 'none'\">\r\n          <report-graph  [payload]=\"filtersPayload\"#reportsGraph [rowData]=\"rowData\" [gridOptions]=\"gridOptions\" xAxisData=\"firstName lastName\"\r\n            [filteredColumnDefsCache]=\"filteredColumnDefsCache\" reportType=\"status-report\" (exportStarted)=\"isExporting = true\" (exportFinished)=\"isExporting = false\"></report-graph>\r\n        </div>\r\n      </ng-template>\r\n      <ng-container\r\n        *ngIf=\"!rowData?.length || (isCustomStatusEnabled ? isCustomStatusReportLoading : isStatusReportLoading); else statusData\">\r\n        <div *ngIf=\"(isCustomStatusEnabled ? isCustomStatusReportLoading : isStatusReportLoading)\"\r\n          class=\"flex-center w-100 h-100-337 min-h-250\">\r\n          <ng-container [ngTemplateOutlet]=\"reportsLoader\"></ng-container>\r\n        </div>\r\n        <div *ngIf=\"(isCustomStatusEnabled ? !isCustomStatusReportLoading : !isStatusReportLoading) && !rowData?.length\"\r\n          class=\"flex-center-col h-100-337\">\r\n          <img src=\"assets/images/layered-cards.svg\" alt=\"No Data Found\">\r\n          <div class=\"header-3 fw-600 text-center\">{{'PROFILE.no-data-found' | translate }}</div>\r\n        </div>\r\n      </ng-container>\r\n    </div>\r\n  </div>\r\n</div>\r\n<ng-template #reportsLoader>\r\n  <div class=\"flex-center h-100\">\r\n    <application-loader></application-loader>\r\n  </div>\r\n</ng-template>\r\n\r\n"]}, "metadata": {}, "sourceType": "module"}