{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.timeoutWith = void 0;\n\nvar async_1 = require(\"../scheduler/async\");\n\nvar isDate_1 = require(\"../util/isDate\");\n\nvar timeout_1 = require(\"./timeout\");\n\nfunction timeoutWith(due, withObservable, scheduler) {\n  var first;\n  var each;\n\n  var _with;\n\n  scheduler = scheduler !== null && scheduler !== void 0 ? scheduler : async_1.async;\n\n  if (isDate_1.isValidDate(due)) {\n    first = due;\n  } else if (typeof due === 'number') {\n    each = due;\n  }\n\n  if (withObservable) {\n    _with = function () {\n      return withObservable;\n    };\n  } else {\n    throw new TypeError('No observable provided to switch to');\n  }\n\n  if (first == null && each == null) {\n    throw new TypeError('No timeout provided.');\n  }\n\n  return timeout_1.timeout({\n    first: first,\n    each: each,\n    scheduler: scheduler,\n    with: _with\n  });\n}\n\nexports.timeoutWith = timeoutWith;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "timeoutWith", "async_1", "require", "isDate_1", "timeout_1", "due", "withObservable", "scheduler", "first", "each", "_with", "async", "isValidDate", "TypeError", "timeout", "with"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/rxjs/dist/cjs/internal/operators/timeoutWith.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.timeoutWith = void 0;\nvar async_1 = require(\"../scheduler/async\");\nvar isDate_1 = require(\"../util/isDate\");\nvar timeout_1 = require(\"./timeout\");\nfunction timeoutWith(due, withObservable, scheduler) {\n    var first;\n    var each;\n    var _with;\n    scheduler = scheduler !== null && scheduler !== void 0 ? scheduler : async_1.async;\n    if (isDate_1.isValidDate(due)) {\n        first = due;\n    }\n    else if (typeof due === 'number') {\n        each = due;\n    }\n    if (withObservable) {\n        _with = function () { return withObservable; };\n    }\n    else {\n        throw new TypeError('No observable provided to switch to');\n    }\n    if (first == null && each == null) {\n        throw new TypeError('No timeout provided.');\n    }\n    return timeout_1.timeout({\n        first: first,\n        each: each,\n        scheduler: scheduler,\n        with: _with,\n    });\n}\nexports.timeoutWith = timeoutWith;\n"], "mappings": "AAAA;;AACAA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;EAAEC,KAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACE,WAAR,GAAsB,KAAK,CAA3B;;AACA,IAAIC,OAAO,GAAGC,OAAO,CAAC,oBAAD,CAArB;;AACA,IAAIC,QAAQ,GAAGD,OAAO,CAAC,gBAAD,CAAtB;;AACA,IAAIE,SAAS,GAAGF,OAAO,CAAC,WAAD,CAAvB;;AACA,SAASF,WAAT,CAAqBK,GAArB,EAA0BC,cAA1B,EAA0CC,SAA1C,EAAqD;EACjD,IAAIC,KAAJ;EACA,IAAIC,IAAJ;;EACA,IAAIC,KAAJ;;EACAH,SAAS,GAAGA,SAAS,KAAK,IAAd,IAAsBA,SAAS,KAAK,KAAK,CAAzC,GAA6CA,SAA7C,GAAyDN,OAAO,CAACU,KAA7E;;EACA,IAAIR,QAAQ,CAACS,WAAT,CAAqBP,GAArB,CAAJ,EAA+B;IAC3BG,KAAK,GAAGH,GAAR;EACH,CAFD,MAGK,IAAI,OAAOA,GAAP,KAAe,QAAnB,EAA6B;IAC9BI,IAAI,GAAGJ,GAAP;EACH;;EACD,IAAIC,cAAJ,EAAoB;IAChBI,KAAK,GAAG,YAAY;MAAE,OAAOJ,cAAP;IAAwB,CAA9C;EACH,CAFD,MAGK;IACD,MAAM,IAAIO,SAAJ,CAAc,qCAAd,CAAN;EACH;;EACD,IAAIL,KAAK,IAAI,IAAT,IAAiBC,IAAI,IAAI,IAA7B,EAAmC;IAC/B,MAAM,IAAII,SAAJ,CAAc,sBAAd,CAAN;EACH;;EACD,OAAOT,SAAS,CAACU,OAAV,CAAkB;IACrBN,KAAK,EAAEA,KADc;IAErBC,IAAI,EAAEA,IAFe;IAGrBF,SAAS,EAAEA,SAHU;IAIrBQ,IAAI,EAAEL;EAJe,CAAlB,CAAP;AAMH;;AACDZ,OAAO,CAACE,WAAR,GAAsBA,WAAtB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}