<div class="max-h-100-88 scrollbar position-relative">
    <div class="lead-adv-filter pl-30 pb-30 bg-white brbl-15 brbr-15">
        <div class="d-flex flex-wrap ng-select-sm input-sm">
            <div class="flex-column w-33 ip-w-50 ph-w-100">
                <div class="field-label">{{projectSubType ? projectSubType : 'Unit'}} Area</div>
                <div class="w-100 align-center">
                    <div class="w-60pr no-input-validation input-sm">
                        <form-errors-wrapper>
                            <div class="w-100 d-flex">
                                <div class="w-50">
                                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                        (input)="validateArea()" [(ngModel)]="appliedFilter.MinArea" id="inpMinArea"
                                        data-automate-id="inpMinArea" placeholder="ex. 123">
                                </div>
                                <h6 class="text-sm text-mud align-center m-4">To</h6>
                                <div class="w-50">
                                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                        [(ngModel)]="appliedFilter.MaxArea" (input)="validateArea()" id="inpMaxArea"
                                        data-automate-id="inpMaxArea" placeholder="ex. 123">
                                </div>
                            </div>
                        </form-errors-wrapper>
                    </div>
                    <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                        *ngIf="appliedFilter.MinArea && appliedFilter.MaxArea && !areaValidations ">
                        {{'PROPERTY.area-validation' | translate}}</div>
                    <div class="w-40pr ml-8 mr-20">
                        <form-errors-wrapper>
                            <ng-select class="align-center" [(ngModel)]="appliedFilter.AreaUnitId"
                                [virtualScroll]="true" placeholder="ex. sq. feet." [items]="areaUnit" bindValue="id"
                                bindLabel="unit"></ng-select>
                        </form-errors-wrapper>
                    </div>
                </div>
            </div>
            <div class="flex-column w-33 ip-w-50 ph-w-100" *ngIf="projectSubType !== 'Plot'">
                <div class="field-label">Carpet Area</div>
                <div class="w-100 align-center">
                    <div class="w-60pr no-input-validation input-sm">
                        <form-errors-wrapper>
                            <div class="w-100 d-flex">
                                <div class="w-50">
                                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                        (input)="validateCarpetArea()" [(ngModel)]="appliedFilter.MinCarpetArea"
                                        id="inpMinCarpetArea" min="0" data-automate-id="inpMinCarpetArea"
                                        placeholder="ex. 123">
                                </div>
                                <h6 class="text-sm text-mud align-center m-4">To</h6>
                                <div class="w-50">
                                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                        [(ngModel)]="appliedFilter.MaxCarpetArea" (input)="validateCarpetArea()"
                                        id="inpMaxCarpetArea" min="0" data-automate-id="inpMaxCarpetArea"
                                        placeholder="ex. 123">
                                </div>
                            </div>
                        </form-errors-wrapper>
                    </div>
                    <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                        *ngIf="appliedFilter.MinCarpetArea && appliedFilter.MaxCarpetArea && !carpetAreaValidations ">
                        {{'PROPERTY.area-validation' | translate}}</div>
                    <div class="w-40pr ml-8 mr-20">
                        <form-errors-wrapper>
                            <ng-select class="align-center" [(ngModel)]="appliedFilter.CarpetAreaUnitId"
                                [virtualScroll]="true" placeholder="ex. sq. feet." [items]="areaUnit" bindValue="id"
                                bindLabel="unit"></ng-select>
                        </form-errors-wrapper>
                    </div>
                </div>
            </div>
            <div class="flex-column w-33 ip-w-50 ph-w-100" *ngIf="projectSubType !== 'Plot'">
                <div class="field-label">Built-up Area</div>
                <div class="w-100 align-center">
                    <div class="w-60pr no-input-validation input-sm">
                        <form-errors-wrapper>
                            <div class="w-100 d-flex">
                                <div class="w-50">
                                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                        (input)="validateBuiltupArea()" [(ngModel)]="appliedFilter.MinBuiltupArea"
                                        id="inpMinBuiltupArea" min="0" data-automate-id="inpMinBuiltupArea"
                                        placeholder="ex. 123">
                                </div>
                                <h6 class="text-sm text-mud align-center m-4">To</h6>
                                <div class="w-50">
                                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                        [(ngModel)]="appliedFilter.MaxBuiltupArea" (input)="validateBuiltupArea()"
                                        id="inpMaxBuiltupArea" min="0" data-automate-id="inpMaxBuiltupArea"
                                        placeholder="ex. 123">
                                </div>
                            </div>
                        </form-errors-wrapper>
                    </div>
                    <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                        *ngIf="appliedFilter.MinBuiltupArea && appliedFilter.MaxBuiltupArea && !builtupAreaValidations ">
                        {{'PROPERTY.area-validation' | translate}}</div>
                    <div class="w-40pr ml-8 mr-20">
                        <form-errors-wrapper>
                            <ng-select class="align-center" [(ngModel)]="appliedFilter.BuiltupAreaUnitId"
                                [virtualScroll]="true" placeholder="ex. sq. feet." [items]="areaUnit" bindValue="id"
                                bindLabel="unit"></ng-select>
                        </form-errors-wrapper>
                    </div>
                </div>
            </div>
            <div class="flex-column w-33 ip-w-50 ph-w-100" *ngIf="projectSubType !== 'Plot'">
                <div class="field-label">Super Built-up Area</div>
                <div class="w-100 align-center">
                    <div class="w-60pr no-input-validation input-sm">
                        <form-errors-wrapper>
                            <div class="w-100 d-flex">
                                <div class="w-50">
                                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                        (input)="validateSuperBuiltupArea()"
                                        [(ngModel)]="appliedFilter.MinSuperBuiltupArea" id="inpMinSuperBuiltupArea"
                                        min="0" data-automate-id="inpMinSuperBuiltupArea" placeholder="ex. 123">
                                </div>
                                <h6 class="text-sm text-mud align-center m-4">To</h6>
                                <div class="w-50">
                                    <input type="number" (keydown)="onlyNumbersWithDecimal($event,$event.target.value)"
                                        [(ngModel)]="appliedFilter.MaxSuperBuiltupArea"
                                        (input)="validateSuperBuiltupArea()" id="inpMaxSuperBuiltupArea" min="0"
                                        data-automate-id="inpMaxSuperBuiltupArea" placeholder="ex. 123">
                                </div>
                            </div>
                        </form-errors-wrapper>
                    </div>
                    <div class="text-xs mt-50 text-red fw-semi-bold position-absolute"
                        *ngIf="appliedFilter.MinSuperBuiltupArea && appliedFilter.MaxSuperBuiltupArea && !superBuiltupAreaValidations">
                        {{'PROPERTY.area-validation' | translate}}</div>
                    <div class="w-40pr ml-8 mr-20">
                        <form-errors-wrapper>
                            <ng-select class="align-center" [(ngModel)]="appliedFilter.SuperBuiltupAreaUnitId"
                                [virtualScroll]="true" placeholder="ex. sq. feet." [items]="areaUnit" bindValue="id"
                                bindLabel="unit"></ng-select>
                        </form-errors-wrapper>
                    </div>
                </div>
            </div>
            <div class="w-33 ip-w-50 ph-w-100 field-rupees-tag">
                <div class="mr-20">
                    <div class="field-label">Maintenance Cost</div>
                    <div class="position-relative lead adv-dropdown">
                        <form-errors-wrapper>
                            <input [(ngModel)]="appliedFilter.MaintenanceCost" type="number" id="inpPropAmt"
                                data-automate-id="inpPropAmt" placeholder="ex. 200000" autocomplete="off">
                            <div class="no-validation">
                                <ng-container *ngIf="currencyList?.length > 1; else showCurrencySymbol">
                                    <ng-select class="mt-2 ml-2 position-absolute top-0 manage-dropdown"
                                        [(ngModel)]="appliedFilter.Currency" [virtualScroll]="true"
                                        placeholder="ex. INR" [items]="currencyList" bindValue="symbol"
                                        bindLabel="symbol">
                                    </ng-select>
                                </ng-container>
                                <ng-template #showCurrencySymbol>
                                    <h5 class="rupees px-12 py-4 fw-600 m-4">{{defaultCurrency}}</h5>
                                </ng-template>
                            </div>
                        </form-errors-wrapper>
                    </div>
                </div>
            </div>
            <div class="w-33 ip-w-50 ph-w-100 field-rupees-tag">
                <div class="mr-20">
                    <div class="field-label">Price Per {{projectSubType ? projectSubType : 'Unit'}} Area</div>
                    <div class="position-relative lead adv-dropdown">
                        <form-errors-wrapper>
                            <input [(ngModel)]="appliedFilter.PricePerUnit" type="number" id="inpPropAmt"
                                data-automate-id="inpPropAmt" placeholder="ex. 200000" autocomplete="off">
                            <div class="no-validation">
                                <ng-container *ngIf="currencyList?.length > 1; else showCurrencySymbol">
                                    <ng-select class="mt-2 ml-2 position-absolute top-0 manage-dropdown"
                                        [(ngModel)]="appliedFilter.Currency" [virtualScroll]="true"
                                        placeholder="ex. INR" [items]="currencyList" bindValue="symbol"
                                        bindLabel="symbol">
                                    </ng-select>
                                </ng-container>
                            </div>
                        </form-errors-wrapper>
                    </div>
                </div>
            </div>
            <div class="w-33 ip-w-50 ph-w-100 field-rupees-tag">
                <div class="mr-20">
                    <div class="field-label">Total Price</div>
                    <div class="position-relative lead adv-dropdown">
                        <form-errors-wrapper>
                            <input [(ngModel)]="appliedFilter.TotalPrice" type="number" id="inpPropAmt"
                                data-automate-id="inpPropAmt" placeholder="ex. 200000" autocomplete="off">
                            <div class="no-validation">
                                <ng-container *ngIf="currencyList?.length > 1; else showCurrencySymbol">
                                    <ng-select class="mt-2 ml-2 position-absolute top-0 manage-dropdown"
                                        [(ngModel)]="appliedFilter.Currency" [virtualScroll]="true"
                                        placeholder="ex. INR" [items]="currencyList" bindValue="symbol"
                                        bindLabel="symbol">
                                    </ng-select>
                                </ng-container>
                            </div>
                        </form-errors-wrapper>
                    </div>
                </div>
            </div>
            <div class="w-33 tb-w-33 ip-w-50 ph-w-100">
                <div class="mr-20">
                    <div class="field-label">{{projectSubType ? projectSubType : 'Unit'}} Type</div>
                    <form-errors-wrapper>
                        <ng-select [(ngModel)]="appliedFilter.UnitType" [virtualScroll]="true" [multiple]="true"
                            [items]="allUnitTypes" [closeOnSelect]="false" bindLabel="displayName" bindValue="id"
                            name="projectsList" placeholder="ex. Residential"
                            (change)="changeUnitType(appliedFilter.UnitType)">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark" autocomplete="off"></span>{{item.displayName}}
                                </div>
                            </ng-template>
                        </ng-select>
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-33 tb-w-33 ip-w-50 ph-w-100">
                <div class="mr-20">
                    <div class="field-label">{{projectSubType ? projectSubType : 'Unit'}} Sub-Type</div>
                    <form-errors-wrapper>
                        <ng-select [(ngModel)]="appliedFilter.UnitSubType" [virtualScroll]="true" [items]="unitSubTypes"
                            [closeOnSelect]="false" [multiple]="true" bindLabel="displayName" bindValue="id"
                            name="projectsList" placeholder="ex. Plot">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark" autocomplete="off"></span>{{item.displayName}}
                                </div>
                            </ng-template>
                        </ng-select>
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-33 tb-w-33 ip-w-50 ph-w-100" *ngIf="projectSubType !== 'Plot'">
                <div class="mr-20">
                    <div class="field-label">{{'PROPERTY.bhk' | translate}}</div>
                    <form-errors-wrapper>
                        <ng-select [(ngModel)]="appliedFilter.BHKs" [virtualScroll]="true" [items]="bhkNoList"
                            [closeOnSelect]="false" [multiple]="true" placeholder="ex. 1 BHK">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                        data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                        class="checkmark" autocomplete="off"></span>{{getBHKDisplayString(item)}}
                                </div>
                            </ng-template>
                        </ng-select>
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-33 tb-w-33 ip-w-50 ph-w-100" *ngIf="projectSubType !== 'Plot'">
                <div class="field-label">{{'PROPERTY.bhk' | translate}} {{'LABEL.type'| translate}}</div>
                <div class="mr-20">
                    <ng-select [virtualScroll]="true" [items]="bhkType" [multiple]="true" [closeOnSelect]="false"
                        placeholder="{{'GLOBAL.select' | translate}}" [(ngModel)]="appliedFilter.BHKTypes">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span>{{item}}
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="w-33 tb-w-33 ip-w-50 ph-w-100" *ngIf="projectSubType !== 'Plot'">
                <div class="field-label">Furnish Status</div>
                <div class="mr-20">
                    <ng-select [virtualScroll]="true" [items]="furnishingStatusList" [multiple]="true"
                        [closeOnSelect]="false" placeholder="{{'GLOBAL.select' | translate}}"
                        [(ngModel)]="appliedFilter.FurnishingStatuses" bindLabel="value" bindValue="dispName">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span>{{item.dispName}}
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="w-33 tb-w-33 ip-w-50 ph-w-100">
                <div class="field-label">Facing</div>
                <div class="mr-20">
                    <ng-select [virtualScroll]="true" [items]="facingList" [multiple]="true" [closeOnSelect]="false"
                        placeholder="{{'GLOBAL.select' | translate}}" bindValue="displayName" bindLabel="displayName"
                        [(ngModel)]="appliedFilter.Facings">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span>{{item.displayName}}
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="w-33 tb-w-33 ip-w-50 ph-w-100">
                <div class="field-label">No.of Bathrooms</div>
                <div class="mr-20">
                    <ng-select [virtualScroll]="true" [items]="numbers" [multiple]="true" [closeOnSelect]="false"
                        placeholder="{{'GLOBAL.select' | translate}}" bindValue="value" bindLabel="value"
                        [(ngModel)]="appliedFilter.NoOfBathrooms">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span>{{item.display}}
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="w-33 tb-w-33 ip-w-50 ph-w-100" *ngIf="projectSubType !== 'Plot'">
                <div class="field-label">No.of Living rooms</div>
                <div class="mr-20">
                    <ng-select [virtualScroll]="true" [items]="numbers" [multiple]="true" [closeOnSelect]="false"
                        placeholder="{{'GLOBAL.select' | translate}}" bindValue="value" bindLabel="value"
                        [(ngModel)]="appliedFilter.NoOfLivingrooms">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span>{{item.display}}
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="w-33 tb-w-33 ip-w-50 ph-w-100" *ngIf="projectSubType !== 'Plot'">
                <div class="field-label">No.of Bedrooms</div>
                <div class="mr-20">
                    <ng-select [virtualScroll]="true" [items]="numbers" [multiple]="true" [closeOnSelect]="false"
                        placeholder="{{'GLOBAL.select' | translate}}" bindValue="value" bindLabel="value"
                        [(ngModel)]="appliedFilter.NoOfBedrooms">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span>{{item.display}}
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="w-33 tb-w-33 ip-w-50 ph-w-100"  *ngIf="projectSubType !== 'Plot'">
                <div class="field-label">No.of Utilites</div>
                <div class="mr-20">
                    <ng-select [virtualScroll]="true" [items]="numbers" [multiple]="true" [closeOnSelect]="false"
                        placeholder="{{'GLOBAL.select' | translate}}" bindValue="value" bindLabel="value"
                        [(ngModel)]="appliedFilter.NoOfUtilites">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span>{{item.display}}
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="w-33 tb-w-33 ip-w-50 ph-w-100"  *ngIf="projectSubType !== 'Plot'">
                <div class="field-label">No. of Kitchens</div>
                <div class="mr-20">
                    <ng-select [virtualScroll]="true" [items]="numbers" [multiple]="true" [closeOnSelect]="false"
                        placeholder="{{'GLOBAL.select' | translate}}" bindValue="value" bindLabel="value"
                        [(ngModel)]="appliedFilter.NoOfKitchens">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span>{{item.display}}
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="w-33 tb-w-33 ip-w-50 ph-w-100"  *ngIf="projectSubType !== 'Plot'">
                <div class="field-label">No. of Balconies</div>
                <div class="mr-20">
                    <ng-select [virtualScroll]="true" [items]="numbers" [multiple]="true" [closeOnSelect]="false"
                        placeholder="{{'GLOBAL.select' | translate}}" bindValue="value" bindLabel="value"
                        [(ngModel)]="appliedFilter.NoOfBalconies">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span>{{item.display}}
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
            <div class="w-33 tb-w-33 ip-w-50 ph-w-100" *ngIf="projectSubType !== 'Plot'">
                <div class="field-label">No. of Maximum Occupants</div>
                <div class="mr-20">
                    <ng-select [virtualScroll]="true" [items]="numbers" [multiple]="true" [closeOnSelect]="false"
                        placeholder="{{'GLOBAL.select' | translate}}" bindValue="value" bindLabel="value"
                        [(ngModel)]="appliedFilter.NoOfMaximumOccupants">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                    class="checkmark"></span>{{item.display}}
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer bg-white justify-end position-sticky bottom-0 z-index-1021">
    <u class="mr-20 fw-semi-bold text-mud cursor-pointer" (click)="modalService.hide()">Cancel</u>
    <div class="btn-gray mr-20" (click)="reset()">Reset</div>
    <div class="btn-coal" (click)="applyAdvancedFilter()">Search</div>
</div>