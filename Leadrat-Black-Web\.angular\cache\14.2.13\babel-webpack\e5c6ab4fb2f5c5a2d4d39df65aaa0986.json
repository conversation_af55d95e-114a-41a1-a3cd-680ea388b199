{"ast": null, "code": "/**\n * A specialized version of `_.reduceRight` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @param {boolean} [initAccum] Specify using the last element of `array` as\n *  the initial value.\n * @returns {*} Returns the accumulated value.\n */\nfunction arrayReduceRight(array, iteratee, accumulator, initAccum) {\n  var length = array == null ? 0 : array.length;\n\n  if (initAccum && length) {\n    accumulator = array[--length];\n  }\n\n  while (length--) {\n    accumulator = iteratee(accumulator, array[length], length, array);\n  }\n\n  return accumulator;\n}\n\nexport default arrayReduceRight;", "map": {"version": 3, "names": ["arrayReduceRight", "array", "iteratee", "accumulator", "initAccum", "length"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/lodash-es/_arrayReduceRight.js"], "sourcesContent": ["/**\n * A specialized version of `_.reduceRight` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @param {boolean} [initAccum] Specify using the last element of `array` as\n *  the initial value.\n * @returns {*} Returns the accumulated value.\n */\nfunction arrayReduceRight(array, iteratee, accumulator, initAccum) {\n  var length = array == null ? 0 : array.length;\n  if (initAccum && length) {\n    accumulator = array[--length];\n  }\n  while (length--) {\n    accumulator = iteratee(accumulator, array[length], length, array);\n  }\n  return accumulator;\n}\n\nexport default arrayReduceRight;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,gBAAT,CAA0BC,KAA1B,EAAiCC,QAAjC,EAA2CC,WAA3C,EAAwDC,SAAxD,EAAmE;EACjE,IAAIC,MAAM,GAAGJ,KAAK,IAAI,IAAT,GAAgB,CAAhB,GAAoBA,KAAK,CAACI,MAAvC;;EACA,IAAID,SAAS,IAAIC,MAAjB,EAAyB;IACvBF,WAAW,GAAGF,KAAK,CAAC,EAAEI,MAAH,CAAnB;EACD;;EACD,OAAOA,MAAM,EAAb,EAAiB;IACfF,WAAW,GAAGD,QAAQ,CAACC,WAAD,EAAcF,KAAK,CAACI,MAAD,CAAnB,EAA6BA,MAA7B,EAAqCJ,KAArC,CAAtB;EACD;;EACD,OAAOE,WAAP;AACD;;AAED,eAAeH,gBAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}