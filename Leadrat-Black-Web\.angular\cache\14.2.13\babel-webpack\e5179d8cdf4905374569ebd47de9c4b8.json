{"ast": null, "code": "export const observable = (() => typeof Symbol === 'function' && Symbol.observable || '@@observable')();", "map": {"version": 3, "names": ["observable", "Symbol"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/rxjs/dist/esm/internal/symbol/observable.js"], "sourcesContent": ["export const observable = (() => (typeof Symbol === 'function' && Symbol.observable) || '@@observable')();\n"], "mappings": "AAAA,OAAO,MAAMA,UAAU,GAAG,CAAC,MAAO,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACD,UAAxC,IAAuD,cAA9D,GAAnB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}