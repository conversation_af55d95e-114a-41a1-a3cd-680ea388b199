<form [formGroup]="updateForm" autocomplete="off" class="h-100vh">
    <div class="bg-coal w-100 px-16 py-12 text-white flex-between">
        <h3 class="fw-semi-bold">Assignment</h3>
        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="closeModal()"></div>
    </div>
    <div class="p-16 scrollbar h-100-108">
        <img src="../../../../assets/images/integration/facebook.svg" alt="img" />
        <div class="bg-light-pearl mt-20 br-6 flex-between">
            <div class="flex-column pt-10 pl-10 pb-20">
                <div class="fw-semi-bold fv-sm-caps">
                    Account Name </div>
                <div class="fw-700 text-large">{{selectedAccountName}}</div>
                <div>
                    <div class="fw-semi-bold fv-sm-caps mt-10">{{isAdAccount ? 'Ad Name' : 'Lead Form'}}</div>
                </div>
                <div class="fw-700 text-large">{{selectedAdName}}</div>
            </div>
            <div><img src="../../../../assets/images/profile.svg" alt="" class="mt-8" /></div>
        </div>
        <div class="field-label">Country Code</div>
        <div class="form-group">
            <ngx-mat-intl-tel-input #contactNoInput [preferredCountries]="preferredCountries" [enablePlaceholder]="true"
                [enableSearch]="true" formControlName="countryCode" class="no-validation contactNoInput no-number-input"
                placeholder="9133XXXXXX">
            </ngx-mat-intl-tel-input>
        </div>
        <div class="field-label"> {{'PROJECTS.project' | translate}}</div>
        <ng-select *ngIf="!projectListIsLoading else fieldLoader" [virtualScroll]="true" [items]="allProjectList"
            class="bg-white" bindLabel="name" ResizableDropdown bindValue="id" placeholder="ex. ABC project"
            formControlName="project"></ng-select>
        <div class="field-label">{{'LABEL.property' | translate}}</div>
        <ng-select [virtualScroll]="true" [items]="propertyList" [ngClass]="{'blinking pe-none': propertyListIsLoading}"
            bindLabel="title" bindValue="title" class="bg-white" ResizableDropdown placeholder="Select Property"
            formControlName="property"></ng-select>
        <div class="field-label"> {{'LOCATION.location' | translate}}</div>
        <ng-select *ngIf="!allLocationsIsLoading else fieldLoader" [virtualScroll]="true" [items]="placesList"
            class="bg-white" bindLabel="location" ResizableDropdown bindValue="id" placeholder="ex. ABC location"
            formControlName="location"></ng-select>
        <div class="field-label fw-semi-bold">{{'REPORTS.agency' | translate}}
        </div>
        <ng-select [virtualScroll]="true" [items]="agencyNameList" [ngClass]="{'blinking pe-none': agencyListIsLoading}"
            class="bg-white" ResizableDropdown placeholder="Select Agency" formControlName="agencyName"></ng-select>
        <div class="field-label fw-semi-bold">{{'INTEGRATION.campaign' | translate}}
        </div>
        <ng-select [virtualScroll]="true" [items]="campaignList" [ngClass]="{'blinking pe-none': campaignListIsLoading}"
            class="bg-white" ResizableDropdown placeholder="Select Campaign" formControlName="campaign"></ng-select>
        <div class="field-label fw-semi-bold">{{'INTEGRATION.channel-partner' | translate}}
        </div>
        <ng-select [virtualScroll]="true" [items]="channelPartnerList"
            [ngClass]="{'blinking pe-none': channelPartnerListIsLoading}" class="bg-white" ResizableDropdown
            placeholder="Select Channelpartner" formControlName="channelPartner"></ng-select>
    </div>
    <div class="flex-end mt-20">
        <button class="btn-gray mr-20" (click)="closeModal()">
            {{ 'BUTTONS.cancel' | translate }}</button>
        <button class="btn-coal" (click)="updateProjectAndLocation()">
            {{ 'BUTTONS.save' | translate }}</button>
    </div>
</form>
<ng-template #fieldLoader>
    <ng-select [virtualScroll]="true" class="pe-none blinking"></ng-select>
</ng-template>