{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Version, Injectable } from '@angular/core';\nimport { getApps } from 'firebase/app';\nimport { isSupported as isSupported$2 } from 'firebase/remote-config';\nimport { isSupported as isSupported$1 } from 'firebase/messaging';\nimport { isSupported } from 'firebase/analytics';\nimport { queueScheduler, asyncScheduler, Observable } from 'rxjs';\nimport { tap, observeOn, subscribeOn } from 'rxjs/operators';\nconst VERSION = new Version('7.6.1');\nconst isAnalyticsSupportedValueSymbol = '__angularfire_symbol__analyticsIsSupportedValue';\nconst isAnalyticsSupportedPromiseSymbol = '__angularfire_symbol__analyticsIsSupported';\nconst isRemoteConfigSupportedValueSymbol = '__angularfire_symbol__remoteConfigIsSupportedValue';\nconst isRemoteConfigSupportedPromiseSymbol = '__angularfire_symbol__remoteConfigIsSupported';\nconst isMessagingSupportedValueSymbol = '__angularfire_symbol__messagingIsSupportedValue';\nconst isMessagingSupportedPromiseSymbol = '__angularfire_symbol__messagingIsSupported';\nglobalThis[isAnalyticsSupportedPromiseSymbol] || (globalThis[isAnalyticsSupportedPromiseSymbol] = isSupported().then(it => globalThis[isAnalyticsSupportedValueSymbol] = it).catch(() => globalThis[isAnalyticsSupportedValueSymbol] = false));\nglobalThis[isMessagingSupportedPromiseSymbol] || (globalThis[isMessagingSupportedPromiseSymbol] = isSupported$1().then(it => globalThis[isMessagingSupportedValueSymbol] = it).catch(() => globalThis[isMessagingSupportedValueSymbol] = false));\nglobalThis[isRemoteConfigSupportedPromiseSymbol] || (globalThis[isRemoteConfigSupportedPromiseSymbol] = isSupported$2().then(it => globalThis[isRemoteConfigSupportedValueSymbol] = it).catch(() => globalThis[isRemoteConfigSupportedValueSymbol] = false));\n\nconst isSupportedError = module => `The APP_INITIALIZER that is \"making\" isSupported() sync for the sake of convenient DI has not resolved in this\ncontext. Rather than injecting ${module} in the constructor, first ensure that ${module} is supported by calling\n\\`await isSupported()\\`, then retrieve the instance from the injector manually \\`injector.get(${module})\\`.`;\n\nconst ɵisMessagingSupportedFactory = {\n  async: () => globalThis[isMessagingSupportedPromiseSymbol],\n  sync: () => {\n    const ret = globalThis[isMessagingSupportedValueSymbol];\n\n    if (ret === undefined) {\n      throw new Error(isSupportedError('Messaging'));\n    }\n\n    return ret;\n  }\n};\nconst ɵisRemoteConfigSupportedFactory = {\n  async: () => globalThis[isRemoteConfigSupportedPromiseSymbol],\n  sync: () => {\n    const ret = globalThis[isRemoteConfigSupportedValueSymbol];\n\n    if (ret === undefined) {\n      throw new Error(isSupportedError('RemoteConfig'));\n    }\n\n    return ret;\n  }\n};\nconst ɵisAnalyticsSupportedFactory = {\n  async: () => globalThis[isAnalyticsSupportedPromiseSymbol],\n  sync: () => {\n    const ret = globalThis[isAnalyticsSupportedValueSymbol];\n\n    if (ret === undefined) {\n      throw new Error(isSupportedError('Analytics'));\n    }\n\n    return ret;\n  }\n};\n\nfunction ɵgetDefaultInstanceOf(identifier, provided, defaultApp) {\n  if (provided) {\n    // Was provide* only called once? If so grab that\n    if (provided.length === 1) {\n      return provided[0];\n    }\n\n    const providedUsingDefaultApp = provided.filter(it => it.app === defaultApp); // Was provide* only called once, using the default app? If so use that\n\n    if (providedUsingDefaultApp.length === 1) {\n      return providedUsingDefaultApp[0];\n    }\n  } // Grab the default instance from the defaultApp\n\n\n  const defaultAppWithContainer = defaultApp;\n  const provider = defaultAppWithContainer.container.getProvider(identifier);\n  return provider.getImmediate({\n    optional: true\n  });\n}\n\nconst ɵgetAllInstancesOf = (identifier, app) => {\n  const apps = app ? [app] : getApps();\n  const instances = [];\n  apps.forEach(app => {\n    const provider = app.container.getProvider(identifier);\n    provider.instances.forEach(instance => {\n      if (!instances.includes(instance)) {\n        instances.push(instance);\n      }\n    });\n  });\n  return instances;\n};\n\nfunction noop() {}\n/**\n * Schedules tasks so that they are invoked inside the Zone that is passed in the constructor.\n */\n// tslint:disable-next-line:class-name\n\n\nclass ɵZoneScheduler {\n  constructor(zone, delegate = queueScheduler) {\n    this.zone = zone;\n    this.delegate = delegate;\n  }\n\n  now() {\n    return this.delegate.now();\n  }\n\n  schedule(work, delay, state) {\n    const targetZone = this.zone; // Wrap the specified work function to make sure that if nested scheduling takes place the\n    // work is executed in the correct zone\n\n    const workInZone = function (state) {\n      targetZone.runGuarded(() => {\n        work.apply(this, [state]);\n      });\n    }; // Scheduling itself needs to be run in zone to ensure setInterval calls for async scheduling are done\n    // inside the correct zone. This scheduler needs to schedule asynchronously always to ensure that\n    // firebase emissions are never synchronous. Specifying a delay causes issues with the queueScheduler delegate.\n\n\n    return this.delegate.schedule(workInZone, delay, state);\n  }\n\n}\n\nclass BlockUntilFirstOperator {\n  constructor(zone) {\n    this.zone = zone;\n    this.task = null;\n  }\n\n  call(subscriber, source) {\n    const unscheduleTask = this.unscheduleTask.bind(this);\n    this.task = this.zone.run(() => Zone.current.scheduleMacroTask('firebaseZoneBlock', noop, {}, noop, noop));\n    return source.pipe(tap({\n      next: unscheduleTask,\n      complete: unscheduleTask,\n      error: unscheduleTask\n    })).subscribe(subscriber).add(unscheduleTask);\n  }\n\n  unscheduleTask() {\n    // maybe this is a race condition, invoke in a timeout\n    // hold for 10ms while I try to figure out what is going on\n    setTimeout(() => {\n      if (this.task != null && this.task.state === 'scheduled') {\n        this.task.invoke();\n        this.task = null;\n      }\n    }, 10);\n  }\n\n} // tslint:disable-next-line:class-name\n\n\nclass ɵAngularFireSchedulers {\n  constructor(ngZone) {\n    this.ngZone = ngZone;\n    this.outsideAngular = ngZone.runOutsideAngular(() => new ɵZoneScheduler(Zone.current));\n    this.insideAngular = ngZone.run(() => new ɵZoneScheduler(Zone.current, asyncScheduler));\n    globalThis.ɵAngularFireScheduler || (globalThis.ɵAngularFireScheduler = this);\n  }\n\n}\n\nɵAngularFireSchedulers.ɵfac = function ɵAngularFireSchedulers_Factory(t) {\n  return new (t || ɵAngularFireSchedulers)(i0.ɵɵinject(i0.NgZone));\n};\n\nɵAngularFireSchedulers.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ɵAngularFireSchedulers,\n  factory: ɵAngularFireSchedulers.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ɵAngularFireSchedulers, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n\nfunction getSchedulers() {\n  const schedulers = globalThis.ɵAngularFireScheduler;\n\n  if (!schedulers) {\n    throw new Error(`Either AngularFireModule has not been provided in your AppModule (this can be done manually or implictly using\nprovideFirebaseApp) or you're calling an AngularFire method outside of an NgModule (which is not supported).`);\n  }\n\n  return schedulers;\n}\n\nfunction runOutsideAngular(fn) {\n  return getSchedulers().ngZone.runOutsideAngular(() => fn());\n}\n\nfunction run(fn) {\n  return getSchedulers().ngZone.run(() => fn());\n}\n\nfunction observeOutsideAngular(obs$) {\n  return obs$.pipe(observeOn(getSchedulers().outsideAngular));\n}\n\nfunction observeInsideAngular(obs$) {\n  return obs$.pipe(observeOn(getSchedulers().insideAngular));\n}\n\nfunction keepUnstableUntilFirst(obs$) {\n  const scheduler = getSchedulers();\n  return ɵkeepUnstableUntilFirstFactory(getSchedulers())(obs$);\n}\n/**\n * Operator to block the zone until the first value has been emitted or the observable\n * has completed/errored. This is used to make sure that universal waits until the first\n * value from firebase but doesn't block the zone forever since the firebase subscription\n * is still alive.\n */\n\n\nfunction ɵkeepUnstableUntilFirstFactory(schedulers) {\n  return function keepUnstableUntilFirst(obs$) {\n    obs$ = obs$.lift(new BlockUntilFirstOperator(schedulers.ngZone));\n    return obs$.pipe( // Run the subscribe body outside of Angular (e.g. calling Firebase SDK to add a listener to a change event)\n    subscribeOn(schedulers.outsideAngular), // Run operators inside the angular zone (e.g. side effects via tap())\n    observeOn(schedulers.insideAngular) // INVESTIGATE https://github.com/angular/angularfire/pull/2315\n    // share()\n    );\n  };\n}\n\nconst zoneWrapFn = (it, macrotask) => {\n  const _this = this; // function() is needed for the arguments object\n  // tslint:disable-next-line:only-arrow-functions\n\n\n  return function () {\n    const _arguments = arguments;\n\n    if (macrotask) {\n      setTimeout(() => {\n        if (macrotask.state === 'scheduled') {\n          macrotask.invoke();\n        }\n      }, 10);\n    }\n\n    return run(() => it.apply(_this, _arguments));\n  };\n};\n\nconst ɵzoneWrap = (it, blockUntilFirst) => {\n  // function() is needed for the arguments object\n  // tslint:disable-next-line:only-arrow-functions\n  return function () {\n    let macrotask;\n    const _arguments = arguments; // if this is a callback function, e.g, onSnapshot, we should create a microtask and invoke it\n    // only once one of the callback functions is tripped.\n\n    for (let i = 0; i < arguments.length; i++) {\n      if (typeof _arguments[i] === 'function') {\n        if (blockUntilFirst) {\n          macrotask || (macrotask = run(() => Zone.current.scheduleMacroTask('firebaseZoneBlock', noop, {}, noop, noop)));\n        } // TODO create a microtask to track callback functions\n\n\n        _arguments[i] = zoneWrapFn(_arguments[i], macrotask);\n      }\n    }\n\n    const ret = runOutsideAngular(() => it.apply(this, _arguments));\n\n    if (!blockUntilFirst) {\n      if (ret instanceof Observable) {\n        const schedulers = getSchedulers();\n        return ret.pipe(subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n      } else {\n        return run(() => ret);\n      }\n    }\n\n    if (ret instanceof Observable) {\n      return ret.pipe(keepUnstableUntilFirst);\n    } else if (ret instanceof Promise) {\n      return run(() => new Promise((resolve, reject) => ret.then(it => run(() => resolve(it)), reason => run(() => reject(reason)))));\n    } else if (typeof ret === 'function' && macrotask) {\n      // Handle unsubscribe\n      // function() is needed for the arguments object\n      // tslint:disable-next-line:only-arrow-functions\n      return function () {\n        setTimeout(() => {\n          if (macrotask && macrotask.state === 'scheduled') {\n            macrotask.invoke();\n          }\n        }, 10);\n        return ret.apply(this, arguments);\n      };\n    } else {\n      // TODO how do we handle storage uploads in Zone? and other stuff with cancel() etc?\n      return run(() => ret);\n    }\n  };\n};\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { VERSION, keepUnstableUntilFirst, observeInsideAngular, observeOutsideAngular, ɵAngularFireSchedulers, ɵZoneScheduler, ɵgetAllInstancesOf, ɵgetDefaultInstanceOf, ɵisAnalyticsSupportedFactory, ɵisMessagingSupportedFactory, ɵisRemoteConfigSupportedFactory, ɵkeepUnstableUntilFirstFactory, ɵzoneWrap };", "map": {"version": 3, "names": ["i0", "Version", "Injectable", "getApps", "isSupported", "isSupported$2", "isSupported$1", "queueScheduler", "asyncScheduler", "Observable", "tap", "observeOn", "subscribeOn", "VERSION", "isAnalyticsSupportedValueSymbol", "isAnalyticsSupportedPromiseSymbol", "isRemoteConfigSupportedValueSymbol", "isRemoteConfigSupportedPromiseSymbol", "isMessagingSupportedValueSymbol", "isMessagingSupportedPromiseSymbol", "globalThis", "then", "it", "catch", "isSupportedError", "module", "ɵisMessagingSupportedFactory", "async", "sync", "ret", "undefined", "Error", "ɵisRemoteConfigSupportedFactory", "ɵisAnalyticsSupportedFactory", "ɵgetDefaultInstanceOf", "identifier", "provided", "defaultApp", "length", "providedUsingDefaultApp", "filter", "app", "defaultAppWithContainer", "provider", "container", "get<PERSON><PERSON><PERSON>", "getImmediate", "optional", "ɵgetAllInstancesOf", "apps", "instances", "for<PERSON>ach", "instance", "includes", "push", "noop", "ɵZoneScheduler", "constructor", "zone", "delegate", "now", "schedule", "work", "delay", "state", "targetZone", "workInZone", "runGuarded", "apply", "BlockUntilFirstOperator", "task", "call", "subscriber", "source", "unscheduleTask", "bind", "run", "Zone", "current", "scheduleMacroTask", "pipe", "next", "complete", "error", "subscribe", "add", "setTimeout", "invoke", "ɵAngularFireSchedulers", "ngZone", "outsideAngular", "runOutsideAngular", "insideAngular", "ɵAngularFireScheduler", "ɵfac", "NgZone", "ɵprov", "type", "args", "providedIn", "getSchedulers", "schedulers", "fn", "observeOutsideAngular", "obs$", "observeInsideAngular", "keepUnstableUntilFirst", "scheduler", "ɵkeepUnstableUntilFirstFactory", "lift", "zoneWrapFn", "macrotask", "_this", "_arguments", "arguments", "ɵzoneWrap", "blockUntilFirst", "i", "Promise", "resolve", "reject", "reason"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/@angular/fire/fesm2015/angular-fire.js"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Version, Injectable } from '@angular/core';\nimport { getApps } from 'firebase/app';\nimport { isSupported as isSupported$2 } from 'firebase/remote-config';\nimport { isSupported as isSupported$1 } from 'firebase/messaging';\nimport { isSupported } from 'firebase/analytics';\nimport { queueScheduler, asyncScheduler, Observable } from 'rxjs';\nimport { tap, observeOn, subscribeOn } from 'rxjs/operators';\n\nconst VERSION = new Version('7.6.1');\nconst isAnalyticsSupportedValueSymbol = '__angularfire_symbol__analyticsIsSupportedValue';\nconst isAnalyticsSupportedPromiseSymbol = '__angularfire_symbol__analyticsIsSupported';\nconst isRemoteConfigSupportedValueSymbol = '__angularfire_symbol__remoteConfigIsSupportedValue';\nconst isRemoteConfigSupportedPromiseSymbol = '__angularfire_symbol__remoteConfigIsSupported';\nconst isMessagingSupportedValueSymbol = '__angularfire_symbol__messagingIsSupportedValue';\nconst isMessagingSupportedPromiseSymbol = '__angularfire_symbol__messagingIsSupported';\nglobalThis[isAnalyticsSupportedPromiseSymbol] || (globalThis[isAnalyticsSupportedPromiseSymbol] = isSupported().then(it => globalThis[isAnalyticsSupportedValueSymbol] = it).catch(() => globalThis[isAnalyticsSupportedValueSymbol] = false));\nglobalThis[isMessagingSupportedPromiseSymbol] || (globalThis[isMessagingSupportedPromiseSymbol] = isSupported$1().then(it => globalThis[isMessagingSupportedValueSymbol] = it).catch(() => globalThis[isMessagingSupportedValueSymbol] = false));\nglobalThis[isRemoteConfigSupportedPromiseSymbol] || (globalThis[isRemoteConfigSupportedPromiseSymbol] = isSupported$2().then(it => globalThis[isRemoteConfigSupportedValueSymbol] = it).catch(() => globalThis[isRemoteConfigSupportedValueSymbol] = false));\nconst isSupportedError = (module) => `The APP_INITIALIZER that is \"making\" isSupported() sync for the sake of convenient DI has not resolved in this\ncontext. Rather than injecting ${module} in the constructor, first ensure that ${module} is supported by calling\n\\`await isSupported()\\`, then retrieve the instance from the injector manually \\`injector.get(${module})\\`.`;\nconst ɵisMessagingSupportedFactory = {\n    async: () => globalThis[isMessagingSupportedPromiseSymbol],\n    sync: () => {\n        const ret = globalThis[isMessagingSupportedValueSymbol];\n        if (ret === undefined) {\n            throw new Error(isSupportedError('Messaging'));\n        }\n        return ret;\n    }\n};\nconst ɵisRemoteConfigSupportedFactory = {\n    async: () => globalThis[isRemoteConfigSupportedPromiseSymbol],\n    sync: () => {\n        const ret = globalThis[isRemoteConfigSupportedValueSymbol];\n        if (ret === undefined) {\n            throw new Error(isSupportedError('RemoteConfig'));\n        }\n        return ret;\n    }\n};\nconst ɵisAnalyticsSupportedFactory = {\n    async: () => globalThis[isAnalyticsSupportedPromiseSymbol],\n    sync: () => {\n        const ret = globalThis[isAnalyticsSupportedValueSymbol];\n        if (ret === undefined) {\n            throw new Error(isSupportedError('Analytics'));\n        }\n        return ret;\n    }\n};\nfunction ɵgetDefaultInstanceOf(identifier, provided, defaultApp) {\n    if (provided) {\n        // Was provide* only called once? If so grab that\n        if (provided.length === 1) {\n            return provided[0];\n        }\n        const providedUsingDefaultApp = provided.filter((it) => it.app === defaultApp);\n        // Was provide* only called once, using the default app? If so use that\n        if (providedUsingDefaultApp.length === 1) {\n            return providedUsingDefaultApp[0];\n        }\n    }\n    // Grab the default instance from the defaultApp\n    const defaultAppWithContainer = defaultApp;\n    const provider = defaultAppWithContainer.container.getProvider(identifier);\n    return provider.getImmediate({ optional: true });\n}\nconst ɵgetAllInstancesOf = (identifier, app) => {\n    const apps = app ? [app] : getApps();\n    const instances = [];\n    apps.forEach((app) => {\n        const provider = app.container.getProvider(identifier);\n        provider.instances.forEach((instance) => {\n            if (!instances.includes(instance)) {\n                instances.push(instance);\n            }\n        });\n    });\n    return instances;\n};\n\nfunction noop() {\n}\n/**\n * Schedules tasks so that they are invoked inside the Zone that is passed in the constructor.\n */\n// tslint:disable-next-line:class-name\nclass ɵZoneScheduler {\n    constructor(zone, delegate = queueScheduler) {\n        this.zone = zone;\n        this.delegate = delegate;\n    }\n    now() {\n        return this.delegate.now();\n    }\n    schedule(work, delay, state) {\n        const targetZone = this.zone;\n        // Wrap the specified work function to make sure that if nested scheduling takes place the\n        // work is executed in the correct zone\n        const workInZone = function (state) {\n            targetZone.runGuarded(() => {\n                work.apply(this, [state]);\n            });\n        };\n        // Scheduling itself needs to be run in zone to ensure setInterval calls for async scheduling are done\n        // inside the correct zone. This scheduler needs to schedule asynchronously always to ensure that\n        // firebase emissions are never synchronous. Specifying a delay causes issues with the queueScheduler delegate.\n        return this.delegate.schedule(workInZone, delay, state);\n    }\n}\nclass BlockUntilFirstOperator {\n    constructor(zone) {\n        this.zone = zone;\n        this.task = null;\n    }\n    call(subscriber, source) {\n        const unscheduleTask = this.unscheduleTask.bind(this);\n        this.task = this.zone.run(() => Zone.current.scheduleMacroTask('firebaseZoneBlock', noop, {}, noop, noop));\n        return source.pipe(tap({ next: unscheduleTask, complete: unscheduleTask, error: unscheduleTask })).subscribe(subscriber).add(unscheduleTask);\n    }\n    unscheduleTask() {\n        // maybe this is a race condition, invoke in a timeout\n        // hold for 10ms while I try to figure out what is going on\n        setTimeout(() => {\n            if (this.task != null && this.task.state === 'scheduled') {\n                this.task.invoke();\n                this.task = null;\n            }\n        }, 10);\n    }\n}\n// tslint:disable-next-line:class-name\nclass ɵAngularFireSchedulers {\n    constructor(ngZone) {\n        this.ngZone = ngZone;\n        this.outsideAngular = ngZone.runOutsideAngular(() => new ɵZoneScheduler(Zone.current));\n        this.insideAngular = ngZone.run(() => new ɵZoneScheduler(Zone.current, asyncScheduler));\n        globalThis.ɵAngularFireScheduler || (globalThis.ɵAngularFireScheduler = this);\n    }\n}\nɵAngularFireSchedulers.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: ɵAngularFireSchedulers, deps: [{ token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\nɵAngularFireSchedulers.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: ɵAngularFireSchedulers, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: ɵAngularFireSchedulers, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }]; } });\nfunction getSchedulers() {\n    const schedulers = globalThis.ɵAngularFireScheduler;\n    if (!schedulers) {\n        throw new Error(`Either AngularFireModule has not been provided in your AppModule (this can be done manually or implictly using\nprovideFirebaseApp) or you're calling an AngularFire method outside of an NgModule (which is not supported).`);\n    }\n    return schedulers;\n}\nfunction runOutsideAngular(fn) {\n    return getSchedulers().ngZone.runOutsideAngular(() => fn());\n}\nfunction run(fn) {\n    return getSchedulers().ngZone.run(() => fn());\n}\nfunction observeOutsideAngular(obs$) {\n    return obs$.pipe(observeOn(getSchedulers().outsideAngular));\n}\nfunction observeInsideAngular(obs$) {\n    return obs$.pipe(observeOn(getSchedulers().insideAngular));\n}\nfunction keepUnstableUntilFirst(obs$) {\n    const scheduler = getSchedulers();\n    return ɵkeepUnstableUntilFirstFactory(getSchedulers())(obs$);\n}\n/**\n * Operator to block the zone until the first value has been emitted or the observable\n * has completed/errored. This is used to make sure that universal waits until the first\n * value from firebase but doesn't block the zone forever since the firebase subscription\n * is still alive.\n */\nfunction ɵkeepUnstableUntilFirstFactory(schedulers) {\n    return function keepUnstableUntilFirst(obs$) {\n        obs$ = obs$.lift(new BlockUntilFirstOperator(schedulers.ngZone));\n        return obs$.pipe(\n        // Run the subscribe body outside of Angular (e.g. calling Firebase SDK to add a listener to a change event)\n        subscribeOn(schedulers.outsideAngular), \n        // Run operators inside the angular zone (e.g. side effects via tap())\n        observeOn(schedulers.insideAngular)\n        // INVESTIGATE https://github.com/angular/angularfire/pull/2315\n        // share()\n        );\n    };\n}\nconst zoneWrapFn = (it, macrotask) => {\n    const _this = this;\n    // function() is needed for the arguments object\n    // tslint:disable-next-line:only-arrow-functions\n    return function () {\n        const _arguments = arguments;\n        if (macrotask) {\n            setTimeout(() => {\n                if (macrotask.state === 'scheduled') {\n                    macrotask.invoke();\n                }\n            }, 10);\n        }\n        return run(() => it.apply(_this, _arguments));\n    };\n};\nconst ɵzoneWrap = (it, blockUntilFirst) => {\n    // function() is needed for the arguments object\n    // tslint:disable-next-line:only-arrow-functions\n    return function () {\n        let macrotask;\n        const _arguments = arguments;\n        // if this is a callback function, e.g, onSnapshot, we should create a microtask and invoke it\n        // only once one of the callback functions is tripped.\n        for (let i = 0; i < arguments.length; i++) {\n            if (typeof _arguments[i] === 'function') {\n                if (blockUntilFirst) {\n                    macrotask || (macrotask = run(() => Zone.current.scheduleMacroTask('firebaseZoneBlock', noop, {}, noop, noop)));\n                }\n                // TODO create a microtask to track callback functions\n                _arguments[i] = zoneWrapFn(_arguments[i], macrotask);\n            }\n        }\n        const ret = runOutsideAngular(() => it.apply(this, _arguments));\n        if (!blockUntilFirst) {\n            if (ret instanceof Observable) {\n                const schedulers = getSchedulers();\n                return ret.pipe(subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n            }\n            else {\n                return run(() => ret);\n            }\n        }\n        if (ret instanceof Observable) {\n            return ret.pipe(keepUnstableUntilFirst);\n        }\n        else if (ret instanceof Promise) {\n            return run(() => new Promise((resolve, reject) => ret.then(it => run(() => resolve(it)), reason => run(() => reject(reason)))));\n        }\n        else if (typeof ret === 'function' && macrotask) {\n            // Handle unsubscribe\n            // function() is needed for the arguments object\n            // tslint:disable-next-line:only-arrow-functions\n            return function () {\n                setTimeout(() => {\n                    if (macrotask && macrotask.state === 'scheduled') {\n                        macrotask.invoke();\n                    }\n                }, 10);\n                return ret.apply(this, arguments);\n            };\n        }\n        else {\n            // TODO how do we handle storage uploads in Zone? and other stuff with cancel() etc?\n            return run(() => ret);\n        }\n    };\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { VERSION, keepUnstableUntilFirst, observeInsideAngular, observeOutsideAngular, ɵAngularFireSchedulers, ɵZoneScheduler, ɵgetAllInstancesOf, ɵgetDefaultInstanceOf, ɵisAnalyticsSupportedFactory, ɵisMessagingSupportedFactory, ɵisRemoteConfigSupportedFactory, ɵkeepUnstableUntilFirstFactory, ɵzoneWrap };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,OAAT,EAAkBC,UAAlB,QAAoC,eAApC;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,WAAW,IAAIC,aAAxB,QAA6C,wBAA7C;AACA,SAASD,WAAW,IAAIE,aAAxB,QAA6C,oBAA7C;AACA,SAASF,WAAT,QAA4B,oBAA5B;AACA,SAASG,cAAT,EAAyBC,cAAzB,EAAyCC,UAAzC,QAA2D,MAA3D;AACA,SAASC,GAAT,EAAcC,SAAd,EAAyBC,WAAzB,QAA4C,gBAA5C;AAEA,MAAMC,OAAO,GAAG,IAAIZ,OAAJ,CAAY,OAAZ,CAAhB;AACA,MAAMa,+BAA+B,GAAG,iDAAxC;AACA,MAAMC,iCAAiC,GAAG,4CAA1C;AACA,MAAMC,kCAAkC,GAAG,oDAA3C;AACA,MAAMC,oCAAoC,GAAG,+CAA7C;AACA,MAAMC,+BAA+B,GAAG,iDAAxC;AACA,MAAMC,iCAAiC,GAAG,4CAA1C;AACAC,UAAU,CAACL,iCAAD,CAAV,KAAkDK,UAAU,CAACL,iCAAD,CAAV,GAAgDX,WAAW,GAAGiB,IAAd,CAAmBC,EAAE,IAAIF,UAAU,CAACN,+BAAD,CAAV,GAA8CQ,EAAvE,EAA2EC,KAA3E,CAAiF,MAAMH,UAAU,CAACN,+BAAD,CAAV,GAA8C,KAArI,CAAlG;AACAM,UAAU,CAACD,iCAAD,CAAV,KAAkDC,UAAU,CAACD,iCAAD,CAAV,GAAgDb,aAAa,GAAGe,IAAhB,CAAqBC,EAAE,IAAIF,UAAU,CAACF,+BAAD,CAAV,GAA8CI,EAAzE,EAA6EC,KAA7E,CAAmF,MAAMH,UAAU,CAACF,+BAAD,CAAV,GAA8C,KAAvI,CAAlG;AACAE,UAAU,CAACH,oCAAD,CAAV,KAAqDG,UAAU,CAACH,oCAAD,CAAV,GAAmDZ,aAAa,GAAGgB,IAAhB,CAAqBC,EAAE,IAAIF,UAAU,CAACJ,kCAAD,CAAV,GAAiDM,EAA5E,EAAgFC,KAAhF,CAAsF,MAAMH,UAAU,CAACJ,kCAAD,CAAV,GAAiD,KAA7I,CAAxG;;AACA,MAAMQ,gBAAgB,GAAIC,MAAD,IAAa;AACtC,iCAAiCA,MAAO,0CAAyCA,MAAO;AACxF,gGAAgGA,MAAO,MAFvG;;AAGA,MAAMC,4BAA4B,GAAG;EACjCC,KAAK,EAAE,MAAMP,UAAU,CAACD,iCAAD,CADU;EAEjCS,IAAI,EAAE,MAAM;IACR,MAAMC,GAAG,GAAGT,UAAU,CAACF,+BAAD,CAAtB;;IACA,IAAIW,GAAG,KAAKC,SAAZ,EAAuB;MACnB,MAAM,IAAIC,KAAJ,CAAUP,gBAAgB,CAAC,WAAD,CAA1B,CAAN;IACH;;IACD,OAAOK,GAAP;EACH;AARgC,CAArC;AAUA,MAAMG,+BAA+B,GAAG;EACpCL,KAAK,EAAE,MAAMP,UAAU,CAACH,oCAAD,CADa;EAEpCW,IAAI,EAAE,MAAM;IACR,MAAMC,GAAG,GAAGT,UAAU,CAACJ,kCAAD,CAAtB;;IACA,IAAIa,GAAG,KAAKC,SAAZ,EAAuB;MACnB,MAAM,IAAIC,KAAJ,CAAUP,gBAAgB,CAAC,cAAD,CAA1B,CAAN;IACH;;IACD,OAAOK,GAAP;EACH;AARmC,CAAxC;AAUA,MAAMI,4BAA4B,GAAG;EACjCN,KAAK,EAAE,MAAMP,UAAU,CAACL,iCAAD,CADU;EAEjCa,IAAI,EAAE,MAAM;IACR,MAAMC,GAAG,GAAGT,UAAU,CAACN,+BAAD,CAAtB;;IACA,IAAIe,GAAG,KAAKC,SAAZ,EAAuB;MACnB,MAAM,IAAIC,KAAJ,CAAUP,gBAAgB,CAAC,WAAD,CAA1B,CAAN;IACH;;IACD,OAAOK,GAAP;EACH;AARgC,CAArC;;AAUA,SAASK,qBAAT,CAA+BC,UAA/B,EAA2CC,QAA3C,EAAqDC,UAArD,EAAiE;EAC7D,IAAID,QAAJ,EAAc;IACV;IACA,IAAIA,QAAQ,CAACE,MAAT,KAAoB,CAAxB,EAA2B;MACvB,OAAOF,QAAQ,CAAC,CAAD,CAAf;IACH;;IACD,MAAMG,uBAAuB,GAAGH,QAAQ,CAACI,MAAT,CAAiBlB,EAAD,IAAQA,EAAE,CAACmB,GAAH,KAAWJ,UAAnC,CAAhC,CALU,CAMV;;IACA,IAAIE,uBAAuB,CAACD,MAAxB,KAAmC,CAAvC,EAA0C;MACtC,OAAOC,uBAAuB,CAAC,CAAD,CAA9B;IACH;EACJ,CAX4D,CAY7D;;;EACA,MAAMG,uBAAuB,GAAGL,UAAhC;EACA,MAAMM,QAAQ,GAAGD,uBAAuB,CAACE,SAAxB,CAAkCC,WAAlC,CAA8CV,UAA9C,CAAjB;EACA,OAAOQ,QAAQ,CAACG,YAAT,CAAsB;IAAEC,QAAQ,EAAE;EAAZ,CAAtB,CAAP;AACH;;AACD,MAAMC,kBAAkB,GAAG,CAACb,UAAD,EAAaM,GAAb,KAAqB;EAC5C,MAAMQ,IAAI,GAAGR,GAAG,GAAG,CAACA,GAAD,CAAH,GAAWtC,OAAO,EAAlC;EACA,MAAM+C,SAAS,GAAG,EAAlB;EACAD,IAAI,CAACE,OAAL,CAAcV,GAAD,IAAS;IAClB,MAAME,QAAQ,GAAGF,GAAG,CAACG,SAAJ,CAAcC,WAAd,CAA0BV,UAA1B,CAAjB;IACAQ,QAAQ,CAACO,SAAT,CAAmBC,OAAnB,CAA4BC,QAAD,IAAc;MACrC,IAAI,CAACF,SAAS,CAACG,QAAV,CAAmBD,QAAnB,CAAL,EAAmC;QAC/BF,SAAS,CAACI,IAAV,CAAeF,QAAf;MACH;IACJ,CAJD;EAKH,CAPD;EAQA,OAAOF,SAAP;AACH,CAZD;;AAcA,SAASK,IAAT,GAAgB,CACf;AACD;AACA;AACA;AACA;;;AACA,MAAMC,cAAN,CAAqB;EACjBC,WAAW,CAACC,IAAD,EAAOC,QAAQ,GAAGpD,cAAlB,EAAkC;IACzC,KAAKmD,IAAL,GAAYA,IAAZ;IACA,KAAKC,QAAL,GAAgBA,QAAhB;EACH;;EACDC,GAAG,GAAG;IACF,OAAO,KAAKD,QAAL,CAAcC,GAAd,EAAP;EACH;;EACDC,QAAQ,CAACC,IAAD,EAAOC,KAAP,EAAcC,KAAd,EAAqB;IACzB,MAAMC,UAAU,GAAG,KAAKP,IAAxB,CADyB,CAEzB;IACA;;IACA,MAAMQ,UAAU,GAAG,UAAUF,KAAV,EAAiB;MAChCC,UAAU,CAACE,UAAX,CAAsB,MAAM;QACxBL,IAAI,CAACM,KAAL,CAAW,IAAX,EAAiB,CAACJ,KAAD,CAAjB;MACH,CAFD;IAGH,CAJD,CAJyB,CASzB;IACA;IACA;;;IACA,OAAO,KAAKL,QAAL,CAAcE,QAAd,CAAuBK,UAAvB,EAAmCH,KAAnC,EAA0CC,KAA1C,CAAP;EACH;;AArBgB;;AAuBrB,MAAMK,uBAAN,CAA8B;EAC1BZ,WAAW,CAACC,IAAD,EAAO;IACd,KAAKA,IAAL,GAAYA,IAAZ;IACA,KAAKY,IAAL,GAAY,IAAZ;EACH;;EACDC,IAAI,CAACC,UAAD,EAAaC,MAAb,EAAqB;IACrB,MAAMC,cAAc,GAAG,KAAKA,cAAL,CAAoBC,IAApB,CAAyB,IAAzB,CAAvB;IACA,KAAKL,IAAL,GAAY,KAAKZ,IAAL,CAAUkB,GAAV,CAAc,MAAMC,IAAI,CAACC,OAAL,CAAaC,iBAAb,CAA+B,mBAA/B,EAAoDxB,IAApD,EAA0D,EAA1D,EAA8DA,IAA9D,EAAoEA,IAApE,CAApB,CAAZ;IACA,OAAOkB,MAAM,CAACO,IAAP,CAAYtE,GAAG,CAAC;MAAEuE,IAAI,EAAEP,cAAR;MAAwBQ,QAAQ,EAAER,cAAlC;MAAkDS,KAAK,EAAET;IAAzD,CAAD,CAAf,EAA4FU,SAA5F,CAAsGZ,UAAtG,EAAkHa,GAAlH,CAAsHX,cAAtH,CAAP;EACH;;EACDA,cAAc,GAAG;IACb;IACA;IACAY,UAAU,CAAC,MAAM;MACb,IAAI,KAAKhB,IAAL,IAAa,IAAb,IAAqB,KAAKA,IAAL,CAAUN,KAAV,KAAoB,WAA7C,EAA0D;QACtD,KAAKM,IAAL,CAAUiB,MAAV;QACA,KAAKjB,IAAL,GAAY,IAAZ;MACH;IACJ,CALS,EAKP,EALO,CAAV;EAMH;;AAnByB,C,CAqB9B;;;AACA,MAAMkB,sBAAN,CAA6B;EACzB/B,WAAW,CAACgC,MAAD,EAAS;IAChB,KAAKA,MAAL,GAAcA,MAAd;IACA,KAAKC,cAAL,GAAsBD,MAAM,CAACE,iBAAP,CAAyB,MAAM,IAAInC,cAAJ,CAAmBqB,IAAI,CAACC,OAAxB,CAA/B,CAAtB;IACA,KAAKc,aAAL,GAAqBH,MAAM,CAACb,GAAP,CAAW,MAAM,IAAIpB,cAAJ,CAAmBqB,IAAI,CAACC,OAAxB,EAAiCtE,cAAjC,CAAjB,CAArB;IACAY,UAAU,CAACyE,qBAAX,KAAqCzE,UAAU,CAACyE,qBAAX,GAAmC,IAAxE;EACH;;AANwB;;AAQ7BL,sBAAsB,CAACM,IAAvB;EAAA,iBAAmHN,sBAAnH,EAAyGxF,EAAzG,UAA2JA,EAAE,CAAC+F,MAA9J;AAAA;;AACAP,sBAAsB,CAACQ,KAAvB,kBADyGhG,EACzG;EAAA,OAAuHwF,sBAAvH;EAAA,SAAuHA,sBAAvH;EAAA,YAA2J;AAA3J;;AACA;EAAA,mDAFyGxF,EAEzG,mBAA2FwF,sBAA3F,EAA+H,CAAC;IACpHS,IAAI,EAAE/F,UAD8G;IAEpHgG,IAAI,EAAE,CAAC;MACCC,UAAU,EAAE;IADb,CAAD;EAF8G,CAAD,CAA/H,EAK4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEjG,EAAE,CAAC+F;IAAX,CAAD,CAAP;EAA+B,CALzE;AAAA;;AAMA,SAASK,aAAT,GAAyB;EACrB,MAAMC,UAAU,GAAGjF,UAAU,CAACyE,qBAA9B;;EACA,IAAI,CAACQ,UAAL,EAAiB;IACb,MAAM,IAAItE,KAAJ,CAAW;AACzB,6GADc,CAAN;EAEH;;EACD,OAAOsE,UAAP;AACH;;AACD,SAASV,iBAAT,CAA2BW,EAA3B,EAA+B;EAC3B,OAAOF,aAAa,GAAGX,MAAhB,CAAuBE,iBAAvB,CAAyC,MAAMW,EAAE,EAAjD,CAAP;AACH;;AACD,SAAS1B,GAAT,CAAa0B,EAAb,EAAiB;EACb,OAAOF,aAAa,GAAGX,MAAhB,CAAuBb,GAAvB,CAA2B,MAAM0B,EAAE,EAAnC,CAAP;AACH;;AACD,SAASC,qBAAT,CAA+BC,IAA/B,EAAqC;EACjC,OAAOA,IAAI,CAACxB,IAAL,CAAUrE,SAAS,CAACyF,aAAa,GAAGV,cAAjB,CAAnB,CAAP;AACH;;AACD,SAASe,oBAAT,CAA8BD,IAA9B,EAAoC;EAChC,OAAOA,IAAI,CAACxB,IAAL,CAAUrE,SAAS,CAACyF,aAAa,GAAGR,aAAjB,CAAnB,CAAP;AACH;;AACD,SAASc,sBAAT,CAAgCF,IAAhC,EAAsC;EAClC,MAAMG,SAAS,GAAGP,aAAa,EAA/B;EACA,OAAOQ,8BAA8B,CAACR,aAAa,EAAd,CAA9B,CAAgDI,IAAhD,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASI,8BAAT,CAAwCP,UAAxC,EAAoD;EAChD,OAAO,SAASK,sBAAT,CAAgCF,IAAhC,EAAsC;IACzCA,IAAI,GAAGA,IAAI,CAACK,IAAL,CAAU,IAAIxC,uBAAJ,CAA4BgC,UAAU,CAACZ,MAAvC,CAAV,CAAP;IACA,OAAOe,IAAI,CAACxB,IAAL,EACP;IACApE,WAAW,CAACyF,UAAU,CAACX,cAAZ,CAFJ,EAGP;IACA/E,SAAS,CAAC0F,UAAU,CAACT,aAAZ,CAJF,CAKP;IACA;IANO,CAAP;EAQH,CAVD;AAWH;;AACD,MAAMkB,UAAU,GAAG,CAACxF,EAAD,EAAKyF,SAAL,KAAmB;EAClC,MAAMC,KAAK,GAAG,IAAd,CADkC,CAElC;EACA;;;EACA,OAAO,YAAY;IACf,MAAMC,UAAU,GAAGC,SAAnB;;IACA,IAAIH,SAAJ,EAAe;MACXzB,UAAU,CAAC,MAAM;QACb,IAAIyB,SAAS,CAAC/C,KAAV,KAAoB,WAAxB,EAAqC;UACjC+C,SAAS,CAACxB,MAAV;QACH;MACJ,CAJS,EAIP,EAJO,CAAV;IAKH;;IACD,OAAOX,GAAG,CAAC,MAAMtD,EAAE,CAAC8C,KAAH,CAAS4C,KAAT,EAAgBC,UAAhB,CAAP,CAAV;EACH,CAVD;AAWH,CAfD;;AAgBA,MAAME,SAAS,GAAG,CAAC7F,EAAD,EAAK8F,eAAL,KAAyB;EACvC;EACA;EACA,OAAO,YAAY;IACf,IAAIL,SAAJ;IACA,MAAME,UAAU,GAAGC,SAAnB,CAFe,CAGf;IACA;;IACA,KAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,SAAS,CAAC5E,MAA9B,EAAsC+E,CAAC,EAAvC,EAA2C;MACvC,IAAI,OAAOJ,UAAU,CAACI,CAAD,CAAjB,KAAyB,UAA7B,EAAyC;QACrC,IAAID,eAAJ,EAAqB;UACjBL,SAAS,KAAKA,SAAS,GAAGnC,GAAG,CAAC,MAAMC,IAAI,CAACC,OAAL,CAAaC,iBAAb,CAA+B,mBAA/B,EAAoDxB,IAApD,EAA0D,EAA1D,EAA8DA,IAA9D,EAAoEA,IAApE,CAAP,CAApB,CAAT;QACH,CAHoC,CAIrC;;;QACA0D,UAAU,CAACI,CAAD,CAAV,GAAgBP,UAAU,CAACG,UAAU,CAACI,CAAD,CAAX,EAAgBN,SAAhB,CAA1B;MACH;IACJ;;IACD,MAAMlF,GAAG,GAAG8D,iBAAiB,CAAC,MAAMrE,EAAE,CAAC8C,KAAH,CAAS,IAAT,EAAe6C,UAAf,CAAP,CAA7B;;IACA,IAAI,CAACG,eAAL,EAAsB;MAClB,IAAIvF,GAAG,YAAYpB,UAAnB,EAA+B;QAC3B,MAAM4F,UAAU,GAAGD,aAAa,EAAhC;QACA,OAAOvE,GAAG,CAACmD,IAAJ,CAASpE,WAAW,CAACyF,UAAU,CAACX,cAAZ,CAApB,EAAiD/E,SAAS,CAAC0F,UAAU,CAACT,aAAZ,CAA1D,CAAP;MACH,CAHD,MAIK;QACD,OAAOhB,GAAG,CAAC,MAAM/C,GAAP,CAAV;MACH;IACJ;;IACD,IAAIA,GAAG,YAAYpB,UAAnB,EAA+B;MAC3B,OAAOoB,GAAG,CAACmD,IAAJ,CAAS0B,sBAAT,CAAP;IACH,CAFD,MAGK,IAAI7E,GAAG,YAAYyF,OAAnB,EAA4B;MAC7B,OAAO1C,GAAG,CAAC,MAAM,IAAI0C,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB3F,GAAG,CAACR,IAAJ,CAASC,EAAE,IAAIsD,GAAG,CAAC,MAAM2C,OAAO,CAACjG,EAAD,CAAd,CAAlB,EAAuCmG,MAAM,IAAI7C,GAAG,CAAC,MAAM4C,MAAM,CAACC,MAAD,CAAb,CAApD,CAAjC,CAAP,CAAV;IACH,CAFI,MAGA,IAAI,OAAO5F,GAAP,KAAe,UAAf,IAA6BkF,SAAjC,EAA4C;MAC7C;MACA;MACA;MACA,OAAO,YAAY;QACfzB,UAAU,CAAC,MAAM;UACb,IAAIyB,SAAS,IAAIA,SAAS,CAAC/C,KAAV,KAAoB,WAArC,EAAkD;YAC9C+C,SAAS,CAACxB,MAAV;UACH;QACJ,CAJS,EAIP,EAJO,CAAV;QAKA,OAAO1D,GAAG,CAACuC,KAAJ,CAAU,IAAV,EAAgB8C,SAAhB,CAAP;MACH,CAPD;IAQH,CAZI,MAaA;MACD;MACA,OAAOtC,GAAG,CAAC,MAAM/C,GAAP,CAAV;IACH;EACJ,CA/CD;AAgDH,CAnDD;AAqDA;AACA;AACA;;;AAEA,SAAShB,OAAT,EAAkB6F,sBAAlB,EAA0CD,oBAA1C,EAAgEF,qBAAhE,EAAuFf,sBAAvF,EAA+GhC,cAA/G,EAA+HR,kBAA/H,EAAmJd,qBAAnJ,EAA0KD,4BAA1K,EAAwMP,4BAAxM,EAAsOM,+BAAtO,EAAuQ4E,8BAAvQ,EAAuSO,SAAvS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}