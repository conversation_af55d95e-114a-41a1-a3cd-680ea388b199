{"ast": null, "code": "import withMetadataArgument from './withMetadataArgument.js';\nimport { getCountries as _getCountries } from '../../core/index.js';\nexport function getCountries() {\n  return withMetadataArgument(_getCountries, arguments);\n}", "map": {"version": 3, "names": ["withMetadataArgument", "getCountries", "_getCountries", "arguments"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/libphonenumber-js/min/exports/getCountries.js"], "sourcesContent": ["import withMetadataArgument from './withMetadataArgument.js'\r\nimport { getCountries as _getCountries } from '../../core/index.js'\r\n\r\nexport function getCountries() {\r\n\treturn withMetadataArgument(_getCountries, arguments)\r\n}"], "mappings": "AAAA,OAAOA,oBAAP,MAAiC,2BAAjC;AACA,SAASC,YAAY,IAAIC,aAAzB,QAA8C,qBAA9C;AAEA,OAAO,SAASD,YAAT,GAAwB;EAC9B,OAAOD,oBAAoB,CAACE,aAAD,EAAgBC,SAAhB,CAA3B;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module"}