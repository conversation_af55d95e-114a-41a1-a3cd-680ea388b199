import { Action, createSelector } from '@ngrx/store';
import { DataDateType, DataFilterType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { ConversionStatus, DataTopFilter } from 'src/app/core/interfaces/data-management.interface';
import {
  AddCardData,
  CommunicationDataCountSuccess,
  DataExcelUploadSuccess,
  DataManagementActionTypes,
  ExcelUploadSuccess,
  FetchAllDataSuccess,
  FetchDataAltCountryCodeSuccess,
  FetchDataByIdSuccess,
  FetchDataCitiesSuccess,
  FetchDataClusterNameSuccess,
  FetchDataCommunicationByIdsSuccess,
  FetchDataCommunitiesSuccess,
  FetchDataConversionStatusSuccess,
  FetchDataCountriesSuccess,
  FetchDataCountryCodeSuccess,
  FetchDataCurrencySuccess,
  FetchDataCustomStatusFilterSuccess,
  FetchDataExcelUploadedListSuccess,
  FetchDataExportStatusSuccess,
  FetchDataExportSuccess,
  FetchDataHistoryByIdSuccess,
  FetchDataIdWithContactNoSuccess,
  FetchDataLandLineSuccess,
  FetchDataLocalitiesSuccess,
  FetchDataLocationsSuccess,
  FetchDataMigrateExcelUploadedListSuccess,
  FetchDataNationalitySuccess,
  FetchDataPostalCodeSuccess,
  FetchDataQRCodeSuccess,
  FetchDataSourceListSuccess,
  FetchDataStatesSuccess,
  FetchDataStatusSuccess,
  FetchDataSubCommunitiesSuccess,
  FetchDataTopFiltersSuccess,
  FetchDataTowerNamesSuccess,
  FetchDataUnitNameSuccess,
  FetchDataZonesSuccess,
  FetchUploadTypeNameListSuccess,
  UpdateAllDataIsLoading,
  UpdateDataFilterPayload,
  UpdateDataTopFilterLoadedOnce,
} from './data-management.actions';

export interface DataManagementFilters {
  showFilterCount: boolean;
  showCommunicationCount: boolean;
  AltCountryCode: any;
  CountryCode: any;
  MinNetArea: number;
  MaxNetArea: number;
  MinPropertyArea: number;
  MaxPropertyArea: number;
  UnitNames: any;
  Localities: any;
  ClosingManagers: any;
  SourcingManagers: any;
  ReferralEmail: any;
  ReferralName: any;
  ReferralContactNo: any;
  TowerNames: any;
  SubCommunities: any;
  Communities: any;
  Countries: any;
  PostalCodes: any;
  Furnished: any;
  OfferTypes: any;
  Floors: any;
  Baths: any;
  Beds: any;
  LandLine: any;
  ProspectSearch: string;
  PageNumber: number;
  PageSize: number;
  DateType: DataDateType;
  FromDate: string;
  ToDate: string;
  FilterType: DataFilterType;
  AssignTo: string[];
  AssignedFromIds: string[];
  CreatedByIds: string[];
  QualifiedByIds: string[];
  LastModifiedByIds: string[],
  ConvertedByIds: string[],
  DeletedByIds: string[],
  RestoredByIds: string[],
  StatusIds: string[];
  SourceIds: string[];
  SubSources: string[];
  Properties: string[];
  PropertyType: string[];
  PropertySubType: string[];
  Projects: string[];
  EnquiryTypes: number[];
  FromMinBudget: number,
  ToMinBudget: number,
  FromMaxBudget: number,
  ToMaxBudget: number,
  Currency: string,
  NoOfBHKs: number[];
  BHKTypes: number[];
  MinCarpetArea: number;
  MaxCarpetArea: number;
  CarpetAreaUnitId: string;
  MinSaleableArea: number;
  MaxSaleableArea: number;
  SaleableAreaUnitId: string;
  MinBuiltUpArea: number;
  MaxBuiltUpArea: number;
  BuiltUpAreaUnitId: string;
  NetAreaUnitId: string;
  PropertyAreaUnitId: string;
  AgencyNames: string[];
  ChannelPartnerNames: string[];
  CampaignNames: string[];
  CompanyNames: string[];
  Designations: string[];
  Profession: number[];
  Locations: string[];
  Cities: string[];
  States: string[];
  path: string;
  IsWithTeam: boolean;
  FirstLevelFilter: number | string;
  SecondLevelFilter: number;
  ProspectVisiblity: number;
  CustomFilterId: string;
  CustomSecondFilterId: string;
  CustomFilterBaseIds: any;
  Nationality: string[];
  ClusterName: string;
  Purposes: string;
  UploadTypeName: string;
  PropertyToSearch: string[];
  PossesionType: any;
  FromPossesionDate: any;
  ToPossesionDate: any;
  MaritalStatuses: any;
  GenderTypes: any;
  DateOfBirth: any;
}

export type DataManagementState = {
  dataCountryCodeLoading: any;
  dataAltCountryCodeLoading: any;
  dataAltCountryCode: any;
  dataCountryCode: any;
  localities: string[];
  isLocalitiesLoading: boolean;
  isCountriesLoading: boolean;
  isTowerNamesLoading: boolean;
  towerNames: string[];
  isCommunitiesLoading: boolean;
  communities: string[];
  isSubCommunitiesLoading: boolean;
  subCommunities: string[];
  countries: string[];
  zones: string[];
  isZonesLoading: boolean
  activeData: any;
  isActiveDataLoading: boolean;
  allData: any[];
  allDataIsLoading: boolean;
  totalDataCount: number;
  isFetchingAllData: boolean;
  dataIsAdding: boolean;
  dataIsUpdating: boolean;
  dataSourceList: Array<any>;
  isDataSourceListLoading: boolean;
  dataSubSourceList: Array<any>;
  dataSubSourceListIsLoading: boolean;
  duplicateDataId: {
    canNavigate: boolean;
    id: string;
  };
  duplicateDataAltId: {
    canNavigate: boolean;
    id: string;
  };
  filtersPayload: DataManagementFilters;
  dataStatusList: Array<any>;
  isDataStatusListLoading: boolean;
  isBulkConvertToLeadLoading: boolean;
  isConvertToLeadLoading: boolean;
  isDataCustomStatusFilterLoading: boolean;
  isDataTopFiltersLoading: boolean;
  qrCode: string;
  isLoadingHasDataInfo: boolean;
  isLoadingHasDataAltInfo: boolean;
  history: any;
  isHistoryLoading: boolean;
  filterCount: any;
  locations: string[];
  isLocationsLoading: boolean;
  cities: string[];
  isCitiesLoading: boolean;
  excelColumnHeading?: any;
  duplicateData?: string;
  excelUploadedList?: any;
  excelMigrateUploadedList?: any;
  isExcelUploadedListLoading: boolean;
  isExcelMigrateUploadedListLoading: boolean;
  dataExport?: any;
  exportStatus?: any;
  isExportStatusLoading: boolean;
  dataCurrency?: any[];
  dataCustomStatusFilter: DataTopFilter[];
  dataTopFilters: DataTopFilter[];
  isTopFiltersLoadedOnce: boolean;
  dataConversionStatus: ConversionStatus[];
  isBulkUpdateStatusLoading: boolean;
  isBulkAssignLoading: boolean;
  isBulkDeleteLoading: boolean;
  isBulkRestoreLoading: boolean;
  isBulkSourceLoading: boolean;
  currencyListLoading: boolean;
  states: any[];
  isStatesLoading: boolean;
  cardData: any[];
  dataNationality: any[];
  dataNationalityLoading: boolean;
  dataClusterName: any[];
  dataUnitName: any[];
  dataClusterNameLoading: boolean;
  dataUnitNameLoading: boolean;
  dataPostalCode: any[];
  uploadtypeNameList?: any;
  dataPostalCodeLoading: boolean;
  dataLandLine: any[];
  dataLandLineLoading: boolean;
  uploadTypeListIsLoading: boolean;
  dataCommunication: Record<string, any>;
  dataCommunicationIsLoading: boolean;
  bulkCampaignIsLoading: boolean;
  bulkChannelPartnerIsLoading: boolean;
  bulkAgencyIsLoading: boolean;
};

export const initialState: DataManagementState = {
  activeData: {},
  isActiveDataLoading: true,
  allData: [],
  allDataIsLoading: true,
  totalDataCount: 0,
  isFetchingAllData: true,
  dataIsAdding: false,
  dataIsUpdating: false,
  dataSourceList: [],
  isDataSourceListLoading: true,
  dataSubSourceList: [],
  dataSubSourceListIsLoading: true,
  duplicateDataId: {
    canNavigate: false,
    id: null,
  },
  duplicateDataAltId: {
    canNavigate: false,
    id: null,
  },
  filtersPayload: {
    ProspectSearch: null,
    PageNumber: 1,
    PageSize: 10,
    DateType: 0,
    FromDate: null,
    ToDate: null,
    FilterType: 0,
    AssignTo: null,
    AssignedFromIds: null,
    CreatedByIds: null,
    QualifiedByIds: null,
    LastModifiedByIds: null,
    ConvertedByIds: null,
    DeletedByIds: null,
    RestoredByIds: null,
    StatusIds: null,
    SourceIds: null,
    SubSources: null,
    Properties: null,
    PropertyType: null,
    PropertySubType: null,
    Projects: null,
    EnquiryTypes: null,
    FromMinBudget: null,
    ToMinBudget: null,
    FromMaxBudget: null,
    ToMaxBudget: null,
    Currency: null,
    NoOfBHKs: null,
    BHKTypes: null,
    MinCarpetArea: null,
    MaxCarpetArea: null,
    CarpetAreaUnitId: null,
    AgencyNames: null,
    ChannelPartnerNames: null,
    CampaignNames: null,
    CompanyNames: null,
    Designations: null,
    Locations: null,
    Cities: null,
    States: null,
    Profession: null,
    path: "prospect",
    IsWithTeam: null,
    FirstLevelFilter: 1,
    SecondLevelFilter: 0,
    ProspectVisiblity: 0,
    CustomFilterId: null,
    CustomSecondFilterId: null,
    CustomFilterBaseIds: null,
    ReferralEmail: null,
    ReferralName: null,
    ReferralContactNo: null,
    TowerNames: null,
    SubCommunities: null,
    Communities: null,
    Countries: null,
    PostalCodes: null,
    Furnished: null,
    OfferTypes: null,
    Floors: null,
    Baths: null,
    Beds: null,
    MinSaleableArea: null,
    MaxSaleableArea: null,
    SaleableAreaUnitId: null,
    MinBuiltUpArea: null,
    MaxBuiltUpArea: null,
    BuiltUpAreaUnitId: null,
    ClosingManagers: null,
    SourcingManagers: null,
    Localities: null,
    MinNetArea: null,
    MaxNetArea: null,
    MinPropertyArea: null,
    MaxPropertyArea: null,
    UnitNames: null,
    NetAreaUnitId: null,
    PropertyAreaUnitId: null,
    ClusterName: null,
    Nationality: null,
    Purposes: null,
    UploadTypeName: null,
    LandLine: null,
    PropertyToSearch: null,
    PossesionType: null,
    FromPossesionDate: null,
    ToPossesionDate: null,
    MaritalStatuses: null,
    GenderTypes: null,
    DateOfBirth: null,
    AltCountryCode: null,
    CountryCode: null,
    showCommunicationCount: false,
    showFilterCount: false
  },
  dataStatusList: [],
  isDataStatusListLoading: true,
  isBulkConvertToLeadLoading: false,
  isConvertToLeadLoading: false,
  qrCode: null,
  isLoadingHasDataInfo: false,
  isLoadingHasDataAltInfo: false,
  history: {},
  isHistoryLoading: true,
  filterCount: {},
  locations: [],
  isLocationsLoading: true,
  cities: [],
  isCitiesLoading: true,
  states: [],
  isStatesLoading: true,
  excelColumnHeading: {},
  duplicateData: '',
  excelUploadedList: [],
  excelMigrateUploadedList: [],
  dataCurrency: [],
  isExcelUploadedListLoading: true,
  isExcelMigrateUploadedListLoading: true,
  dataExport: null,
  exportStatus: null,
  isExportStatusLoading: true,
  dataCustomStatusFilter: [],
  dataTopFilters: [],
  isDataCustomStatusFilterLoading: false,
  isDataTopFiltersLoading: false,
  isTopFiltersLoadedOnce: false,
  dataConversionStatus: [],
  isBulkUpdateStatusLoading: false,
  isBulkAssignLoading: false,
  isBulkDeleteLoading: false,
  isBulkRestoreLoading: false,
  isBulkSourceLoading: false,
  currencyListLoading: true,
  cardData: [],
  isTowerNamesLoading: true,
  towerNames: [],
  isCommunitiesLoading: true,
  communities: [],
  isSubCommunitiesLoading: true,
  subCommunities: [],
  countries: [],
  isCountriesLoading: true,
  isZonesLoading: true,
  zones: [],
  localities: [],
  isLocalitiesLoading: true,
  dataNationality: [],
  dataNationalityLoading: true,
  dataClusterName: [],
  dataUnitName: [],
  dataClusterNameLoading: true,
  dataUnitNameLoading: true,
  dataPostalCode: [],
  dataPostalCodeLoading: true,
  dataLandLine: [],
  dataLandLineLoading: true,
  uploadTypeListIsLoading: true,
  uploadtypeNameList: [],
  dataCommunication: {},
  dataCommunicationIsLoading: true,
  dataCountryCodeLoading: false,
  dataAltCountryCodeLoading: false,
  dataAltCountryCode: [],
  dataCountryCode: [],
  bulkAgencyIsLoading: true,
  bulkCampaignIsLoading: true,
  bulkChannelPartnerIsLoading: true,
};

export function dataManagementReducer(
  state: DataManagementState = initialState,
  action: Action
): DataManagementState {
  switch (action.type) {
    case DataManagementActionTypes.FETCH_DATA_BY_ID:
      return {
        ...state,
        isActiveDataLoading: true
      };
    case DataManagementActionTypes.FETCH_DATA_BY_ID_SUCCESS:
      const response = (action as FetchDataByIdSuccess).response;
      return {
        ...state,
        activeData: response,
        isActiveDataLoading: false
      };
    case DataManagementActionTypes.FETCH_ALL_DATA:
      return {
        ...state,
        allDataIsLoading: true,
        isDataCustomStatusFilterLoading: true,
        isDataTopFiltersLoading: true
      };
    case DataManagementActionTypes.FETCH_ALL_DATA_SUCCESS:
      const resp = (action as FetchAllDataSuccess).response;
      return {
        ...state,
        allData: resp?.items,
        totalDataCount: resp?.totalCount,
        allDataIsLoading: false,
      };
    case DataManagementActionTypes.UPDATE_ALL_DATA_IS_LOADING:
      return {
        ...state,
        allDataIsLoading: (action as UpdateAllDataIsLoading)?.isLoading
      };
    case DataManagementActionTypes.FETCH_ALL_DATA_COUNT_SUCCESS:
      const count = (action as FetchDataByIdSuccess).response;
      return {
        ...state,
        filterCount: count?.data,
      };
    case DataManagementActionTypes.ADD_NEW_DATA:
      return {
        ...state,
        dataIsAdding: true,
      };
    case DataManagementActionTypes.ADD_NEW_DATA_SUCCESS:
      return {
        ...state,
        dataIsAdding: false,
      };
    case DataManagementActionTypes.UPDATE_DATA:
      return {
        ...state,
        dataIsUpdating: true,
      };
    case DataManagementActionTypes.UPDATE_DATA_SUCCESS:
      return {
        ...state,
        dataIsUpdating: false,
      };
    case DataManagementActionTypes.FETCH_DATA_SOURCE_LIST:
      return {
        ...state,
        isDataSourceListLoading: true
      };
    case DataManagementActionTypes.FETCH_DATA_SOURCE_LIST_SUCCESS:
      return {
        ...state,
        dataSourceList: (action as FetchDataSourceListSuccess).response,
        isDataSourceListLoading: false
      };
    case DataManagementActionTypes.FETCH_DATA_SUB_SOURCE_LIST:
      return {
        ...state,
        dataSubSourceListIsLoading: true
      };
    case DataManagementActionTypes.FETCH_DATA_SUB_SOURCE_LIST_SUCCESS:
      const convertedData: any = {};
      (action as FetchDataSourceListSuccess).response?.forEach((item: any) => {
        const sourceName = item.source.displayName;
        const subSource = item.subSource;
        convertedData[sourceName] = subSource;
      });
      return {
        ...state,
        dataSubSourceList: convertedData,
        dataSubSourceListIsLoading: false
      };
    case DataManagementActionTypes.FETCH_DATA_ID_WITH_CONTACT_NO:
      return {
        ...state,
        isLoadingHasDataInfo: true,
      };
    case DataManagementActionTypes.FETCH_DATA_ID_WITH_CONTACT_NO_SUCCESS:
      return {
        ...state,
        duplicateDataId: (action as FetchDataIdWithContactNoSuccess).response,
        isLoadingHasDataInfo: false,
      };
    case DataManagementActionTypes.FETCH_DATA_ID_WITH_ALT_NO:
      return {
        ...state,
        isLoadingHasDataAltInfo: true,
      };
    case DataManagementActionTypes.FETCH_DATA_ID_WITH_ALT_NO_SUCCESS:
      return {
        ...state,
        duplicateDataAltId: (action as FetchDataIdWithContactNoSuccess).response,
        isLoadingHasDataAltInfo: false,
      };
    case DataManagementActionTypes.UPDATE_DATA_FILTER_PAYLOAD:
      return {
        ...state,
        filtersPayload: {
          ...state.filtersPayload,
          ...((action as UpdateDataFilterPayload).filter),
        },
      };
    case DataManagementActionTypes.FETCH_DATA_STATUS:
      return {
        ...state,
        isDataStatusListLoading: true
      };
    case DataManagementActionTypes.FETCH_DATA_STATUS_SUCCESS:
      return {
        ...state,
        dataStatusList: (action as FetchDataStatusSuccess).response,
        isDataStatusListLoading: false
      };
    case DataManagementActionTypes.BULK_CONVERT_TO_LEAD:
      return {
        ...state,
        isBulkConvertToLeadLoading: true,
      };
    case DataManagementActionTypes.BULK_CONVERT_TO_LEAD_SUCCESS:
      return {
        ...state,
        isBulkConvertToLeadLoading: false,
      };
    case DataManagementActionTypes.CONVERT_TO_LEAD:
      return {
        ...state,
        isConvertToLeadLoading: true,
      };
    case DataManagementActionTypes.CONVERT_TO_LEAD_SUCCESS:
      return {
        ...state,
        isConvertToLeadLoading: false,
      };
    case DataManagementActionTypes.FETCH_DATA_QR_CODE_SUCCESS:
      return {
        ...state,
        qrCode: (action as FetchDataQRCodeSuccess).response,
      };
    case DataManagementActionTypes.COMMUNICATION_DATA_COUNT_SUCCESS:
      const input = action as CommunicationDataCountSuccess;
      return {
        ...state,
        allData: state.allData.map((data) => {
          if (data?.id === input?.id) {
            const contactRecords = {
              ...data.contactRecords,
              WhatsApp:
                (data.contactRecords?.WhatsApp || 0) +
                (input?.payload?.contactType === 0 ? 1 : 0),
              Call:
                (data.contactRecords?.Call || 0) +
                (input?.payload?.contactType === 1 ? 1 : 0),
              Email:
                (data.contactRecords?.Email || 0) +
                (input?.payload?.contactType === 2 ? 1 : 0),
            };

            return {
              ...data,
              contactRecords,
            };
          }
          return data;
        }),
      };
    case DataManagementActionTypes.FETCH_DATA_HISTORY_BY_ID:
      return {
        ...state,
        history: {},
        isHistoryLoading: true
      };
    case DataManagementActionTypes.FETCH_DATA_HISTORY_BY_ID_SUCCESS:
      const history = (action as FetchDataHistoryByIdSuccess).response;
      return {
        ...state,
        history,
        isHistoryLoading: false
      };
    case DataManagementActionTypes.FETCH_DATA_LOCATIONS:
      return {
        ...state,
        isLocationsLoading: true
      };
    case DataManagementActionTypes.FETCH_DATA_LOCATIONS_SUCCESS:
      return {
        ...state,
        locations: (action as FetchDataLocationsSuccess).response,
        isLocationsLoading: false
      };
    case DataManagementActionTypes.FETCH_DATA_CITIES:
      return {
        ...state,
        isCitiesLoading: true
      };
    case DataManagementActionTypes.FETCH_DATA_CITIES_SUCCESS:
      return {
        ...state,
        cities: (action as FetchDataCitiesSuccess).response,
        isCitiesLoading: false
      };
    case DataManagementActionTypes.FETCH_DATA_STATES:
      return {
        ...state,
        isStatesLoading: true
      };
    case DataManagementActionTypes.FETCH_DATA_STATES_SUCCESS:
      return {
        ...state,
        states: (action as FetchDataStatesSuccess).response,
        isStatesLoading: false
      };
    case DataManagementActionTypes.FETCH_DATA_COUNTRIES:
      return {
        ...state,
        isCountriesLoading: true
      };
    case DataManagementActionTypes.FETCH_DATA_COUNTRIES_SUCCESS:
      return {
        ...state,
        countries: (action as FetchDataCountriesSuccess).response,
        isCountriesLoading: false
      };
    case DataManagementActionTypes.FETCH_DATA_SUB_COMMUNITIES:
      return {
        ...state,
        isSubCommunitiesLoading: true
      };
    case DataManagementActionTypes.FETCH_DATA_SUB_COMMUNITIES_SUCCESS:
      return {
        ...state,
        subCommunities: (action as FetchDataSubCommunitiesSuccess).response,
        isSubCommunitiesLoading: false
      };
    case DataManagementActionTypes.FETCH_DATA_COMMUNITIES:
      return {
        ...state,
        isCommunitiesLoading: true
      };
    case DataManagementActionTypes.FETCH_DATA_COMMUNITIES_SUCCESS:
      return {
        ...state,
        communities: (action as FetchDataCommunitiesSuccess).response,
        isCommunitiesLoading: false
      };
    case DataManagementActionTypes.FETCH_DATA_TOWER:
      return {
        ...state,
        isTowerNamesLoading: true
      };
    case DataManagementActionTypes.FETCH_DATA_TOWER_SUCCESS:
      return {
        ...state,
        towerNames: (action as FetchDataTowerNamesSuccess).response,
        isTowerNamesLoading: false
      };
    case DataManagementActionTypes.FETCH_DATA_ZONES:
      return {
        ...state,
        isZonesLoading: true
      };
    case DataManagementActionTypes.FETCH_DATA_ZONES_SUCCESS:
      return {
        ...state,
        zones: (action as FetchDataZonesSuccess).response,
        isZonesLoading: false
      };
    case DataManagementActionTypes.FETCH_DATA_LOCALITIES:
      return {
        ...state,
        isLocalitiesLoading: true
      };
    case DataManagementActionTypes.FETCH_DATA_LOCALITIES_SUCCESS:
      return {
        ...state,
        localities: (action as FetchDataLocalitiesSuccess).response,
        isLocalitiesLoading: false
      };
    case DataManagementActionTypes.EXCEL_UPLOAD_SUCCESS:
      return {
        ...state,
        duplicateData: (action as ExcelUploadSuccess).resp,
      };
    case DataManagementActionTypes.DATA_EXCEL_UPLOAD_SUCCESS:
      return {
        ...state,
        excelColumnHeading: (action as DataExcelUploadSuccess).resp,
      };
    case DataManagementActionTypes.FETCH_EXCEL_UPLOADED_LIST:
      return {
        ...state,
        isExcelUploadedListLoading: true
      };
    case DataManagementActionTypes.FETCH_EXCEL_UPLOADED_LIST_SUCCESS:
      return {
        ...state,
        excelUploadedList: (action as FetchDataExcelUploadedListSuccess)
          .response,
        isExcelUploadedListLoading: false
      };
    case DataManagementActionTypes.FETCH_DATA_EXPORT_SUCCESS:
      return {
        ...state,
        dataExport: (action as FetchDataExportSuccess).response,
      };
    case DataManagementActionTypes.FETCH_DATA_EXPORT_STATUS:
      return {
        ...state,
        isExportStatusLoading: true
      };
    case DataManagementActionTypes.FETCH_DATA_EXPORT_STATUS_SUCCESS:
      return {
        ...state,
        exportStatus: (action as FetchDataExportStatusSuccess).response,
        isExportStatusLoading: false
      };
    case DataManagementActionTypes.FETCH_DATA_CONVERSION_STATUS_SUCCESS:
      return {
        ...state,
        dataConversionStatus: (action as FetchDataConversionStatusSuccess).response,
      };
    case DataManagementActionTypes.FETCH_DATA_CUSTOM_STATUS_FILTER:
      return {
        ...state,
        isDataCustomStatusFilterLoading: true,
      };
    case DataManagementActionTypes.FETCH_DATA_CUSTOM_STATUS_FILTER_SUCCESS:
      return {
        ...state,
        dataCustomStatusFilter: (action as FetchDataCustomStatusFilterSuccess)?.response,
        isDataCustomStatusFilterLoading: false,
      };
    case DataManagementActionTypes.FETCH_DATA_TOP_FILTERS:
      return {
        ...state,
        isDataTopFiltersLoading: true,
      };
    case DataManagementActionTypes.FETCH_DATA_TOP_FILTERS_SUCCESS:
      return {
        ...state,
        dataTopFilters: (action as FetchDataTopFiltersSuccess)?.response,
        isDataTopFiltersLoading: false,
      };
    case DataManagementActionTypes.UPDATE_DATA_TOP_FILTER_LOADED_ONCE:
      return {
        ...state,
        isTopFiltersLoadedOnce: (action as UpdateDataTopFilterLoadedOnce)?.isLoadedOnce,
      };
    case DataManagementActionTypes.BULK_UPDATE_STATUS:
      return {
        ...state,
        isBulkUpdateStatusLoading: true,
      };
    case DataManagementActionTypes.BULK_UPDATE_STATUS_SUCCESS:
      return {
        ...state,
        isBulkUpdateStatusLoading: false,
      };
    case DataManagementActionTypes.BULK_SOURCE:
      return {
        ...state,
        isBulkSourceLoading: true,
      };
    case DataManagementActionTypes.BULK_SOURCE_SUCCESS:
      return {
        ...state,
        isBulkSourceLoading: false,
      };
    case DataManagementActionTypes.BULK_ASSIGN_DATA:
      return {
        ...state,
        isBulkAssignLoading: true,
      };
    case DataManagementActionTypes.BULK_ASSIGN_DATA_SUCCESS:
      return {
        ...state,
        isBulkAssignLoading: false,
      };
    case DataManagementActionTypes.BULK_DELETE_DATA:
      return {
        ...state,
        isBulkDeleteLoading: true,
      };
    case DataManagementActionTypes.BULK_DELETE_DATA_SUCCESS:
      return {
        ...state,
        isBulkDeleteLoading: false,
      };
    case DataManagementActionTypes.RESTORE_DATA:
      return {
        ...state,
        isBulkRestoreLoading: true,
      };
    case DataManagementActionTypes.RESTORE_DATA_SUCCESS:
      return {
        ...state,
        isBulkRestoreLoading: false,
      };
    case DataManagementActionTypes.FETCH_DATA_CURRENCY_LIST:
      return {
        ...state,
        currencyListLoading: true,
      };
    case DataManagementActionTypes.FETCH_DATA_CURRENCY_LIST_SUCCESS:
      return {
        ...state,
        dataCurrency: (action as FetchDataCurrencySuccess).response,
        currencyListLoading: false,
      };
    case DataManagementActionTypes.FETCH_MIGRATE_EXCEL_UPLOADED_LIST:
      return {
        ...state,
        isExcelMigrateUploadedListLoading: true
      };
    case DataManagementActionTypes.FETCH_MIGRATE_EXCEL_UPLOADED_LIST_SUCCESS:
      return {
        ...state,
        excelMigrateUploadedList: (action as FetchDataMigrateExcelUploadedListSuccess)
          .response,
        isExcelMigrateUploadedListLoading: false
      };
    case DataManagementActionTypes.CLEAR_DATA:
      return {
        ...state,
        allData: [],
      };
    case DataManagementActionTypes.PROSPECT_CARD_DATA:
      return {
        ...state,
        cardData: (action as AddCardData).data
      }
    case DataManagementActionTypes.FETCH_DATA_NATIONALITY:
      return {
        ...state,
        dataNationalityLoading: true,
      }
    case DataManagementActionTypes.FETCH_DATA_NATIONALITY_SUCCESS:
      return {
        ...state,
        dataNationality: (action as FetchDataNationalitySuccess).resp,
        dataNationalityLoading: false,
      }
    case DataManagementActionTypes.FETCH_DATA_CLUSTER_NAME:
      return {
        ...state,
        dataClusterNameLoading: true,
      }
    case DataManagementActionTypes.FETCH_DATA_CLUSTER_NAME_SUCCESS:
      return {
        ...state,
        dataClusterName: (action as FetchDataClusterNameSuccess).resp,
        dataClusterNameLoading: false,
      }
    case DataManagementActionTypes.FETCH_DATA_UNIT_NAME:
      return {
        ...state,
        dataUnitNameLoading: true,
      }
    case DataManagementActionTypes.FETCH_DATA_UNIT_NAME_SUCCESS:
      return {
        ...state,
        dataUnitName: (action as FetchDataUnitNameSuccess).resp,
        dataUnitNameLoading: false,
      }
    case DataManagementActionTypes.FETCH_DATA_POSTAL_CODE:
      return {
        ...state,
        dataPostalCodeLoading: true,
      }
    case DataManagementActionTypes.FETCH_DATA_POSTAL_CODE_SUCCESS:
      return {
        ...state,
        dataPostalCode: (action as FetchDataPostalCodeSuccess).resp,
        dataPostalCodeLoading: false,
      }
    case DataManagementActionTypes.FETCH_DATA_LANDLINE:
      return {
        ...state,
        dataLandLineLoading: true,
      }
    case DataManagementActionTypes.FETCH_DATA_LANDLINE_SUCCESS:
      return {
        ...state,
        dataLandLine: (action as FetchDataLandLineSuccess).resp,
        dataLandLineLoading: false,
      }
    case DataManagementActionTypes.FETCH_UPLOADTYPENAME_LIST:
      return {
        ...state,
        uploadTypeListIsLoading: true,
      };
    case DataManagementActionTypes.FETCH_UPLOADTYPENAME_LIST_SUCCESS:
      return {
        ...state,
        uploadtypeNameList: (action as FetchUploadTypeNameListSuccess).response,
        uploadTypeListIsLoading: false,
      };
    case DataManagementActionTypes.FETCH_DATA_COMMUNICATION_BY_IDS:
      return {
        ...state,
        dataCommunicationIsLoading: true,
      };
    case DataManagementActionTypes.FETCH_DATA_COMMUNICATION_BY_IDS_SUCCESS:
      return {
        ...state,
        dataCommunication: {
          ...state?.dataCommunication,
          ...(action as FetchDataCommunicationByIdsSuccess)?.resp,
        },
        dataCommunicationIsLoading: false,
      };
    case DataManagementActionTypes.FETCH_DATA_COUNTRY_CODE:
      return {
        ...state,
        dataCountryCodeLoading: true,
      };
    case DataManagementActionTypes.FETCH_DATA_COUNTRY_CODE_SUCCESS:
      return {
        ...state,
        dataCountryCode: (action as FetchDataCountryCodeSuccess).resp,
        dataCountryCodeLoading: false,
      };
    case DataManagementActionTypes.FETCH_DATA_ALT_COUNTRY_CODE:
      return {
        ...state,
        dataAltCountryCodeLoading: true,
      };
    case DataManagementActionTypes.FETCH_DATA_ALT_COUNTRY_CODE_SUCCESS:
      return {
        ...state,
        dataAltCountryCode: (action as FetchDataAltCountryCodeSuccess).resp,
        dataAltCountryCodeLoading: false,
      };
    default:
      return state;
  }
}
// TODO: uncomment accordingly when data-binding
export const selectFeature = (state: AppState) => state.dataManagement;

export const getDataIsAdding = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state?.dataIsAdding;
  }
);

export const getDataIsUpdating = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state?.dataIsUpdating;
  }
);

export const getDataSourceList = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state?.dataSourceList;
  }
);

export const getDataSourceListIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state?.isDataSourceListLoading;
  }
);

export const getDataSubSourceList = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state?.dataSubSourceList;
  }
);

export const getDataSubSourceListIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state?.dataSubSourceListIsLoading;
  }
);

export const getDuplicateDataId = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state?.duplicateDataId;
  }
);

export const getActiveData = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state?.activeData;
  }
);

export const getActiveDataIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state?.isActiveDataLoading;
  }
);

export const getAllData = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state?.allData;
  }
);

export const getAllDataIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state?.allDataIsLoading;
  }
);

export const getTotalDataCount = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state?.totalDataCount;
  }
);

export const getDataFiltersPayload = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state.filtersPayload;
  }
);

export const getDataStatusList = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state.dataStatusList;
  }
);

export const getDataStatusListIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state.isDataStatusListLoading;
  }
);

export const getIsBulkConvertToLeadLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state.isBulkConvertToLeadLoading;
  }
);

export const getIsConvertToLeadLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state.isConvertToLeadLoading;
  }
);

export const getDataQRCode = createSelector(
  selectFeature,
  (state: DataManagementState) => state.qrCode
);

export const getDataHistory = createSelector(
  selectFeature,
  (state: DataManagementState) => state.history
);

export const getDataHistoryIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isHistoryLoading
);

export const getDataFilterCountHistory = createSelector(
  selectFeature,
  (state: DataManagementState) => state.filterCount
);

export const getDataLocations = createSelector(
  selectFeature,
  (state: DataManagementState) => state.locations
);

export const getDataLocationsIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isLocationsLoading
);

export const getDataCities = createSelector(
  selectFeature,
  (state: DataManagementState) => state.cities
);

export const getDataCitiesIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isCitiesLoading
);

export const getDataStates = createSelector(
  selectFeature,
  (state: DataManagementState) => state.states
);

export const getDataStatesIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isStatesLoading
);


export const getDataCountries = createSelector(
  selectFeature,
  (state: DataManagementState) => state.countries
);

export const getDataCountriesIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isCountriesLoading
);
export const getDataSubCommunities = createSelector(
  selectFeature,
  (state: DataManagementState) => state.subCommunities
);

export const getDataSubCommunitiesIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isSubCommunitiesLoading
);
export const getchDataCommunities = createSelector(
  selectFeature,
  (state: DataManagementState) => state.communities
);

export const getDataCommunitiesIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isCommunitiesLoading
);

export const getDataTowerNames = createSelector(
  selectFeature,
  (state: DataManagementState) => state.towerNames
);

export const getDataTowerNamesIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isTowerNamesLoading
);

export const getDataZones = createSelector(
  selectFeature,
  (state: DataManagementState) => state.zones
);

export const getDataZonesIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isZonesLoading
);

export const getDataLocalities = createSelector(
  selectFeature,
  (state: DataManagementState) => state.localities
);

export const getDataLocalitiesIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isLocalitiesLoading
);


export const getBulkUpdateStatusIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isBulkUpdateStatusLoading
);

export const getBulkAssignIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isBulkAssignLoading
);

export const getBulkDeleteIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isBulkDeleteLoading
);

export const getBulkRestoreIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isBulkRestoreLoading
);

export const getBulkSourceIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isBulkSourceLoading
);

export const getDataColumnHeadings = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state.excelColumnHeading;
  }
);

export const getDuplicateData = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    return state.duplicateData;
  }
);

export const getDataExcelUploadedList = createSelector(
  selectFeature,
  (state: DataManagementState) => state.excelUploadedList
);

export const getDataExcelUploadedListIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isExcelUploadedListLoading
);

export const getDataExport = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataExport
);

export const getDataExportStatus = createSelector(
  selectFeature,
  (state: DataManagementState) => state.exportStatus
);

export const getDataExportStatusIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isExportStatusLoading
);

export const getDataConversionStatus = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataConversionStatus
);

export const getDataTopFilters = createSelector(
  selectFeature,
  (state: DataManagementState) => {
    const topFiltersObj = {
      dataTopLevelFilters: [...state?.dataTopFilters],
      isTopFiltersLoadedOnce: state?.isTopFiltersLoadedOnce
    };
    return topFiltersObj;
  }
);

export const getDataCustomStatusFilter = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataCustomStatusFilter
);

export const getIsDataCustomStatusFilterLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isDataCustomStatusFilterLoading
);

export const getIsDataTopFiltersLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isDataTopFiltersLoading
);

export const getDataCurrencyList = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataCurrency
);

export const getDataCurrencyListIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.currencyListLoading
);

export const getDataMigrateExcelUploadedList = createSelector(
  selectFeature,
  (state: DataManagementState) => state.excelMigrateUploadedList
);

export const getDataMigrateExcelUploadedListIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.isExcelMigrateUploadedListLoading
);

export const getCardData = createSelector(
  selectFeature,
  (state: DataManagementState) => state.cardData
)

export const getDataNationality = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataNationality
)

export const getDataNationalityLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataNationalityLoading
)

export const getDataClusterName = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataClusterName
)

export const getDataClusterNameLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataClusterNameLoading
)

export const getDataUnitName = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataUnitName
)

export const getDataUnitNameLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataUnitNameLoading
)

export const getDataPostalCode = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataPostalCode
)

export const getDataPostalCodeLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataPostalCodeLoading
)

export const getDataLandLine = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataLandLine
)

export const getDataLandLineLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataLandLineLoading
)

export const getUploadTypeNameListIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.uploadTypeListIsLoading
);

export const getUploadTypeNameList = createSelector(
  selectFeature,
  (state: DataManagementState) => state.uploadtypeNameList
);

export const getDataCommunication = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataCommunication
);

export const getDataCommunicationIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataCommunicationIsLoading
);


export const getDataCountryCode = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataCountryCode
)

export const getDataCountryCodeLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataCountryCodeLoading
);

export const getDataAltCountryCode = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataAltCountryCode
)

export const getDataAltCountryCodeLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.dataAltCountryCodeLoading
)

export const getBulkAgencyIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.bulkAgencyIsLoading
);

export const getBulkCPIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.bulkChannelPartnerIsLoading
);

export const getBulkCampaignIsLoading = createSelector(
  selectFeature,
  (state: DataManagementState) => state.bulkCampaignIsLoading
);