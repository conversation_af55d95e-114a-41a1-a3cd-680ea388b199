import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store, select } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalService } from 'ngx-bootstrap/modal';
import { catchError, from, map, mergeMap, of, throwError } from 'rxjs';
import { switchMap, take, tap, withLatestFrom } from 'rxjs/operators';

import { CloseModal, OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import { DataManagementService } from 'src/app/services/controllers/data-management.service';
import { CommonService } from 'src/app/services/shared/common.service';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import {
  AddNewData,
  AddNewDataSuccess,
  AssignData,
  BulkAgency,
  BulkAgencySuccess,
  BulkAssignData,
  BulkAssignDataSuccess,
  BulkCampaign,
  BulkCampaignSuccess,
  BulkChannelPartner,
  BulkChannelPartnerSuccess,
  BulkConvertToLead,
  BulkConvertToLeadSuccess,
  BulkDeleteData,
  BulkDeleteDataSuccess,
  BulkSource,
  BulkSourceSuccess,
  BulkStatusUpdate,
  BulkStatusUpdateSuccess,
  CheckDuplicateAndConvertToLead,
  CommunicationBulkDataCount,
  CommunicationBulkDataMessage,
  CommunicationBulkDataMessageSuccess,
  CommunicationDataCount,
  CommunicationDataCountSuccess,
  CommunicationDataMessage,
  CommunicationDataMessageSuccess,
  ConvertToLead,
  ConvertToLeadSuccess,
  DataExcelUpload,
  DataExcelUploadSuccess,
  DataManagementActionTypes,
  DataMigrateUploadMappedColumns,
  DataUploadMappedColumns,
  ExportData,
  ExportDataSuccess,
  FetchAllData,
  FetchAllDataCount,
  FetchAllDataCountSuccess,
  FetchAllDataSuccess,
  FetchDataAltCountryCode,
  FetchDataAltCountryCodeSuccess,
  FetchDataById,
  FetchDataByIdSuccess,
  FetchDataCities,
  FetchDataCitiesSuccess,
  FetchDataClusterName,
  FetchDataClusterNameSuccess,
  FetchDataCommunicationByIds,
  FetchDataCommunicationByIdsSuccess,
  FetchDataCommunities,
  FetchDataCommunitiesSuccess,
  FetchDataConversionStatus,
  FetchDataConversionStatusSuccess,
  FetchDataCountries,
  FetchDataCountriesSuccess,
  FetchDataCountryCode,
  FetchDataCountryCodeSuccess,
  FetchDataCurrency,
  FetchDataCurrencySuccess,
  FetchDataCustomStatusFilter,
  FetchDataCustomStatusFilterSuccess,
  FetchDataExcelUploadedList,
  FetchDataExcelUploadedListSuccess,
  FetchDataExportStatus,
  FetchDataExportStatusSuccess,
  FetchDataHistoryById,
  FetchDataHistoryByIdSuccess,
  FetchDataIdWithAltNo,
  FetchDataIdWithAltNoSuccess,
  FetchDataIdWithContactNo,
  FetchDataIdWithContactNoSuccess,
  FetchDataLandLine,
  FetchDataLandLineSuccess,
  FetchDataLocalities,
  FetchDataLocalitiesSuccess,
  FetchDataLocations,
  FetchDataLocationsSuccess,
  FetchDataMigrateExcelUploadedList,
  FetchDataMigrateExcelUploadedListSuccess,
  FetchDataNationality,
  FetchDataNationalitySuccess,
  FetchDataPostalCode,
  FetchDataPostalCodeSuccess,
  FetchDataQRCode,
  FetchDataQRCodeSuccess,
  FetchDataSourceList,
  FetchDataSourceListSuccess,
  FetchDataStates,
  FetchDataStatesSuccess,
  FetchDataStatus,
  FetchDataStatusSuccess,
  FetchDataSubCommunities,
  FetchDataSubCommunitiesSuccess,
  FetchDataSubSourceList,
  FetchDataSubSourceListSuccess,
  FetchDataTopFilters,
  FetchDataTopFiltersSuccess,
  FetchDataTowerNames,
  FetchDataTowerNamesSuccess,
  FetchDataUnitName,
  FetchDataUnitNameSuccess,
  FetchDataZones,
  FetchDataZonesSuccess,
  FetchUploadTypeNameList,
  FetchUploadTypeNameListSuccess,
  PermanentDeleteData,
  PermanentDeleteDataSuccess,
  RestoreData,
  RestoreDataSuccess,
  UpdateData,
  UpdateDataFilterPayload,
  UpdateDataNotes,
  UpdateDataSuccess,
  UpdateStatus,
} from './data-management.actions';
import { getAllData, getDataFiltersPayload } from './data-management.reducer';

@Injectable()
export class DataManagementEffects {
  getDataTopFiltersAllAndMyData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_CUSTOM_STATUS_FILTER),
      withLatestFrom(
        this._store.pipe(
          select((state) => state?.dataManagement?.filtersPayload)
        )
      ),
      switchMap(
        ([action, filtersFromState]: [FetchDataCustomStatusFilter, any]) => {
          const filters = Object.keys(action.filtersPayload).length
            ? action.filtersPayload
            : filtersFromState;
          const filterPayload: any = {
            ...filters,
            path: 'prospect/custom-filters-count-level1',
          };

          return this.commonService
            .getModuleListByAdvFilter(filterPayload)
            .pipe(
              map((resp: any) => {
                const responseData = resp?.data;
                localStorage.setItem('dataLevelFilter', JSON.stringify(responseData));
                return new FetchDataCustomStatusFilterSuccess(responseData);
              }),
              catchError((err) => of(new OnError(err)))
            );
        }
      )
    )
  );

  getDataTopFilters$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_TOP_FILTERS),
      withLatestFrom(
        this._store.pipe(
          select((state) => state?.dataManagement?.filtersPayload)
        )
      ),
      switchMap(([action, filtersFromState]: [FetchDataTopFilters, any]) => {
        const filters = Object.keys(action.filtersPayload).length
          ? action.filtersPayload
          : filtersFromState;
        const filterPayload: any = {
          ...filters,
          path: 'prospect/basefilter-count',
        };
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            localStorage.setItem('dataTopLevelFilter', JSON.stringify(resp?.items));
            return new FetchDataTopFiltersSuccess(resp?.items);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAllData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_ALL_DATA),
      map((action: FetchAllData) => action),
      withLatestFrom(
        this._store.pipe(
          select((state) => state?.dataManagement?.filtersPayload)
        )
      ),
      switchMap(([action, filtersFromState]: any) => {
        const filters = Object.keys(action.filtersPayload).length
          ? action.filtersPayload
          : filtersFromState;
        const filterPayload: any = {
          ...filters,
          path: 'prospect/custom-filters',
        };
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          tap((resp: any) => this.dispatchDataBatches(resp?.items, filters?.showCommunicationCount)),
          switchMap((resp: any) => {
            if (resp.succeeded && resp?.items?.length > 0) {
              if (filters?.showFilterCount) {
                this._store.dispatch(
                  new FetchDataCustomStatusFilter(filterPayload)
                );
                this._store.dispatch(new FetchDataTopFilters(filterPayload));
              }
              return of(new FetchAllDataSuccess(resp));
            } else {
              const previousPageNumber = filterPayload?.PageNumber || 1;
              const retryPayload = {
                ...filterPayload,
                PageNumber: previousPageNumber > 1 ? previousPageNumber - 1 : 1,
              };
              this._store.dispatch(new UpdateDataFilterPayload(retryPayload));
              return this.commonService
                .getModuleListByAdvFilter(retryPayload)
                .pipe(
                  map((retryResp: any) => {
                    if (retryPayload?.showFilterCount) {
                      this._store.dispatch(
                        new FetchDataCustomStatusFilter(retryPayload)
                      );
                      this._store.dispatch(new FetchDataTopFilters(retryPayload));
                    }
                    return new FetchAllDataSuccess(retryResp);
                  }),
                  catchError((err) => of(new OnError(err)))
                );
            }
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );


  getAllDataCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_ALL_DATA_COUNT),
      map((action: FetchAllDataCount) => action),
      switchMap((action: any) => {
        const filterPayload: any = { ...action.filtersPayload };
        filterPayload.path = 'prospect/count';
        return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchAllDataCountSuccess(resp);
            }
            return new FetchAllDataCountSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_BY_ID),
      map((action: FetchDataById) => action),
      switchMap((action: any) => {
        return this.api.getDataById(action.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataByIdSuccess(resp.data);
            }
            return new FetchDataByIdSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.ADD_NEW_DATA),
      map((action: AddNewData) => action),
      switchMap((action: any) => {
        return this.api.addNewData(action.payload).pipe(
          map((resp: any) => {
            this.router.navigate(['data']);
            return new AddNewDataSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.UPDATE_DATA),
      map((action: UpdateData) => action),
      switchMap((action: any) => {
        return this.api.updateData(action.id, action.payload).pipe(
          map((resp: any) => {
            this.router.navigate(['data']);
            return new UpdateDataSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataSource$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_SOURCE_LIST),
      map((action: FetchDataSourceList) => action),
      switchMap((action: any) => {
        return this.api.getSourceList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataSourceListSuccess(resp?.data);
            }
            return new FetchDataSourceListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataSubSource$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_SUB_SOURCE_LIST),
      map((action: FetchDataSubSourceList) => action),
      switchMap((action: any) => {
        return this.api.getSubSourceList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataSubSourceListSuccess(resp?.data);
            }
            return new FetchDataSubSourceListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataIdWithContact$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_ID_WITH_CONTACT_NO),
      map((action: FetchDataIdWithContactNo) => action),
      switchMap((action: any) => {
        return this.api.getDataIdWithContactNo(action?.contactNo).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataIdWithContactNoSuccess(resp?.data);
            }
            return new FetchDataIdWithContactNoSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataIdWithAlt$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_ID_WITH_ALT_NO),
      map((action: FetchDataIdWithAltNo) => action),
      switchMap((action: any) => {
        return this.api.getDataIdWithContactNo(action?.contactNo).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataIdWithAltNoSuccess(resp?.data);
            }
            return new FetchDataIdWithAltNoSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  communicationDataCount$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(DataManagementActionTypes.COMMUNICATION_DATA_COUNT),
        map((action: CommunicationDataCount) => action),
        switchMap((data: any) => {
          return this.api.communicationDataCount(data?.id, data?.payload).pipe(
            map((resp: any) => {
              this._store.dispatch(
                new CommunicationDataCountSuccess(data?.id, data?.payload)
              );
            }),
            catchError((err) => of(new OnError(err)))
          );
        })
      ),
    { dispatch: false }
  );

  communicationBulkDataCount$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(DataManagementActionTypes.COMMUNICATION_BULK_DATA_COUNT),
        map((action: CommunicationBulkDataCount) => action),
        switchMap((data: any) => {
          return this.api.communicationBulkDataCount(data?.payload).pipe(
            map((resp: any) => {
              data?.payload?.ids?.forEach((prospect: any) => {
                var currentData: any;
                this._store
                  .select(getAllData)
                  .pipe(take(1))
                  .subscribe((filteredProspect: any) => {
                    currentData = filteredProspect?.filter(
                      (filteredData: any) => filteredData?.id === prospect
                    )?.[0];
                    currentData = {
                      ...currentData,
                      contactType: data?.payload?.contactType,
                    };
                  });
                this._store.dispatch(
                  new CommunicationDataCountSuccess(prospect, currentData)
                );
              });
            }),
            catchError((err) => of(new OnError(err)))
          );
        })
      ),
    { dispatch: false }
  );

  restoreData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.RESTORE_DATA),
      map((action: RestoreData) => action),
      switchMap((data: any) => {
        return this.api.restoreData({ ids: data?.ids }).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Restored Successfully');
              this._store.dispatch(new RestoreDataSuccess());
              return new FetchAllData();
            }
            this._store.dispatch(new RestoreDataSuccess());
            return new FetchAllData();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkDeleteData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.BULK_DELETE_DATA),
      map((action: BulkDeleteData) => action),
      switchMap((data: any) => {
        return this.api.bulkDeleteData(data?.ids).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Deleted Successfully');
              this._store.dispatch(new BulkDeleteDataSuccess());
              return new FetchAllData();
            }
            this._store.dispatch(new BulkDeleteDataSuccess());
            return new FetchAllData();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkAssignData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.BULK_ASSIGN_DATA),
      map((action: BulkAssignData) => action),
      withLatestFrom(this._store.select(getDataFiltersPayload)),
      switchMap(([data, filters]: [BulkAssignData, any]) =>
        this.api.bulkReassign(data?.payload).pipe(
          mergeMap((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Reassigned Successfully');
            }
            return from([
              new FetchAllData(filters),
              new BulkAssignDataSuccess(),
            ]);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  assignData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.ASSIGN_DATA),
      map((action: AssignData) => action),
      withLatestFrom(this._store.select(getDataFiltersPayload)),
      switchMap(([data, filters]: [AssignData, any]) =>
        this.api.bulkReassign(data?.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Assigned Successfully');
              return new FetchAllData(filters);
            }
            return new FetchAllData(filters);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  getDataStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_STATUS),
      map((action: FetchDataStatus) => action),
      switchMap((action: any) => {
        return this.api.getDataStatuses().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataStatusSuccess(resp?.data);
            }
            return new FetchDataStatusSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkStatusUpdate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.BULK_UPDATE_STATUS),
      map((action: BulkStatusUpdate) => action),
      withLatestFrom(this._store.select(getDataFiltersPayload)),
      switchMap(([data, filters]: [BulkStatusUpdate, any]) =>
        this.api.bulkUpdateStatus(data?.payload).pipe(
          mergeMap((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Status Updated Successfully');
            }
            return from([
              new FetchAllData(filters),
              new BulkStatusUpdateSuccess(),
            ]);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  bulkSource$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.BULK_SOURCE),
      map((action: BulkSource) => action),
      withLatestFrom(this._store.select(getDataFiltersPayload)),
      switchMap(([data, filters]: [BulkSource, any]) =>
        this.api.bulkSource(data?.payload).pipe(
          mergeMap((resp: any) => {
            if (resp.succeeded) {
              this._store.dispatch(new FetchDataSubSourceList());
              this._notificationService.success('Source Updated Successfully');
            }
            return from([new FetchAllData(filters), new BulkSourceSuccess()]);
          }),
          catchError((err) => of(new OnError(err)))
        )
      )
    )
  );

  bulkConvertToLead$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.BULK_CONVERT_TO_LEAD),
      map((action: BulkConvertToLead) => action),
      switchMap((data: any) => {
        return this.api.bulkConvertToLead(data?.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._store.dispatch(new BulkConvertToLeadSuccess());
              return new FetchAllData();
            }
            return new FetchAllData();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  convertToLead$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.CONVERT_TO_LEAD),
      map((action: ConvertToLead) => action),
      switchMap((data: any) => {
        return this.api.convertToLead(data?.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._store.dispatch(new ConvertToLeadSuccess());
              return new FetchAllData();
            }
            return new FetchAllData();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  checkDuplicateAndConvertToLead$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(DataManagementActionTypes.CHECK_DUPLICATE_AND_CONVERT_TO_LEAD),
        map((action: CheckDuplicateAndConvertToLead) => action),
        switchMap((data: any) => {
          return this.api
            .checkDuplicateBeforeConvertToLead(data?.payload, data?.number)
            .pipe(
              map((resp: any) => {
                if (!resp.succeeded)
                  return this._store.dispatch(new ConvertToLead(data));

                let initialState: any = {
                  type: 'message',
                  data: {
                    fieldType: 'Warning',
                    heading: 'Same Lead Exists',
                    message: `Would you like to create a re-enquire for the same ?`,
                    note: 'Admin, Manager, Lead Owner will be notified in the case of Re-Enquiry',
                  },
                  class: 'modal-400 modal-dialog-centered ph-modal-unset',
                };
                this.modalService.show(
                  UserAlertPopupComponent,
                  Object.assign(
                    {},
                    {
                      class: 'modal-400 modal-dialog-centered ph-modal-unset',
                      initialState,
                    }
                  )
                );
              }),
              catchError((err) => of(new OnError(err)))
            );
        })
      ),
    { dispatch: false }
  );

  updateStatus$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(DataManagementActionTypes.UPDATE_STATUS),
        map((action: UpdateStatus) => action),
        switchMap((data: any) => {
          return this.api.updateStatus(data?.payload).pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                this._notificationService.success(
                  'Status Updated Successfully'
                );

                if (data?.isSaveAndClose) {
                  this.router.navigate(['/data']);
                } else if (data?.isSaveAndNext) {
                  this._store.dispatch(new FetchDataById(data?.payload?.id));
                } else {
                  this._store.dispatch(new FetchDataById(data?.payload?.id));
                  this._store.dispatch(
                    new FetchDataHistoryById(data?.payload?.id)
                  );
                }
              }
            }),
            catchError((err) => of(new OnError(err)))
          );
        })
      ),
    { dispatch: false }
  );

  getDataQRCode$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_QR_CODE),
      map((action: FetchDataQRCode) => action),
      switchMap((action: FetchDataQRCode) => {
        return this.api.getQRCode().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataQRCodeSuccess(resp.data);
            }
            return new FetchDataQRCodeSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  communicationDataMessage$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.COMMUNICATION_DATA_MESSAGE),
      switchMap((action: CommunicationDataMessage) => {
        return this.api.communicationDataMessage(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new CommunicationDataMessageSuccess(resp);
            }
            return new CommunicationDataMessageSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  communicationBulkDataMessage$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.COMMUNICATION_BULK_DATA_MESSAGE),
      switchMap((action: CommunicationBulkDataMessage) => {
        return this.api.communicationBulkDataMessage(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new CommunicationBulkDataMessageSuccess(resp);
            }
            return new CommunicationBulkDataMessageSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataHistoryById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_HISTORY_BY_ID),
      map((action: FetchDataHistoryById) => action),
      switchMap((action: any) => {
        return this.api.getHistoryById(action.id).pipe(
          map((resp: any) => {
            if (resp?.succeeded) {
              return new FetchDataHistoryByIdSuccess(resp.data);
            }
            return new FetchDataHistoryByIdSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  updateDataNotes$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.UPDATE_DATA_NOTES),
      map((action: UpdateDataNotes) => action),
      switchMap((action: any) => {
        return this.api.updateDataNotes(action?.payload).pipe(
          map((resp: any) => {
            if (action?.isSaveAndClose) {
              this.router.navigate(['/data']);
            } else if (!action.isSaveAndNext) {
              return new FetchDataHistoryById(action?.payload?.prospectId);
            }
            return null;
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataLocations$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_LOCATIONS),
      map((action: FetchDataLocations) => action),
      switchMap((data: any) => {
        return this.api.getDataLocations().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataLocationsSuccess(resp.data);
            }
            return new FetchDataLocationsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataCities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_CITIES),
      map((action: FetchDataCities) => action),
      switchMap((data: any) => {
        return this.api.getDataCities().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataCitiesSuccess(resp.data);
            }
            return new FetchDataCitiesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataStates$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_STATES),
      map((action: FetchDataStates) => action),
      switchMap((data: any) => {
        return this.api.getDataStates().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataStatesSuccess(resp.data);
            }
            return new FetchDataStatesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataCountries$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_COUNTRIES),
      map((action: FetchDataCountries) => action),
      switchMap((data: any) => {
        return this.api.getDataCountries().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataCountriesSuccess(resp.data);
            }
            return new FetchDataCountriesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataLocalities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_LOCALITIES),
      map((action: FetchDataLocalities) => action),
      switchMap((data: any) => {
        return this.api.getDataLocalities().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataLocalitiesSuccess(resp.data);
            }
            return new FetchDataLocalitiesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataSubCommunities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_SUB_COMMUNITIES),
      map((action: FetchDataSubCommunities) => action),
      switchMap((data: any) => {
        return this.api.getDataSubCommunities().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataSubCommunitiesSuccess(resp.data);
            }
            return new FetchDataSubCommunitiesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataCommunities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_COMMUNITIES),
      map((action: FetchDataCommunities) => action),
      switchMap((data: any) => {
        return this.api.getDataCommunities().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataCommunitiesSuccess(resp.data);
            }
            return new FetchDataCommunitiesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataTowerNames$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_TOWER),
      map((action: FetchDataTowerNames) => action),
      switchMap((data: any) => {
        return this.api.getDataTowerNames().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataTowerNamesSuccess(resp.data);
            }
            return new FetchDataTowerNamesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataZones$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_ZONES),
      map((action: FetchDataZones) => action),
      switchMap((data: any) => {
        return this.api.getDataZones().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataZonesSuccess(resp.data);
            }
            return new FetchDataZonesSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  addBulkDataExcel$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.DATA_EXCEL_UPLOAD),
      switchMap((action: DataExcelUpload) => {
        return this.api.uploadExcel(action.file).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                'Data Excel uploaded Successfully'
              );
              return new DataExcelUploadSuccess(resp.data);
            } else {
              this._store.dispatch(new CloseModal());
              this._notificationService.warn(`${resp.message}`);
              return new FetchAllData();
            }
          }),
          catchError((err: any) => {
            throwError(err);
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  uploadMappedColumns$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.DATA_UPLOAD_MAPPED_COLUMNS),
      switchMap((action: DataUploadMappedColumns) => {
        return this.api.uploadMappedColumns(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              if (resp.data) {
                if (resp.data?.excelUrl) {
                  const dataCount = resp.message?.DataCount || '';
                  this._notificationService.success(
                    `${dataCount} Invalid Data Not Uploaded`
                  );
                  return new DataExcelUploadSuccess(resp.data);
                } else {
                  this._notificationService.success(
                    'Excel Uploaded Successfully'
                  );
                  return new DataExcelUploadSuccess(resp.data);
                }
              } else {
                this._notificationService.error(resp.message);
              }
              return new FetchAllData();
            }
            return new FetchAllDataSuccess();
          }),
          catchError((err: any) => {
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  getExcelUploadedList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_EXCEL_UPLOADED_LIST),
      map((action: FetchDataExcelUploadedList) => action),
      switchMap((data: any) => {
        return this.api
          .getExcelUploadedList(data?.pageNumber, data?.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchDataExcelUploadedListSuccess(resp);
              }
              return new FetchDataExcelUploadedListSuccess();
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  exportLeads$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.EXPORT_DATA),
      switchMap((action: ExportData) => {
        let filterPayload: any = {
          ...action?.payload,
        };
        if (Object.keys(filterPayload).includes('PageNumber'))
          delete filterPayload.PageNumber;
        if (Object.keys(filterPayload).includes('PageSize'))
          delete filterPayload.PageSize;
        return this.api.exportData(filterPayload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Data is being exported in excel format`
              );
              return new ExportDataSuccess(resp);
            }
            return new ExportDataSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getExportStatusList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_EXPORT_STATUS),
      map((action: FetchDataExportStatus) => action),
      switchMap((data: any) => {
        return this.api.getExportStatus(data.pageNumber, data.pageSize).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataExportStatusSuccess(resp);
            }
            return new FetchDataExportStatusSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getConversionStatusList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_CONVERSION_STATUS),
      map((action: FetchDataConversionStatus) => action),
      switchMap((data: any) => {
        return this.api.getDataConversionStatus().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataConversionStatusSuccess(
                resp?.data?.masterLeadStatusDtos
              );
            }
            return new FetchDataConversionStatusSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataCurrency$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_CURRENCY_LIST),
      map((action: FetchDataCurrency) => action),
      switchMap((data: any) => {
        return this.api.getDataCurrency().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataCurrencySuccess(resp.data);
            }
            return new FetchDataCurrencySuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  permanentDeleteLeads$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.PERMANENT_DELETE_DATA),
      map((action: PermanentDeleteData) => action),
      switchMap((data: any) => {
        return this.api.permanentdeleteData(data.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Deleted Successfully');
              this._store.dispatch(new PermanentDeleteDataSuccess());
              return new FetchAllData();
            }
            return new FetchAllData();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  uploadMigrateMappedColumns$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.DATA_MIGRATE_UPLOAD_MAPPED_COLUMNS),
      switchMap((action: DataMigrateUploadMappedColumns) => {
        return this.api.uploadMigrateMappedColumns(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              if (resp.data) {
                if (resp.data?.excelUrl) {
                  const dataCount = resp.message?.DataCount || '';
                  this._notificationService.success(
                    `${dataCount} Invalid Data Not Uploaded`
                  );
                  return new FetchDataMigrateExcelUploadedListSuccess(
                    resp.data
                  );
                } else {
                  this._notificationService.success(
                    'Excel Uploaded Successfully'
                  );
                  return new DataExcelUploadSuccess(resp.data);
                }
              } else {
                this._notificationService.error(resp.message);
              }
              return new FetchAllData();
            }
            return new FetchAllDataSuccess();
          }),
          catchError((err: any) => {
            Array.isArray(err?.error?.messages)
              ? this._notificationService.error(err.error.messages[0])
              : this._notificationService.error(err?.error?.messages);
            return throwError(() => err);
          })
        );
      })
    )
  );

  getMigrateExcelUploadedList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_MIGRATE_EXCEL_UPLOADED_LIST),
      map((action: FetchDataMigrateExcelUploadedList) => action),
      switchMap((data: any) => {
        return this.api
          .getMigrateExcelUploadedList(data?.pageNumber, data?.pageSize)
          .pipe(
            map((resp: any) => {
              if (resp.succeeded) {
                return new FetchDataMigrateExcelUploadedListSuccess(resp);
              }
              return new FetchDataMigrateExcelUploadedListSuccess();
            }),
            catchError((err) => of(new OnError(err)))
          );
      })
    )
  );

  getDataNationality$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_NATIONALITY),
      map((action: FetchDataNationality) => action),
      switchMap((data: any) => {
        return this.api.getDataNationality().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataNationalitySuccess(resp?.data);
            }
            return new FetchDataNationalitySuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getClusterNames$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_CLUSTER_NAME),
      map((action: FetchDataClusterName) => action),
      switchMap((data: any) => {
        return this.api.getDataClusterName().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataClusterNameSuccess(resp?.data);
            }
            return new FetchDataClusterNameSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getUnitNames$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_UNIT_NAME),
      map((action: FetchDataUnitName) => action),
      switchMap((data: any) => {
        return this.api.getDataUnitName().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataUnitNameSuccess(resp?.data);
            }
            return new FetchDataUnitNameSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataPostalCode$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_POSTAL_CODE),
      map((action: FetchDataPostalCode) => action),
      switchMap((data: any) => {
        return this.api.getDataPostalCode().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataPostalCodeSuccess(resp?.data);
            }
            return new FetchDataPostalCodeSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getDataLandLine$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_LANDLINE),
      map((action: FetchDataLandLine) => action),
      switchMap((data: any) => {
        return this.api.getDataLandLine().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataLandLineSuccess(resp?.data);
            }
            return new FetchDataLandLineSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getUploadTypeNameList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_UPLOADTYPENAME_LIST),
      map((action: FetchUploadTypeNameList) => action),
      switchMap((data: any) => {
        return this.api.getUploadTypeNameList().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchUploadTypeNameListSuccess(resp.data);
            }
            return new FetchUploadTypeNameListSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getLeadCommunicationsById$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_COMMUNICATION_BY_IDS),
      switchMap((action: FetchDataCommunicationByIds) => {
        const payload: {
          ProspectIds: string[];
          path: string;
          showCommunicationCount?: boolean
        } = { ...action?.payload, path: 'prospect/communications' };
        // Only proceed if showFilterCount is true
        if (!payload.showCommunicationCount) {
          return of({ type: '[DATA] Noop' });
        }
        return this.commonService.getModuleListByAdvFilter(payload).pipe(
          map((resp: any) => {
            if (resp?.succeeded) {
              return new FetchDataCommunicationByIdsSuccess(resp?.data);
            }
            return new FetchDataCommunicationByIdsSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getAltCountryCode$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_ALT_COUNTRY_CODE),
      map((action: FetchDataAltCountryCode) => action),
      switchMap((data: any) => {
        return this.api.getDataAltCountryCode().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataAltCountryCodeSuccess(resp?.data);
            }
            return new FetchDataAltCountryCodeSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getCountryCode$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.FETCH_DATA_COUNTRY_CODE),
      map((action: FetchDataCountryCode) => action),
      switchMap((data: any) => {
        return this.api.getDataCountryCode().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchDataCountryCodeSuccess(resp?.data);
            }
            return new FetchDataCountryCodeSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkAgency$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.BULK_AGENCY),
      switchMap((action: BulkAgency) => {
        return this.api.bulkUpdateAgency(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Agency(s) Updated Successfully`
              );
              this._store.dispatch(new BulkAgencySuccess());
              return new FetchAllData();
            }
            return new FetchAllDataSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkChannelPartner$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.BULK_CHANNEL_PARTNER),
      switchMap((action: BulkChannelPartner) => {
        return this.api.bulkUpdateChannelPartner(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Channel partner(s) Updated Successfully`
              );
              this._store.dispatch(new BulkChannelPartnerSuccess());
              return new FetchAllData();
            }
            return new FetchAllDataSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  bulkCampaign$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DataManagementActionTypes.BULK_CAMPAIGN),
      switchMap((action: BulkCampaign) => {
        return this.api.bulkUpdateCampaign(action.payload).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success(
                `Campaign(s) Updated Successfully`
              );
              this._store.dispatch(new BulkCampaignSuccess());
              return new FetchAllData();
            }
            return new FetchAllDataSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  private dispatchDataBatches(items: any[] = [], showCommunicationCount: any): void {
    if (!items?.length) return;

    const ProspectIds = items.map((data: any) => data?.id).filter(Boolean);
    const batchSize = 50;

    for (let i = 0; i < ProspectIds.length; i += batchSize) {
      const payload = {
        ProspectIds: ProspectIds.slice(i, i + batchSize),
        showCommunicationCount: showCommunicationCount
      };
      this._store.dispatch(new FetchDataCommunicationByIds(payload));
    }
  }

  constructor(
    private actions$: Actions,
    private api: DataManagementService,
    private _notificationService: NotificationsService,
    private _store: Store<AppState>,
    private router: Router,
    private commonService: CommonService,
    public modalService: BsModalService
  ) { }
}
