{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Malay [ms]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/weldan\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var ms = moment.defineLocale('ms', {\n    months: 'Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember'.split('_'),\n    monthsShort: 'Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis'.split('_'),\n    weekdays: 'Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu'.split('_'),\n    weekdaysShort: 'Ahd_Isn_Se<PERSON>_Ra<PERSON>_<PERSON>ha_Ju<PERSON>_Sab'.split('_'),\n    weekdaysMin: 'Ah_Is_Sl_Rb_Km_Jm_Sb'.split('_'),\n    longDateFormat: {\n      LT: 'HH.mm',\n      LTS: 'HH.mm.ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY [pukul] HH.mm',\n      LLLL: 'dddd, D MMMM YYYY [pukul] HH.mm'\n    },\n    meridiemParse: /pagi|tengahari|petang|malam/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n\n      if (meridiem === 'pagi') {\n        return hour;\n      } else if (meridiem === 'tengahari') {\n        return hour >= 11 ? hour : hour + 12;\n      } else if (meridiem === 'petang' || meridiem === 'malam') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 11) {\n        return 'pagi';\n      } else if (hours < 15) {\n        return 'tengahari';\n      } else if (hours < 19) {\n        return 'petang';\n      } else {\n        return 'malam';\n      }\n    },\n    calendar: {\n      sameDay: '[Hari ini pukul] LT',\n      nextDay: '[Esok pukul] LT',\n      nextWeek: 'dddd [pukul] LT',\n      lastDay: '[Kelmarin pukul] LT',\n      lastWeek: 'dddd [lepas pukul] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'dalam %s',\n      past: '%s yang lepas',\n      s: 'beberapa saat',\n      ss: '%d saat',\n      m: 'seminit',\n      mm: '%d minit',\n      h: 'sejam',\n      hh: '%d jam',\n      d: 'sehari',\n      dd: '%d hari',\n      M: 'sebulan',\n      MM: '%d bulan',\n      y: 'setahun',\n      yy: '%d tahun'\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n\n    }\n  });\n  return ms;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "ms", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "meridiemHour", "hour", "meridiem", "hours", "minutes", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/moment/locale/ms.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Malay [ms]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/weldan\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var ms = moment.defineLocale('ms', {\n        months: 'Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember'.split(\n            '_'\n        ),\n        monthsShort: 'Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis'.split('_'),\n        weekdays: 'Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu'.split('_'),\n        weekdaysShort: 'Ahd_Isn_Sel_Ra<PERSON>_<PERSON>ha_Ju<PERSON>_Sab'.split('_'),\n        weekdaysMin: 'Ah_Is_Sl_Rb_Km_Jm_Sb'.split('_'),\n        longDateFormat: {\n            LT: 'HH.mm',\n            LTS: 'HH.mm.ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY [pukul] HH.mm',\n            LLLL: 'dddd, D MMMM YYYY [pukul] HH.mm',\n        },\n        meridiemParse: /pagi|tengahari|petang|malam/,\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (meridiem === 'pagi') {\n                return hour;\n            } else if (meridiem === 'tengahari') {\n                return hour >= 11 ? hour : hour + 12;\n            } else if (meridiem === 'petang' || meridiem === 'malam') {\n                return hour + 12;\n            }\n        },\n        meridiem: function (hours, minutes, isLower) {\n            if (hours < 11) {\n                return 'pagi';\n            } else if (hours < 15) {\n                return 'tengahari';\n            } else if (hours < 19) {\n                return 'petang';\n            } else {\n                return 'malam';\n            }\n        },\n        calendar: {\n            sameDay: '[Hari ini pukul] LT',\n            nextDay: '[Esok pukul] LT',\n            nextWeek: 'dddd [pukul] LT',\n            lastDay: '[Kelmarin pukul] LT',\n            lastWeek: 'dddd [lepas pukul] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'dalam %s',\n            past: '%s yang lepas',\n            s: 'beberapa saat',\n            ss: '%d saat',\n            m: 'seminit',\n            mm: '%d minit',\n            h: 'sejam',\n            hh: '%d jam',\n            d: 'sehari',\n            dd: '%d hari',\n            M: 'sebulan',\n            MM: '%d bulan',\n            y: 'setahun',\n            yy: '%d tahun',\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return ms;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AAEA;;AAAE,WAAUA,MAAV,EAAkBC,OAAlB,EAA2B;EAC1B,OAAOC,OAAP,KAAmB,QAAnB,IAA+B,OAAOC,MAAP,KAAkB,WAAjD,IACO,OAAOC,OAAP,KAAmB,UAD1B,GACuCH,OAAO,CAACG,OAAO,CAAC,WAAD,CAAR,CAD9C,GAEA,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAAvC,GAA6CD,MAAM,CAAC,CAAC,WAAD,CAAD,EAAgBJ,OAAhB,CAAnD,GACAA,OAAO,CAACD,MAAM,CAACO,MAAR,CAHP;AAIF,CALC,EAKA,IALA,EAKO,UAAUA,MAAV,EAAkB;EAAE,aAAF,CAEvB;;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAP,CAAoB,IAApB,EAA0B;IAC/BC,MAAM,EAAE,oFAAoFC,KAApF,CACJ,GADI,CADuB;IAI/BC,WAAW,EAAE,kDAAkDD,KAAlD,CAAwD,GAAxD,CAJkB;IAK/BE,QAAQ,EAAE,6CAA6CF,KAA7C,CAAmD,GAAnD,CALqB;IAM/BG,aAAa,EAAE,8BAA8BH,KAA9B,CAAoC,GAApC,CANgB;IAO/BI,WAAW,EAAE,uBAAuBJ,KAAvB,CAA6B,GAA7B,CAPkB;IAQ/BK,cAAc,EAAE;MACZC,EAAE,EAAE,OADQ;MAEZC,GAAG,EAAE,UAFO;MAGZC,CAAC,EAAE,YAHS;MAIZC,EAAE,EAAE,aAJQ;MAKZC,GAAG,EAAE,2BALO;MAMZC,IAAI,EAAE;IANM,CARe;IAgB/BC,aAAa,EAAE,6BAhBgB;IAiB/BC,YAAY,EAAE,UAAUC,IAAV,EAAgBC,QAAhB,EAA0B;MACpC,IAAID,IAAI,KAAK,EAAb,EAAiB;QACbA,IAAI,GAAG,CAAP;MACH;;MACD,IAAIC,QAAQ,KAAK,MAAjB,EAAyB;QACrB,OAAOD,IAAP;MACH,CAFD,MAEO,IAAIC,QAAQ,KAAK,WAAjB,EAA8B;QACjC,OAAOD,IAAI,IAAI,EAAR,GAAaA,IAAb,GAAoBA,IAAI,GAAG,EAAlC;MACH,CAFM,MAEA,IAAIC,QAAQ,KAAK,QAAb,IAAyBA,QAAQ,KAAK,OAA1C,EAAmD;QACtD,OAAOD,IAAI,GAAG,EAAd;MACH;IACJ,CA5B8B;IA6B/BC,QAAQ,EAAE,UAAUC,KAAV,EAAiBC,OAAjB,EAA0BC,OAA1B,EAAmC;MACzC,IAAIF,KAAK,GAAG,EAAZ,EAAgB;QACZ,OAAO,MAAP;MACH,CAFD,MAEO,IAAIA,KAAK,GAAG,EAAZ,EAAgB;QACnB,OAAO,WAAP;MACH,CAFM,MAEA,IAAIA,KAAK,GAAG,EAAZ,EAAgB;QACnB,OAAO,QAAP;MACH,CAFM,MAEA;QACH,OAAO,OAAP;MACH;IACJ,CAvC8B;IAwC/BG,QAAQ,EAAE;MACNC,OAAO,EAAE,qBADH;MAENC,OAAO,EAAE,iBAFH;MAGNC,QAAQ,EAAE,iBAHJ;MAINC,OAAO,EAAE,qBAJH;MAKNC,QAAQ,EAAE,uBALJ;MAMNC,QAAQ,EAAE;IANJ,CAxCqB;IAgD/BC,YAAY,EAAE;MACVC,MAAM,EAAE,UADE;MAEVC,IAAI,EAAE,eAFI;MAGVC,CAAC,EAAE,eAHO;MAIVC,EAAE,EAAE,SAJM;MAKVC,CAAC,EAAE,SALO;MAMVC,EAAE,EAAE,UANM;MAOVC,CAAC,EAAE,OAPO;MAQVC,EAAE,EAAE,QARM;MASVC,CAAC,EAAE,QATO;MAUVC,EAAE,EAAE,SAVM;MAWVC,CAAC,EAAE,SAXO;MAYVC,EAAE,EAAE,UAZM;MAaVC,CAAC,EAAE,SAbO;MAcVC,EAAE,EAAE;IAdM,CAhDiB;IAgE/BC,IAAI,EAAE;MACFC,GAAG,EAAE,CADH;MACM;MACRC,GAAG,EAAE,CAFH,CAEM;;IAFN;EAhEyB,CAA1B,CAAT;EAsEA,OAAO9C,EAAP;AAEH,CAjFC,CAAD", "ignoreList": []}, "metadata": {}, "sourceType": "script"}