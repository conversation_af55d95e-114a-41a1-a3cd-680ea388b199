{"ast": null, "code": "import baseIsEqualDeep from './_baseIsEqualDeep.js';\nimport isObjectLike from './isObjectLike.js';\n/**\n * The base implementation of `_.isEqual` which supports partial comparisons\n * and tracks traversed objects.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @param {boolean} bitmask The bitmask flags.\n *  1 - Unordered comparison\n *  2 - Partial comparison\n * @param {Function} [customizer] The function to customize comparisons.\n * @param {Object} [stack] Tracks traversed `value` and `other` objects.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n */\n\nfunction baseIsEqual(value, other, bitmask, customizer, stack) {\n  if (value === other) {\n    return true;\n  }\n\n  if (value == null || other == null || !isObjectLike(value) && !isObjectLike(other)) {\n    return value !== value && other !== other;\n  }\n\n  return baseIsEqualDeep(value, other, bitmask, customizer, baseIsEqual, stack);\n}\n\nexport default baseIsEqual;", "map": {"version": 3, "names": ["baseIsEqualDeep", "isObjectLike", "baseIsEqual", "value", "other", "bitmask", "customizer", "stack"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/lodash-es/_baseIsEqual.js"], "sourcesContent": ["import baseIsEqualDeep from './_baseIsEqualDeep.js';\nimport isObjectLike from './isObjectLike.js';\n\n/**\n * The base implementation of `_.isEqual` which supports partial comparisons\n * and tracks traversed objects.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @param {boolean} bitmask The bitmask flags.\n *  1 - Unordered comparison\n *  2 - Partial comparison\n * @param {Function} [customizer] The function to customize comparisons.\n * @param {Object} [stack] Tracks traversed `value` and `other` objects.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n */\nfunction baseIsEqual(value, other, bitmask, customizer, stack) {\n  if (value === other) {\n    return true;\n  }\n  if (value == null || other == null || (!isObjectLike(value) && !isObjectLike(other))) {\n    return value !== value && other !== other;\n  }\n  return baseIsEqualDeep(value, other, bitmask, customizer, baseIsEqual, stack);\n}\n\nexport default baseIsEqual;\n"], "mappings": "AAAA,OAAOA,eAAP,MAA4B,uBAA5B;AACA,OAAOC,YAAP,MAAyB,mBAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,WAAT,CAAqBC,KAArB,EAA4BC,KAA5B,EAAmCC,OAAnC,EAA4CC,UAA5C,EAAwDC,KAAxD,EAA+D;EAC7D,IAAIJ,KAAK,KAAKC,KAAd,EAAqB;IACnB,OAAO,IAAP;EACD;;EACD,IAAID,KAAK,IAAI,IAAT,IAAiBC,KAAK,IAAI,IAA1B,IAAmC,CAACH,YAAY,CAACE,KAAD,CAAb,IAAwB,CAACF,YAAY,CAACG,KAAD,CAA5E,EAAsF;IACpF,OAAOD,KAAK,KAAKA,KAAV,IAAmBC,KAAK,KAAKA,KAApC;EACD;;EACD,OAAOJ,eAAe,CAACG,KAAD,EAAQC,KAAR,EAAeC,OAAf,EAAwBC,UAAxB,EAAoCJ,WAApC,EAAiDK,KAAjD,CAAtB;AACD;;AAED,eAAeL,WAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}