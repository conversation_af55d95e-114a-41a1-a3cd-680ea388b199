{"ast": null, "code": "import { concatMap } from './concatMap';\nimport { isFunction } from '../util/isFunction';\nexport function concatMapTo(innerObservable, resultSelector) {\n  return isFunction(resultSelector) ? concatMap(() => innerObservable, resultSelector) : concatMap(() => innerObservable);\n}", "map": {"version": 3, "names": ["concatMap", "isFunction", "concatMapTo", "innerObservable", "resultSelector"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/rxjs/dist/esm/internal/operators/concatMapTo.js"], "sourcesContent": ["import { concatMap } from './concatMap';\nimport { isFunction } from '../util/isFunction';\nexport function concatMapTo(innerObservable, resultSelector) {\n    return isFunction(resultSelector) ? concatMap(() => innerObservable, resultSelector) : concatMap(() => innerObservable);\n}\n"], "mappings": "AAAA,SAASA,SAAT,QAA0B,aAA1B;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,OAAO,SAASC,WAAT,CAAqBC,eAArB,EAAsCC,cAAtC,EAAsD;EACzD,OAAOH,UAAU,CAACG,cAAD,CAAV,GAA6BJ,SAAS,CAAC,MAAMG,eAAP,EAAwBC,cAAxB,CAAtC,GAAgFJ,SAAS,CAAC,MAAMG,eAAP,CAAhG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}