<div class="px-20 h-100-182 scrollbar">
  <div
    class="section-navbar p-4 br-6 mb-20 flex-col justify-content-between position-relative ip-flex-col ip-mb-20 ip-mr-10">
    <div class="flex-between">
      <h5 class="fw-600 pb-1 border-bottom">Project gallery</h5>
      <div class="flex-between input-sm position-relative">
        <input type="text" placeholder="Add Project URL" [(ngModel)]="newUrl" class="outline-0 w-180 br-20 padd-r pr-36"
          (keyup.enter)="focusableURL.click()">
        <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md cursor-pointer" #focusableURL
          (click)="addInputField()"><span class="icon ic-plus ic-x-xs"></span>
        </div>
      </div>
    </div>
    <div class="d-flex flex-wrap mt-12">
      <div class="align-center border br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm" *ngFor="let link of links">
        <a class="fw-600 text-sm mr-4 text-black-100 cursor-pointer border-bottom-slate-40" href="{{link}}"
          target="_blank">{{link}}</a>
        <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
          (click)="removeInputField(link)"></span>
      </div>
    </div>
    <div class="mt-16">
      <div class="align-center w-100">
        <div class="fw-600">Photos</div>
        <div class="flex-grow-1 border-bottom ml-12 mr-40"></div>
      </div>
      <div class="mt-8 text-gray fw-semi-bold align-center">{{'PROPERTY.GALLERY.file-type' | translate}}
        <label *ngIf="waterMarkSettingsObj.isWaterMarkEnabled"
          class="checkbox-container w-fit-content tb-w-33 ip-w-50 ph-w-100 text-secondary ml-20 mt-0">
          Add water mark
          <input type="checkbox" class="mr-10" [(ngModel)]="waterMarkSettingsObj.isToAddWaterMark"
            (change)="waterMarkSettingsObj.isToAddWaterMark = $event.target?.checked">
          <span class="checkmark"></span>
        </label>
      </div>
      <div class="mt-16 d-flex flex-wrap">
        <div class="mr-20 mb-16">
          <label>
            <div class="add-image h-120 w-120">
              <div class="upload-button flex-center-col">
                <ng-lottie [options]='addImage' width="70px"></ng-lottie>
                <div class="fw-600 text-xs">+ Add Photos</div>
                <div class="text-gray text-xxs fw-semi-bold">(max upload limit 500 MB)</div>
                <file-upload requiredFileType="image/x-png,image/gif,image/jpeg,image/tiff"
                  (imageFileName)="uploadImage($event)" [isImageGallery]="true"
                  (addWaterMarkImage)="waterMarkImages($event)">
                </file-upload>
              </div>
            </div>
          </label>
        </div>
        <div *ngFor="let url of galleryS3Paths; let i = index" class="position-relative mr-20 mb-16">

          <a [href]="url?.includes(s3BucketUrl)?url:getVidPath(url)" target="_blank">
            <img [type]="'leadrat'" [appImage]="url?.includes(s3BucketUrl)?url:getVidPath(url)" width="120" height="120"
              class="obj-cover cursor-pointer br-5" alt="img" />
          </a>
          <div class="position-absolute top-4 right-4">
            <span (click)="deleteImage(i,url)" class="dot bg-black-4 cursor-pointer bg-hover-red">
              <span class="icon ic-delete ic-xxxs" id="clkDeleteImage" data-automate-id="clkDeleteImage"></span>
            </span>
          </div>
          <div [ngClass]="{'gallery-cover-selected': coverImg === url?true:i === coverImgIndex}" class="gallery-cover"
            (click)="onSetCoverImage(url, i)">
            <span class="icon ic-star-full ic-xxxs mr-4"></span>Cover
          </div>
          <div class="position-absolute w-100 bottom-0 image-dropdown">
            <ng-select [virtualScroll]="true" [addTag]="true" placeholder="Select field" ResizableDropdown
              (change)="setImageCategory($event, url)" [(ngModel)]="galleryMapping[url]" [items]="galleryDropdownData">
            </ng-select>
          </div>
        </div>
        <ng-container *ngIf="isImageUploading">
          <ng-container *ngFor="let item of galleryImageArray" [ngTemplateOutlet]="dotLoader"></ng-container>
        </ng-container>
      </div>
    </div>
    <div class="mt-16">
      <div class="align-center w-100">
        <div class="fw-600">Videos</div>
        <div class="flex-grow-1 border-bottom ml-12 mr-40"></div>
      </div>
      <div class="mt-8 text-gray fw-semi-bold">
        Only .mp4 file will be accepted.
      </div>
      <div class="mt-16 d-flex flex-wrap">
        <div class="mr-20 mb-16">
          <label>
            <div class="add-image h-120 w-120">
              <div class="upload-button flex-center-col">
                <ng-lottie [options]='addVideo' width="70px"></ng-lottie>
                <div class="fw-600 text-xs">+ Add Videos</div>
                <div class="text-gray text-xxs fw-semi-bold">(max upload limit 500 MB)</div>
                <file-upload
                  requiredFileType="video/mp4,video/mpeg,video/quicktime,video/webm,video/x-msvideo,video/x-ms-wmv"
                  (imageFileName)="uploadVideo($event)" [isImageGallery]="true">
                </file-upload>
              </div>
            </div>
          </label>
        </div>
        <div *ngFor="let url of galleryS3PathsVid; let i = index"
          class="position-relative mr-20 mb-16 box-shadow-10 br-5">
          <video width="120" height="120" controls class="br-5" alt="video">
            <source [src]="url.imageFilePath" type="video/mp4">
            Your browser does not support the video tag.
          </video>
          <div class="position-absolute top-4 right-4">
            <span (click)="deleteVideo(i)" class="dot bg-black-4 cursor-pointer bg-hover-red">
              <span class="icon ic-delete ic-xxxs" id="clkDeleteImage" data-automate-id="clkDeleteImage"></span>
            </span>
          </div>
          <div class="position-absolute w-100 bottom-0 image-dropdown">
            <div class="p-1 flex-between bg-white-100 brbr-6 brbl-6">
              <div class="text-truncate-1 break-all fw-semi-bold">{{url.name ? url.name : 'Video '}}</div>
            </div>
          </div>
        </div>
        <ng-container *ngIf="isVideoUploading">
          <ng-container *ngFor="let item of galleryVideoArray" [ngTemplateOutlet]="dotLoader"></ng-container>
        </ng-container>
      </div>
    </div>
    <div class="mt-16">
      <div class="align-center w-100">
        <div class="fw-600">Brochures</div>
        <div class="flex-grow-1 border-bottom ml-12 mr-40"></div>
      </div>
      <div class="mt-8 text-gray fw-semi-bold">
        Only .pdf file will be accepted.
      </div>
      <div class="mt-16 d-flex flex-wrap">
        <div class="mr-20 mb-16">
          <label>
            <div class="add-image h-120 w-120">
              <div class="upload-button flex-center-col">
                <ng-lottie [options]='addDoc' width="70px"></ng-lottie>
                <div class="fw-600 text-xs mt-1">+ Add Documents</div>
                <div class="text-gray text-xxs fw-semi-bold">(max upload limit 15 MB)</div>
                <file-upload requiredFileType="application/pdf" (imageFileName)="uploadFile($event)"
                  [isImageGallery]="true">
                </file-upload>
              </div>
            </div>
          </label>
        </div>
        <div *ngFor="let url of galleryS3PathsDoc; let i = index" class="mr-30 mb-20 w-130 h-120 flex-col">
          <a [href]="url?.path" target="_blank">
            <img src="../../../../assets/images/pdf.svg" alt="">
          </a>
          <div class="p-10 flex-between bg-white-100 brbr-6 brbl-6">
            <div class="text-truncate-1 break-all fw-semi-bold">{{url.name ? url.name : 'Brochures ' +
              (i+1)}}</div>
            <span class="dot bg-light-red cursor-pointer" (click)="deleteDoc(i)">
              <div title="Delete" class="ic-delete icon ic-xxxs cursor-pointer" id="clkDeletePdf"
                data-automate-id="clkDeletePdf"></div>
            </span>
          </div>
        </div>
        <ng-container *ngIf="isDocumentUploading">
          <ng-container *ngFor="let item of galleryDocArray" [ngTemplateOutlet]="dotLoader"></ng-container>
        </ng-container>
      </div>
    </div>
  </div>
</div>
<div class="flex-end px-20 py-16 bg-white box-shadow-10">
  <u class="mr-20 text-black-200 text-large fw-bold cursor-pointer"
    (click)="router.navigate(['/projects/manage-projects'])">Cancel</u>
  <div class="btn-coal" (click)="saveAndFinish()">Save and Finish</div>
</div>
<ng-template #dotLoader>
  <div class="w-120 h-120 overlay mr-20 mb-16 br-4 border align-center">
    <div class="container px-4 d-inline flex-center">
      <span>Uploading&nbsp;</span><ng-container *ngFor="let dot of [1, 2, 3]">
        <div class="dot-falling"></div>
      </ng-container>
    </div>
  </div>
</ng-template>