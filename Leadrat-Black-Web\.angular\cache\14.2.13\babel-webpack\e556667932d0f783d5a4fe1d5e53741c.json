{"ast": null, "code": "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null;\nvar ReflectApply = R && typeof R.apply === 'function' ? R.apply : function ReflectApply(target, receiver, args) {\n  return Function.prototype.apply.call(target, receiver, args);\n};\nvar ReflectOwnKeys;\n\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys;\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target).concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\n\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\n\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n};\n\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\n\nmodule.exports = EventEmitter;\nmodule.exports.once = once; // Backwards-compat with node 0.10.x\n\nEventEmitter.EventEmitter = EventEmitter;\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined; // By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\n\nvar defaultMaxListeners = 10;\n\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\n\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function () {\n    return defaultMaxListeners;\n  },\n  set: function (arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n\n    defaultMaxListeners = arg;\n  }\n});\n\nEventEmitter.init = function () {\n  if (this._events === undefined || this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n\n  this._maxListeners = this._maxListeners || undefined;\n}; // Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\n\n\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n\n  this._maxListeners = n;\n  return this;\n};\n\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined) return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\n\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\n\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n\n  var doError = type === 'error';\n  var events = this._events;\n  if (events !== undefined) doError = doError && events.error === undefined;else if (!doError) return false; // If there is no 'error' event listener then throw.\n\n  if (doError) {\n    var er;\n    if (args.length > 0) er = args[0];\n\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    } // At least give some kind of context to the user\n\n\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n\n  var handler = events[type];\n  if (handler === undefined) return false;\n\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n\n    for (var i = 0; i < len; ++i) ReflectApply(listeners[i], this, args);\n  }\n\n  return true;\n};\n\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n  checkListener(listener);\n  events = target._events;\n\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type, listener.listener ? listener.listener : listener); // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n\n      events = target._events;\n    }\n\n    existing = events[type];\n  }\n\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] = prepend ? [listener, existing] : [existing, listener]; // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    } // Check for listener leak\n\n\n    m = _getMaxListeners(target);\n\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true; // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n\n      var w = new Error('Possible EventEmitter memory leak detected. ' + existing.length + ' ' + String(type) + ' listeners ' + 'added. Use emitter.setMaxListeners() to ' + 'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n\n  return target;\n}\n\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\n\nEventEmitter.prototype.prependListener = function prependListener(type, listener) {\n  return _addListener(this, type, listener, true);\n};\n\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0) return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\n\nfunction _onceWrap(target, type, listener) {\n  var state = {\n    fired: false,\n    wrapFn: undefined,\n    target: target,\n    type: type,\n    listener: listener\n  };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\n\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\n\nEventEmitter.prototype.prependOnceListener = function prependOnceListener(type, listener) {\n  checkListener(listener);\n  this.prependListener(type, _onceWrap(this, type, listener));\n  return this;\n}; // Emits a 'removeListener' event if and only if the listener was removed.\n\n\nEventEmitter.prototype.removeListener = function removeListener(type, listener) {\n  var list, events, position, i, originalListener;\n  checkListener(listener);\n  events = this._events;\n  if (events === undefined) return this;\n  list = events[type];\n  if (list === undefined) return this;\n\n  if (list === listener || list.listener === listener) {\n    if (--this._eventsCount === 0) this._events = Object.create(null);else {\n      delete events[type];\n      if (events.removeListener) this.emit('removeListener', type, list.listener || listener);\n    }\n  } else if (typeof list !== 'function') {\n    position = -1;\n\n    for (i = list.length - 1; i >= 0; i--) {\n      if (list[i] === listener || list[i].listener === listener) {\n        originalListener = list[i].listener;\n        position = i;\n        break;\n      }\n    }\n\n    if (position < 0) return this;\n    if (position === 0) list.shift();else {\n      spliceOne(list, position);\n    }\n    if (list.length === 1) events[type] = list[0];\n    if (events.removeListener !== undefined) this.emit('removeListener', type, originalListener || listener);\n  }\n\n  return this;\n};\n\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(type) {\n  var listeners, events, i;\n  events = this._events;\n  if (events === undefined) return this; // not listening for removeListener, no need to emit\n\n  if (events.removeListener === undefined) {\n    if (arguments.length === 0) {\n      this._events = Object.create(null);\n      this._eventsCount = 0;\n    } else if (events[type] !== undefined) {\n      if (--this._eventsCount === 0) this._events = Object.create(null);else delete events[type];\n    }\n\n    return this;\n  } // emit removeListener for all listeners on all events\n\n\n  if (arguments.length === 0) {\n    var keys = Object.keys(events);\n    var key;\n\n    for (i = 0; i < keys.length; ++i) {\n      key = keys[i];\n      if (key === 'removeListener') continue;\n      this.removeAllListeners(key);\n    }\n\n    this.removeAllListeners('removeListener');\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n    return this;\n  }\n\n  listeners = events[type];\n\n  if (typeof listeners === 'function') {\n    this.removeListener(type, listeners);\n  } else if (listeners !== undefined) {\n    // LIFO order\n    for (i = listeners.length - 1; i >= 0; i--) {\n      this.removeListener(type, listeners[i]);\n    }\n  }\n\n  return this;\n};\n\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n  if (events === undefined) return [];\n  var evlistener = events[type];\n  if (evlistener === undefined) return [];\n  if (typeof evlistener === 'function') return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n  return unwrap ? unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\n\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\n\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\n\nEventEmitter.listenerCount = function (emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\n\nEventEmitter.prototype.listenerCount = listenerCount;\n\nfunction listenerCount(type) {\n  var events = this._events;\n\n  if (events !== undefined) {\n    var evlistener = events[type];\n\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n\n  return 0;\n}\n\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\n\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n\n  for (var i = 0; i < n; ++i) copy[i] = arr[i];\n\n  return copy;\n}\n\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++) list[index] = list[index + 1];\n\n  list.pop();\n}\n\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n\n  return ret;\n}\n\nfunction once(emitter, name) {\n  return new Promise(function (resolve, reject) {\n    function errorListener(err) {\n      emitter.removeListener(name, resolver);\n      reject(err);\n    }\n\n    function resolver() {\n      if (typeof emitter.removeListener === 'function') {\n        emitter.removeListener('error', errorListener);\n      }\n\n      resolve([].slice.call(arguments));\n    }\n\n    ;\n    eventTargetAgnosticAddListener(emitter, name, resolver, {\n      once: true\n    });\n\n    if (name !== 'error') {\n      addErrorHandlerIfEventEmitter(emitter, errorListener, {\n        once: true\n      });\n    }\n  });\n}\n\nfunction addErrorHandlerIfEventEmitter(emitter, handler, flags) {\n  if (typeof emitter.on === 'function') {\n    eventTargetAgnosticAddListener(emitter, 'error', handler, flags);\n  }\n}\n\nfunction eventTargetAgnosticAddListener(emitter, name, listener, flags) {\n  if (typeof emitter.on === 'function') {\n    if (flags.once) {\n      emitter.once(name, listener);\n    } else {\n      emitter.on(name, listener);\n    }\n  } else if (typeof emitter.addEventListener === 'function') {\n    // EventTarget does not have `error` event semantics like Node\n    // EventEmitters, we do not listen for `error` events here.\n    emitter.addEventListener(name, function wrapListener(arg) {\n      // IE does not have builtin `{ once: true }` support so we\n      // have to do it manually.\n      if (flags.once) {\n        emitter.removeEventListener(name, wrapListener);\n      }\n\n      listener(arg);\n    });\n  } else {\n    throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type ' + typeof emitter);\n  }\n}", "map": {"version": 3, "names": ["R", "Reflect", "ReflectApply", "apply", "target", "receiver", "args", "Function", "prototype", "call", "ReflectOwnKeys", "ownKeys", "Object", "getOwnPropertySymbols", "getOwnPropertyNames", "concat", "ProcessEmitWarning", "warning", "console", "warn", "NumberIsNaN", "Number", "isNaN", "value", "EventEmitter", "init", "module", "exports", "once", "_events", "undefined", "_eventsCount", "_maxListeners", "defaultMaxListeners", "checkListener", "listener", "TypeError", "defineProperty", "enumerable", "get", "set", "arg", "RangeError", "getPrototypeOf", "create", "setMaxListeners", "n", "_getMaxListeners", "that", "getMaxListeners", "emit", "type", "i", "arguments", "length", "push", "do<PERSON><PERSON><PERSON>", "events", "error", "er", "Error", "err", "message", "context", "handler", "len", "listeners", "arrayClone", "_addListener", "prepend", "m", "existing", "newListener", "unshift", "warned", "w", "String", "name", "emitter", "count", "addListener", "on", "prependListener", "onceWrapper", "fired", "removeListener", "wrapFn", "_onceWrap", "state", "wrapped", "bind", "prependOnceListener", "list", "position", "originalListener", "shift", "spliceOne", "off", "removeAllListeners", "keys", "key", "_listeners", "unwrap", "evlistener", "unwrapListeners", "rawListeners", "listenerCount", "eventNames", "arr", "copy", "Array", "index", "pop", "ret", "Promise", "resolve", "reject", "errorListener", "resolver", "slice", "eventTargetAgnosticAddListener", "addErrorHandlerIfEventEmitter", "flags", "addEventListener", "wrapListener", "removeEventListener"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/events/events.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null\nvar ReflectApply = R && typeof R.apply === 'function'\n  ? R.apply\n  : function ReflectApply(target, receiver, args) {\n    return Function.prototype.apply.call(target, receiver, args);\n  }\n\nvar ReflectOwnKeys\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target)\n      .concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\n\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\n\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n}\n\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\nmodule.exports = EventEmitter;\nmodule.exports.once = once;\n\n// Backwards-compat with node 0.10.x\nEventEmitter.EventEmitter = EventEmitter;\n\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined;\n\n// By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\nvar defaultMaxListeners = 10;\n\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\n\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function() {\n    return defaultMaxListeners;\n  },\n  set: function(arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n    defaultMaxListeners = arg;\n  }\n});\n\nEventEmitter.init = function() {\n\n  if (this._events === undefined ||\n      this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n\n  this._maxListeners = this._maxListeners || undefined;\n};\n\n// Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n  this._maxListeners = n;\n  return this;\n};\n\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined)\n    return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\n\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\n\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n  var doError = (type === 'error');\n\n  var events = this._events;\n  if (events !== undefined)\n    doError = (doError && events.error === undefined);\n  else if (!doError)\n    return false;\n\n  // If there is no 'error' event listener then throw.\n  if (doError) {\n    var er;\n    if (args.length > 0)\n      er = args[0];\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    }\n    // At least give some kind of context to the user\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n\n  var handler = events[type];\n\n  if (handler === undefined)\n    return false;\n\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n    for (var i = 0; i < len; ++i)\n      ReflectApply(listeners[i], this, args);\n  }\n\n  return true;\n};\n\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n\n  checkListener(listener);\n\n  events = target._events;\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type,\n                  listener.listener ? listener.listener : listener);\n\n      // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n      events = target._events;\n    }\n    existing = events[type];\n  }\n\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] =\n        prepend ? [listener, existing] : [existing, listener];\n      // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    }\n\n    // Check for listener leak\n    m = _getMaxListeners(target);\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true;\n      // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n      var w = new Error('Possible EventEmitter memory leak detected. ' +\n                          existing.length + ' ' + String(type) + ' listeners ' +\n                          'added. Use emitter.setMaxListeners() to ' +\n                          'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n\n  return target;\n}\n\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\n\nEventEmitter.prototype.prependListener =\n    function prependListener(type, listener) {\n      return _addListener(this, type, listener, true);\n    };\n\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0)\n      return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\n\nfunction _onceWrap(target, type, listener) {\n  var state = { fired: false, wrapFn: undefined, target: target, type: type, listener: listener };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\n\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\n\nEventEmitter.prototype.prependOnceListener =\n    function prependOnceListener(type, listener) {\n      checkListener(listener);\n      this.prependListener(type, _onceWrap(this, type, listener));\n      return this;\n    };\n\n// Emits a 'removeListener' event if and only if the listener was removed.\nEventEmitter.prototype.removeListener =\n    function removeListener(type, listener) {\n      var list, events, position, i, originalListener;\n\n      checkListener(listener);\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      list = events[type];\n      if (list === undefined)\n        return this;\n\n      if (list === listener || list.listener === listener) {\n        if (--this._eventsCount === 0)\n          this._events = Object.create(null);\n        else {\n          delete events[type];\n          if (events.removeListener)\n            this.emit('removeListener', type, list.listener || listener);\n        }\n      } else if (typeof list !== 'function') {\n        position = -1;\n\n        for (i = list.length - 1; i >= 0; i--) {\n          if (list[i] === listener || list[i].listener === listener) {\n            originalListener = list[i].listener;\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0)\n          return this;\n\n        if (position === 0)\n          list.shift();\n        else {\n          spliceOne(list, position);\n        }\n\n        if (list.length === 1)\n          events[type] = list[0];\n\n        if (events.removeListener !== undefined)\n          this.emit('removeListener', type, originalListener || listener);\n      }\n\n      return this;\n    };\n\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n\nEventEmitter.prototype.removeAllListeners =\n    function removeAllListeners(type) {\n      var listeners, events, i;\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      // not listening for removeListener, no need to emit\n      if (events.removeListener === undefined) {\n        if (arguments.length === 0) {\n          this._events = Object.create(null);\n          this._eventsCount = 0;\n        } else if (events[type] !== undefined) {\n          if (--this._eventsCount === 0)\n            this._events = Object.create(null);\n          else\n            delete events[type];\n        }\n        return this;\n      }\n\n      // emit removeListener for all listeners on all events\n      if (arguments.length === 0) {\n        var keys = Object.keys(events);\n        var key;\n        for (i = 0; i < keys.length; ++i) {\n          key = keys[i];\n          if (key === 'removeListener') continue;\n          this.removeAllListeners(key);\n        }\n        this.removeAllListeners('removeListener');\n        this._events = Object.create(null);\n        this._eventsCount = 0;\n        return this;\n      }\n\n      listeners = events[type];\n\n      if (typeof listeners === 'function') {\n        this.removeListener(type, listeners);\n      } else if (listeners !== undefined) {\n        // LIFO order\n        for (i = listeners.length - 1; i >= 0; i--) {\n          this.removeListener(type, listeners[i]);\n        }\n      }\n\n      return this;\n    };\n\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n\n  if (events === undefined)\n    return [];\n\n  var evlistener = events[type];\n  if (evlistener === undefined)\n    return [];\n\n  if (typeof evlistener === 'function')\n    return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n\n  return unwrap ?\n    unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\n\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\n\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\n\nEventEmitter.listenerCount = function(emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\n\nEventEmitter.prototype.listenerCount = listenerCount;\nfunction listenerCount(type) {\n  var events = this._events;\n\n  if (events !== undefined) {\n    var evlistener = events[type];\n\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n\n  return 0;\n}\n\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\n\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n  for (var i = 0; i < n; ++i)\n    copy[i] = arr[i];\n  return copy;\n}\n\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++)\n    list[index] = list[index + 1];\n  list.pop();\n}\n\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n  return ret;\n}\n\nfunction once(emitter, name) {\n  return new Promise(function (resolve, reject) {\n    function errorListener(err) {\n      emitter.removeListener(name, resolver);\n      reject(err);\n    }\n\n    function resolver() {\n      if (typeof emitter.removeListener === 'function') {\n        emitter.removeListener('error', errorListener);\n      }\n      resolve([].slice.call(arguments));\n    };\n\n    eventTargetAgnosticAddListener(emitter, name, resolver, { once: true });\n    if (name !== 'error') {\n      addErrorHandlerIfEventEmitter(emitter, errorListener, { once: true });\n    }\n  });\n}\n\nfunction addErrorHandlerIfEventEmitter(emitter, handler, flags) {\n  if (typeof emitter.on === 'function') {\n    eventTargetAgnosticAddListener(emitter, 'error', handler, flags);\n  }\n}\n\nfunction eventTargetAgnosticAddListener(emitter, name, listener, flags) {\n  if (typeof emitter.on === 'function') {\n    if (flags.once) {\n      emitter.once(name, listener);\n    } else {\n      emitter.on(name, listener);\n    }\n  } else if (typeof emitter.addEventListener === 'function') {\n    // EventTarget does not have `error` event semantics like Node\n    // EventEmitters, we do not listen for `error` events here.\n    emitter.addEventListener(name, function wrapListener(arg) {\n      // IE does not have builtin `{ once: true }` support so we\n      // have to do it manually.\n      if (flags.once) {\n        emitter.removeEventListener(name, wrapListener);\n      }\n      listener(arg);\n    });\n  } else {\n    throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type ' + typeof emitter);\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAEA,IAAIA,CAAC,GAAG,OAAOC,OAAP,KAAmB,QAAnB,GAA8BA,OAA9B,GAAwC,IAAhD;AACA,IAAIC,YAAY,GAAGF,CAAC,IAAI,OAAOA,CAAC,CAACG,KAAT,KAAmB,UAAxB,GACfH,CAAC,CAACG,KADa,GAEf,SAASD,YAAT,CAAsBE,MAAtB,EAA8BC,QAA9B,EAAwCC,IAAxC,EAA8C;EAC9C,OAAOC,QAAQ,CAACC,SAAT,CAAmBL,KAAnB,CAAyBM,IAAzB,CAA8BL,MAA9B,EAAsCC,QAAtC,EAAgDC,IAAhD,CAAP;AACD,CAJH;AAMA,IAAII,cAAJ;;AACA,IAAIV,CAAC,IAAI,OAAOA,CAAC,CAACW,OAAT,KAAqB,UAA9B,EAA0C;EACxCD,cAAc,GAAGV,CAAC,CAACW,OAAnB;AACD,CAFD,MAEO,IAAIC,MAAM,CAACC,qBAAX,EAAkC;EACvCH,cAAc,GAAG,SAASA,cAAT,CAAwBN,MAAxB,EAAgC;IAC/C,OAAOQ,MAAM,CAACE,mBAAP,CAA2BV,MAA3B,EACJW,MADI,CACGH,MAAM,CAACC,qBAAP,CAA6BT,MAA7B,CADH,CAAP;EAED,CAHD;AAID,CALM,MAKA;EACLM,cAAc,GAAG,SAASA,cAAT,CAAwBN,MAAxB,EAAgC;IAC/C,OAAOQ,MAAM,CAACE,mBAAP,CAA2BV,MAA3B,CAAP;EACD,CAFD;AAGD;;AAED,SAASY,kBAAT,CAA4BC,OAA5B,EAAqC;EACnC,IAAIC,OAAO,IAAIA,OAAO,CAACC,IAAvB,EAA6BD,OAAO,CAACC,IAAR,CAAaF,OAAb;AAC9B;;AAED,IAAIG,WAAW,GAAGC,MAAM,CAACC,KAAP,IAAgB,SAASF,WAAT,CAAqBG,KAArB,EAA4B;EAC5D,OAAOA,KAAK,KAAKA,KAAjB;AACD,CAFD;;AAIA,SAASC,YAAT,GAAwB;EACtBA,YAAY,CAACC,IAAb,CAAkBhB,IAAlB,CAAuB,IAAvB;AACD;;AACDiB,MAAM,CAACC,OAAP,GAAiBH,YAAjB;AACAE,MAAM,CAACC,OAAP,CAAeC,IAAf,GAAsBA,IAAtB,C,CAEA;;AACAJ,YAAY,CAACA,YAAb,GAA4BA,YAA5B;AAEAA,YAAY,CAAChB,SAAb,CAAuBqB,OAAvB,GAAiCC,SAAjC;AACAN,YAAY,CAAChB,SAAb,CAAuBuB,YAAvB,GAAsC,CAAtC;AACAP,YAAY,CAAChB,SAAb,CAAuBwB,aAAvB,GAAuCF,SAAvC,C,CAEA;AACA;;AACA,IAAIG,mBAAmB,GAAG,EAA1B;;AAEA,SAASC,aAAT,CAAuBC,QAAvB,EAAiC;EAC/B,IAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;IAClC,MAAM,IAAIC,SAAJ,CAAc,qEAAqE,OAAOD,QAA1F,CAAN;EACD;AACF;;AAEDvB,MAAM,CAACyB,cAAP,CAAsBb,YAAtB,EAAoC,qBAApC,EAA2D;EACzDc,UAAU,EAAE,IAD6C;EAEzDC,GAAG,EAAE,YAAW;IACd,OAAON,mBAAP;EACD,CAJwD;EAKzDO,GAAG,EAAE,UAASC,GAAT,EAAc;IACjB,IAAI,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,GAAG,CAAjC,IAAsCrB,WAAW,CAACqB,GAAD,CAArD,EAA4D;MAC1D,MAAM,IAAIC,UAAJ,CAAe,oGAAoGD,GAApG,GAA0G,GAAzH,CAAN;IACD;;IACDR,mBAAmB,GAAGQ,GAAtB;EACD;AAVwD,CAA3D;;AAaAjB,YAAY,CAACC,IAAb,GAAoB,YAAW;EAE7B,IAAI,KAAKI,OAAL,KAAiBC,SAAjB,IACA,KAAKD,OAAL,KAAiBjB,MAAM,CAAC+B,cAAP,CAAsB,IAAtB,EAA4Bd,OADjD,EAC0D;IACxD,KAAKA,OAAL,GAAejB,MAAM,CAACgC,MAAP,CAAc,IAAd,CAAf;IACA,KAAKb,YAAL,GAAoB,CAApB;EACD;;EAED,KAAKC,aAAL,GAAqB,KAAKA,aAAL,IAAsBF,SAA3C;AACD,CATD,C,CAWA;AACA;;;AACAN,YAAY,CAAChB,SAAb,CAAuBqC,eAAvB,GAAyC,SAASA,eAAT,CAAyBC,CAAzB,EAA4B;EACnE,IAAI,OAAOA,CAAP,KAAa,QAAb,IAAyBA,CAAC,GAAG,CAA7B,IAAkC1B,WAAW,CAAC0B,CAAD,CAAjD,EAAsD;IACpD,MAAM,IAAIJ,UAAJ,CAAe,kFAAkFI,CAAlF,GAAsF,GAArG,CAAN;EACD;;EACD,KAAKd,aAAL,GAAqBc,CAArB;EACA,OAAO,IAAP;AACD,CAND;;AAQA,SAASC,gBAAT,CAA0BC,IAA1B,EAAgC;EAC9B,IAAIA,IAAI,CAAChB,aAAL,KAAuBF,SAA3B,EACE,OAAON,YAAY,CAACS,mBAApB;EACF,OAAOe,IAAI,CAAChB,aAAZ;AACD;;AAEDR,YAAY,CAAChB,SAAb,CAAuByC,eAAvB,GAAyC,SAASA,eAAT,GAA2B;EAClE,OAAOF,gBAAgB,CAAC,IAAD,CAAvB;AACD,CAFD;;AAIAvB,YAAY,CAAChB,SAAb,CAAuB0C,IAAvB,GAA8B,SAASA,IAAT,CAAcC,IAAd,EAAoB;EAChD,IAAI7C,IAAI,GAAG,EAAX;;EACA,KAAK,IAAI8C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,SAAS,CAACC,MAA9B,EAAsCF,CAAC,EAAvC,EAA2C9C,IAAI,CAACiD,IAAL,CAAUF,SAAS,CAACD,CAAD,CAAnB;;EAC3C,IAAII,OAAO,GAAIL,IAAI,KAAK,OAAxB;EAEA,IAAIM,MAAM,GAAG,KAAK5B,OAAlB;EACA,IAAI4B,MAAM,KAAK3B,SAAf,EACE0B,OAAO,GAAIA,OAAO,IAAIC,MAAM,CAACC,KAAP,KAAiB5B,SAAvC,CADF,KAEK,IAAI,CAAC0B,OAAL,EACH,OAAO,KAAP,CAT8C,CAWhD;;EACA,IAAIA,OAAJ,EAAa;IACX,IAAIG,EAAJ;IACA,IAAIrD,IAAI,CAACgD,MAAL,GAAc,CAAlB,EACEK,EAAE,GAAGrD,IAAI,CAAC,CAAD,CAAT;;IACF,IAAIqD,EAAE,YAAYC,KAAlB,EAAyB;MACvB;MACA;MACA,MAAMD,EAAN,CAHuB,CAGb;IACX,CARU,CASX;;;IACA,IAAIE,GAAG,GAAG,IAAID,KAAJ,CAAU,sBAAsBD,EAAE,GAAG,OAAOA,EAAE,CAACG,OAAV,GAAoB,GAAvB,GAA6B,EAArD,CAAV,CAAV;IACAD,GAAG,CAACE,OAAJ,GAAcJ,EAAd;IACA,MAAME,GAAN,CAZW,CAYA;EACZ;;EAED,IAAIG,OAAO,GAAGP,MAAM,CAACN,IAAD,CAApB;EAEA,IAAIa,OAAO,KAAKlC,SAAhB,EACE,OAAO,KAAP;;EAEF,IAAI,OAAOkC,OAAP,KAAmB,UAAvB,EAAmC;IACjC9D,YAAY,CAAC8D,OAAD,EAAU,IAAV,EAAgB1D,IAAhB,CAAZ;EACD,CAFD,MAEO;IACL,IAAI2D,GAAG,GAAGD,OAAO,CAACV,MAAlB;IACA,IAAIY,SAAS,GAAGC,UAAU,CAACH,OAAD,EAAUC,GAAV,CAA1B;;IACA,KAAK,IAAIb,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGa,GAApB,EAAyB,EAAEb,CAA3B,EACElD,YAAY,CAACgE,SAAS,CAACd,CAAD,CAAV,EAAe,IAAf,EAAqB9C,IAArB,CAAZ;EACH;;EAED,OAAO,IAAP;AACD,CA1CD;;AA4CA,SAAS8D,YAAT,CAAsBhE,MAAtB,EAA8B+C,IAA9B,EAAoChB,QAApC,EAA8CkC,OAA9C,EAAuD;EACrD,IAAIC,CAAJ;EACA,IAAIb,MAAJ;EACA,IAAIc,QAAJ;EAEArC,aAAa,CAACC,QAAD,CAAb;EAEAsB,MAAM,GAAGrD,MAAM,CAACyB,OAAhB;;EACA,IAAI4B,MAAM,KAAK3B,SAAf,EAA0B;IACxB2B,MAAM,GAAGrD,MAAM,CAACyB,OAAP,GAAiBjB,MAAM,CAACgC,MAAP,CAAc,IAAd,CAA1B;IACAxC,MAAM,CAAC2B,YAAP,GAAsB,CAAtB;EACD,CAHD,MAGO;IACL;IACA;IACA,IAAI0B,MAAM,CAACe,WAAP,KAAuB1C,SAA3B,EAAsC;MACpC1B,MAAM,CAAC8C,IAAP,CAAY,aAAZ,EAA2BC,IAA3B,EACYhB,QAAQ,CAACA,QAAT,GAAoBA,QAAQ,CAACA,QAA7B,GAAwCA,QADpD,EADoC,CAIpC;MACA;;MACAsB,MAAM,GAAGrD,MAAM,CAACyB,OAAhB;IACD;;IACD0C,QAAQ,GAAGd,MAAM,CAACN,IAAD,CAAjB;EACD;;EAED,IAAIoB,QAAQ,KAAKzC,SAAjB,EAA4B;IAC1B;IACAyC,QAAQ,GAAGd,MAAM,CAACN,IAAD,CAAN,GAAehB,QAA1B;IACA,EAAE/B,MAAM,CAAC2B,YAAT;EACD,CAJD,MAIO;IACL,IAAI,OAAOwC,QAAP,KAAoB,UAAxB,EAAoC;MAClC;MACAA,QAAQ,GAAGd,MAAM,CAACN,IAAD,CAAN,GACTkB,OAAO,GAAG,CAAClC,QAAD,EAAWoC,QAAX,CAAH,GAA0B,CAACA,QAAD,EAAWpC,QAAX,CADnC,CAFkC,CAIlC;IACD,CALD,MAKO,IAAIkC,OAAJ,EAAa;MAClBE,QAAQ,CAACE,OAAT,CAAiBtC,QAAjB;IACD,CAFM,MAEA;MACLoC,QAAQ,CAAChB,IAAT,CAAcpB,QAAd;IACD,CAVI,CAYL;;;IACAmC,CAAC,GAAGvB,gBAAgB,CAAC3C,MAAD,CAApB;;IACA,IAAIkE,CAAC,GAAG,CAAJ,IAASC,QAAQ,CAACjB,MAAT,GAAkBgB,CAA3B,IAAgC,CAACC,QAAQ,CAACG,MAA9C,EAAsD;MACpDH,QAAQ,CAACG,MAAT,GAAkB,IAAlB,CADoD,CAEpD;MACA;;MACA,IAAIC,CAAC,GAAG,IAAIf,KAAJ,CAAU,iDACEW,QAAQ,CAACjB,MADX,GACoB,GADpB,GAC0BsB,MAAM,CAACzB,IAAD,CADhC,GACyC,aADzC,GAEE,0CAFF,GAGE,gBAHZ,CAAR;MAIAwB,CAAC,CAACE,IAAF,GAAS,6BAAT;MACAF,CAAC,CAACG,OAAF,GAAY1E,MAAZ;MACAuE,CAAC,CAACxB,IAAF,GAASA,IAAT;MACAwB,CAAC,CAACI,KAAF,GAAUR,QAAQ,CAACjB,MAAnB;MACAtC,kBAAkB,CAAC2D,CAAD,CAAlB;IACD;EACF;;EAED,OAAOvE,MAAP;AACD;;AAEDoB,YAAY,CAAChB,SAAb,CAAuBwE,WAAvB,GAAqC,SAASA,WAAT,CAAqB7B,IAArB,EAA2BhB,QAA3B,EAAqC;EACxE,OAAOiC,YAAY,CAAC,IAAD,EAAOjB,IAAP,EAAahB,QAAb,EAAuB,KAAvB,CAAnB;AACD,CAFD;;AAIAX,YAAY,CAAChB,SAAb,CAAuByE,EAAvB,GAA4BzD,YAAY,CAAChB,SAAb,CAAuBwE,WAAnD;;AAEAxD,YAAY,CAAChB,SAAb,CAAuB0E,eAAvB,GACI,SAASA,eAAT,CAAyB/B,IAAzB,EAA+BhB,QAA/B,EAAyC;EACvC,OAAOiC,YAAY,CAAC,IAAD,EAAOjB,IAAP,EAAahB,QAAb,EAAuB,IAAvB,CAAnB;AACD,CAHL;;AAKA,SAASgD,WAAT,GAAuB;EACrB,IAAI,CAAC,KAAKC,KAAV,EAAiB;IACf,KAAKhF,MAAL,CAAYiF,cAAZ,CAA2B,KAAKlC,IAAhC,EAAsC,KAAKmC,MAA3C;IACA,KAAKF,KAAL,GAAa,IAAb;IACA,IAAI/B,SAAS,CAACC,MAAV,KAAqB,CAAzB,EACE,OAAO,KAAKnB,QAAL,CAAc1B,IAAd,CAAmB,KAAKL,MAAxB,CAAP;IACF,OAAO,KAAK+B,QAAL,CAAchC,KAAd,CAAoB,KAAKC,MAAzB,EAAiCiD,SAAjC,CAAP;EACD;AACF;;AAED,SAASkC,SAAT,CAAmBnF,MAAnB,EAA2B+C,IAA3B,EAAiChB,QAAjC,EAA2C;EACzC,IAAIqD,KAAK,GAAG;IAAEJ,KAAK,EAAE,KAAT;IAAgBE,MAAM,EAAExD,SAAxB;IAAmC1B,MAAM,EAAEA,MAA3C;IAAmD+C,IAAI,EAAEA,IAAzD;IAA+DhB,QAAQ,EAAEA;EAAzE,CAAZ;EACA,IAAIsD,OAAO,GAAGN,WAAW,CAACO,IAAZ,CAAiBF,KAAjB,CAAd;EACAC,OAAO,CAACtD,QAAR,GAAmBA,QAAnB;EACAqD,KAAK,CAACF,MAAN,GAAeG,OAAf;EACA,OAAOA,OAAP;AACD;;AAEDjE,YAAY,CAAChB,SAAb,CAAuBoB,IAAvB,GAA8B,SAASA,IAAT,CAAcuB,IAAd,EAAoBhB,QAApB,EAA8B;EAC1DD,aAAa,CAACC,QAAD,CAAb;EACA,KAAK8C,EAAL,CAAQ9B,IAAR,EAAcoC,SAAS,CAAC,IAAD,EAAOpC,IAAP,EAAahB,QAAb,CAAvB;EACA,OAAO,IAAP;AACD,CAJD;;AAMAX,YAAY,CAAChB,SAAb,CAAuBmF,mBAAvB,GACI,SAASA,mBAAT,CAA6BxC,IAA7B,EAAmChB,QAAnC,EAA6C;EAC3CD,aAAa,CAACC,QAAD,CAAb;EACA,KAAK+C,eAAL,CAAqB/B,IAArB,EAA2BoC,SAAS,CAAC,IAAD,EAAOpC,IAAP,EAAahB,QAAb,CAApC;EACA,OAAO,IAAP;AACD,CALL,C,CAOA;;;AACAX,YAAY,CAAChB,SAAb,CAAuB6E,cAAvB,GACI,SAASA,cAAT,CAAwBlC,IAAxB,EAA8BhB,QAA9B,EAAwC;EACtC,IAAIyD,IAAJ,EAAUnC,MAAV,EAAkBoC,QAAlB,EAA4BzC,CAA5B,EAA+B0C,gBAA/B;EAEA5D,aAAa,CAACC,QAAD,CAAb;EAEAsB,MAAM,GAAG,KAAK5B,OAAd;EACA,IAAI4B,MAAM,KAAK3B,SAAf,EACE,OAAO,IAAP;EAEF8D,IAAI,GAAGnC,MAAM,CAACN,IAAD,CAAb;EACA,IAAIyC,IAAI,KAAK9D,SAAb,EACE,OAAO,IAAP;;EAEF,IAAI8D,IAAI,KAAKzD,QAAT,IAAqByD,IAAI,CAACzD,QAAL,KAAkBA,QAA3C,EAAqD;IACnD,IAAI,EAAE,KAAKJ,YAAP,KAAwB,CAA5B,EACE,KAAKF,OAAL,GAAejB,MAAM,CAACgC,MAAP,CAAc,IAAd,CAAf,CADF,KAEK;MACH,OAAOa,MAAM,CAACN,IAAD,CAAb;MACA,IAAIM,MAAM,CAAC4B,cAAX,EACE,KAAKnC,IAAL,CAAU,gBAAV,EAA4BC,IAA5B,EAAkCyC,IAAI,CAACzD,QAAL,IAAiBA,QAAnD;IACH;EACF,CARD,MAQO,IAAI,OAAOyD,IAAP,KAAgB,UAApB,EAAgC;IACrCC,QAAQ,GAAG,CAAC,CAAZ;;IAEA,KAAKzC,CAAC,GAAGwC,IAAI,CAACtC,MAAL,GAAc,CAAvB,EAA0BF,CAAC,IAAI,CAA/B,EAAkCA,CAAC,EAAnC,EAAuC;MACrC,IAAIwC,IAAI,CAACxC,CAAD,CAAJ,KAAYjB,QAAZ,IAAwByD,IAAI,CAACxC,CAAD,CAAJ,CAAQjB,QAAR,KAAqBA,QAAjD,EAA2D;QACzD2D,gBAAgB,GAAGF,IAAI,CAACxC,CAAD,CAAJ,CAAQjB,QAA3B;QACA0D,QAAQ,GAAGzC,CAAX;QACA;MACD;IACF;;IAED,IAAIyC,QAAQ,GAAG,CAAf,EACE,OAAO,IAAP;IAEF,IAAIA,QAAQ,KAAK,CAAjB,EACED,IAAI,CAACG,KAAL,GADF,KAEK;MACHC,SAAS,CAACJ,IAAD,EAAOC,QAAP,CAAT;IACD;IAED,IAAID,IAAI,CAACtC,MAAL,KAAgB,CAApB,EACEG,MAAM,CAACN,IAAD,CAAN,GAAeyC,IAAI,CAAC,CAAD,CAAnB;IAEF,IAAInC,MAAM,CAAC4B,cAAP,KAA0BvD,SAA9B,EACE,KAAKoB,IAAL,CAAU,gBAAV,EAA4BC,IAA5B,EAAkC2C,gBAAgB,IAAI3D,QAAtD;EACH;;EAED,OAAO,IAAP;AACD,CAlDL;;AAoDAX,YAAY,CAAChB,SAAb,CAAuByF,GAAvB,GAA6BzE,YAAY,CAAChB,SAAb,CAAuB6E,cAApD;;AAEA7D,YAAY,CAAChB,SAAb,CAAuB0F,kBAAvB,GACI,SAASA,kBAAT,CAA4B/C,IAA5B,EAAkC;EAChC,IAAIe,SAAJ,EAAeT,MAAf,EAAuBL,CAAvB;EAEAK,MAAM,GAAG,KAAK5B,OAAd;EACA,IAAI4B,MAAM,KAAK3B,SAAf,EACE,OAAO,IAAP,CAL8B,CAOhC;;EACA,IAAI2B,MAAM,CAAC4B,cAAP,KAA0BvD,SAA9B,EAAyC;IACvC,IAAIuB,SAAS,CAACC,MAAV,KAAqB,CAAzB,EAA4B;MAC1B,KAAKzB,OAAL,GAAejB,MAAM,CAACgC,MAAP,CAAc,IAAd,CAAf;MACA,KAAKb,YAAL,GAAoB,CAApB;IACD,CAHD,MAGO,IAAI0B,MAAM,CAACN,IAAD,CAAN,KAAiBrB,SAArB,EAAgC;MACrC,IAAI,EAAE,KAAKC,YAAP,KAAwB,CAA5B,EACE,KAAKF,OAAL,GAAejB,MAAM,CAACgC,MAAP,CAAc,IAAd,CAAf,CADF,KAGE,OAAOa,MAAM,CAACN,IAAD,CAAb;IACH;;IACD,OAAO,IAAP;EACD,CAnB+B,CAqBhC;;;EACA,IAAIE,SAAS,CAACC,MAAV,KAAqB,CAAzB,EAA4B;IAC1B,IAAI6C,IAAI,GAAGvF,MAAM,CAACuF,IAAP,CAAY1C,MAAZ,CAAX;IACA,IAAI2C,GAAJ;;IACA,KAAKhD,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG+C,IAAI,CAAC7C,MAArB,EAA6B,EAAEF,CAA/B,EAAkC;MAChCgD,GAAG,GAAGD,IAAI,CAAC/C,CAAD,CAAV;MACA,IAAIgD,GAAG,KAAK,gBAAZ,EAA8B;MAC9B,KAAKF,kBAAL,CAAwBE,GAAxB;IACD;;IACD,KAAKF,kBAAL,CAAwB,gBAAxB;IACA,KAAKrE,OAAL,GAAejB,MAAM,CAACgC,MAAP,CAAc,IAAd,CAAf;IACA,KAAKb,YAAL,GAAoB,CAApB;IACA,OAAO,IAAP;EACD;;EAEDmC,SAAS,GAAGT,MAAM,CAACN,IAAD,CAAlB;;EAEA,IAAI,OAAOe,SAAP,KAAqB,UAAzB,EAAqC;IACnC,KAAKmB,cAAL,CAAoBlC,IAApB,EAA0Be,SAA1B;EACD,CAFD,MAEO,IAAIA,SAAS,KAAKpC,SAAlB,EAA6B;IAClC;IACA,KAAKsB,CAAC,GAAGc,SAAS,CAACZ,MAAV,GAAmB,CAA5B,EAA+BF,CAAC,IAAI,CAApC,EAAuCA,CAAC,EAAxC,EAA4C;MAC1C,KAAKiC,cAAL,CAAoBlC,IAApB,EAA0Be,SAAS,CAACd,CAAD,CAAnC;IACD;EACF;;EAED,OAAO,IAAP;AACD,CAjDL;;AAmDA,SAASiD,UAAT,CAAoBjG,MAApB,EAA4B+C,IAA5B,EAAkCmD,MAAlC,EAA0C;EACxC,IAAI7C,MAAM,GAAGrD,MAAM,CAACyB,OAApB;EAEA,IAAI4B,MAAM,KAAK3B,SAAf,EACE,OAAO,EAAP;EAEF,IAAIyE,UAAU,GAAG9C,MAAM,CAACN,IAAD,CAAvB;EACA,IAAIoD,UAAU,KAAKzE,SAAnB,EACE,OAAO,EAAP;EAEF,IAAI,OAAOyE,UAAP,KAAsB,UAA1B,EACE,OAAOD,MAAM,GAAG,CAACC,UAAU,CAACpE,QAAX,IAAuBoE,UAAxB,CAAH,GAAyC,CAACA,UAAD,CAAtD;EAEF,OAAOD,MAAM,GACXE,eAAe,CAACD,UAAD,CADJ,GACmBpC,UAAU,CAACoC,UAAD,EAAaA,UAAU,CAACjD,MAAxB,CAD1C;AAED;;AAED9B,YAAY,CAAChB,SAAb,CAAuB0D,SAAvB,GAAmC,SAASA,SAAT,CAAmBf,IAAnB,EAAyB;EAC1D,OAAOkD,UAAU,CAAC,IAAD,EAAOlD,IAAP,EAAa,IAAb,CAAjB;AACD,CAFD;;AAIA3B,YAAY,CAAChB,SAAb,CAAuBiG,YAAvB,GAAsC,SAASA,YAAT,CAAsBtD,IAAtB,EAA4B;EAChE,OAAOkD,UAAU,CAAC,IAAD,EAAOlD,IAAP,EAAa,KAAb,CAAjB;AACD,CAFD;;AAIA3B,YAAY,CAACkF,aAAb,GAA6B,UAAS5B,OAAT,EAAkB3B,IAAlB,EAAwB;EACnD,IAAI,OAAO2B,OAAO,CAAC4B,aAAf,KAAiC,UAArC,EAAiD;IAC/C,OAAO5B,OAAO,CAAC4B,aAAR,CAAsBvD,IAAtB,CAAP;EACD,CAFD,MAEO;IACL,OAAOuD,aAAa,CAACjG,IAAd,CAAmBqE,OAAnB,EAA4B3B,IAA5B,CAAP;EACD;AACF,CAND;;AAQA3B,YAAY,CAAChB,SAAb,CAAuBkG,aAAvB,GAAuCA,aAAvC;;AACA,SAASA,aAAT,CAAuBvD,IAAvB,EAA6B;EAC3B,IAAIM,MAAM,GAAG,KAAK5B,OAAlB;;EAEA,IAAI4B,MAAM,KAAK3B,SAAf,EAA0B;IACxB,IAAIyE,UAAU,GAAG9C,MAAM,CAACN,IAAD,CAAvB;;IAEA,IAAI,OAAOoD,UAAP,KAAsB,UAA1B,EAAsC;MACpC,OAAO,CAAP;IACD,CAFD,MAEO,IAAIA,UAAU,KAAKzE,SAAnB,EAA8B;MACnC,OAAOyE,UAAU,CAACjD,MAAlB;IACD;EACF;;EAED,OAAO,CAAP;AACD;;AAED9B,YAAY,CAAChB,SAAb,CAAuBmG,UAAvB,GAAoC,SAASA,UAAT,GAAsB;EACxD,OAAO,KAAK5E,YAAL,GAAoB,CAApB,GAAwBrB,cAAc,CAAC,KAAKmB,OAAN,CAAtC,GAAuD,EAA9D;AACD,CAFD;;AAIA,SAASsC,UAAT,CAAoByC,GAApB,EAAyB9D,CAAzB,EAA4B;EAC1B,IAAI+D,IAAI,GAAG,IAAIC,KAAJ,CAAUhE,CAAV,CAAX;;EACA,KAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGN,CAApB,EAAuB,EAAEM,CAAzB,EACEyD,IAAI,CAACzD,CAAD,CAAJ,GAAUwD,GAAG,CAACxD,CAAD,CAAb;;EACF,OAAOyD,IAAP;AACD;;AAED,SAASb,SAAT,CAAmBJ,IAAnB,EAAyBmB,KAAzB,EAAgC;EAC9B,OAAOA,KAAK,GAAG,CAAR,GAAYnB,IAAI,CAACtC,MAAxB,EAAgCyD,KAAK,EAArC,EACEnB,IAAI,CAACmB,KAAD,CAAJ,GAAcnB,IAAI,CAACmB,KAAK,GAAG,CAAT,CAAlB;;EACFnB,IAAI,CAACoB,GAAL;AACD;;AAED,SAASR,eAAT,CAAyBI,GAAzB,EAA8B;EAC5B,IAAIK,GAAG,GAAG,IAAIH,KAAJ,CAAUF,GAAG,CAACtD,MAAd,CAAV;;EACA,KAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6D,GAAG,CAAC3D,MAAxB,EAAgC,EAAEF,CAAlC,EAAqC;IACnC6D,GAAG,CAAC7D,CAAD,CAAH,GAASwD,GAAG,CAACxD,CAAD,CAAH,CAAOjB,QAAP,IAAmByE,GAAG,CAACxD,CAAD,CAA/B;EACD;;EACD,OAAO6D,GAAP;AACD;;AAED,SAASrF,IAAT,CAAckD,OAAd,EAAuBD,IAAvB,EAA6B;EAC3B,OAAO,IAAIqC,OAAJ,CAAY,UAAUC,OAAV,EAAmBC,MAAnB,EAA2B;IAC5C,SAASC,aAAT,CAAuBxD,GAAvB,EAA4B;MAC1BiB,OAAO,CAACO,cAAR,CAAuBR,IAAvB,EAA6ByC,QAA7B;MACAF,MAAM,CAACvD,GAAD,CAAN;IACD;;IAED,SAASyD,QAAT,GAAoB;MAClB,IAAI,OAAOxC,OAAO,CAACO,cAAf,KAAkC,UAAtC,EAAkD;QAChDP,OAAO,CAACO,cAAR,CAAuB,OAAvB,EAAgCgC,aAAhC;MACD;;MACDF,OAAO,CAAC,GAAGI,KAAH,CAAS9G,IAAT,CAAc4C,SAAd,CAAD,CAAP;IACD;;IAAA;IAEDmE,8BAA8B,CAAC1C,OAAD,EAAUD,IAAV,EAAgByC,QAAhB,EAA0B;MAAE1F,IAAI,EAAE;IAAR,CAA1B,CAA9B;;IACA,IAAIiD,IAAI,KAAK,OAAb,EAAsB;MACpB4C,6BAA6B,CAAC3C,OAAD,EAAUuC,aAAV,EAAyB;QAAEzF,IAAI,EAAE;MAAR,CAAzB,CAA7B;IACD;EACF,CAjBM,CAAP;AAkBD;;AAED,SAAS6F,6BAAT,CAAuC3C,OAAvC,EAAgDd,OAAhD,EAAyD0D,KAAzD,EAAgE;EAC9D,IAAI,OAAO5C,OAAO,CAACG,EAAf,KAAsB,UAA1B,EAAsC;IACpCuC,8BAA8B,CAAC1C,OAAD,EAAU,OAAV,EAAmBd,OAAnB,EAA4B0D,KAA5B,CAA9B;EACD;AACF;;AAED,SAASF,8BAAT,CAAwC1C,OAAxC,EAAiDD,IAAjD,EAAuD1C,QAAvD,EAAiEuF,KAAjE,EAAwE;EACtE,IAAI,OAAO5C,OAAO,CAACG,EAAf,KAAsB,UAA1B,EAAsC;IACpC,IAAIyC,KAAK,CAAC9F,IAAV,EAAgB;MACdkD,OAAO,CAAClD,IAAR,CAAaiD,IAAb,EAAmB1C,QAAnB;IACD,CAFD,MAEO;MACL2C,OAAO,CAACG,EAAR,CAAWJ,IAAX,EAAiB1C,QAAjB;IACD;EACF,CAND,MAMO,IAAI,OAAO2C,OAAO,CAAC6C,gBAAf,KAAoC,UAAxC,EAAoD;IACzD;IACA;IACA7C,OAAO,CAAC6C,gBAAR,CAAyB9C,IAAzB,EAA+B,SAAS+C,YAAT,CAAsBnF,GAAtB,EAA2B;MACxD;MACA;MACA,IAAIiF,KAAK,CAAC9F,IAAV,EAAgB;QACdkD,OAAO,CAAC+C,mBAAR,CAA4BhD,IAA5B,EAAkC+C,YAAlC;MACD;;MACDzF,QAAQ,CAACM,GAAD,CAAR;IACD,CAPD;EAQD,CAXM,MAWA;IACL,MAAM,IAAIL,SAAJ,CAAc,wEAAwE,OAAO0C,OAA7F,CAAN;EACD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script"}