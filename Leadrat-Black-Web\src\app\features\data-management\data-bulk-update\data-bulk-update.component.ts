import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { GridApi } from 'ag-grid-community';
import { BsDropdownDirective } from 'ngx-bootstrap/dropdown';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { catchError, combineLatest, of, skipWhile, take, takeUntil } from 'rxjs';
import {
  VALIDATION_CLEAR,
  VALIDATION_SET
} from 'src/app/app.constants';
import { LeadAssignmentType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  ConversionStatus,
  DataTopFilter,
} from 'src/app/core/interfaces/data-management.interface';
import {
  assignToSort,
  changeCalendar,
  getAssignedToDetails,
  getTimeZoneDate,
  onPickerOpened,
  setTimeZoneDateWithTime,
  toggleValidation,
  validateAllFormFields,
  validateScheduleTime
} from 'src/app/core/utils/common.util';
import { ConvertConfirmationComponent } from 'src/app/features/data-management/convert-confirmation/convert-confirmation.component';
import { BulkLeadsEmailShareComponent } from 'src/app/features/leads/bulk-leads-email-share/bulk-leads-email-share.component';
import { LeadBulkShareComponent } from 'src/app/features/leads/lead-bulk-share/lead-bulk-share.component';
import {
  BulkAgency,
  BulkAssignData,
  BulkCampaign,
  BulkChannelPartner,
  BulkDeleteData,
  BulkSource,
  BulkStatusUpdate,
  FetchAllData,
  FetchDataConversionStatus,
  FetchDataSourceList,
  FetchDataSubSourceList,
  PermanentDeleteData,
  RestoreData
} from 'src/app/reducers/data/data-management.actions';
import {
  DataManagementFilters,
  getBulkAgencyIsLoading,
  getBulkAssignIsLoading,
  getBulkCampaignIsLoading,
  getBulkCPIsLoading,
  getBulkDeleteIsLoading,
  getBulkRestoreIsLoading,
  getBulkSourceIsLoading,
  getBulkUpdateStatusIsLoading,
  getDataConversionStatus,
  getDataFiltersPayload,
  getDataSourceList,
  getDataStatusList,
  getDataSubSourceList,
  getDataTopFilters,
} from 'src/app/reducers/data/data-management.reducer';
import { FetchAllSources } from 'src/app/reducers/global-settings/global-settings.actions';
import { getAllSources, getAllSourcesLoading, getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchAgencyNameList, FetchBulkOperation, FetchCampaignList, FetchChannelPartnerList } from 'src/app/reducers/lead/lead.actions';
import { getAgencyNameList, getAgencyNameListIsLoading, getCampaignList, getCampaignListIsLoading, getChannelPartnerList, getChannelPartnerListIsLoading, getProjectList, getProjectListIsLoading } from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { DataManagementService } from 'src/app/services/controllers/data-management.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { BulkOperationTrackerComponent } from 'src/app/shared/components/bulk-operation-tracker/bulk-operation-tracker.component';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'data-bulk-update',
  templateUrl: './data-bulk-update.component.html',
})
export class DataBulkUpdateComponent implements OnInit, OnDestroy, OnChanges {
  stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('BulkConvertToLeadModal') BulkConvertToLeadModal: any;
  @ViewChild('convertPopUp') convertPopUp: any;
  @ViewChild('moreActionsDropdownRef', { static: false }) moreActionsDropdown!: BsDropdownDirective;
  @Input() gridApi: GridApi;
  @Input() allUsers: any;
  @Input() deactiveUsers: any;
  statusList: any[] = [];
  statusIdMap: any = {};

  canEditData: boolean = false;
  canAssignData: boolean = false;
  canDeleteData: boolean = false;

  assignToUsersList: { label: string; value: string }[];
  bulkReassignForm: FormGroup;
  bulkAgencyForm: FormGroup;
  convertToLeadForm: FormGroup;
  bulkUpdateStatusForm: FormGroup;
  bulkChannelPartnerForm: FormGroup;
  bulkCampaignForm: FormGroup;
  selectedChannelPartner: any;
  campaignList: any[] = [];
  campaignListIsLoading: boolean = false;
  channelPartnerList: any[] = [];
  channelPartnerListIsLoading: boolean = false;
  selectedCampaign: any;
  bulkSourceForm: FormGroup;
  dataStatus: Array<string> = [
    'New',
    'Qualified',
    'Follow Ups',
    ' Not Interested',
    'Not Answered',
    'Not Reachable',
    'Invalid/Wrong Number',
  ];

  conditionalStatus: ConversionStatus[];
  selectedStatus: any;
  projectList: Array<any> = [];
  isDeletedView: boolean = true;
  isConvertedView: boolean = true;
  topFilters: DataTopFilter[] = [];
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened;

  filtersPayload: DataManagementFilters;
  isBulkConvertLoading: boolean = false;
  isBulkUpdateStatusLoading: boolean = false;
  isBulkAssignLoading: boolean = false;
  isBulkDeleteLoading: boolean = false;
  isBulkRestoreLoading: boolean = false;
  isBulkSourceLoading: boolean = false;
  isBulkDataDelete: boolean = false;
  canBulkDelete: boolean = false;
  canBulkUpdateStatus: boolean = false;
  canBulkReassign: boolean = false;
  canBulkRestore: boolean = false;
  canBulkWhatsapp: boolean = false;
  canBulkEmail: boolean = false;
  canBulkConvertToLead: boolean = false;
  canBulkUpdateSource: boolean = false;
  bulkDuplicateData: any[];
  convertSuccessMessage: any = {
    header: '',
    message: '',
  };

  selectedNodes: any;
  selectedSources: any[];
  message: string;
  userData: any;
  leadSources: any[] = [];
  filteredSubSourceList: any[] = [];
  isSourcesLoading: boolean = false;
  successful: AnimationOptions = {
    path: 'assets/animations/party.json',
  };

  sad: AnimationOptions = {
    path: 'assets/animations/sad.json',
  };
  selectedStatusList: any[] = [];
  allSubSourceList: any;
  subSourceListIsLoading: boolean = true;
  canUpdateSource: boolean = false;
  canViewLeadSource: boolean = false;
  globalSettingsData: any;
  isUnassignLeadSelected: boolean;
  projectListIsLoading: boolean = true;
  leadAssignmentType = LeadAssignmentType;
  selectedAgencies: any[] = [];
  sameStatusRows: any[] = [];
  bulkAgencyIsLoading: boolean = false;
  bulkCampaignIsLoading: boolean = false;
  bulkCPIsLoading: boolean = false;
  type: string;
  agencyList: any[] = [];
  agencyListIsLoading: boolean = false;
  assignmentTypeOptions = [
    { value: this.leadAssignmentType.WithHistory, label: 'With history' },
    { value: this.leadAssignmentType.WithoutHistory, label: 'Without history' },
    {
      value: this.leadAssignmentType.WithoutHistoryWithNewStatus,
      label: 'Without history and new status',
    },
  ];
  visibleButtons: { id: string; }[];
  canBulkAgency: boolean = false;
  canBulkCampaign: boolean = false;
  canBulkChannelPartner: boolean = false;
  get minDate(): Date {
    const minDate = new Date(this.currentDate);
    minDate.setHours(0, 0, 0, 0);
    return minDate;
  }
  get isUnassignedData(): boolean {
    return this.filtersPayload?.ProspectVisiblity == 5;
  }
  @ViewChild('trackerInfoModal') trackerInfoModal: any;

  constructor(
    public modalService: BsModalService,
    private bulkReassignModalRef: BsModalRef,
    private bulkUpdateStatusModalRef: BsModalRef,
    private bulkDeleteModalRef: BsModalRef,
    private bulkConvertToLeadModalRef: BsModalRef,
    private bulkSourceModalRef: BsModalRef,
    private BulkSuccessModalRef: BsModalRef,
    private _store: Store<AppState>,
    private modalRef: BsModalRef,
    private formBuilder: FormBuilder,
    private DataManagementService: DataManagementService,
    public trackingService: TrackingService,
    private bulkChannelPartnerModalRef: BsModalRef,
    private bulkCampaignModalRef: BsModalRef,
    private bulkAgencyModalRef: BsModalRef,
  ) {
    // this.minDate = new Date();
    // this.minDate.setDate(this.minDate.getDate());
  }

  ngOnInit(): void {
    this._store.dispatch(new FetchDataConversionStatus());
    this._store.dispatch(new FetchDataSubSourceList());
    this._store.dispatch(new FetchAllSources());
    const allSources$ = this._store.select(getAllSources);
    const dataSourceList$ = this._store.select(getDataSourceList);
    combineLatest([allSources$, dataSourceList$])
      .pipe(takeUntil(this.stopper))
      .subscribe(([leadSource, dataSource]) => {
        if (leadSource && dataSource) {
          const matched = dataSource
            .filter((data: any) =>
              leadSource.some((lead: any) => lead?.value === data?.value && lead?.isEnabled))
            .map((data: any) => {
              const matchedLead = leadSource.find((lead: any) => lead?.value === data?.value && lead?.isEnabled);
              return {
                ...data,
                isEnabled: matchedLead?.isEnabled ?? false,
                isDefault: matchedLead?.isDefault ?? false
              };
            });
          const sorted = matched.sort((a: any, b: any) => {
            if (a.isEnabled === b.isEnabled) {
              return a.displayName.localeCompare(b.displayName);
            }
            return a.isEnabled ? -1 : 1;
          });
          this.leadSources = sorted;
        }
      });

    this._store
      .select(getAllSourcesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isSourcesLoading = loading;
      });

    this._store
      .select(getDataSubSourceList)
      .pipe(takeUntil(this.stopper))
      .subscribe((subSources: any) => {
        this.allSubSourceList = subSources;
        this.filteredSubSourceList = []; // Start with empty array until source is selected
      });

    this._store
      .select(getDataConversionStatus)
      .pipe(takeUntil(this.stopper))
      .subscribe((statuses: ConversionStatus[]) => {
        this.conditionalStatus = statuses;
      });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canEditData = permissionsSet.has('Permissions.Prospects.Update');
        this.canAssignData = permissionsSet.has('Permissions.Prospects.Assign');
        this.canDeleteData = permissionsSet.has('Permissions.Prospects.Delete');

        this.canBulkConvertToLead = permissionsSet.has('Permissions.Prospects.BulkConvertToLead');
        this.canBulkUpdateStatus = permissionsSet.has('Permissions.Prospects.BulkUpdateStatus');
        this.canBulkReassign = permissionsSet.has('Permissions.Prospects.BulkReassign');
        this.canBulkEmail = permissionsSet.has('Permissions.Prospects.BulkEmail');
        this.canBulkAgency = permissionsSet.has('Permissions.Prospects.BulkUpdateAgency');
        this.canBulkCampaign = permissionsSet.has('Permissions.Prospects.BulkUpdateCampaign');
        this.canBulkChannelPartner = permissionsSet.has('Permissions.Prospects.BulkUpdateChannelPartner');
        this.canBulkWhatsapp = permissionsSet.has('Permissions.Prospects.BulkWhatsApp');
        this.canBulkDelete = permissionsSet.has('Permissions.Prospects.BulkDelete');
        this.canBulkRestore = permissionsSet.has('Permissions.Prospects.BulkRestore');
        this.canBulkUpdateSource = permissionsSet.has('Permissions.Prospects.BulkUpdateSource');
        this.canUpdateSource = permissionsSet.has(
          'Permissions.Leads.UpdateSource'
        );
        this.canViewLeadSource = permissionsSet.has(
          'Permissions.Leads.ViewLeadSource'
        );
        this.updateVisibleButtons();
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (Object.keys(data || {}).length) {
          this.globalSettingsData = data;
          this.updateVisibleButtons();
        }
      });

    this._store
      .select(getProjectList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.projectList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.projectListIsLoading = loading
      });

    this._store
      .select(getAgencyNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.agencyList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getAgencyNameListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.agencyListIsLoading = loading
      });

    this._store
      .select(getChannelPartnerList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.channelPartnerList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getChannelPartnerListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.channelPartnerListIsLoading = loading
      });

    this._store
      .select(getCampaignList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.campaignList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this._store
      .select(getCampaignListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.campaignListIsLoading = loading
      });

    this._store
      .select(getDataFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((filters: DataManagementFilters) => {
        this.filtersPayload = filters;
        this.checkFilterView();
        this.updateVisibleButtons();
      });

    this._store
      .select(getDataTopFilters)
      .pipe(takeUntil(this.stopper))
      .subscribe(
        (dataTopFilters: {
          dataTopLevelFilters: DataTopFilter[];
          isTopFiltersLoadedOnce: boolean;
        }) => {
          this.topFilters = dataTopFilters?.dataTopLevelFilters;
          this.checkFilterView();
        }
      );

    this._store
      .select(getDataStatusList)
      .pipe(takeUntil(this.stopper))
      .subscribe((statuses: any) => {
        this.statusList = statuses;
        statuses.forEach((status: any) => {
          this.statusIdMap[status.id] = status.displayName;
        });
      });
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });
    this.updateVisibleButtons();
  }

  /**
   * Function to check if the top level filter selected is Deleted or Converted
   */
  checkFilterView(): void {
    this.isDeletedView =
      this.topFilters?.filter((filter: DataTopFilter) => filter?.isDeleted)?.[0]
        ?.enumValue === this.filtersPayload?.ProspectVisiblity;
    this.isConvertedView =
      this.topFilters?.filter(
        (filter: DataTopFilter) => filter?.isConverted
      )?.[0]?.enumValue === this.filtersPayload?.ProspectVisiblity;
  }

  hexToRgba(hex: string, opacity: number) {
    hex = hex.replace('#', '');
    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);
    const validOpacity = Math.min(1, Math.max(0, opacity));
    return `rgba(${r}, ${g}, ${b}, ${validOpacity})`;
  }

  getBgColor(color: string) {
    return `color:${color || '#4B4B4B'}; background-color: ${this.hexToRgba(
      color || '#4B4B4B',
      0.08
    )}`;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (Object.keys(changes).includes('allUsers')) {
      this.allUsers = changes?.allUsers?.currentValue;
      const sortedActiveUsers = assignToSort(
        this.allUsers?.filter((user: any) => user.isActive),
        null
      );

      this.assignToUsersList = sortedActiveUsers.map((user: any) => {
        return {
          label: `${user.firstName} ${user.lastName}`,
          value: user.id,
        };
      });
    }
  }

  updateVisibleButtons() {
    const buttons = [];
    if (!this.isDeletedView && !this.isConvertedView && this.filtersPayload?.ProspectVisiblity !== 4) {
      if (!this.isUnassignedData && this.filtersPayload?.ProspectVisiblity !== 3 && this.canBulkConvertToLead) {
        buttons.push({ id: 'bulkConvertToLead' });
      }
      if (this.canBulkUpdateStatus && !this.isUnassignedData && this.filtersPayload?.ProspectVisiblity !== 3) {
        buttons.push({ id: 'bulkUpdateStatus' });
      }
      if (this.canBulkReassign && this.filtersPayload?.ProspectVisiblity !== 3) {
        buttons.push({ id: 'bulkReassign' });
      }
      if (this.canBulkWhatsapp) {
        buttons.push({ id: 'bulkWhatsapp' });
      }
      if (this.canBulkEmail) {
        buttons.push({ id: 'bulkEmail' });
      }
      if (this.canBulkAgency) {
        buttons.push({ id: 'bulkAgency' });
      }
      if (this.canBulkCampaign) {
        buttons.push({ id: 'bulkCampaign' });
      }
      if (this.canBulkChannelPartner) {
        buttons.push({ id: 'bulkChannelPartner' });
      }
      if (this.canBulkUpdateSource) {
        buttons.push({ id: 'bulkSource' });
      }
    }
    if (this.canBulkDelete && this.filtersPayload?.ProspectVisiblity !== 4) {
      buttons.push({ id: 'bulkDelete' });
    }
    if (this.canBulkRestore && this.filtersPayload?.ProspectVisiblity === 4) {
      buttons.push({ id: 'bulkRestore' });
    }
    if (this.canBulkDelete && this.isDeletedView) {
      buttons.push({ id: 'bulkDelete' });
    }
    this.visibleButtons = buttons;
  }

  openBulkChannelPartnerModal(BulkChannelPartnerModal: TemplateRef<any>) {
    this.bulkChannelPartnerForm = this.formBuilder.group({
      channelPartner: [null],
      shouldRemoveExistingChannelPartener: [false],
    });
    this._store.dispatch(new FetchChannelPartnerList());
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.selectedChannelPartner = this.selectedNodes?.map((node: any) => node.data);
    let initialState: any = {
      data: Object.values(this.selectedChannelPartner),
      class: 'right-modal modal-500 ip-modal-unset',
    };
    this.bulkChannelPartnerModalRef = this.modalService.show(
      BulkChannelPartnerModal,
      initialState
    );
  }

  openBulkCampaignModal(BulkCampaignModal: TemplateRef<any>) {
    this.bulkCampaignForm = this.formBuilder.group({
      campaign: [null],
      shouldRemoveExistingCampaign: [false],
    });
    this._store.dispatch(new FetchCampaignList());
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.selectedCampaign = this.selectedNodes?.map((node: any) => node.data);
    let initialState: any = {
      data: Object.values(this.selectedCampaign),
      class: 'right-modal modal-500 ip-modal-unset',
    };
    this.bulkCampaignModalRef = this.modalService.show(
      BulkCampaignModal,
      initialState
    );
  }

  openBulkAgencyModal(BulkAgencyModal: TemplateRef<any>) {
    this.bulkAgencyForm = this.formBuilder.group({
      agency: [null],
      shouldRemoveExistingAgency: [false],
    });
    this._store.dispatch(new FetchAgencyNameList());
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.selectedAgencies = this.selectedNodes?.map((node: any) => node.data);
    let initialState: any = {
      data: Object.values(this.selectedAgencies),
      class: 'right-modal modal-500 ip-modal-unset',
    };
    this.bulkAgencyModalRef = this.modalService.show(
      BulkAgencyModal,
      initialState
    );
  }

  updateBulkAgency() {
    if (!this.bulkAgencyForm.valid) {
      validateAllFormFields(this.bulkAgencyForm);
      return;
    }
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.sameStatusRows = this.selectedNodes?.map((node: any) => node.data);
    let resource: any = {
      ids: this.sameStatusRows.map((item: any) => item.id),
    };
    let selectedAgency = this.bulkAgencyForm.get('agency').value;

    resource = {
      ...resource,
      agencyNames: selectedAgency,
      shouldRemoveExistingAgency: this.bulkAgencyForm?.get(
        'shouldRemoveExistingAgency'
      )?.value,
    };
    this._store.dispatch(new BulkAgency(resource));
    const intervalId = setInterval(() => {
      if (this.modalService.getModalsCount() === 0) {
        const numberOfLeads = resource?.ids?.length;
        if (numberOfLeads >= 50) {
          this.type = 'Bulk Agency';
          this.modalRef = this.modalService.show(
            this.trackerInfoModal,
            Object.assign(
              {},
              {
                class: 'modal-400 top-modal ph-modal-unset',
                ignoreBackdropClick: true,
                keyboard: false,
              }
            )
          );
        }
      }
      clearInterval(intervalId);
    }, 2000);
    this.bulkAgencyIsLoading = true;
    this._store
      .select(getBulkAgencyIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.bulkAgencyIsLoading = isLoading;
        if (!isLoading) {
          this.bulkAgencyModalRef.hide();
        }
      });
    this.selectedNodes = {};
    this.sameStatusRows = [];
    this.bulkAgencyForm.reset();
    if (this.bulkAgencyModalRef) {
      this.bulkAgencyModalRef.hide();
      this.gridApi.deselectAll();
    }
  }

  updateBulkCampaign() {
    if (!this.bulkCampaignForm.valid) {
      validateAllFormFields(this.bulkCampaignForm);
      return;
    }
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.sameStatusRows = this.selectedNodes?.map((node: any) => node.data);
    let resource: any = {
      ids: this.sameStatusRows.map((item: any) => item.id),
    };
    let selectedCampaign = this.bulkCampaignForm.get('campaign').value;

    resource = {
      ...resource,
      campaignNames: selectedCampaign,
      shouldRemoveExistingCampaign: this.bulkCampaignForm?.get(
        'shouldRemoveExistingCampaign'
      )?.value,
    };
    this._store.dispatch(new BulkCampaign(resource));
    const intervalId = setInterval(() => {
      if (this.modalService.getModalsCount() === 0) {
        const numberOfLeads = resource?.ids?.length;
        if (numberOfLeads >= 50) {
          this.type = 'Bulk Campaign';
          this.modalRef = this.modalService.show(
            this.trackerInfoModal,
            Object.assign(
              {},
              {
                class: 'modal-400 top-modal ph-modal-unset',
                ignoreBackdropClick: true,
                keyboard: false,
              }
            )
          );
        }
      }
      clearInterval(intervalId);
    }, 2000);
    this.bulkCampaignIsLoading = true;
    this._store
      .select(getBulkCampaignIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.bulkCampaignIsLoading = isLoading;
        if (!isLoading) {
          this.bulkCampaignModalRef.hide();
        }
      });
    this.selectedNodes = {};
    this.sameStatusRows = [];
    this.bulkCampaignForm.reset();
    if (this.bulkCampaignModalRef) {
      this.bulkCampaignModalRef.hide();
      this.gridApi.deselectAll();
    }
  }

  updateBulkChannelPartner() {
    if (!this.bulkChannelPartnerForm.valid) {
      validateAllFormFields(this.bulkChannelPartnerForm);
      return;
    }
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.sameStatusRows = this.selectedNodes?.map((node: any) => node.data);
    let resource: any = {
      ids: this.sameStatusRows.map((item: any) => item.id),
    };
    let selectedChannelPartner = this.bulkChannelPartnerForm.get('channelPartner').value;

    resource = {
      ...resource,
      channelPartnerNames: selectedChannelPartner,
      shouldRemoveExistingChannelPartener: this.bulkChannelPartnerForm?.get(
        'shouldRemoveExistingChannelPartener'
      )?.value,
    };
    this._store.dispatch(new BulkChannelPartner(resource));
    const intervalId = setInterval(() => {
      if (this.modalService.getModalsCount() === 0) {
        const numberOfLeads = resource?.ids?.length;
        if (numberOfLeads >= 50) {
          this.type = 'Bulk Channel Partner';
          this.modalRef = this.modalService.show(
            this.trackerInfoModal,
            Object.assign(
              {},
              {
                class: 'modal-400 top-modal ph-modal-unset',
                ignoreBackdropClick: true,
                keyboard: false,
              }
            )
          );
        }
      }
      clearInterval(intervalId);
    }, 2000);
    this.bulkCPIsLoading = true;
    this._store
      .select(getBulkCPIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.bulkCPIsLoading = isLoading;
        if (!isLoading) {
          this.bulkChannelPartnerModalRef.hide();
        }
      });
    this.selectedNodes = {};
    this.sameStatusRows = [];
    this.bulkChannelPartnerForm.reset();
    if (this.bulkChannelPartnerModalRef) {
      this.bulkChannelPartnerModalRef.hide();
      this.gridApi.deselectAll();
    }
  }

  getAgencyNames(data: any) {
    return data?.agencies.map((agency: any) => agency.name).join(', ');
  }

  getCampaignNames(data: any) {
    return data?.campaigns.map((campaign: any) => campaign.name).join(', ');
  }

  getChannelPartnerNames(data: any) {
    return data?.channelPartners.map((channelPartner: any) => channelPartner.firmName).join(', ');
  }

  openBulkReassignModal(BulkReassignModal: TemplateRef<any>) {
    this.bulkReassignForm = this.formBuilder.group({
      assignedToUsers: ['', Validators.required],
      assignmentType: 0,
      isChangeSourceSelected: false,
      selectedSource: null,
      selectedSubSource: null,
      isChangeProjectSelected: false,
      selectedProject: null,
    });
    this._store.dispatch(new FetchDataSubSourceList());
    this._store.dispatch(new FetchDataSourceList());

    this.bulkReassignForm
      .get('isChangeSourceSelected')
      .valueChanges.subscribe((val: any) => {
        if (val) {
          toggleValidation(
            VALIDATION_SET,
            this.bulkReassignForm,
            'selectedSource',
            [Validators.required]
          );
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.bulkReassignForm,
            'selectedSource'
          );
        }
      });

    this.bulkReassignForm
      .get('isChangeProjectSelected')
      .valueChanges.subscribe((val: any) => {
        if (val) {
          toggleValidation(
            VALIDATION_SET,
            this.bulkReassignForm,
            'selectedProject',
            [Validators.required]
          );
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.bulkReassignForm,
            'selectedProject'
          );
        }
      });
    let initialState: any = {
      class: 'modal-500 right-modal ip-modal-unset',
    };

    this.trackingService.trackFeature('Web.Data.Page.BulkReassign.Visit')
    this.bulkReassignModalRef = this.modalService.show(
      BulkReassignModal,
      initialState
    );
  }

  openBulkUpdateStatusModal(BulkUpdateStatusModal: TemplateRef<any>) {
    this.bulkUpdateStatusForm = this.formBuilder.group({
      statusId: ['', Validators.required],
      scheduleDate: [null, this.validateScheduleTime(this.currentDate)],
      notes: null,
    });

    this.bulkUpdateStatusForm.get('statusId').valueChanges.subscribe((_: any) => {
      this.bulkUpdateStatusForm.get('scheduleDate').setValue(null)
      const makeRequired: any = {
        ScheduledDate: ['scheduleDate', 'scheduleTime']
      };

      if (this.selectedStatusList?.length) {
        this.selectedStatusList.forEach((status: any) => {
          const controlsToMakeRequired = makeRequired[status.field.name];

          controlsToMakeRequired?.forEach((controlName: string) => {
            const control = this.bulkUpdateStatusForm.get(controlName);

            if (
              status.isRequired === true ||
              (status.validators && status.validators.includes('required'))
            ) {
              if (controlName === 'scheduleTime') {
                control?.setValidators([Validators.required, this.validateScheduleTime(this.currentDate)]);
              } else {
                control?.setValidators([Validators.required]);
              }
            } else {
              if (controlName === 'scheduleTime') {
                const scheduleDateValue = this.bulkUpdateStatusForm.get('scheduleDate')?.value;
                if (scheduleDateValue) {
                  control?.setValidators([this.validateScheduleTime(this.currentDate)]);
                } else {
                  control?.clearValidators();
                }
              } else {
                control?.clearValidators();
              }
            }
            control?.updateValueAndValidity();
          });
        });
      } else {
        (Object.values(makeRequired).flat() as string[]).forEach((controlName: string) => {
          const control = this.bulkUpdateStatusForm.get(controlName);
          control?.clearValidators();
          control?.updateValueAndValidity();
        });
      }
    });

    let initialState: any = {
      class: 'modal-500 right-modal ip-modal-unset',
    };
    this.bulkUpdateStatusModalRef = this.modalService.show(
      BulkUpdateStatusModal,
      initialState
    );
    this.bulkUpdateStatusModalRef.onHide.subscribe(() => {
      this.selectedStatusList = []
    })
  }

  bulkUpdateStatus() {
    if (!this.bulkUpdateStatusForm.valid) {
      validateAllFormFields(this.bulkUpdateStatusForm);
      return;
    }
    const formData = this.bulkUpdateStatusForm.value;

    const payload = {
      ids: this.gridApi
        ?.getSelectedNodes()
        ?.map((dataNodes: any) => dataNodes?.data?.id),
      statusId: formData?.statusId,
      scheduleDate:
        setTimeZoneDateWithTime(
          formData?.scheduleDate,
          this.userData?.timeZoneInfo?.baseUTcOffset
        ) || null,
      notes: formData?.notes || null,
    };
    this.isBulkUpdateStatusLoading = true;

    this._store.dispatch(new BulkStatusUpdate(payload));
    this._store
      .select(getBulkUpdateStatusIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => isLoading),
        take(1)
      )
      .subscribe(() => {
        this.isBulkUpdateStatusLoading = false;
        const intervalId = setInterval(() => {
          if (this.modalService.getModalsCount() === 0) {
            const numberOfLeads = payload?.ids?.length;
            if (numberOfLeads >= 50) {
              this.modalRef = this.modalService.show(
                this.trackerInfoModal,
                Object.assign(
                  {},
                  {
                    class: 'modal-400 top-modal ph-modal-unset',
                    ignoreBackdropClick: true,
                    keyboard: false,
                  }
                )
              );
            }
          }
          clearInterval(intervalId);
        }, 2000);
        this.bulkUpdateStatusModalRef.hide();
      });
  }

  openBulkDeleteModal(BulkDeleteModal: TemplateRef<any>) {
    let initialState: any = {
      class: 'modal-300 right-modal',
    };
    this.trackingService.trackFeature('Web.Data.Page.BulkDelete.Visit')
    this.bulkDeleteModalRef = this.modalService.show(
      BulkDeleteModal,
      initialState
    );
  }

  openEmailConfirmation(changePopup: any, noMail: any) {
    this.selectedNodes = this.gridApi?.getSelectedNodes();
    const emails: string[] = this.selectedNodes?.map((node: any) =>
      node?.data?.email ? node?.data?.email : null
    );
    const noOfEmails: number = emails?.filter((email: string) => email)?.length;
    if (emails?.length == noOfEmails) {
      this.openBulkEmailPopup();
      return;
    }
    if (noOfEmails === 0) {
      this.message = `No emails were attached in the selected leads`;
      this.modalRef = this.modalService.show(noMail, {
        class: 'modal-600 top-modal ip-modal-unset',
      });
      return;
    }

    this.modalRef = this.modalService.show(changePopup, {
      class: 'modal-600 top-modal ip-modal-unset',
    });

    this.message = `You have selected ${emails?.length} Data, only ${noOfEmails} of them have emails attached. Do you want to proceed?`;
  }

  openBulkShareModal() {
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    let initialState: any = {
      data: {
        bulkData: this.selectedNodes?.map((data: any) => data?.data),
        shareType: 'WhasApp',
        isData: true,
      },
    };
    this.modalService.show(
      LeadBulkShareComponent,
      Object.assign(
        {},
        {
          class: 'right-modal modal-300',
          initialState,
        }
      )
    );
  }

  openBulkSourceModal(BulkSourceModal: TemplateRef<any>) {
    this.bulkSourceForm = this.formBuilder.group({
      source: [null, Validators.required],
      subsource: [null],
    });
    this._store.dispatch(new FetchDataSubSourceList());
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.selectedSources = this.selectedNodes?.map((node: any) => node.data);
    let initialState: any = {
      data: Object.values(this.selectedSources),
      class: 'right-modal modal-500 ip-modal-unset',
    };
    this.bulkSourceModalRef = this.modalService.show(
      BulkSourceModal,
      initialState
    );
    this.trackingService.trackFeature('Web.Data.Button.BulkSource.Click');
  }

  updateBulkSource() {
    if (this.bulkSourceForm.invalid) {
      validateAllFormFields(this.bulkSourceForm);
      return;
    }
    if (this.gridApi) {
      this.selectedNodes = this.gridApi?.getSelectedNodes();
    }
    this.isBulkSourceLoading = true;
    this.selectedSources = this.selectedNodes?.map((node: any) => node.data);
    const selectedSource = this.bulkSourceForm.get('source').value;
    let resource: any = {
      prospectIds: this.selectedSources.map((item: any) => item.id),
      prospectSourceId: selectedSource.id,
      subSource: this.bulkSourceForm.get('subsource').value ?? null,
    };
    if (selectedSource) {
      this._store.dispatch(new BulkSource(resource));
      const intervalId = setInterval(() => {
        if (this.modalService.getModalsCount() === 0) {
          const numberOfLeads = resource?.prospectIds?.length;
          if (numberOfLeads >= 50) {
            this.modalRef = this.modalService.show(
              this.trackerInfoModal,
              Object.assign(
                {},
                {
                  class: 'modal-400 top-modal ph-modal-unset',
                  ignoreBackdropClick: true,
                  keyboard: false,
                }
              )
            );
          }
        }
        clearInterval(intervalId);
      }, 2000);
      this._store
        .select(getBulkSourceIsLoading)
        .pipe(
          skipWhile((isLoading: boolean) => isLoading),
          take(1)
        )
        .subscribe(() => {
          this.isBulkSourceLoading = false;
          this.bulkSourceModalRef.hide();
        });
    }
  }

  updateSubSourceForBulkSource() {
    this.filteredSubSourceList = [];
    const selectedSource = this.bulkSourceForm?.get('source')?.value;
    if (selectedSource) {
      this.bulkSourceForm?.get('subsource')?.setValue(null);
      if (selectedSource.displayName === '99 Acres') {
        this.filteredSubSourceList = this.allSubSourceList['NinetyNineAcres'] || [];
      } else if (selectedSource.displayName === 'Roof&Floor') {
        this.filteredSubSourceList = this.allSubSourceList['RoofandFloor'] || [];
      } else {
        const formattedSourceName = selectedSource.displayName.replace(/\s+/g, '');
        if (this.allSubSourceList[formattedSourceName]) {
          this.filteredSubSourceList = this.allSubSourceList[formattedSourceName] || [];
        } else {
          this.filteredSubSourceList = this.allSubSourceList[selectedSource.displayName] || [];
        }
      }
    } else {
      this.bulkSourceForm?.get('source')?.setValue(null);
      this.bulkSourceForm?.get('subsource')?.setValue(null);
    }
  }

  openBulkEmailPopup() {
    this.modalRef?.hide();
    this.selectedNodes = this.gridApi?.getSelectedNodes();
    let initialState: any = {
      data: {
        bulkData: this.selectedNodes?.map((data: any) => data?.data),
        shareType: 'Email',
        selectedNodes: this.selectedNodes,
        isData: true,
      },
    };

    this.modalRef = this.modalService.show(
      BulkLeadsEmailShareComponent,
      Object.assign(
        {},
        { class: 'modal-600 right-modal ip-modal-unset', initialState }
      )
    );
  }

  openBulkConvertToLeadModal(
    BulkConvertToLeadModal: TemplateRef<any>,
    confirmationPopup: TemplateRef<any>
  ) {
    this.trackingService.trackFeature('Web.Data.Page.BulkConvertLead.Visit')
    this.selectedStatus = null;
    this.convertToLeadForm = this.formBuilder.group({
      assignedToUsers: [null, Validators.required],
      status: [null],
      subStatus: [null],
      scheduledDate: [null],
      notes: [null],
      projects: [null],
    });
    let payload = this.gridApi?.getSelectedNodes().map((node: any) => {
      return {
        prospectId: node?.data?.id,
        primaryContactNo: node?.data?.contactNo,
        alternateContactNo: node?.data?.alternateContactNo,
      };
    });
    this.DataManagementService.convertToLeadBulk(payload)
      .pipe(
        take(1),
        catchError((err) => {
          console.error('Error occurred:', err);
          return of({ data: null }); // You can adjust this to suit your error handling strategy
        })
      )
      .subscribe((data: any) => {
        if (!data?.data.length) {
          let initialState: any = {
            class: 'modal-350 right-modal',
          };
          this.bulkConvertToLeadModalRef = this.modalService.show(
            BulkConvertToLeadModal,
            initialState
          );
        } else {
          this.bulkDuplicateData = this.gridApi
            .getSelectedNodes()
            .filter((node: any) => data?.data?.includes(node?.data?.id));
          let initialState: any = {
            class: 'modal-500 top-modal ip-modal-unset',
          };
          this.modalRef = this.modalService.show(
            confirmationPopup,
            initialState
          );
        }
      });
  }

  openBulkUpdatedStatus() {
    this._store.dispatch(new FetchBulkOperation(1, 10, 'prospect'));
    this.modalService.show(BulkOperationTrackerComponent, {

      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
      initialState: {
        moduleType: 'prospect',
      },
    });
  }

  proceedWithDuplicates() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      class: 'modal-350 right-modal',
    };
    this.bulkConvertToLeadModalRef = this.modalService.show(
      this.BulkConvertToLeadModal,
      initialState
    );
  }

  proceedWithoutDuplicates() {
    if (this.modalRef) {
      this.modalRef.hide();
    }

    const duplicateIds = this.bulkDuplicateData.map(
      (node: any) => node?.data?.id
    );

    this.gridApi?.getSelectedNodes().forEach((node: any) => {
      if (duplicateIds.includes(node?.data?.id)) {
        node.setSelected(false);
      }
    });
    let initialState: any = {
      class: 'modal-350 right-modal',
    };
    this.bulkConvertToLeadModalRef = this.modalService.show(
      this.BulkConvertToLeadModal,
      initialState
    );
  }

  /**
   * Function to update the selectedStatus when the user selects a status
   * and add or remove required validation for sub statuses.
   * The required validation is added if there are sub statuses for the selected status
   * If there are no sub statuses the validation is removed
   * @param status Currently selected status
   */
  statusChanged(status: any): void {
    this.selectedStatus = status;
    toggleValidation(VALIDATION_SET, this.convertToLeadForm, 'scheduledDate', [
      Validators.required,
      validateScheduleTime(this.currentDate),
    ]);
    if (this.selectedStatus?.childTypes?.length) {
      toggleValidation(VALIDATION_SET, this.convertToLeadForm, 'subStatus', [
        Validators.required,
      ]);
    }
  }

  /**
   * Function to deselct the status selected
   * remove the validation on scheduledDate
   * reset form values for status, subStatus, scheduledDate
   */
  deselectStatus(): void {
    this.selectedStatus = null;
    toggleValidation(VALIDATION_CLEAR, this.convertToLeadForm, 'scheduledDate');
    toggleValidation(VALIDATION_CLEAR, this.convertToLeadForm, 'subStatus');
    this.convertToLeadForm?.patchValue({
      status: null,
      subStatus: null,
      scheduledDate: null,
      projects: null,
    });
  }

  openBulkSuceessModal() {
    if (!this.convertToLeadForm.valid) {
      validateAllFormFields(this.convertToLeadForm);
      return;
    }
    const formData = this.convertToLeadForm.value;
    const payload = {
      ids: this.gridApi
        ?.getSelectedNodes()
        ?.map((dataNodes: any) => dataNodes?.data?.id),
      assignTo: formData.assignedToUsers?.map((user: any) => user?.value),
      leadStatusId: formData?.subStatus || formData?.status,
      scheduledDate: setTimeZoneDateWithTime(
        formData?.scheduledDate,
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      notes: formData?.notes,
      projects: formData?.projects,
      convertedDateTime: this.userData?.timeZoneInfo?.timeZoneName
        ? `${getTimeZoneDate(this.currentDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dateWithFullTime')} (${this.userData?.timeZoneInfo?.timeZoneName})`
        : getTimeZoneDate(this.currentDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dateWithFullTime')
    };
    this.isBulkConvertLoading = true;
    // this._store.dispatch(new BulkConvertToLead(payload))
    // this._store
    //   .select(getIsBulkConvertToLeadLoading)
    //   .pipe(skipWhile((isLoading: boolean) => isLoading), take(1))
    //   .subscribe(() => {
    //     this.showConvertToLeadSuccess()
    //     this.selectedStatus = null;
    //     this.bulkConvertToLeadModalRef.hide();
    //     this.isBulkConvertLoading = false;
    //   })
    // if (this.gridApi?.getSelectedNodes().length < 50) {
    this.DataManagementService.bulkConvertToLead(payload).subscribe(
      (resp: any) => {
        if (resp.succeeded) {
          if (resp?.data) {
            if (
              resp.data?.leads?.length ===
              this.gridApi?.getSelectedNodes().length
            ) {
              this.convertSuccessMessage.header = 'Uh-Oh!';
              (this.convertSuccessMessage.message =
                'Duplicate data cannot be converted into lead as it is being assigned to the same user.'),
                (this.convertSuccessMessage.success = false);
            } else {
              this.convertSuccessMessage.header = 'Awesome!';
              (this.convertSuccessMessage.message = `only ${resp.data?.leads?.length} duplicate data cannot be converted into lead as it is being assigned to the same user`),
                (this.convertSuccessMessage.success = true);
            }

            let successModal: any = this.modalService.show(this.convertPopUp, {
              class: 'modal-450 modal-dialog-centered ph-modal-unset',
              ignoreBackdropClick: false,
            });

            setTimeout(() => {
              successModal.hide();
            }, 10000);
          } else {
            this.showConvertToLeadSuccess();
          }
          this.selectedStatus = null;
          this.bulkConvertToLeadModalRef.hide();
          this.isBulkConvertLoading = false;
          this._store.dispatch(new FetchAllData());
        }
      }
    );
    // }
    //  else {
    //   this._store.dispatch(new BulkConvertToLead(payload));
    //   this.bulkConvertToLeadModalRef.hide();
    //   const intervalId = setInterval(() => {
    //     if (this.modalService.getModalsCount() === 0) {
    //       const numberOfLeads = payload?.ids?.length;
    //       if (numberOfLeads >= 50) {
    //         this.modalRef = this.modalService.show(
    //           this.trackerInfoModal,
    //           Object.assign(
    //             {},
    //             {
    //               class: 'modal-400 top-modal ph-modal-unset',
    //               ignoreBackdropClick: true,
    //               keyboard: false,
    //             }
    //           )
    //         );
    //       }
    //     }
    //     clearInterval(intervalId);
    //   }, 2000);
    //   this._store.dispatch(new FetchAllData());
    // }
  }

  showConvertToLeadSuccess() {
    if (this.bulkConvertToLeadModalRef) {
      this.bulkConvertToLeadModalRef.hide();
    }
    let initialState: any = {
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
      ignoreBackdropClick: false,
    };
    this.BulkSuccessModalRef = this.modalService.show(
      ConvertConfirmationComponent,
      initialState
    );
    setTimeout(() => {
      this.BulkSuccessModalRef.hide();
    }, 10000);
  }

  deselectData(): void {
    this.gridApi?.deselectAll();
  }

  bulkDelete(): void {
    const ids = this.gridApi
      ?.getSelectedNodes()
      .map((dataNodes: any) => dataNodes?.data?.id);
    if (this.isDeletedView && !this.isBulkDataDelete) {
      this.isBulkRestoreLoading = true;
      this._store.dispatch(new RestoreData(ids));
      this._store
        .select(getBulkRestoreIsLoading)
        .pipe(
          skipWhile((isLoading: boolean) => isLoading),
          take(1)
        )
        .subscribe(() => {
          this.isBulkRestoreLoading = false;
          this.modalService.hide();
        });
    } else if (this.isDeletedView && this.isBulkDataDelete) {
      this.permanentDelete(ids);
      this.isBulkDataDelete = false;
      this.bulkDeleteModalRef.hide();
    } else {
      this.isBulkDeleteLoading = true;
      this._store.dispatch(new BulkDeleteData(ids));
      this._store
        .select(getBulkDeleteIsLoading)
        .pipe(
          skipWhile((isLoading: boolean) => isLoading),
          take(1)
        )
        .subscribe(() => {
          this.isBulkDeleteLoading = false;
          this.modalService.hide();
        });
    }
  }

  getName(data: any): string {
    return (
      getAssignedToDetails(
        data?.assignTo,
        [...this.allUsers, ...this.deactiveUsers],
        true
      ) || ''
    );
  }

  openConfirmDeleteModal(dataName: string, dataId: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: dataName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.deselectDataWithId(dataId);
        }
        if (this.gridApi?.getSelectedNodes()?.length == 0)
          this.modalService.hide();
      });
    }
  }

  deselectDataWithId(id: string): void {
    const nodes = this.gridApi
      ?.getSelectedNodes()
      ?.filter((dataNodes: any) => dataNodes?.data?.id === id);
    if (nodes && nodes.length > 0) {
      nodes[0].setSelected(false);
    }
    this.selectedAgencies = this.selectedAgencies?.filter(agency => agency?.id !== id);
    if (this.selectedAgencies?.length <= 0 && this.bulkAgencyModalRef) {
      this.bulkAgencyModalRef.hide();
    }

    this.selectedSources = this.selectedSources.filter(source => source.id !== id);
    if (this.selectedSources.length <= 0 && this.bulkSourceModalRef) {
      this.bulkSourceModalRef.hide();
    }
  }

  updateBulkAssign(): void {
    if (!this.bulkReassignForm.valid) {
      validateAllFormFields(this.bulkReassignForm);
      return;
    }
    const formData = this.bulkReassignForm.value;
    const payload = {
      ids: this.gridApi
        ?.getSelectedNodes()
        ?.map((dataNodes: any) => dataNodes?.data?.id),
      userIds: formData.assignedToUsers?.map((user: any) => user?.value),
      assignmentType: formData.assignmentType,
      updateSource: formData.isChangeSourceSelected,
      prospectSourceId:
        formData.selectedSource && formData.isChangeSourceSelected
          ? formData.selectedSource?.id
          : null,
      updateSubSource: !!formData.selectedSubSource,
      subSource: formData.isChangeSourceSelected
        ? formData.selectedSubSource?.label
          ? formData.selectedSubSource.label
          : formData.selectedSubSource
        : null,
      createDuplicate: formData.isCreateDuplicateSelected,
      updateProject: formData.isChangeProjectSelected,
      projects:
        formData.isChangeProjectSelected && formData?.selectedProject?.length
          ? [...formData.selectedProject]
          : null,
      // bulkCategory: BulkType.BulkAssignment,

    };

    this.isBulkAssignLoading = true;
    this._store.dispatch(new BulkAssignData(payload));
    this._store.select(getBulkAssignIsLoading).subscribe((isLoading: boolean) => {
      if (!isLoading) {
        this.isBulkAssignLoading = isLoading;
        this.modalService.hide()
      }
    })
    const intervalId = setInterval(() => {
      if (this.modalService.getModalsCount() === 0) {
        const numberOfLeads = payload?.ids?.length;
        if (numberOfLeads >= 50) {
          this.modalRef = this.modalService.show(
            this.trackerInfoModal,
            Object.assign(
              {},
              {
                class: 'modal-400 top-modal ph-modal-unset',
                ignoreBackdropClick: true,
                keyboard: false,
              }
            )
          );
        }
      }
      clearInterval(intervalId);
    }, 2000);
  }

  permanentDelete(data: any) {
    if (this.modalRef) this.modalRef.hide();

    let initialState: any = {
      type: 'permanentDelete',
      data: {
        fieldType: 'Delete',
        heading: `Delete ${data?.length} data permanently?`,
        message: `You are about to delete the ${data?.length} data permanently. 🚫`,
        description:
          'Delete all information associated with these data. Once the data are deleted, the action is irreversible, and data recovery will not be possible.',
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };

    this.modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        {
          class: 'modal-450 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );

    if (this.modalRef?.onHide)
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed')
          this._store.dispatch(new PermanentDeleteData(data));
      });
  }

  updateStatus(status: any) {
    this.selectedStatusList = status?.customFields || []
  }

  validateScheduleTime(currDateOnTZ: any): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const currentTime = new Date(currDateOnTZ);
      const selectedTime = new Date(control?.value);

      const selectedHours = selectedTime?.getHours();
      const selectedMinutes = selectedTime?.getMinutes();
      const currentDate = new Date(currDateOnTZ);
      const scheduleDate = new Date(this.bulkUpdateStatusForm?.value?.scheduleDate);

      currentDate.setHours(0, 0, 0, 0);
      scheduleDate.setHours(0, 0, 0, 0);

      if (!control?.value) {
        return { required: true };
      }

      if (currentDate.getTime() === scheduleDate.getTime()) {
        const timeDifference = selectedTime.getTime() - currentTime.getTime();

        if (
          selectedHours < currentTime.getHours() ||
          (selectedHours === currentTime.getHours() &&
            selectedMinutes <= currentTime.getMinutes()) ||
          timeDifference < 1 * 60 * 1000
        ) {
          return { invalidTime: true };
        }
      }

      return null;
    };
  }

  onDatePickerClosed(controlName: string) {
    const control = this.bulkUpdateStatusForm.get(controlName);
    if (control && control.value) {
      control?.setValidators([this.validateScheduleTime(this.currentDate)]);
      control.updateValueAndValidity();
    }
  }

  onSourceChange(): void {
    this.filteredSubSourceList = [];
    const selectedSource = this.bulkReassignForm?.get('selectedSource')?.value;
    if (selectedSource) {
      this.bulkReassignForm?.get('selectedSubSource')?.setValue(null);
      if (selectedSource.displayName === '99 Acres') {
        this.filteredSubSourceList = this.allSubSourceList['NinetyNineAcres'] || [];
      } else if (selectedSource.displayName === 'Roof&Floor') {
        this.filteredSubSourceList = this.allSubSourceList['RoofandFloor'] || [];
      } else {
        const formattedSourceName = selectedSource.displayName.replace(/\s+/g, '');
        if (this.allSubSourceList[formattedSourceName]) {
          this.filteredSubSourceList = this.allSubSourceList[formattedSourceName] || [];
        } else {
          this.filteredSubSourceList = this.allSubSourceList[selectedSource.displayName] || [];
        }
      }
    } else {
      this.bulkReassignForm?.get('selectedSource')?.setValue(null);
      this.bulkReassignForm?.get('selectedSubSource')?.setValue(null);
    }
  }

  assignToUserListChanged(): void {
    if (
      this.bulkReassignForm
        .get('assignedToUsers')
        .value.some((user: any) => user.value === '')
    ) {
      const assignedToUsers = this.bulkReassignForm
        .get('assignedToUsers')
        .value.filter((user: any) => user.value === '');
      this.bulkReassignForm.patchValue({
        assignedToUsers: assignedToUsers,
        assignmentType: LeadAssignmentType.WithHistory,
        isChangeSourceSelected: false,
        selectedSource: null,
        selectedSubSource: null,
        isChangeProjectSelected: false,
        selectedProject: null,
        isCreateDuplicateSelected: false,
      });
      this.isUnassignLeadSelected = true;
      return;
    }
    this.isUnassignLeadSelected = false;
    this.trackingService.trackFeature('Web.Leads.Options.AssignToView.Click');
  }

  onAssignmentTypeChange(selectedValue: string) {
    let eventName = '';
    switch (selectedValue) {
      case 'With history':
        eventName = 'Web.Leads.Button.withHistory.Click';
        break;
      case 'Without history':
        eventName = 'Web.Leads.Button.withoutHistory.Click';
        break;
      case 'Without history and new status':
        eventName = 'Web.Leads.Button.withouthistorynewstatus.Click';
        break;
    }
    if (eventName) {
      this.trackingService.trackFeature(eventName);
    }
  }

  onScrollableContainerScroll(): void {
    if (this.moreActionsDropdown?.isOpen) {
      this.moreActionsDropdown.hide();
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
