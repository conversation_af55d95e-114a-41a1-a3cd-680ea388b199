{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Irish or Irish Gaelic [ga]\n//! author : <PERSON> : https://github.com/askpt\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var months = ['<PERSON>an<PERSON>ir', '<PERSON><PERSON><PERSON>', 'M<PERSON><PERSON>', 'Aibreán', 'Bealtaine', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>hai<PERSON>', '<PERSON>hain', '<PERSON>lla<PERSON>'],\n      monthsShort = ['Ean', '<PERSON>abh', '<PERSON>árt', 'Aib', 'Beal', 'Meith', '<PERSON><PERSON>il', 'Lún', 'M.F.', '<PERSON><PERSON><PERSON>.', '<PERSON><PERSON>', 'Noll'],\n      weekdays = ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON> Sathairn'],\n      weekdaysShort = ['Domh', 'Luan', 'Máirt', 'Céad', 'Déar', 'Aoine', 'Sath'],\n      weekdaysMin = ['Do', 'Lu', 'Má', 'Cé', 'Dé', 'A', 'Sa'];\n  var ga = moment.defineLocale('ga', {\n    months: months,\n    monthsShort: monthsShort,\n    monthsParseExact: true,\n    weekdays: weekdays,\n    weekdaysShort: weekdaysShort,\n    weekdaysMin: weekdaysMin,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Inniu ag] LT',\n      nextDay: '[Amárach ag] LT',\n      nextWeek: 'dddd [ag] LT',\n      lastDay: '[Inné ag] LT',\n      lastWeek: 'dddd [seo caite] [ag] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'i %s',\n      past: '%s ó shin',\n      s: 'cúpla soicind',\n      ss: '%d soicind',\n      m: 'nóiméad',\n      mm: '%d nóiméad',\n      h: 'uair an chloig',\n      hh: '%d uair an chloig',\n      d: 'lá',\n      dd: '%d lá',\n      M: 'mí',\n      MM: '%d míonna',\n      y: 'bliain',\n      yy: '%d bliain'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(d|na|mh)/,\n    ordinal: function (number) {\n      var output = number === 1 ? 'd' : number % 10 === 2 ? 'na' : 'mh';\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return ga;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "months", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "ga", "defineLocale", "monthsParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "output", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/moment/locale/ga.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Irish or Irish Gaelic [ga]\n//! author : <PERSON> : https://github.com/askpt\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var months = [\n            '<PERSON>an<PERSON>ir',\n            '<PERSON><PERSON><PERSON>',\n            '<PERSON><PERSON><PERSON>',\n            'Aibreán',\n            '<PERSON>altaine',\n            '<PERSON><PERSON><PERSON><PERSON>',\n            '<PERSON><PERSON><PERSON>',\n            '<PERSON><PERSON><PERSON>',\n            '<PERSON><PERSON>',\n            '<PERSON><PERSON><PERSON><PERSON>hai<PERSON>',\n            '<PERSON>hain',\n            '<PERSON><PERSON><PERSON>',\n        ],\n        monthsShort = [\n            'Ean',\n            '<PERSON>abh',\n            '<PERSON>árt',\n            'Aib',\n            'Beal',\n            'Meith',\n            '<PERSON><PERSON>il',\n            '<PERSON>ún',\n            'M.F.',\n            '<PERSON><PERSON><PERSON>.',\n            '<PERSON><PERSON>',\n            'Noll',\n        ],\n        weekdays = [\n            '<PERSON><PERSON>',\n            '<PERSON><PERSON>',\n            '<PERSON><PERSON>',\n            '<PERSON><PERSON>',\n            '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n            '<PERSON><PERSON>',\n            '<PERSON>é Sathairn',\n        ],\n        weekdaysShort = ['Domh', 'Luan', 'Máirt', 'Céad', 'Déar', 'Aoine', 'Sath'],\n        weekdaysMin = ['Do', 'Lu', 'Má', 'Cé', 'Dé', 'A', 'Sa'];\n\n    var ga = moment.defineLocale('ga', {\n        months: months,\n        monthsShort: monthsShort,\n        monthsParseExact: true,\n        weekdays: weekdays,\n        weekdaysShort: weekdaysShort,\n        weekdaysMin: weekdaysMin,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Inniu ag] LT',\n            nextDay: '[Amárach ag] LT',\n            nextWeek: 'dddd [ag] LT',\n            lastDay: '[Inné ag] LT',\n            lastWeek: 'dddd [seo caite] [ag] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'i %s',\n            past: '%s ó shin',\n            s: 'cúpla soicind',\n            ss: '%d soicind',\n            m: 'nóiméad',\n            mm: '%d nóiméad',\n            h: 'uair an chloig',\n            hh: '%d uair an chloig',\n            d: 'lá',\n            dd: '%d lá',\n            M: 'mí',\n            MM: '%d míonna',\n            y: 'bliain',\n            yy: '%d bliain',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(d|na|mh)/,\n        ordinal: function (number) {\n            var output = number === 1 ? 'd' : number % 10 === 2 ? 'na' : 'mh';\n            return number + output;\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return ga;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AAEA;;AAAE,WAAUA,MAAV,EAAkBC,OAAlB,EAA2B;EAC1B,OAAOC,OAAP,KAAmB,QAAnB,IAA+B,OAAOC,MAAP,KAAkB,WAAjD,IACO,OAAOC,OAAP,KAAmB,UAD1B,GACuCH,OAAO,CAACG,OAAO,CAAC,WAAD,CAAR,CAD9C,GAEA,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAAvC,GAA6CD,MAAM,CAAC,CAAC,WAAD,CAAD,EAAgBJ,OAAhB,CAAnD,GACAA,OAAO,CAACD,MAAM,CAACO,MAAR,CAHP;AAIF,CALC,EAKA,IALA,EAKO,UAAUA,MAAV,EAAkB;EAAE,aAAF,CAEvB;;EAEA,IAAIC,MAAM,GAAG,CACL,QADK,EAEL,SAFK,EAGL,OAHK,EAIL,SAJK,EAKL,WALK,EAML,WANK,EAOL,MAPK,EAQL,QARK,EASL,cATK,EAUL,kBAVK,EAWL,SAXK,EAYL,SAZK,CAAb;EAAA,IAcIC,WAAW,GAAG,CACV,KADU,EAEV,OAFU,EAGV,MAHU,EAIV,KAJU,EAKV,MALU,EAMV,OANU,EAOV,MAPU,EAQV,KARU,EASV,MATU,EAUV,MAVU,EAWV,MAXU,EAYV,MAZU,CAdlB;EAAA,IA4BIC,QAAQ,GAAG,CACP,cADO,EAEP,UAFO,EAGP,UAHO,EAIP,aAJO,EAKP,WALO,EAMP,WANO,EAOP,aAPO,CA5Bf;EAAA,IAqCIC,aAAa,GAAG,CAAC,MAAD,EAAS,MAAT,EAAiB,OAAjB,EAA0B,MAA1B,EAAkC,MAAlC,EAA0C,OAA1C,EAAmD,MAAnD,CArCpB;EAAA,IAsCIC,WAAW,GAAG,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB,EAA+B,GAA/B,EAAoC,IAApC,CAtClB;EAwCA,IAAIC,EAAE,GAAGN,MAAM,CAACO,YAAP,CAAoB,IAApB,EAA0B;IAC/BN,MAAM,EAAEA,MADuB;IAE/BC,WAAW,EAAEA,WAFkB;IAG/BM,gBAAgB,EAAE,IAHa;IAI/BL,QAAQ,EAAEA,QAJqB;IAK/BC,aAAa,EAAEA,aALgB;IAM/BC,WAAW,EAAEA,WANkB;IAO/BI,cAAc,EAAE;MACZC,EAAE,EAAE,OADQ;MAEZC,GAAG,EAAE,UAFO;MAGZC,CAAC,EAAE,YAHS;MAIZC,EAAE,EAAE,aAJQ;MAKZC,GAAG,EAAE,mBALO;MAMZC,IAAI,EAAE;IANM,CAPe;IAe/BC,QAAQ,EAAE;MACNC,OAAO,EAAE,eADH;MAENC,OAAO,EAAE,iBAFH;MAGNC,QAAQ,EAAE,cAHJ;MAINC,OAAO,EAAE,cAJH;MAKNC,QAAQ,EAAE,0BALJ;MAMNC,QAAQ,EAAE;IANJ,CAfqB;IAuB/BC,YAAY,EAAE;MACVC,MAAM,EAAE,MADE;MAEVC,IAAI,EAAE,WAFI;MAGVC,CAAC,EAAE,eAHO;MAIVC,EAAE,EAAE,YAJM;MAKVC,CAAC,EAAE,SALO;MAMVC,EAAE,EAAE,YANM;MAOVC,CAAC,EAAE,gBAPO;MAQVC,EAAE,EAAE,mBARM;MASVC,CAAC,EAAE,IATO;MAUVC,EAAE,EAAE,OAVM;MAWVC,CAAC,EAAE,IAXO;MAYVC,EAAE,EAAE,WAZM;MAaVC,CAAC,EAAE,QAbO;MAcVC,EAAE,EAAE;IAdM,CAvBiB;IAuC/BC,sBAAsB,EAAE,kBAvCO;IAwC/BC,OAAO,EAAE,UAAUC,MAAV,EAAkB;MACvB,IAAIC,MAAM,GAAGD,MAAM,KAAK,CAAX,GAAe,GAAf,GAAqBA,MAAM,GAAG,EAAT,KAAgB,CAAhB,GAAoB,IAApB,GAA2B,IAA7D;MACA,OAAOA,MAAM,GAAGC,MAAhB;IACH,CA3C8B;IA4C/BC,IAAI,EAAE;MACFC,GAAG,EAAE,CADH;MACM;MACRC,GAAG,EAAE,CAFH,CAEM;;IAFN;EA5CyB,CAA1B,CAAT;EAkDA,OAAOtC,EAAP;AAEH,CArGC,CAAD", "ignoreList": []}, "metadata": {}, "sourceType": "script"}