{"ast": null, "code": "\"use strict\";\n\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n      m = s && o[s],\n      i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.bufferTime = void 0;\n\nvar Subscription_1 = require(\"../Subscription\");\n\nvar lift_1 = require(\"../util/lift\");\n\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\n\nvar arrRemove_1 = require(\"../util/arrRemove\");\n\nvar async_1 = require(\"../scheduler/async\");\n\nvar args_1 = require(\"../util/args\");\n\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\n\nfunction bufferTime(bufferTimeSpan) {\n  var _a, _b;\n\n  var otherArgs = [];\n\n  for (var _i = 1; _i < arguments.length; _i++) {\n    otherArgs[_i - 1] = arguments[_i];\n  }\n\n  var scheduler = (_a = args_1.popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : async_1.asyncScheduler;\n  var bufferCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n  var maxBufferSize = otherArgs[1] || Infinity;\n  return lift_1.operate(function (source, subscriber) {\n    var bufferRecords = [];\n    var restartOnEmit = false;\n\n    var emit = function (record) {\n      var buffer = record.buffer,\n          subs = record.subs;\n      subs.unsubscribe();\n      arrRemove_1.arrRemove(bufferRecords, record);\n      subscriber.next(buffer);\n      restartOnEmit && startBuffer();\n    };\n\n    var startBuffer = function () {\n      if (bufferRecords) {\n        var subs = new Subscription_1.Subscription();\n        subscriber.add(subs);\n        var buffer = [];\n        var record_1 = {\n          buffer: buffer,\n          subs: subs\n        };\n        bufferRecords.push(record_1);\n        executeSchedule_1.executeSchedule(subs, scheduler, function () {\n          return emit(record_1);\n        }, bufferTimeSpan);\n      }\n    };\n\n    if (bufferCreationInterval !== null && bufferCreationInterval >= 0) {\n      executeSchedule_1.executeSchedule(subscriber, scheduler, startBuffer, bufferCreationInterval, true);\n    } else {\n      restartOnEmit = true;\n    }\n\n    startBuffer();\n    var bufferTimeSubscriber = OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      var e_1, _a;\n\n      var recordsCopy = bufferRecords.slice();\n\n      try {\n        for (var recordsCopy_1 = __values(recordsCopy), recordsCopy_1_1 = recordsCopy_1.next(); !recordsCopy_1_1.done; recordsCopy_1_1 = recordsCopy_1.next()) {\n          var record = recordsCopy_1_1.value;\n          var buffer = record.buffer;\n          buffer.push(value);\n          maxBufferSize <= buffer.length && emit(record);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (recordsCopy_1_1 && !recordsCopy_1_1.done && (_a = recordsCopy_1.return)) _a.call(recordsCopy_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }, function () {\n      while (bufferRecords === null || bufferRecords === void 0 ? void 0 : bufferRecords.length) {\n        subscriber.next(bufferRecords.shift().buffer);\n      }\n\n      bufferTimeSubscriber === null || bufferTimeSubscriber === void 0 ? void 0 : bufferTimeSubscriber.unsubscribe();\n      subscriber.complete();\n      subscriber.unsubscribe();\n    }, undefined, function () {\n      return bufferRecords = null;\n    });\n    source.subscribe(bufferTimeSubscriber);\n  });\n}\n\nexports.bufferTime = bufferTime;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "Object", "defineProperty", "exports", "bufferTime", "Subscription_1", "require", "lift_1", "OperatorSubscriber_1", "arrRemove_1", "async_1", "args_1", "executeSchedule_1", "bufferTimeSpan", "_a", "_b", "otherArgs", "_i", "arguments", "scheduler", "popScheduler", "asyncScheduler", "bufferCreationInterval", "maxBufferSize", "Infinity", "operate", "source", "subscriber", "bufferRecords", "restartOnEmit", "emit", "record", "buffer", "subs", "unsubscribe", "arr<PERSON><PERSON><PERSON>", "startBuffer", "Subscription", "add", "record_1", "push", "executeSchedule", "bufferTimeSubscriber", "createOperatorSubscriber", "e_1", "recordsCopy", "slice", "recordsCopy_1", "recordsCopy_1_1", "e_1_1", "error", "return", "shift", "complete", "undefined", "subscribe"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/rxjs/dist/cjs/internal/operators/bufferTime.js"], "sourcesContent": ["\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.bufferTime = void 0;\nvar Subscription_1 = require(\"../Subscription\");\nvar lift_1 = require(\"../util/lift\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nvar arrRemove_1 = require(\"../util/arrRemove\");\nvar async_1 = require(\"../scheduler/async\");\nvar args_1 = require(\"../util/args\");\nvar executeSchedule_1 = require(\"../util/executeSchedule\");\nfunction bufferTime(bufferTimeSpan) {\n    var _a, _b;\n    var otherArgs = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        otherArgs[_i - 1] = arguments[_i];\n    }\n    var scheduler = (_a = args_1.popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : async_1.asyncScheduler;\n    var bufferCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n    var maxBufferSize = otherArgs[1] || Infinity;\n    return lift_1.operate(function (source, subscriber) {\n        var bufferRecords = [];\n        var restartOnEmit = false;\n        var emit = function (record) {\n            var buffer = record.buffer, subs = record.subs;\n            subs.unsubscribe();\n            arrRemove_1.arrRemove(bufferRecords, record);\n            subscriber.next(buffer);\n            restartOnEmit && startBuffer();\n        };\n        var startBuffer = function () {\n            if (bufferRecords) {\n                var subs = new Subscription_1.Subscription();\n                subscriber.add(subs);\n                var buffer = [];\n                var record_1 = {\n                    buffer: buffer,\n                    subs: subs,\n                };\n                bufferRecords.push(record_1);\n                executeSchedule_1.executeSchedule(subs, scheduler, function () { return emit(record_1); }, bufferTimeSpan);\n            }\n        };\n        if (bufferCreationInterval !== null && bufferCreationInterval >= 0) {\n            executeSchedule_1.executeSchedule(subscriber, scheduler, startBuffer, bufferCreationInterval, true);\n        }\n        else {\n            restartOnEmit = true;\n        }\n        startBuffer();\n        var bufferTimeSubscriber = OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n            var e_1, _a;\n            var recordsCopy = bufferRecords.slice();\n            try {\n                for (var recordsCopy_1 = __values(recordsCopy), recordsCopy_1_1 = recordsCopy_1.next(); !recordsCopy_1_1.done; recordsCopy_1_1 = recordsCopy_1.next()) {\n                    var record = recordsCopy_1_1.value;\n                    var buffer = record.buffer;\n                    buffer.push(value);\n                    maxBufferSize <= buffer.length && emit(record);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (recordsCopy_1_1 && !recordsCopy_1_1.done && (_a = recordsCopy_1.return)) _a.call(recordsCopy_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }, function () {\n            while (bufferRecords === null || bufferRecords === void 0 ? void 0 : bufferRecords.length) {\n                subscriber.next(bufferRecords.shift().buffer);\n            }\n            bufferTimeSubscriber === null || bufferTimeSubscriber === void 0 ? void 0 : bufferTimeSubscriber.unsubscribe();\n            subscriber.complete();\n            subscriber.unsubscribe();\n        }, undefined, function () { return (bufferRecords = null); });\n        source.subscribe(bufferTimeSubscriber);\n    });\n}\nexports.bufferTime = bufferTime;\n"], "mappings": "AAAA;;AACA,IAAIA,QAAQ,GAAI,QAAQ,KAAKA,QAAd,IAA2B,UAASC,CAAT,EAAY;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,QAA/C;EAAA,IAAyDC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAD,CAAnE;EAAA,IAAwEI,CAAC,GAAG,CAA5E;EACA,IAAID,CAAJ,EAAO,OAAOA,CAAC,CAACE,IAAF,CAAON,CAAP,CAAP;EACP,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAT,KAAoB,QAA7B,EAAuC,OAAO;IAC1CC,IAAI,EAAE,YAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAhB,EAAwBP,CAAC,GAAG,KAAK,CAAT;MACxB,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAF,CAAf;QAAsBK,IAAI,EAAE,CAACV;MAA7B,CAAP;IACH;EAJyC,CAAP;EAMvC,MAAM,IAAIW,SAAJ,CAAcV,CAAC,GAAG,yBAAH,GAA+B,iCAA9C,CAAN;AACH,CAVD;;AAWAW,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;EAAEL,KAAK,EAAE;AAAT,CAA7C;AACAK,OAAO,CAACC,UAAR,GAAqB,KAAK,CAA1B;;AACA,IAAIC,cAAc,GAAGC,OAAO,CAAC,iBAAD,CAA5B;;AACA,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAD,CAApB;;AACA,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAD,CAAlC;;AACA,IAAIG,WAAW,GAAGH,OAAO,CAAC,mBAAD,CAAzB;;AACA,IAAII,OAAO,GAAGJ,OAAO,CAAC,oBAAD,CAArB;;AACA,IAAIK,MAAM,GAAGL,OAAO,CAAC,cAAD,CAApB;;AACA,IAAIM,iBAAiB,GAAGN,OAAO,CAAC,yBAAD,CAA/B;;AACA,SAASF,UAAT,CAAoBS,cAApB,EAAoC;EAChC,IAAIC,EAAJ,EAAQC,EAAR;;EACA,IAAIC,SAAS,GAAG,EAAhB;;EACA,KAAK,IAAIC,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAGC,SAAS,CAACtB,MAAhC,EAAwCqB,EAAE,EAA1C,EAA8C;IAC1CD,SAAS,CAACC,EAAE,GAAG,CAAN,CAAT,GAAoBC,SAAS,CAACD,EAAD,CAA7B;EACH;;EACD,IAAIE,SAAS,GAAG,CAACL,EAAE,GAAGH,MAAM,CAACS,YAAP,CAAoBJ,SAApB,CAAN,MAA0C,IAA1C,IAAkDF,EAAE,KAAK,KAAK,CAA9D,GAAkEA,EAAlE,GAAuEJ,OAAO,CAACW,cAA/F;EACA,IAAIC,sBAAsB,GAAG,CAACP,EAAE,GAAGC,SAAS,CAAC,CAAD,CAAf,MAAwB,IAAxB,IAAgCD,EAAE,KAAK,KAAK,CAA5C,GAAgDA,EAAhD,GAAqD,IAAlF;EACA,IAAIQ,aAAa,GAAGP,SAAS,CAAC,CAAD,CAAT,IAAgBQ,QAApC;EACA,OAAOjB,MAAM,CAACkB,OAAP,CAAe,UAAUC,MAAV,EAAkBC,UAAlB,EAA8B;IAChD,IAAIC,aAAa,GAAG,EAApB;IACA,IAAIC,aAAa,GAAG,KAApB;;IACA,IAAIC,IAAI,GAAG,UAAUC,MAAV,EAAkB;MACzB,IAAIC,MAAM,GAAGD,MAAM,CAACC,MAApB;MAAA,IAA4BC,IAAI,GAAGF,MAAM,CAACE,IAA1C;MACAA,IAAI,CAACC,WAAL;MACAzB,WAAW,CAAC0B,SAAZ,CAAsBP,aAAtB,EAAqCG,MAArC;MACAJ,UAAU,CAAC9B,IAAX,CAAgBmC,MAAhB;MACAH,aAAa,IAAIO,WAAW,EAA5B;IACH,CAND;;IAOA,IAAIA,WAAW,GAAG,YAAY;MAC1B,IAAIR,aAAJ,EAAmB;QACf,IAAIK,IAAI,GAAG,IAAI5B,cAAc,CAACgC,YAAnB,EAAX;QACAV,UAAU,CAACW,GAAX,CAAeL,IAAf;QACA,IAAID,MAAM,GAAG,EAAb;QACA,IAAIO,QAAQ,GAAG;UACXP,MAAM,EAAEA,MADG;UAEXC,IAAI,EAAEA;QAFK,CAAf;QAIAL,aAAa,CAACY,IAAd,CAAmBD,QAAnB;QACA3B,iBAAiB,CAAC6B,eAAlB,CAAkCR,IAAlC,EAAwCd,SAAxC,EAAmD,YAAY;UAAE,OAAOW,IAAI,CAACS,QAAD,CAAX;QAAwB,CAAzF,EAA2F1B,cAA3F;MACH;IACJ,CAZD;;IAaA,IAAIS,sBAAsB,KAAK,IAA3B,IAAmCA,sBAAsB,IAAI,CAAjE,EAAoE;MAChEV,iBAAiB,CAAC6B,eAAlB,CAAkCd,UAAlC,EAA8CR,SAA9C,EAAyDiB,WAAzD,EAAsEd,sBAAtE,EAA8F,IAA9F;IACH,CAFD,MAGK;MACDO,aAAa,GAAG,IAAhB;IACH;;IACDO,WAAW;IACX,IAAIM,oBAAoB,GAAGlC,oBAAoB,CAACmC,wBAArB,CAA8ChB,UAA9C,EAA0D,UAAU7B,KAAV,EAAiB;MAClG,IAAI8C,GAAJ,EAAS9B,EAAT;;MACA,IAAI+B,WAAW,GAAGjB,aAAa,CAACkB,KAAd,EAAlB;;MACA,IAAI;QACA,KAAK,IAAIC,aAAa,GAAG3D,QAAQ,CAACyD,WAAD,CAA5B,EAA2CG,eAAe,GAAGD,aAAa,CAAClD,IAAd,EAAlE,EAAwF,CAACmD,eAAe,CAACjD,IAAzG,EAA+GiD,eAAe,GAAGD,aAAa,CAAClD,IAAd,EAAjI,EAAuJ;UACnJ,IAAIkC,MAAM,GAAGiB,eAAe,CAAClD,KAA7B;UACA,IAAIkC,MAAM,GAAGD,MAAM,CAACC,MAApB;UACAA,MAAM,CAACQ,IAAP,CAAY1C,KAAZ;UACAyB,aAAa,IAAIS,MAAM,CAACpC,MAAxB,IAAkCkC,IAAI,CAACC,MAAD,CAAtC;QACH;MACJ,CAPD,CAQA,OAAOkB,KAAP,EAAc;QAAEL,GAAG,GAAG;UAAEM,KAAK,EAAED;QAAT,CAAN;MAAyB,CARzC,SASQ;QACJ,IAAI;UACA,IAAID,eAAe,IAAI,CAACA,eAAe,CAACjD,IAApC,KAA6Ce,EAAE,GAAGiC,aAAa,CAACI,MAAhE,CAAJ,EAA6ErC,EAAE,CAACnB,IAAH,CAAQoD,aAAR;QAChF,CAFD,SAGQ;UAAE,IAAIH,GAAJ,EAAS,MAAMA,GAAG,CAACM,KAAV;QAAkB;MACxC;IACJ,CAlB0B,EAkBxB,YAAY;MACX,OAAOtB,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAAChC,MAAnF,EAA2F;QACvF+B,UAAU,CAAC9B,IAAX,CAAgB+B,aAAa,CAACwB,KAAd,GAAsBpB,MAAtC;MACH;;MACDU,oBAAoB,KAAK,IAAzB,IAAiCA,oBAAoB,KAAK,KAAK,CAA/D,GAAmE,KAAK,CAAxE,GAA4EA,oBAAoB,CAACR,WAArB,EAA5E;MACAP,UAAU,CAAC0B,QAAX;MACA1B,UAAU,CAACO,WAAX;IACH,CAzB0B,EAyBxBoB,SAzBwB,EAyBb,YAAY;MAAE,OAAQ1B,aAAa,GAAG,IAAxB;IAAgC,CAzBjC,CAA3B;IA0BAF,MAAM,CAAC6B,SAAP,CAAiBb,oBAAjB;EACH,CAzDM,CAAP;AA0DH;;AACDvC,OAAO,CAACC,UAAR,GAAqBA,UAArB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}