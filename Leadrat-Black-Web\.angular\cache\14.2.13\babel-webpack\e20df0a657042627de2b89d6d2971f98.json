{"ast": null, "code": "import toInteger from './toInteger.js';\n/** Error message constants. */\n\nvar FUNC_ERROR_TEXT = 'Expected a function';\n/**\n * Creates a function that invokes `func`, with the `this` binding and arguments\n * of the created function, while it's called less than `n` times. Subsequent\n * calls to the created function return the result of the last `func` invocation.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {number} n The number of calls at which `func` is no longer invoked.\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new restricted function.\n * @example\n *\n * jQuery(element).on('click', _.before(5, addContactToList));\n * // => Allows adding up to 4 contacts to the list.\n */\n\nfunction before(n, func) {\n  var result;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n\n  n = toInteger(n);\n  return function () {\n    if (--n > 0) {\n      result = func.apply(this, arguments);\n    }\n\n    if (n <= 1) {\n      func = undefined;\n    }\n\n    return result;\n  };\n}\n\nexport default before;", "map": {"version": 3, "names": ["toInteger", "FUNC_ERROR_TEXT", "before", "n", "func", "result", "TypeError", "apply", "arguments", "undefined"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/lodash-es/before.js"], "sourcesContent": ["import toInteger from './toInteger.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a function that invokes `func`, with the `this` binding and arguments\n * of the created function, while it's called less than `n` times. Subsequent\n * calls to the created function return the result of the last `func` invocation.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {number} n The number of calls at which `func` is no longer invoked.\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new restricted function.\n * @example\n *\n * jQuery(element).on('click', _.before(5, addContactToList));\n * // => Allows adding up to 4 contacts to the list.\n */\nfunction before(n, func) {\n  var result;\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  n = toInteger(n);\n  return function() {\n    if (--n > 0) {\n      result = func.apply(this, arguments);\n    }\n    if (n <= 1) {\n      func = undefined;\n    }\n    return result;\n  };\n}\n\nexport default before;\n"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,gBAAtB;AAEA;;AACA,IAAIC,eAAe,GAAG,qBAAtB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,MAAT,CAAgBC,CAAhB,EAAmBC,IAAnB,EAAyB;EACvB,IAAIC,MAAJ;;EACA,IAAI,OAAOD,IAAP,IAAe,UAAnB,EAA+B;IAC7B,MAAM,IAAIE,SAAJ,CAAcL,eAAd,CAAN;EACD;;EACDE,CAAC,GAAGH,SAAS,CAACG,CAAD,CAAb;EACA,OAAO,YAAW;IAChB,IAAI,EAAEA,CAAF,GAAM,CAAV,EAAa;MACXE,MAAM,GAAGD,IAAI,CAACG,KAAL,CAAW,IAAX,EAAiBC,SAAjB,CAAT;IACD;;IACD,IAAIL,CAAC,IAAI,CAAT,EAAY;MACVC,IAAI,GAAGK,SAAP;IACD;;IACD,OAAOJ,MAAP;EACD,CARD;AASD;;AAED,eAAeH,MAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}