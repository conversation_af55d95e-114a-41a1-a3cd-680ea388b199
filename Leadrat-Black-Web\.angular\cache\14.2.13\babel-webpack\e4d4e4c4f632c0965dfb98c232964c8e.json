{"ast": null, "code": "import { Subject } from './Subject';\nimport { Subscription } from './Subscription';\nexport class AsyncSubject extends Subject {\n  constructor() {\n    super(...arguments);\n    this.value = null;\n    this.hasNext = false;\n    this.hasCompleted = false;\n  }\n\n  _subscribe(subscriber) {\n    if (this.hasError) {\n      subscriber.error(this.thrownError);\n      return Subscription.EMPTY;\n    } else if (this.hasCompleted && this.hasNext) {\n      subscriber.next(this.value);\n      subscriber.complete();\n      return Subscription.EMPTY;\n    }\n\n    return super._subscribe(subscriber);\n  }\n\n  next(value) {\n    if (!this.hasCompleted) {\n      this.value = value;\n      this.hasNext = true;\n    }\n  }\n\n  error(error) {\n    if (!this.hasCompleted) {\n      super.error(error);\n    }\n  }\n\n  complete() {\n    this.hasCompleted = true;\n\n    if (this.hasNext) {\n      super.next(this.value);\n    }\n\n    super.complete();\n  }\n\n}", "map": {"version": 3, "names": ["Subject", "Subscription", "AsyncSubject", "constructor", "arguments", "value", "hasNext", "hasCompleted", "_subscribe", "subscriber", "<PERSON><PERSON><PERSON><PERSON>", "error", "thrownError", "EMPTY", "next", "complete"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/@angular-slider/ngx-slider/node_modules/rxjs/_esm2015/internal/AsyncSubject.js"], "sourcesContent": ["import { Subject } from './Subject';\nimport { Subscription } from './Subscription';\nexport class AsyncSubject extends Subject {\n    constructor() {\n        super(...arguments);\n        this.value = null;\n        this.hasNext = false;\n        this.hasCompleted = false;\n    }\n    _subscribe(subscriber) {\n        if (this.hasError) {\n            subscriber.error(this.thrownError);\n            return Subscription.EMPTY;\n        }\n        else if (this.hasCompleted && this.hasNext) {\n            subscriber.next(this.value);\n            subscriber.complete();\n            return Subscription.EMPTY;\n        }\n        return super._subscribe(subscriber);\n    }\n    next(value) {\n        if (!this.hasCompleted) {\n            this.value = value;\n            this.hasNext = true;\n        }\n    }\n    error(error) {\n        if (!this.hasCompleted) {\n            super.error(error);\n        }\n    }\n    complete() {\n        this.hasCompleted = true;\n        if (this.hasNext) {\n            super.next(this.value);\n        }\n        super.complete();\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,WAAxB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,OAAO,MAAMC,YAAN,SAA2BF,OAA3B,CAAmC;EACtCG,WAAW,GAAG;IACV,MAAM,GAAGC,SAAT;IACA,KAAKC,KAAL,GAAa,IAAb;IACA,KAAKC,OAAL,GAAe,KAAf;IACA,KAAKC,YAAL,GAAoB,KAApB;EACH;;EACDC,UAAU,CAACC,UAAD,EAAa;IACnB,IAAI,KAAKC,QAAT,EAAmB;MACfD,UAAU,CAACE,KAAX,CAAiB,KAAKC,WAAtB;MACA,OAAOX,YAAY,CAACY,KAApB;IACH,CAHD,MAIK,IAAI,KAAKN,YAAL,IAAqB,KAAKD,OAA9B,EAAuC;MACxCG,UAAU,CAACK,IAAX,CAAgB,KAAKT,KAArB;MACAI,UAAU,CAACM,QAAX;MACA,OAAOd,YAAY,CAACY,KAApB;IACH;;IACD,OAAO,MAAML,UAAN,CAAiBC,UAAjB,CAAP;EACH;;EACDK,IAAI,CAACT,KAAD,EAAQ;IACR,IAAI,CAAC,KAAKE,YAAV,EAAwB;MACpB,KAAKF,KAAL,GAAaA,KAAb;MACA,KAAKC,OAAL,GAAe,IAAf;IACH;EACJ;;EACDK,KAAK,CAACA,KAAD,EAAQ;IACT,IAAI,CAAC,KAAKJ,YAAV,EAAwB;MACpB,MAAMI,KAAN,CAAYA,KAAZ;IACH;EACJ;;EACDI,QAAQ,GAAG;IACP,KAAKR,YAAL,GAAoB,IAApB;;IACA,IAAI,KAAKD,OAAT,EAAkB;MACd,MAAMQ,IAAN,CAAW,KAAKT,KAAhB;IACH;;IACD,MAAMU,QAAN;EACH;;AApCqC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}