{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Dutch (Belgium) [nl-be]\n//! author : <PERSON><PERSON> : https://github.com/jorisroling\n//! author : <PERSON> : https://github.com/middagj\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var monthsShortWithDots = 'jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.'.split('_'),\n      monthsShortWithoutDots = 'jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec'.split('_'),\n      monthsParse = [/^jan/i, /^feb/i, /^(maart|mrt\\.?)$/i, /^apr/i, /^mei$/i, /^jun[i.]?$/i, /^jul[i.]?$/i, /^aug/i, /^sep/i, /^okt/i, /^nov/i, /^dec/i],\n      monthsRegex = /^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december|jan\\.?|feb\\.?|mrt\\.?|apr\\.?|ju[nl]\\.?|aug\\.?|sep\\.?|okt\\.?|nov\\.?|dec\\.?)/i;\n  var nlBe = moment.defineLocale('nl-be', {\n    months: 'januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december'.split('_'),\n    monthsShort: function (m, format) {\n      if (!m) {\n        return monthsShortWithDots;\n      } else if (/-MMM-/.test(format)) {\n        return monthsShortWithoutDots[m.month()];\n      } else {\n        return monthsShortWithDots[m.month()];\n      }\n    },\n    monthsRegex: monthsRegex,\n    monthsShortRegex: monthsRegex,\n    monthsStrictRegex: /^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december)/i,\n    monthsShortStrictRegex: /^(jan\\.?|feb\\.?|mrt\\.?|apr\\.?|mei|ju[nl]\\.?|aug\\.?|sep\\.?|okt\\.?|nov\\.?|dec\\.?)/i,\n    monthsParse: monthsParse,\n    longMonthsParse: monthsParse,\n    shortMonthsParse: monthsParse,\n    weekdays: 'zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag'.split('_'),\n    weekdaysShort: 'zo._ma._di._wo._do._vr._za.'.split('_'),\n    weekdaysMin: 'zo_ma_di_wo_do_vr_za'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[vandaag om] LT',\n      nextDay: '[morgen om] LT',\n      nextWeek: 'dddd [om] LT',\n      lastDay: '[gisteren om] LT',\n      lastWeek: '[afgelopen] dddd [om] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'over %s',\n      past: '%s geleden',\n      s: 'een paar seconden',\n      ss: '%d seconden',\n      m: 'één minuut',\n      mm: '%d minuten',\n      h: 'één uur',\n      hh: '%d uur',\n      d: 'één dag',\n      dd: '%d dagen',\n      M: 'één maand',\n      MM: '%d maanden',\n      y: 'één jaar',\n      yy: '%d jaar'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(ste|de)/,\n    ordinal: function (number) {\n      return number + (number === 1 || number === 8 || number >= 20 ? 'ste' : 'de');\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return nlBe;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "monthsShortWithDots", "split", "monthsShortWithoutDots", "<PERSON><PERSON><PERSON>e", "monthsRegex", "nlBe", "defineLocale", "months", "monthsShort", "m", "format", "test", "month", "monthsShortRegex", "monthsStrictRegex", "monthsShortStrictRegex", "longMonthsParse", "shortMonthsParse", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/moment/locale/nl-be.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Dutch (Belgium) [nl-be]\n//! author : <PERSON><PERSON> : https://github.com/jorisroling\n//! author : <PERSON> : https://github.com/middagj\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var monthsShortWithDots =\n            'jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.'.split('_'),\n        monthsShortWithoutDots =\n            'jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec'.split('_'),\n        monthsParse = [\n            /^jan/i,\n            /^feb/i,\n            /^(maart|mrt\\.?)$/i,\n            /^apr/i,\n            /^mei$/i,\n            /^jun[i.]?$/i,\n            /^jul[i.]?$/i,\n            /^aug/i,\n            /^sep/i,\n            /^okt/i,\n            /^nov/i,\n            /^dec/i,\n        ],\n        monthsRegex =\n            /^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december|jan\\.?|feb\\.?|mrt\\.?|apr\\.?|ju[nl]\\.?|aug\\.?|sep\\.?|okt\\.?|nov\\.?|dec\\.?)/i;\n\n    var nlBe = moment.defineLocale('nl-be', {\n        months: 'januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december'.split(\n            '_'\n        ),\n        monthsShort: function (m, format) {\n            if (!m) {\n                return monthsShortWithDots;\n            } else if (/-MMM-/.test(format)) {\n                return monthsShortWithoutDots[m.month()];\n            } else {\n                return monthsShortWithDots[m.month()];\n            }\n        },\n\n        monthsRegex: monthsRegex,\n        monthsShortRegex: monthsRegex,\n        monthsStrictRegex:\n            /^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december)/i,\n        monthsShortStrictRegex:\n            /^(jan\\.?|feb\\.?|mrt\\.?|apr\\.?|mei|ju[nl]\\.?|aug\\.?|sep\\.?|okt\\.?|nov\\.?|dec\\.?)/i,\n\n        monthsParse: monthsParse,\n        longMonthsParse: monthsParse,\n        shortMonthsParse: monthsParse,\n\n        weekdays:\n            'zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag'.split('_'),\n        weekdaysShort: 'zo._ma._di._wo._do._vr._za.'.split('_'),\n        weekdaysMin: 'zo_ma_di_wo_do_vr_za'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[vandaag om] LT',\n            nextDay: '[morgen om] LT',\n            nextWeek: 'dddd [om] LT',\n            lastDay: '[gisteren om] LT',\n            lastWeek: '[afgelopen] dddd [om] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'over %s',\n            past: '%s geleden',\n            s: 'een paar seconden',\n            ss: '%d seconden',\n            m: 'één minuut',\n            mm: '%d minuten',\n            h: 'één uur',\n            hh: '%d uur',\n            d: 'één dag',\n            dd: '%d dagen',\n            M: 'één maand',\n            MM: '%d maanden',\n            y: 'één jaar',\n            yy: '%d jaar',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(ste|de)/,\n        ordinal: function (number) {\n            return (\n                number +\n                (number === 1 || number === 8 || number >= 20 ? 'ste' : 'de')\n            );\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return nlBe;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;AAEA;;AAAE,WAAUA,MAAV,EAAkBC,OAAlB,EAA2B;EAC1B,OAAOC,OAAP,KAAmB,QAAnB,IAA+B,OAAOC,MAAP,KAAkB,WAAjD,IACO,OAAOC,OAAP,KAAmB,UAD1B,GACuCH,OAAO,CAACG,OAAO,CAAC,WAAD,CAAR,CAD9C,GAEA,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAAvC,GAA6CD,MAAM,CAAC,CAAC,WAAD,CAAD,EAAgBJ,OAAhB,CAAnD,GACAA,OAAO,CAACD,MAAM,CAACO,MAAR,CAHP;AAIF,CALC,EAKA,IALA,EAKO,UAAUA,MAAV,EAAkB;EAAE,aAAF,CAEvB;;EAEA,IAAIC,mBAAmB,GACf,6DAA6DC,KAA7D,CAAmE,GAAnE,CADR;EAAA,IAEIC,sBAAsB,GAClB,kDAAkDD,KAAlD,CAAwD,GAAxD,CAHR;EAAA,IAIIE,WAAW,GAAG,CACV,OADU,EAEV,OAFU,EAGV,mBAHU,EAIV,OAJU,EAKV,QALU,EAMV,aANU,EAOV,aAPU,EAQV,OARU,EASV,OATU,EAUV,OAVU,EAWV,OAXU,EAYV,OAZU,CAJlB;EAAA,IAkBIC,WAAW,GACP,oKAnBR;EAqBA,IAAIC,IAAI,GAAGN,MAAM,CAACO,YAAP,CAAoB,OAApB,EAA6B;IACpCC,MAAM,EAAE,0FAA0FN,KAA1F,CACJ,GADI,CAD4B;IAIpCO,WAAW,EAAE,UAAUC,CAAV,EAAaC,MAAb,EAAqB;MAC9B,IAAI,CAACD,CAAL,EAAQ;QACJ,OAAOT,mBAAP;MACH,CAFD,MAEO,IAAI,QAAQW,IAAR,CAAaD,MAAb,CAAJ,EAA0B;QAC7B,OAAOR,sBAAsB,CAACO,CAAC,CAACG,KAAF,EAAD,CAA7B;MACH,CAFM,MAEA;QACH,OAAOZ,mBAAmB,CAACS,CAAC,CAACG,KAAF,EAAD,CAA1B;MACH;IACJ,CAZmC;IAcpCR,WAAW,EAAEA,WAduB;IAepCS,gBAAgB,EAAET,WAfkB;IAgBpCU,iBAAiB,EACb,2FAjBgC;IAkBpCC,sBAAsB,EAClB,kFAnBgC;IAqBpCZ,WAAW,EAAEA,WArBuB;IAsBpCa,eAAe,EAAEb,WAtBmB;IAuBpCc,gBAAgB,EAAEd,WAvBkB;IAyBpCe,QAAQ,EACJ,6DAA6DjB,KAA7D,CAAmE,GAAnE,CA1BgC;IA2BpCkB,aAAa,EAAE,8BAA8BlB,KAA9B,CAAoC,GAApC,CA3BqB;IA4BpCmB,WAAW,EAAE,uBAAuBnB,KAAvB,CAA6B,GAA7B,CA5BuB;IA6BpCoB,kBAAkB,EAAE,IA7BgB;IA8BpCC,cAAc,EAAE;MACZC,EAAE,EAAE,OADQ;MAEZC,GAAG,EAAE,UAFO;MAGZC,CAAC,EAAE,YAHS;MAIZC,EAAE,EAAE,aAJQ;MAKZC,GAAG,EAAE,mBALO;MAMZC,IAAI,EAAE;IANM,CA9BoB;IAsCpCC,QAAQ,EAAE;MACNC,OAAO,EAAE,iBADH;MAENC,OAAO,EAAE,gBAFH;MAGNC,QAAQ,EAAE,cAHJ;MAINC,OAAO,EAAE,kBAJH;MAKNC,QAAQ,EAAE,0BALJ;MAMNC,QAAQ,EAAE;IANJ,CAtC0B;IA8CpCC,YAAY,EAAE;MACVC,MAAM,EAAE,SADE;MAEVC,IAAI,EAAE,YAFI;MAGVC,CAAC,EAAE,mBAHO;MAIVC,EAAE,EAAE,aAJM;MAKV/B,CAAC,EAAE,YALO;MAMVgC,EAAE,EAAE,YANM;MAOVC,CAAC,EAAE,SAPO;MAQVC,EAAE,EAAE,QARM;MASVC,CAAC,EAAE,SATO;MAUVC,EAAE,EAAE,UAVM;MAWVC,CAAC,EAAE,WAXO;MAYVC,EAAE,EAAE,YAZM;MAaVC,CAAC,EAAE,UAbO;MAcVC,EAAE,EAAE;IAdM,CA9CsB;IA8DpCC,sBAAsB,EAAE,iBA9DY;IA+DpCC,OAAO,EAAE,UAAUC,MAAV,EAAkB;MACvB,OACIA,MAAM,IACLA,MAAM,KAAK,CAAX,IAAgBA,MAAM,KAAK,CAA3B,IAAgCA,MAAM,IAAI,EAA1C,GAA+C,KAA/C,GAAuD,IADlD,CADV;IAIH,CApEmC;IAqEpCC,IAAI,EAAE;MACFC,GAAG,EAAE,CADH;MACM;MACRC,GAAG,EAAE,CAFH,CAEM;;IAFN;EArE8B,CAA7B,CAAX;EA2EA,OAAOlD,IAAP;AAEH,CA3GC,CAAD", "ignoreList": []}, "metadata": {}, "sourceType": "script"}