{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter, SimpleChanges, TemplateRef } from '@angular/core';\nimport { NavigationEnd } from '@angular/router';\nimport * as moment from 'moment';\nimport { filter, map, skipWhile, take, takeUntil } from 'rxjs';\nimport { combineLatest } from 'rxjs/internal/observable/combineLatest';\nimport { EMPTY_GUID, UPDATE_STATUS_PAST_TENSE } from 'src/app/app.constants';\nimport { BHKType, EnquiryType, FurnishStatus, Gender, LeadSource, MaritalStatusType, OfferType, Profession, PurposeType } from 'src/app/app.enum';\nimport { convertUrlsToLinks, formatBudget, getAssignedToDetails, getBHKDisplayString, getLocationDetailsByObj, getTimeZoneDate } from 'src/app/core/utils/common.util';\nimport { FetchTagsList } from 'src/app/reducers/custom-tags/custom-tags.actions';\nimport { getTagsList } from 'src/app/reducers/custom-tags/custom-tags.reducer';\nimport { getGlobalAnonymousIsLoading, getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';\nimport { ClearCardData, FetchLeadList, UpdateLeadsTagInfo } from 'src/app/reducers/lead/lead.actions';\nimport { getIsLeadCustomStatusEnabled, getLeadCardData, getLeads } from 'src/app/reducers/lead/lead.reducer';\nimport { LeadPreviewChanged, LeadPreviewSaved } from 'src/app/reducers/loader/loader.actions';\nimport { getLeadPreviewChanged } from 'src/app/reducers/loader/loader.reducer';\nimport { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';\nimport { getAdminsAndReportees, getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-bootstrap/modal\";\nimport * as i2 from \"@ngrx/store\";\nimport * as i3 from \"src/app/services/shared/grid-options.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"src/app/services/shared/share-data.service\";\nimport * as i6 from \"src/app/services/shared/tracking.service\";\nconst _c0 = [\"statusChangeComponent\"];\nconst _c1 = [\"customStatusChangeComponent\"];\n\nfunction LeadPreviewComponent_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 32);\n    i0.ɵɵlistener(\"click\", function LeadPreviewComponent_span_5_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.modalRef.hide());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c2 = function (a0, a1) {\n  return {\n    \"bg-black-200 pe-none\": a0,\n    \"blinking\": a1\n  };\n};\n\nfunction LeadPreviewComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function LeadPreviewComponent_ng_container_11_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      ctx_r20.prevData();\n      return i0.ɵɵresetView(ctx_r20.getNextLead());\n    });\n    i0.ɵɵelement(2, \"span\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c2, ctx_r1.foundData == 0 && ctx_r1.isMobileView || !ctx_r1.isMobileView && ctx_r1.isFirstPage && ctx_r1.foundData == 0, ctx_r1.isPreviousDataLoading && !ctx_r1.isMobileView));\n  }\n}\n\nfunction LeadPreviewComponent_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 34);\n    i0.ɵɵlistener(\"click\", function LeadPreviewComponent_ng_container_14_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      ctx_r22.nextData();\n      return i0.ɵɵresetView(ctx_r22.getNextLead());\n    });\n    i0.ɵɵelement(2, \"span\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c2, ctx_r2.foundData == (ctx_r2.cardData == null ? null : ctx_r2.cardData.length) - 1 && ctx_r2.isMobileView || ctx_r2.totalPages === ctx_r2.currentPage && ctx_r2.foundData == (ctx_r2.cardData == null ? null : ctx_r2.cardData.length) - 1, ctx_r2.isNextDataLoading && ctx_r2.foundData == (ctx_r2.cardData == null ? null : ctx_r2.cardData.length) - 1 || ctx_r2.isNextDataLoading && !ctx_r2.isMobileView));\n  }\n}\n\nfunction LeadPreviewComponent_ng_container_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 35);\n    i0.ɵɵelement(2, \"span\", 36);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\", 37);\n    i0.ɵɵelement(6, \"span\", 38);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 37);\n    i0.ɵɵelement(10, \"span\", 38);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"span\", 35);\n    i0.ɵɵelement(14, \"span\", 39);\n    i0.ɵɵelementStart(15, \"span\", 16);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r3.data == null ? null : ctx_r3.data.contactNo) ? ctx_r3.data == null ? null : ctx_r3.data.contactNo : \"---\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r3.data == null ? null : ctx_r3.data.alternateContactNo) ? ctx_r3.data == null ? null : ctx_r3.data.alternateContactNo : \"---\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r3.data == null ? null : ctx_r3.data.landLine) ? ctx_r3.data == null ? null : ctx_r3.data.landLine : \"---\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r3.data == null ? null : ctx_r3.data.email) ? ctx_r3.data == null ? null : ctx_r3.data.email : \"---\");\n  }\n}\n\nfunction LeadPreviewComponent_span_29_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 42);\n  }\n}\n\nfunction LeadPreviewComponent_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 35)(1, \"span\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, LeadPreviewComponent_span_29_span_3_Template, 1, 0, \"span\", 41);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const enquiry_r24 = ctx.$implicit;\n    const last_r25 = ctx.last;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(enquiry_r24 ? ctx_r4.EnquiryType[enquiry_r24] : \"--\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r25);\n  }\n}\n\nfunction LeadPreviewComponent_ng_container_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 43);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.data.enquiry.propertyTypes == null ? null : ctx_r5.data.enquiry.propertyTypes[0] == null ? null : ctx_r5.data.enquiry.propertyTypes[0].displayName);\n  }\n}\n\nfunction LeadPreviewComponent_ng_container_31_ng_container_2_ng_container_1_span_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \", \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LeadPreviewComponent_ng_container_31_ng_container_2_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, LeadPreviewComponent_ng_container_31_ng_container_2_ng_container_1_span_1_span_2_Template, 2, 0, \"span\", 8);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext();\n    const type_r31 = ctx_r35.$implicit;\n    const last_r32 = ctx_r35.last;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r31.childType.displayName, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r32);\n  }\n}\n\nfunction LeadPreviewComponent_ng_container_31_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LeadPreviewComponent_ng_container_31_ng_container_2_ng_container_1_span_1_Template, 3, 2, \"span\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const type_r31 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", type_r31 == null ? null : type_r31.childType == null ? null : type_r31.childType.displayName);\n  }\n}\n\nfunction LeadPreviewComponent_ng_container_31_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LeadPreviewComponent_ng_container_31_ng_container_2_ng_container_1_Template, 2, 1, \"ng-container\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r27.data == null ? null : ctx_r27.data.enquiry == null ? null : ctx_r27.data.enquiry.propertyTypes);\n  }\n}\n\nfunction LeadPreviewComponent_ng_container_31_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h6\");\n    i0.ɵɵtext(1, \"--\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LeadPreviewComponent_ng_container_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 43);\n    i0.ɵɵtemplate(2, LeadPreviewComponent_ng_container_31_ng_container_2_Template, 2, 1, \"ng-container\", 44);\n    i0.ɵɵtemplate(3, LeadPreviewComponent_ng_container_31_ng_template_3_Template, 2, 0, \"ng-template\", null, 45, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const _r28 = i0.ɵɵreference(4);\n\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.data == null ? null : ctx_r6.data.enquiry == null ? null : ctx_r6.data.enquiry.propertyTypes == null ? null : ctx_r6.data.enquiry.propertyTypes.length)(\"ngIfElse\", _r28);\n  }\n}\n\nfunction LeadPreviewComponent_ng_container_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 43);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r7.bhkNo && (ctx_r7.bhkNo == null ? null : ctx_r7.bhkNo.length) > 0 ? ctx_r7.bhkNo : \"\");\n  }\n}\n\nfunction LeadPreviewComponent_ng_container_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 43);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r8.bhkTypes);\n  }\n}\n\nfunction LeadPreviewComponent_ng_container_34_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"- \", ctx_r36.formatBudget(ctx_r36.data == null ? null : ctx_r36.data.enquiry == null ? null : ctx_r36.data.enquiry.upperBudget, (ctx_r36.data == null ? null : ctx_r36.data.enquiry == null ? null : ctx_r36.data.enquiry.currency) || ctx_r36.defaultCurrency), \"\");\n  }\n}\n\nfunction LeadPreviewComponent_ng_container_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 43);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, LeadPreviewComponent_ng_container_34_span_4_Template, 2, 1, \"span\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r9.formatBudget(ctx_r9.data == null ? null : ctx_r9.data.enquiry == null ? null : ctx_r9.data.enquiry.lowerBudget, (ctx_r9.data == null ? null : ctx_r9.data.enquiry == null ? null : ctx_r9.data.enquiry.currency) || ctx_r9.defaultCurrency));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r9.data == null ? null : ctx_r9.data.enquiry == null ? null : ctx_r9.data.enquiry.lowerBudget) && (ctx_r9.data == null ? null : ctx_r9.data.enquiry == null ? null : ctx_r9.data.enquiry.upperBudget));\n  }\n}\n\nconst _c3 = function (a0) {\n  return {\n    active: a0\n  };\n};\n\nfunction LeadPreviewComponent_ng_container_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 46);\n    i0.ɵɵlistener(\"click\", function LeadPreviewComponent_ng_container_36_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const item_r37 = restoredCtx.$implicit;\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.onSectionSelect(item_r37.section));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const item_r37 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c3, ctx_r10.selectedSection === item_r37.section));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, item_r37.label), \" \");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r72 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵlistener(\"click\", function LeadPreviewComponent_div_37_div_2_div_5_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r72);\n      const ctx_r71 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r71.checkStatus(\"Status\"));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, \"LEADS.change-status\"), \" \");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"div\", 94);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, LeadPreviewComponent_div_37_div_2_div_5_Template, 3, 3, \"div\", 95);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(3, 3, \"GLOBAL.lead\"), \" \", i0.ɵɵpipeBind1(4, 5, \"GLOBAL.status\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r40.currentPath === \"/invoice\" ? ctx_r40.canUpdateInvoice && !(ctx_r40.data == null ? null : ctx_r40.data.isArchived) : ctx_r40.canEditLead && !(!ctx_r40.canUpdateBookedLead && (ctx_r40.data == null ? null : ctx_r40.data.status == null ? null : ctx_r40.data.status.displayName) == \"Booked\") && !(ctx_r40.data == null ? null : ctx_r40.data.isArchived) && (ctx_r40.data == null ? null : ctx_r40.data.status == null ? null : ctx_r40.data.status.status) !== \"invoiced\" && (ctx_r40.data == null ? null : ctx_r40.data.assignTo) !== ctx_r40.EMPTY_GUID);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_3_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r73 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r73.data.status.childType.displayName, \"\");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_3_div_7_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r76 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r76.userData == null ? null : ctx_r76.userData.timeZoneInfo == null ? null : ctx_r76.userData.timeZoneInfo.timeZoneName, \") \");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵelement(1, \"span\", 104);\n    i0.ɵɵelementStart(2, \"div\")(3, \"h5\", 101);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, LeadPreviewComponent_div_37_div_3_div_7_div_5_Template, 2, 1, \"div\", 78);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r74 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r74.data.scheduledDate ? ctx_r74.getTimeZoneDate(ctx_r74.data.scheduledDate, ctx_r74.userData == null ? null : ctx_r74.userData.timeZoneInfo == null ? null : ctx_r74.userData.timeZoneInfo.baseUTcOffset, \"dateWithTime\") : \"---\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r74.userData == null ? null : ctx_r74.userData.timeZoneInfo == null ? null : ctx_r74.userData.timeZoneInfo.timeZoneName) && ctx_r74.data.scheduledDate && (ctx_r74.userData == null ? null : ctx_r74.userData.shouldShowTimeZone));\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_3_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelement(1, \"span\", 107)(2, \"p\", 108);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r75 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", (ctx_r75.data == null ? null : ctx_r75.data.notes) ? ctx_r75.convertUrlsToLinks(ctx_r75.data.notes) : \"---\", i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98)(2, \"div\", 99);\n    i0.ɵɵelement(3, \"span\", 100);\n    i0.ɵɵelementStart(4, \"h5\", 101);\n    i0.ɵɵtext(5);\n    i0.ɵɵtemplate(6, LeadPreviewComponent_div_37_div_3_span_6_Template, 2, 1, \"span\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, LeadPreviewComponent_div_37_div_3_div_7_Template, 6, 2, \"div\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, LeadPreviewComponent_div_37_div_3_div_8_Template, 3, 1, \"div\", 103);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r41.data == null ? null : ctx_r41.data.status == null ? null : ctx_r41.data.status.displayName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.data == null ? null : ctx_r41.data.status == null ? null : ctx_r41.data.status.childType == null ? null : ctx_r41.data.status.childType.displayName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.data.scheduledDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.data == null ? null : ctx_r41.data.notes);\n  }\n}\n\nconst _c4 = function (a0) {\n  return {\n    \"bg-white\": a0\n  };\n};\n\nconst _c5 = function (a0) {\n  return {\n    \"opacity-5\": a0\n  };\n};\n\nfunction LeadPreviewComponent_div_37_div_4_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r80 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 110);\n    i0.ɵɵlistener(\"click\", function LeadPreviewComponent_div_37_div_4_ng_container_5_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const flag_r78 = restoredCtx.$implicit;\n      const ctx_r79 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r79.flagAction(flag_r78));\n    });\n    i0.ɵɵelementStart(2, \"div\", 111);\n    i0.ɵɵelement(3, \"img\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 23);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const flag_r78 = ctx.$implicit;\n    const ctx_r77 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r77.getActiveBackgroundColor(flag_r78));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c4, flag_r78.isActive));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"type\", \"leadrat\")(\"appImage\", flag_r78.isActive ? flag_r78 == null ? null : flag_r78.activeImagePath : flag_r78 == null ? null : flag_r78.inactiveImagePath);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c5, !flag_r78.isActive));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 6, flag_r78.name));\n  }\n}\n\nconst _c6 = function (a0) {\n  return {\n    \"pe-none\": a0\n  };\n};\n\nfunction LeadPreviewComponent_div_37_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 109);\n    i0.ɵɵtemplate(5, LeadPreviewComponent_div_37_div_4_ng_container_5_Template, 7, 12, \"ng-container\", 22);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 3, \"LEADS.tags\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c6, !ctx_r42.canEditTags));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r42.flagOptions);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r82 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 113)(5, \"div\", 35);\n    i0.ɵɵelement(6, \"span\", 114);\n    i0.ɵɵelementStart(7, \"h5\", 115);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 116);\n    i0.ɵɵlistener(\"click\", function LeadPreviewComponent_div_37_div_6_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r82);\n      const ctx_r81 = i0.ɵɵnextContext(2);\n\n      const _r16 = i0.ɵɵreference(44);\n\n      return i0.ɵɵresetView(ctx_r81.data.assignTo !== ctx_r81.EMPTY_GUID ? ctx_r81.openAudioPlayer(_r16) : ctx_r81.openUnassignModal());\n    });\n    i0.ɵɵtext(11, \" View all \");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"LEADS.ivr-call-recordings\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(9, 4, \"LEADS.call-are-recorded\"), \" \");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h5\", 56);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r44 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(3, 3, \"GLOBAL.lead\"), \" \", i0.ɵɵpipeBind1(4, 5, \"LEADS.source\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r44.LeadSource[ctx_r44.data.enquiry == null ? null : ctx_r44.data.enquiry.leadSource] ? ctx_r44.LeadSource[ctx_r44.data.enquiry == null ? null : ctx_r44.data.enquiry.leadSource] : \"---\");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h5\", 117);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r45 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 2, \"LEADS.sub-source\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((ctx_r45.data.enquiry == null ? null : ctx_r45.data.enquiry.subSource) ? ctx_r45.data.enquiry == null ? null : ctx_r45.data.enquiry.subSource : \"--\");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_46_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r84 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r84 == null ? null : value_r84.title, \" \");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_46_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LeadPreviewComponent_div_37_div_46_div_5_div_1_Template, 2, 1, \"div\", 121);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r84 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", value_r84 == null ? null : value_r84.title);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118)(1, \"div\", 119);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 120);\n    i0.ɵɵtemplate(5, LeadPreviewComponent_div_37_div_46_div_5_Template, 2, 1, \"div\", 22);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r46 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"LABEL.property\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r46.data == null ? null : ctx_r46.data.properties);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_47_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r88 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", value_r88 == null ? null : value_r88.name, \" \");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_47_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LeadPreviewComponent_div_37_div_47_div_5_div_1_Template, 2, 1, \"div\", 121);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r88 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", value_r88 == null ? null : value_r88.name);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 123)(1, \"div\", 119);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 120);\n    i0.ɵɵtemplate(5, LeadPreviewComponent_div_37_div_47_div_5_Template, 2, 1, \"div\", 22);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r47 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"SIDEBAR.project\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r47.data == null ? null : ctx_r47.data.projects);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_59_ng_container_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \", \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_59_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 124);\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, LeadPreviewComponent_div_37_ng_container_59_ng_container_1_span_3_Template, 2, 0, \"span\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const type_r92 = ctx.$implicit;\n    const last_r93 = ctx.last;\n    const ctx_r91 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"title\", ctx_r91.getSubtypesTitle());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r92.childType.displayName, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r93);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LeadPreviewComponent_div_37_ng_container_59_ng_container_1_Template, 4, 3, \"ng-container\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r48 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r48.data == null ? null : ctx_r48.data.enquiry == null ? null : ctx_r48.data.enquiry.propertyTypes);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_template_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" -- \");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_62_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h5\", 6);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r95 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", \"BHK\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r95.bhkNo && (ctx_r95.bhkNo == null ? null : ctx_r95.bhkNo.length) > 0 ? ctx_r95.bhkNo : \"--\");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_62_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h5\", 6);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r96 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(3, 3, \"PROPERTY.bhk\"), \" \", i0.ɵɵpipeBind1(4, 5, \"LABEL.type\"), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r96.bhkTypes);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_62_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 65)(2, \"div\", 66);\n    i0.ɵɵtext(3, \"Beds\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h5\", 126);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 65)(7, \"div\", 66);\n    i0.ɵɵtext(8, \"Baths\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"h5\", 126);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r97 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"title\", ctx_r97.beds);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate((ctx_r97.beds == null ? null : ctx_r97.beds.length) ? ctx_r97.beds : \"--\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"title\", ctx_r97.data == null ? null : ctx_r97.data.enquiry == null ? null : ctx_r97.data.enquiry.baths);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate((ctx_r97.data == null ? null : ctx_r97.data.enquiry == null ? null : ctx_r97.data.enquiry.baths) && (ctx_r97.data == null ? null : ctx_r97.data.enquiry == null ? null : ctx_r97.data.enquiry.baths == null ? null : ctx_r97.data.enquiry.baths.length) > 0 ? ctx_r97.data == null ? null : ctx_r97.data.enquiry == null ? null : ctx_r97.data.enquiry.baths : \"--\");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LeadPreviewComponent_div_37_ng_container_62_div_1_Template, 5, 2, \"div\", 125);\n    i0.ɵɵtemplate(2, LeadPreviewComponent_div_37_ng_container_62_div_2_Template, 7, 7, \"div\", 125);\n    i0.ɵɵtemplate(3, LeadPreviewComponent_div_37_ng_container_62_ng_container_3_Template, 11, 4, \"ng-container\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r51.globalSettingsData == null ? null : ctx_r51.globalSettingsData.isCustomLeadFormEnabled));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r51.globalSettingsData == null ? null : ctx_r51.globalSettingsData.isCustomLeadFormEnabled));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r51.globalSettingsData == null ? null : ctx_r51.globalSettingsData.isCustomLeadFormEnabled);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_63_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵtext(2, \"Furnish Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h5\", 6);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r98 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r98.data == null ? null : ctx_r98.data.enquiry == null ? null : ctx_r98.data.enquiry.furnished) ? ctx_r98.FurnishStatus[ctx_r98.data == null ? null : ctx_r98.data.enquiry == null ? null : ctx_r98.data.enquiry.furnished] : \"--\");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_63_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵtext(2, \"Preferred Floors\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h5\", 6);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r99 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r99.data == null ? null : ctx_r99.data.enquiry == null ? null : ctx_r99.data.enquiry.floors) && (ctx_r99.data == null ? null : ctx_r99.data.enquiry == null ? null : ctx_r99.data.enquiry.floors == null ? null : ctx_r99.data.enquiry.floors.length) > 0 ? ctx_r99.data == null ? null : ctx_r99.data.enquiry == null ? null : ctx_r99.data.enquiry.floors : \"--\");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LeadPreviewComponent_div_37_ng_container_63_div_1_Template, 5, 1, \"div\", 125);\n    i0.ɵɵtemplate(2, LeadPreviewComponent_div_37_ng_container_63_div_2_Template, 5, 1, \"div\", 125);\n    i0.ɵɵelementStart(3, \"div\", 65)(4, \"div\", 66);\n    i0.ɵɵtext(5, \"Offering Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h5\", 6);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r52 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r52.data == null ? null : ctx_r52.data.enquiry == null ? null : ctx_r52.data.enquiry.propertyTypes == null ? null : ctx_r52.data.enquiry.propertyTypes[0] == null ? null : ctx_r52.data.enquiry.propertyTypes[0].displayName) == \"Commerical\" || (ctx_r52.data == null ? null : ctx_r52.data.enquiry == null ? null : ctx_r52.data.enquiry.propertyTypes == null ? null : ctx_r52.data.enquiry.propertyTypes[0] == null ? null : ctx_r52.data.enquiry.propertyTypes[0].displayName) == \"Residential\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r52.data == null ? null : ctx_r52.data.enquiry == null ? null : ctx_r52.data.enquiry.propertyTypes == null ? null : ctx_r52.data.enquiry.propertyTypes[0] == null ? null : ctx_r52.data.enquiry.propertyTypes[0].displayName) == \"Commerical\" || (ctx_r52.data == null ? null : ctx_r52.data.enquiry == null ? null : ctx_r52.data.enquiry.propertyTypes == null ? null : ctx_r52.data.enquiry.propertyTypes[0] == null ? null : ctx_r52.data.enquiry.propertyTypes[0].displayName) == \"Residential\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r52.data == null ? null : ctx_r52.data.enquiry == null ? null : ctx_r52.data.enquiry.offerType) ? ctx_r52.OfferType[ctx_r52.data == null ? null : ctx_r52.data.enquiry == null ? null : ctx_r52.data.enquiry.offerType] : \"--\");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"span\", 127);\n    i0.ɵɵelementStart(2, \"h5\", 71);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 1, \"LOCATION.location\"));\n  }\n}\n\nconst _c7 = function (a0) {\n  return {\n    \"border-bottom pb-4\": a0\n  };\n};\n\nfunction LeadPreviewComponent_div_37_div_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 128)(1, \"div\", 129);\n    i0.ɵɵelement(2, \"span\", 130);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const address_r100 = ctx.$implicit;\n    const last_r101 = ctx.last;\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c7, !last_r101));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r54.getLocationDetailsByObj(address_r100), \" \");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_89_ng_container_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \", \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_89_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, LeadPreviewComponent_div_37_ng_container_89_ng_container_1_span_2_Template, 2, 0, \"span\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const agency_r103 = ctx.$implicit;\n    const last_r104 = ctx.last;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", agency_r103.name, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r104);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LeadPreviewComponent_div_37_ng_container_89_ng_container_1_Template, 3, 2, \"ng-container\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r55 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r55.data == null ? null : ctx_r55.data.agencies);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_template_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" -- \");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 73)(2, \"h5\", 74);\n    i0.ɵɵtext(3, \"Property Area\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h5\", 76);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 73)(7, \"h5\", 74);\n    i0.ɵɵtext(8, \"Net Area\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"h5\", 76);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 73)(12, \"h5\", 74);\n    i0.ɵɵtext(13, \"Unit Number/Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"h5\", 77);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 73)(17, \"h5\", 74);\n    i0.ɵɵtext(18, \"Cluster Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"h5\", 77);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 73)(22, \"h5\", 74);\n    i0.ɵɵtext(23, \"Nationality \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"h5\", 131);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r58 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", (ctx_r58.data == null ? null : ctx_r58.data.enquiry == null ? null : ctx_r58.data.enquiry.propertyArea) ? ctx_r58.data == null ? null : ctx_r58.data.enquiry == null ? null : ctx_r58.data.enquiry.propertyArea : \"--\", \" \", (ctx_r58.data == null ? null : ctx_r58.data.enquiry == null ? null : ctx_r58.data.enquiry.propertyArea) && (ctx_r58.data == null ? null : ctx_r58.data.enquiry == null ? null : ctx_r58.data.enquiry.propertyAreaUnit) ? ctx_r58.data == null ? null : ctx_r58.data.enquiry == null ? null : ctx_r58.data.enquiry.propertyAreaUnit : \"\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", (ctx_r58.data == null ? null : ctx_r58.data.enquiry == null ? null : ctx_r58.data.enquiry.netArea) ? ctx_r58.data == null ? null : ctx_r58.data.enquiry == null ? null : ctx_r58.data.enquiry.netArea : \"--\", \" \", (ctx_r58.data == null ? null : ctx_r58.data.enquiry == null ? null : ctx_r58.data.enquiry.netArea) && (ctx_r58.data == null ? null : ctx_r58.data.enquiry == null ? null : ctx_r58.data.enquiry.netAreaUnit) ? ctx_r58.data == null ? null : ctx_r58.data.enquiry == null ? null : ctx_r58.data.enquiry.netAreaUnit : \"\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r58.data.enquiry == null ? null : ctx_r58.data.enquiry.unitName) ? ctx_r58.data == null ? null : ctx_r58.data.enquiry == null ? null : ctx_r58.data.enquiry.unitName : \"--\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r58.data.enquiry == null ? null : ctx_r58.data.enquiry.clusterName) ? ctx_r58.data == null ? null : ctx_r58.data.enquiry == null ? null : ctx_r58.data.enquiry.clusterName : \"--\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"title\", ctx_r58.data == null ? null : ctx_r58.data.nationality);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r58.data == null ? null : ctx_r58.data.nationality) ? ctx_r58.data == null ? null : ctx_r58.data.nationality : \"--\", \" \");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_126_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r59 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r59.userData == null ? null : ctx_r59.userData.timeZoneInfo == null ? null : ctx_r59.userData.timeZoneInfo.timeZoneName, \") \");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_132_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r60 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r60.userData == null ? null : ctx_r60.userData.timeZoneInfo == null ? null : ctx_r60.userData.timeZoneInfo.timeZoneName, \") \");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_170_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \", \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_170_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, LeadPreviewComponent_div_37_ng_container_170_ng_container_1_ng_container_2_Template, 2, 0, \"ng-container\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const partner_r107 = ctx.$implicit;\n    const last_r108 = ctx.last;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", partner_r107 == null ? null : partner_r107.firmName, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r108);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_170_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LeadPreviewComponent_div_37_ng_container_170_ng_container_1_Template, 3, 2, \"ng-container\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r61 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r61.data == null ? null : ctx_r61.data.channelPartners);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_template_171_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \"--\");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_178_ng_container_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \", \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_178_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, LeadPreviewComponent_div_37_ng_container_178_ng_container_1_span_2_Template, 2, 0, \"span\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const campaign_r111 = ctx.$implicit;\n    const last_r112 = ctx.last;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", campaign_r111.name, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r112);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_178_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LeadPreviewComponent_div_37_ng_container_178_ng_container_1_Template, 3, 2, \"ng-container\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r64 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r64.data == null ? null : ctx_r64.data.campaigns);\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_template_179_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" -- \");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_ng_container_193_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 51);\n    i0.ɵɵtext(2, \"Facebook Info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52)(4, \"div\", 72)(5, \"div\", 73)(6, \"h5\", 74);\n    i0.ɵɵtext(7, \"Ad ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 67);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 73)(11, \"h5\", 74);\n    i0.ɵɵtext(12, \"Ad Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"h5\", 76);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 73)(16, \"h5\", 74);\n    i0.ɵɵtext(17, \"Ad Set ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"h5\", 76);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 73)(21, \"h5\", 74);\n    i0.ɵɵtext(22, \"Ad Set Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"h5\", 76);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 73)(26, \"h5\", 74);\n    i0.ɵɵtext(27, \"Page ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"h5\", 77);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 73)(31, \"h5\", 74);\n    i0.ɵɵtext(32, \"Campaign ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"h5\", 76);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 73)(36, \"h5\", 74);\n    i0.ɵɵtext(37, \"Campaign Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"h5\", 76);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 73)(41, \"h5\", 74);\n    i0.ɵɵtext(42, \"Ad Account ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"h5\", 76);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 73)(46, \"h5\", 74);\n    i0.ɵɵtext(47, \"Ad Account Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"h5\", 76);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 73)(51, \"h5\", 74);\n    i0.ɵɵtext(52, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"h5\", 76);\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 73)(56, \"h5\", 74);\n    i0.ɵɵtext(57, \"Facebook ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"h5\", 76);\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r67 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.AdId) ? ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.AdId : \"--\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.AdName) ? ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.AdName : \"--\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.AdSetId) ? ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.AdSetId : \"--\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.AdSetName) ? ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.AdSetName : \"--\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.PageId) ? ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.PageId : \"--\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.CampaignId) ? ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.CampaignId : \"--\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.CampaignName) ? ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.CampaignName : \"--\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.AdAccountId) ? ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.AdAccountId : \"--\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.AdAccountName) ? ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.AdAccountName : \"--\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.Name) ? ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.Name : \"--\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.FacebookId) ? ctx_r67.data == null ? null : ctx_r67.data.additionalProperties == null ? null : ctx_r67.data.additionalProperties.FacebookId : \"--\", \" \");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_208_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r68 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r68.userData == null ? null : ctx_r68.userData.timeZoneInfo == null ? null : ctx_r68.userData.timeZoneInfo.timeZoneName, \") \");\n  }\n}\n\nfunction LeadPreviewComponent_div_37_div_219_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r69 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r69.userData == null ? null : ctx_r69.userData.timeZoneInfo == null ? null : ctx_r69.userData.timeZoneInfo.timeZoneName, \") \");\n  }\n}\n\nconst _c8 = function (a0) {\n  return {\n    \"mt-12\": a0\n  };\n};\n\nfunction LeadPreviewComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 47);\n    i0.ɵɵtemplate(2, LeadPreviewComponent_div_37_div_2_Template, 6, 7, \"div\", 48);\n    i0.ɵɵtemplate(3, LeadPreviewComponent_div_37_div_3_Template, 9, 4, \"div\", 49);\n    i0.ɵɵtemplate(4, LeadPreviewComponent_div_37_div_4_Template, 6, 7, \"div\", 8);\n    i0.ɵɵelement(5, \"individual-reassign\", 50);\n    i0.ɵɵtemplate(6, LeadPreviewComponent_div_37_div_6_Template, 12, 6, \"div\", 8);\n    i0.ɵɵelementStart(7, \"div\", 51);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"translate\");\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 52)(12, \"div\", 53)(13, \"div\", 54)(14, \"div\", 55);\n    i0.ɵɵtext(15, \" Gender \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"h5\", 56);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 54)(19, \"div\", 55);\n    i0.ɵɵtext(20, \" Date of Birth \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"h5\", 56);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 54)(24, \"div\", 55);\n    i0.ɵɵtext(25, \" Marital Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"h5\", 56);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(28, LeadPreviewComponent_div_37_div_28_Template, 7, 7, \"div\", 57);\n    i0.ɵɵtemplate(29, LeadPreviewComponent_div_37_div_29_Template, 6, 4, \"div\", 57);\n    i0.ɵɵelementStart(30, \"div\", 58)(31, \"div\", 55);\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"h5\", 6);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 23)(37, \"div\", 55);\n    i0.ɵɵtext(38, \"Purpose \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"h5\", 6);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 59);\n    i0.ɵɵelement(42, \"span\", 60);\n    i0.ɵɵelementStart(43, \"h5\", 61);\n    i0.ɵɵtext(44);\n    i0.ɵɵpipe(45, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(46, LeadPreviewComponent_div_37_div_46_Template, 6, 4, \"div\", 62);\n    i0.ɵɵtemplate(47, LeadPreviewComponent_div_37_div_47_Template, 6, 4, \"div\", 63);\n    i0.ɵɵelementStart(48, \"div\", 64)(49, \"div\", 65)(50, \"div\", 66);\n    i0.ɵɵtext(51);\n    i0.ɵɵpipe(52, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"h5\", 6);\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 65)(56, \"div\", 66);\n    i0.ɵɵtext(57, \"Property Sub-type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 67);\n    i0.ɵɵtemplate(59, LeadPreviewComponent_div_37_ng_container_59_Template, 2, 1, \"ng-container\", 44);\n    i0.ɵɵtemplate(60, LeadPreviewComponent_div_37_ng_template_60_Template, 1, 0, \"ng-template\", null, 45, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(62, LeadPreviewComponent_div_37_ng_container_62_Template, 4, 3, \"ng-container\", 8);\n    i0.ɵɵtemplate(63, LeadPreviewComponent_div_37_ng_container_63_Template, 8, 3, \"ng-container\", 8);\n    i0.ɵɵelementStart(64, \"div\", 65)(65, \"div\", 66);\n    i0.ɵɵtext(66);\n    i0.ɵɵpipe(67, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"h5\", 6);\n    i0.ɵɵtext(69);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"div\", 65)(71, \"div\", 66);\n    i0.ɵɵtext(72);\n    i0.ɵɵpipe(73, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"h5\", 6);\n    i0.ɵɵtext(75);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(76, LeadPreviewComponent_div_37_div_76_Template, 5, 3, \"div\", 68);\n    i0.ɵɵtemplate(77, LeadPreviewComponent_div_37_div_77_Template, 5, 4, \"div\", 69);\n    i0.ɵɵelementStart(78, \"div\", 59);\n    i0.ɵɵelement(79, \"span\", 70);\n    i0.ɵɵelementStart(80, \"h5\", 71);\n    i0.ɵɵtext(81);\n    i0.ɵɵpipe(82, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(83, \"div\", 72)(84, \"div\", 73)(85, \"h5\", 74);\n    i0.ɵɵtext(86);\n    i0.ɵɵpipe(87, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"div\", 67);\n    i0.ɵɵtemplate(89, LeadPreviewComponent_div_37_ng_container_89_Template, 2, 1, \"ng-container\", 44);\n    i0.ɵɵtemplate(90, LeadPreviewComponent_div_37_ng_template_90_Template, 1, 0, \"ng-template\", null, 75, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(92, \"div\", 73)(93, \"h5\", 74);\n    i0.ɵɵtext(94);\n    i0.ɵɵpipe(95, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(96, \"h5\", 76);\n    i0.ɵɵtext(97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(98, \"div\", 73)(99, \"h5\", 74);\n    i0.ɵɵtext(100);\n    i0.ɵɵpipe(101, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(102, \"h5\", 77);\n    i0.ɵɵtext(103);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(104, \"div\", 73)(105, \"h5\", 74);\n    i0.ɵɵtext(106);\n    i0.ɵɵpipe(107, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(108, \"h5\", 76);\n    i0.ɵɵtext(109);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(110, \"div\", 73)(111, \"h5\", 74);\n    i0.ɵɵtext(112, \"Built-up Area\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(113, \"h5\", 76);\n    i0.ɵɵtext(114);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(115, \"div\", 73)(116, \"h5\", 74);\n    i0.ɵɵtext(117, \"Saleable Area\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(118, \"h5\", 76);\n    i0.ɵɵtext(119);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(120, LeadPreviewComponent_div_37_ng_container_120_Template, 26, 8, \"ng-container\", 8);\n    i0.ɵɵelementStart(121, \"div\", 73)(122, \"h5\", 74);\n    i0.ɵɵtext(123, \"Deleted Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(124, \"h5\", 76);\n    i0.ɵɵtext(125);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(126, LeadPreviewComponent_div_37_div_126_Template, 2, 1, \"div\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(127, \"div\", 73)(128, \"h5\", 74);\n    i0.ɵɵtext(129, \"Possession Needed By\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(130, \"h5\", 76);\n    i0.ɵɵtext(131);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(132, LeadPreviewComponent_div_37_div_132_Template, 2, 1, \"div\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(133, \"div\", 73)(134, \"h5\", 74);\n    i0.ɵɵtext(135, \"Referral Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(136, \"h5\", 77);\n    i0.ɵɵtext(137);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(138, \"div\", 73)(139, \"h5\", 74);\n    i0.ɵɵtext(140, \"Referral Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(141, \"h5\", 76);\n    i0.ɵɵtext(142);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(143, \"div\", 73)(144, \"h5\", 74);\n    i0.ɵɵtext(145, \"Referral Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(146, \"h5\", 76);\n    i0.ɵɵtext(147);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(148, \"div\", 73)(149, \"h5\", 74);\n    i0.ɵɵtext(150, \"Serial Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(151, \"h5\", 76);\n    i0.ɵɵtext(152);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(153, \"div\", 73)(154, \"h5\", 74);\n    i0.ɵɵtext(155);\n    i0.ɵɵpipe(156, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(157, \"h5\", 79);\n    i0.ɵɵtext(158);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(159, \"div\", 73)(160, \"h5\", 74);\n    i0.ɵɵtext(161);\n    i0.ɵɵpipe(162, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(163, \"h5\", 76);\n    i0.ɵɵtext(164);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(165, \"div\", 73)(166, \"h5\", 74);\n    i0.ɵɵtext(167);\n    i0.ɵɵpipe(168, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(169, \"h5\", 80);\n    i0.ɵɵtemplate(170, LeadPreviewComponent_div_37_ng_container_170_Template, 2, 1, \"ng-container\", 44);\n    i0.ɵɵtemplate(171, LeadPreviewComponent_div_37_ng_template_171_Template, 1, 0, \"ng-template\", null, 81, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(173, \"div\", 73)(174, \"h5\", 74);\n    i0.ɵɵtext(175);\n    i0.ɵɵpipe(176, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(177, \"div\", 67);\n    i0.ɵɵtemplate(178, LeadPreviewComponent_div_37_ng_container_178_Template, 2, 1, \"ng-container\", 44);\n    i0.ɵɵtemplate(179, LeadPreviewComponent_div_37_ng_template_179_Template, 1, 0, \"ng-template\", null, 82, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(181, \"div\", 73)(182, \"h5\", 74);\n    i0.ɵɵtext(183);\n    i0.ɵɵpipe(184, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(185, \"h5\", 79);\n    i0.ɵɵtext(186);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(187, \"div\", 83)(188, \"div\", 74);\n    i0.ɵɵtext(189);\n    i0.ɵɵpipe(190, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(191, \"h5\", 84);\n    i0.ɵɵtext(192);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(193, LeadPreviewComponent_div_37_ng_container_193_Template, 60, 11, \"ng-container\", 8);\n    i0.ɵɵelement(194, \"div\", 85);\n    i0.ɵɵelementStart(195, \"div\", 51);\n    i0.ɵɵtext(196);\n    i0.ɵɵpipe(197, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(198, \"div\", 86)(199, \"div\", 35)(200, \"h5\", 87);\n    i0.ɵɵtext(201);\n    i0.ɵɵpipe(202, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(203, \"div\", 88)(204, \"div\", 89);\n    i0.ɵɵtext(205);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(206, \"div\", 90);\n    i0.ɵɵtext(207);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(208, LeadPreviewComponent_div_37_div_208_Template, 2, 1, \"div\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(209, \"div\", 91);\n    i0.ɵɵelementStart(210, \"div\", 92)(211, \"h5\", 87);\n    i0.ɵɵtext(212);\n    i0.ɵɵpipe(213, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(214, \"div\", 88)(215, \"div\", 89);\n    i0.ɵɵtext(216);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(217, \"div\", 90);\n    i0.ɵɵtext(218);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(219, LeadPreviewComponent_div_37_div_219_Template, 2, 1, \"div\", 78);\n    i0.ɵɵelementEnd()()()()();\n  }\n\n  if (rf & 2) {\n    const _r49 = i0.ɵɵreference(61);\n\n    const _r56 = i0.ɵɵreference(91);\n\n    const _r62 = i0.ɵɵreference(172);\n\n    const _r65 = i0.ɵɵreference(180);\n\n    const ctx_r11 = i0.ɵɵnextContext();\n    let tmp_9_0;\n    let tmp_11_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r11.whatsAppComp ? \"h-100-377\" : \"h-100-250\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.canUpdateStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.canUpdateStatus);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.currentPath === \"/invoice\" ? ctx_r11.canUpdateInvoice && !(ctx_r11.data == null ? null : ctx_r11.data.isArchived) : (ctx_r11.data == null ? null : ctx_r11.data.status == null ? null : ctx_r11.data.status.actionName) !== \"Invoiced\" && ctx_r11.canEditTags && !(ctx_r11.data == null ? null : ctx_r11.data.isArchived));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"data\", ctx_r11.data)(\"isLastLead\", ctx_r11.isLastLead)(\"whatsAppComp\", ctx_r11.whatsAppComp);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.data == null ? null : ctx_r11.data.callRecordingUrls);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(9, 83, \"GLOBAL.lead\"), \" \", i0.ɵɵpipeBind1(10, 85, \"LEADS.enquiry-info\"), \"\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate((tmp_9_0 = ctx_r11.Gender[ctx_r11.data.gender]) !== null && tmp_9_0 !== undefined ? tmp_9_0 : \"--\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r11.data.dateOfBirth ? ctx_r11.getTimeZoneDate(ctx_r11.data.dateOfBirth, \"00:00:00\", \"dayMonthYear\") : \"--\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((tmp_11_0 = ctx_r11.MaritalStatusType[ctx_r11.data.maritalStatus]) !== null && tmp_11_0 !== undefined ? tmp_11_0 : \"--\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.canViewLeadSource);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.canViewLeadSource);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(125, _c8, ctx_r11.canViewLeadSource));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(33, 87, \"LEAD_FORM.enquired-for\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r11.enquiryTypes, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(127, _c8, ctx_r11.canViewLeadSource));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r11.PurposeType[ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.purpose] ? ctx_r11.PurposeType[ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.purpose] : \"---\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(45, 89, \"GLOBAL.details\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r11.data == null ? null : ctx_r11.data.properties == null ? null : ctx_r11.data.properties.length) != 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r11.data == null ? null : ctx_r11.data.projects == null ? null : ctx_r11.data.projects.length) != 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Property \", i0.ɵɵpipeBind1(52, 91, \"LABEL.type\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.propertyTypes == null ? null : ctx_r11.data.enquiry.propertyTypes[0] == null ? null : ctx_r11.data.enquiry.propertyTypes[0].displayName) ? ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.propertyTypes == null ? null : ctx_r11.data.enquiry.propertyTypes[0] == null ? null : ctx_r11.data.enquiry.propertyTypes[0].displayName : \"--\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.propertyTypes == null ? null : ctx_r11.data.enquiry.propertyTypes.length)(\"ngIfElse\", _r49);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.propertyTypes == null ? null : ctx_r11.data.enquiry.propertyTypes[0] == null ? null : ctx_r11.data.enquiry.propertyTypes[0].displayName) == \"Residential\" && !ctx_r11.hasPlotSubType(ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.propertyTypes));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.globalSettingsData == null ? null : ctx_r11.globalSettingsData.isCustomLeadFormEnabled);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Min. \", i0.ɵɵpipeBind1(67, 93, \"LABEL.budget\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.lowerBudget) ? ctx_r11.formatBudget(ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.lowerBudget, ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.currency) : \"--\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Max. \", i0.ɵɵpipeBind1(73, 95, \"LABEL.budget\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", (ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.upperBudget) ? ctx_r11.formatBudget(ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.upperBudget, ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.currency) : \"--\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.addresses == null ? null : ctx_r11.data.enquiry.addresses.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.addresses);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(82, 97, \"SIDEBAR.others\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(87, 99, \"INTEGRATION.agency-name\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r11.data == null ? null : ctx_r11.data.agencies) && (ctx_r11.data == null ? null : ctx_r11.data.agencies == null ? null : ctx_r11.data.agencies.length) > 0)(\"ngIfElse\", _r56);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(95, 101, \"GLOBAL.assign-from\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r11.getAssignedToDetails(ctx_r11.data.assignedFrom, ctx_r11.users, true) || \"--\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(101, 103, \"AUTH.company-name\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.data.companyName ? ctx_r11.data.companyName : \"--\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(107, 105, \"LEADS.carpet-area\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", (ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.carpetArea) ? ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.carpetArea : \"--\", \" \", (ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.carpetArea) && (ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.carpetAreaUnit) ? ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.carpetAreaUnit : \"\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", (ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.builtUpArea) ? ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.builtUpArea : \"--\", \" \", (ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.builtUpArea) && (ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.builtUpAreaUnit) ? ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.builtUpAreaUnit : \"\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", (ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.saleableArea) ? ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.saleableArea : \"--\", \" \", (ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.saleableArea) && (ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.saleableAreaUnit) ? ctx_r11.data == null ? null : ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.saleableAreaUnit : \"\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.globalSettingsData == null ? null : ctx_r11.globalSettingsData.isCustomLeadFormEnabled);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.data.archivedOn ? ctx_r11.getTimeZoneDate(ctx_r11.data.archivedOn, ctx_r11.userData == null ? null : ctx_r11.userData.timeZoneInfo == null ? null : ctx_r11.userData.timeZoneInfo.baseUTcOffset, \"dayMonthYearText\") : \"---\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r11.userData == null ? null : ctx_r11.userData.timeZoneInfo == null ? null : ctx_r11.userData.timeZoneInfo.timeZoneName) && ctx_r11.data.archivedOn && (ctx_r11.userData == null ? null : ctx_r11.userData.shouldShowTimeZone));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.possessionDate) ? ctx_r11.getTimeZoneDate(ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.possessionDate, ctx_r11.userData == null ? null : ctx_r11.userData.timeZoneInfo == null ? null : ctx_r11.userData.timeZoneInfo.baseUTcOffset, \"dayMonthYearText\") : \"---\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r11.userData == null ? null : ctx_r11.userData.timeZoneInfo == null ? null : ctx_r11.userData.timeZoneInfo.timeZoneName) && (ctx_r11.data.enquiry == null ? null : ctx_r11.data.enquiry.possessionDate) && (ctx_r11.userData == null ? null : ctx_r11.userData.shouldShowTimeZone));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.data.referralName ? ctx_r11.data.referralName : \"--\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.data.referralContactNo ? ctx_r11.data.referralContactNo : \"--\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.data.referralEmail ? ctx_r11.data.referralEmail : \"--\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.data.serialNumber ? ctx_r11.data.serialNumber : \"--\", \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(156, 107, \"LEADS.sourcing-manager\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((ctx_r11.data == null ? null : ctx_r11.data.sourcingManager) ? ctx_r11.getUserName(ctx_r11.data.sourcingManager) : \"--\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(162, 109, \"LEADS.closing-manager\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r11.data == null ? null : ctx_r11.data.closingManager) ? ctx_r11.getUserName(ctx_r11.data == null ? null : ctx_r11.data.closingManager) : \"--\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(168, 111, \"LEAD_FORM.channel-partner-name\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.data == null ? null : ctx_r11.data.channelPartners == null ? null : ctx_r11.data.channelPartners.length)(\"ngIfElse\", _r62);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(176, 113, \"LEAD_FORM.campaign-name\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r11.data == null ? null : ctx_r11.data.campaigns) && (ctx_r11.data == null ? null : ctx_r11.data.campaigns == null ? null : ctx_r11.data.campaigns.length) > 0)(\"ngIfElse\", _r65);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(184, 115, \"LEADS.profession\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r11.data == null ? null : ctx_r11.data.profession) ? ctx_r11.getProfession(ctx_r11.data.profession) : \"--\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(190, 117, \"LEADS.customer-location\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r11.data == null ? null : ctx_r11.data.address == null ? null : ctx_r11.data.address.subLocality) && (ctx_r11.data == null ? null : ctx_r11.data.address == null ? null : ctx_r11.data.address.city) && (ctx_r11.data == null ? null : ctx_r11.data.address == null ? null : ctx_r11.data.address.state) ? (ctx_r11.data == null ? null : ctx_r11.data.address == null ? null : ctx_r11.data.address.subLocality) + \", \" + (ctx_r11.data == null ? null : ctx_r11.data.address == null ? null : ctx_r11.data.address.city) + \", \" + (ctx_r11.data == null ? null : ctx_r11.data.address == null ? null : ctx_r11.data.address.state) : (ctx_r11.data == null ? null : ctx_r11.data.address == null ? null : ctx_r11.data.address.city) ? ctx_r11.data == null ? null : ctx_r11.data.address == null ? null : ctx_r11.data.address.city : \"--\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.data == null ? null : ctx_r11.data.additionalProperties);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(197, 119, \"LEADS.last-activity\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(202, 121, \"LEADS.created-by\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r11.getAssignedToDetails(ctx_r11.data.createdBy, ctx_r11.users, true) || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"At \", ctx_r11.getTimeZoneDate(ctx_r11.data.createdOn, ctx_r11.userData == null ? null : ctx_r11.userData.timeZoneInfo == null ? null : ctx_r11.userData.timeZoneInfo.baseUTcOffset), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r11.userData == null ? null : ctx_r11.userData.timeZoneInfo == null ? null : ctx_r11.userData.timeZoneInfo.timeZoneName) && ctx_r11.data.createdOn && (ctx_r11.userData == null ? null : ctx_r11.userData.shouldShowTimeZone));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(213, 123, \"LEADS.modified-by\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r11.getAssignedToDetails(ctx_r11.data == null ? null : ctx_r11.data.lastModifiedBy, ctx_r11.users, true) || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"At \", ctx_r11.getTimeZoneDate(ctx_r11.data.lastModifiedOn, ctx_r11.userData == null ? null : ctx_r11.userData.timeZoneInfo == null ? null : ctx_r11.userData.timeZoneInfo.baseUTcOffset), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r11.userData == null ? null : ctx_r11.userData.timeZoneInfo == null ? null : ctx_r11.userData.timeZoneInfo.timeZoneName) && ctx_r11.data.lastModifiedOn && (ctx_r11.userData == null ? null : ctx_r11.userData.shouldShowTimeZone));\n  }\n}\n\nfunction LeadPreviewComponent_div_39_status_change_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"status-change\", 134, 135);\n  }\n\n  if (rf & 2) {\n    const ctx_r114 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"leadInfo\", ctx_r114.data)(\"canShowStatusPopupInPreview\", ctx_r114.canShowStatusPopupInPreview && ctx_r114.showOnlyPopup)(\"canUpdateStatus\", ctx_r114.canUpdateStatus)(\"isLeadPreview\", true)(\"isLastLead\", ctx_r114.isLastLead)(\"closeLeadPreviewModal\", ctx_r114.closeLeadPreviewModal)(\"whatsAppComp\", ctx_r114.whatsAppComp);\n  }\n}\n\nfunction LeadPreviewComponent_div_39_custom_status_change_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"custom-status-change\", 134, 136);\n  }\n\n  if (rf & 2) {\n    const ctx_r115 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"leadInfo\", ctx_r115.data)(\"canShowStatusPopupInPreview\", ctx_r115.canShowStatusPopupInPreview && ctx_r115.showOnlyPopup)(\"canUpdateStatus\", ctx_r115.canUpdateStatus)(\"isLeadPreview\", true)(\"isLastLead\", ctx_r115.isLastLead)(\"closeLeadPreviewModal\", ctx_r115.closeLeadPreviewModal)(\"whatsAppComp\", ctx_r115.whatsAppComp);\n  }\n}\n\nfunction LeadPreviewComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 132);\n    i0.ɵɵtemplate(2, LeadPreviewComponent_div_39_status_change_2_Template, 2, 7, \"status-change\", 133);\n    i0.ɵɵtemplate(3, LeadPreviewComponent_div_39_custom_status_change_3_Template, 2, 7, \"custom-status-change\", 133);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r12.isCustomStatusEnabled);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.isCustomStatusEnabled);\n  }\n}\n\nfunction LeadPreviewComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 29);\n    i0.ɵɵelement(2, \"lead-history\", 137);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"data\", ctx_r13.data)(\"whatsAppComp\", ctx_r13.whatsAppComp);\n  }\n}\n\nfunction LeadPreviewComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r119 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 29)(2, \"lead-notes\", 138);\n    i0.ɵɵlistener(\"notesAdded\", function LeadPreviewComponent_div_41_Template_lead_notes_notesAdded_2_listener($event) {\n      i0.ɵɵrestoreView(_r119);\n      const ctx_r118 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r118.updateNotes($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"data\", ctx_r14.data)(\"whatsAppComp\", ctx_r14.whatsAppComp);\n  }\n}\n\nconst _c9 = function (a0) {\n  return {\n    \"position-relative\": a0\n  };\n};\n\nfunction LeadPreviewComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 139);\n    i0.ɵɵelement(2, \"leads-document-upload\", 140);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c9, ctx_r15.whatsAppComp));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"leadData\", ctx_r15.data)(\"whatsAppComp\", ctx_r15.whatsAppComp);\n  }\n}\n\nfunction LeadPreviewComponent_ng_template_43_ng_container_4_ng_container_1_ng_container_5_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"ng-lottie\", 152);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r127 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r127.loader);\n  }\n}\n\nfunction LeadPreviewComponent_ng_template_43_ng_container_4_ng_container_1_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r129 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 146)(2, \"h5\", 147);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 148)(5, \"audio\", 149, 150);\n    i0.ɵɵlistener(\"play\", function LeadPreviewComponent_ng_template_43_ng_container_4_ng_container_1_ng_container_5_Template_audio_play_5_listener() {\n      i0.ɵɵrestoreView(_r129);\n\n      const _r126 = i0.ɵɵreference(6);\n\n      const ctx_r128 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r128.pauseOtherAudio(_r126));\n    })(\"canplay\", function LeadPreviewComponent_ng_template_43_ng_container_4_ng_container_1_ng_container_5_Template_audio_canplay_5_listener() {\n      i0.ɵɵrestoreView(_r129);\n      const ctx_r130 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r130.isLoading = false);\n    })(\"loadedmetadata\", function LeadPreviewComponent_ng_template_43_ng_container_4_ng_container_1_ng_container_5_Template_audio_loadedmetadata_5_listener() {\n      i0.ɵɵrestoreView(_r129);\n      const ctx_r131 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r131.isLoading = false);\n    });\n    i0.ɵɵelement(7, \"source\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, LeadPreviewComponent_ng_template_43_ng_container_4_ng_container_1_ng_container_5_div_8_Template, 2, 1, \"div\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const audio_r125 = ctx.$implicit;\n    const ctx_r124 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r124.getTimeZoneDate(audio_r125 == null ? null : audio_r125.date, ctx_r124.userData == null ? null : ctx_r124.userData.timeZoneInfo == null ? null : ctx_r124.userData.timeZoneInfo.baseUTcOffset), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", audio_r125 == null ? null : audio_r125.audioUrl, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r124.isLoading);\n  }\n}\n\nfunction LeadPreviewComponent_ng_template_43_ng_container_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 143)(2, \"h5\", 144);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 145);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, LeadPreviewComponent_ng_template_43_ng_container_4_ng_container_1_ng_container_5_Template, 9, 3, \"ng-container\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const month_r123 = ctx.$implicit;\n    const year_r121 = i0.ɵɵnextContext().$implicit;\n    const ctx_r122 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r122.moment(month_r123 == null ? null : month_r123.months).format(\"MMM\"), \" \", year_r121 == null ? null : year_r121.years, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", month_r123.monthData);\n  }\n}\n\nfunction LeadPreviewComponent_ng_template_43_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LeadPreviewComponent_ng_template_43_ng_container_4_ng_container_1_Template, 6, 3, \"ng-container\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const year_r121 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", year_r121.yearData);\n  }\n}\n\nfunction LeadPreviewComponent_ng_template_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 141);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 142);\n    i0.ɵɵtemplate(4, LeadPreviewComponent_ng_template_43_ng_container_4_Template, 2, 1, \"ng-container\", 22);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, \"LEADS.lead-call-recordings\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r17.callRecordingDetails);\n  }\n}\n\nconst _c10 = function (a0) {\n  return {\n    \"blinking\": a0\n  };\n};\n\nconst _c11 = function () {\n  return [\"\"];\n};\n\nexport class LeadPreviewComponent {\n  constructor(modalService, modalRef, store, gridOptionService, router, shareDataService, trackingService) {\n    this.modalService = modalService;\n    this.modalRef = modalRef;\n    this.store = store;\n    this.gridOptionService = gridOptionService;\n    this.router = router;\n    this.shareDataService = shareDataService;\n    this.trackingService = trackingService;\n    this.clickedData = null;\n    this.whatsAppComp = false;\n    this.showCommunicationCount = false;\n    this.stopper = new EventEmitter();\n    this.selectedSection = 'Overview';\n    this.showOnlyPopup = true;\n    this.moment = moment;\n    this.formatBudget = formatBudget;\n    this.getAssignedToDetails = getAssignedToDetails;\n    this.getBHKDisplayString = getBHKDisplayString;\n    this.getLocationDetailsByObj = getLocationDetailsByObj;\n    this.getTimeZoneDate = getTimeZoneDate;\n    this.OfferType = OfferType;\n    this.FurnishStatus = FurnishStatus;\n    this.convertUrlsToLinks = convertUrlsToLinks;\n    this.BHKType = BHKType;\n    this.LeadSource = LeadSource;\n    this.PurposeType = PurposeType;\n    this.EnquiryType = EnquiryType;\n    this.EMPTY_GUID = EMPTY_GUID;\n    this.navigationItems = [{\n      label: 'GLOBAL.overview',\n      section: 'Overview'\n    }, {\n      label: 'GLOBAL.status',\n      section: 'Status'\n    }, {\n      label: 'GLOBAL.history',\n      section: 'History'\n    }, {\n      label: 'TASK.notes',\n      section: 'Notes'\n    }, {\n      label: 'GLOBAL.document',\n      section: 'Document'\n    }];\n    this.flagOptions = [];\n    this.toggleSelectFlag = false;\n    this.defaultCurrency = 'INR';\n    this.isLeadPreviewChanged = false;\n    this.fetchLeadsWhenClosed = false;\n    this.isNextDataLoading = false;\n    this.isPreviousDataLoading = false;\n    this.canEditLead = false;\n    this.canEditTags = false;\n    this.canUpdateInvoice = false;\n    this.canViewLeadSource = false;\n    this.canUpdateStatus = false;\n    this.canUpdateBookedLead = false;\n    this.flagOptionsDefault = [];\n    this.isCustomStatusEnabled = false;\n    this.isGlobalSettingsLoading = true;\n    this.Gender = Gender;\n    this.MaritalStatusType = MaritalStatusType;\n  }\n\n  get canShowStatusPopupInPreview() {\n    var _a, _b, _c, _d;\n\n    return (((_a = this.data.status) === null || _a === void 0 ? void 0 : _a.displayName) === UPDATE_STATUS_PAST_TENSE['meeting-scheduled'] || ((_b = this.data.status) === null || _b === void 0 ? void 0 : _b.displayName) === UPDATE_STATUS_PAST_TENSE['visit-scheduled'] || ((_c = this.data.status) === null || _c === void 0 ? void 0 : _c.displayName) === UPDATE_STATUS_PAST_TENSE['referral-scheduled']) && !this.isCustomStatusEnabled || this.isCustomStatusEnabled && this.shouldOpenAppointmentPageForStatus((_d = this.data) === null || _d === void 0 ? void 0 : _d.status);\n  }\n\n  shouldOpenAppointmentPageForStatus(status) {\n    var _a, _b, _c;\n\n    return (status === null || status === void 0 ? void 0 : status.shouldOpenAppointmentPage) || ((_a = status === null || status === void 0 ? void 0 : status.childType) === null || _a === void 0 ? void 0 : _a.shouldOpenAppointmentPage) || ((_b = status === null || status === void 0 ? void 0 : status.childType) === null || _b === void 0 ? void 0 : _b.length) && ((_c = status === null || status === void 0 ? void 0 : status.childType) === null || _c === void 0 ? void 0 : _c.some(childType => childType === null || childType === void 0 ? void 0 : childType.shouldOpenAppointmentPage)) || false;\n  }\n\n  get isMobileView() {\n    return window.innerWidth <= 480;\n  }\n\n  get isFirstPage() {\n    return this.shareDataService.currentPageNumber === 1;\n  }\n\n  get currentPage() {\n    return this.shareDataService.currentPageNumber;\n  }\n\n  get totalPages() {\n    return this.shareDataService.totalPages;\n  }\n\n  get isLastLead() {\n    var _a, _b;\n\n    return this.foundData == ((_a = this.cardData) === null || _a === void 0 ? void 0 : _a.length) - 1 && this.isMobileView || this.totalPages === this.currentPage && this.foundData == ((_b = this.cardData) === null || _b === void 0 ? void 0 : _b.length) - 1;\n  }\n\n  get enquiryTypes() {\n    var _a, _b, _c, _d;\n\n    return ((_d = (_c = (_b = (_a = this.data) === null || _a === void 0 ? void 0 : _a.enquiry) === null || _b === void 0 ? void 0 : _b.enquiryTypes) === null || _c === void 0 ? void 0 : _c.map(enquiry => EnquiryType[enquiry])) === null || _d === void 0 ? void 0 : _d.join(', ')) || '--';\n  }\n\n  get bhkTypes() {\n    var _a, _b, _c, _d;\n\n    return ((_d = (_c = (_b = (_a = this.data) === null || _a === void 0 ? void 0 : _a.enquiry) === null || _b === void 0 ? void 0 : _b.bhkTypes) === null || _c === void 0 ? void 0 : _c.map(type => BHKType[type])) === null || _d === void 0 ? void 0 : _d.join(', ')) || '--';\n  }\n\n  get bhkNo() {\n    var _a, _b, _c, _d; // return this.globalSettingsData?.isCustomLeadFormEnabled\n    //   ? this.data?.enquiry?.bhKs\n    //     /* BR field commented out\n    //     ?.map((bhk: any) => getBRDisplayString(bhk))\n    //     */\n    //     ?.join(', ')\n    //   :\n\n\n    return (_d = (_c = (_b = (_a = this.data) === null || _a === void 0 ? void 0 : _a.enquiry) === null || _b === void 0 ? void 0 : _b.bhKs) === null || _c === void 0 ? void 0 : _c.map(bhk => getBHKDisplayString(bhk))) === null || _d === void 0 ? void 0 : _d.join(', ');\n  }\n\n  get beds() {\n    var _a, _b;\n\n    return Array.isArray((_a = this.data.enquiry) === null || _a === void 0 ? void 0 : _a.beds) ? (_b = this.data.enquiry) === null || _b === void 0 ? void 0 : _b.beds.map(bed => bed === 0 || bed === '0' ? 'Studio' : bed) : [];\n  }\n\n  get addresses() {\n    var _a, _b, _c, _d;\n\n    return ((_d = (_c = (_b = (_a = this.data) === null || _a === void 0 ? void 0 : _a.enquiry) === null || _b === void 0 ? void 0 : _b.addresses) === null || _c === void 0 ? void 0 : _c.map(address => getLocationDetailsByObj(address))) === null || _d === void 0 ? void 0 : _d.join('; ')) || '--';\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      var _a, _b, _c, _d; // ✅ Reset loading states when component initializes\n\n\n      _this.isNextDataLoading = false;\n      _this.isPreviousDataLoading = false;\n      console.log('🔧 Lead Preview Init - Loading states reset:', {\n        isNextDataLoading: _this.isNextDataLoading,\n        isPreviousDataLoading: _this.isPreviousDataLoading,\n        isMobileView: _this.isMobileView\n      });\n\n      _this.shareDataService.backtoOverview.subscribe(value => {\n        if (value) {\n          _this.selectedSection = value;\n        }\n      }); // ✅ Force reset loading states after a short delay to ensure all initialization is complete\n\n\n      setTimeout(() => {\n        _this.isNextDataLoading = false;\n        _this.isPreviousDataLoading = false;\n        console.log('🔧 Delayed loading state reset:', {\n          isNextDataLoading: _this.isNextDataLoading,\n          isPreviousDataLoading: _this.isPreviousDataLoading\n        });\n      }, 100);\n\n      _this.store.select(getLeads).pipe(takeUntil(_this.stopper), skipWhile(() => _this.isMobileView)).subscribe(data => {\n        _this.cardData = data; // this.data = this.cardData?.find((data: any) => data?.id === this.data?.id)\n      });\n\n      _this.store.select(getLeadCardData).pipe(takeUntil(_this.stopper), skipWhile(() => !_this.isMobileView)).subscribe( /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (data) {\n          _this.cardData = data; // this.data = this.cardData?.find((data: any) => data?.id === this.data?.id)\n        });\n\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n\n      _this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {\n        _this.currentPath = _this.router.url;\n      });\n\n      _this.currentPath = _this.router.url;\n      yield _this.store.select(getGlobalAnonymousIsLoading).pipe(skipWhile(isLoading => {\n        return isLoading;\n      }), take(1)).toPromise();\n      _this.isCustomStatusEnabled = yield _this.store.select(getIsLeadCustomStatusEnabled).pipe(map(data => data), take(1)).toPromise();\n      _this.selectedFlags = (_a = _this.data) === null || _a === void 0 ? void 0 : _a.customFlags;\n\n      _this.store.dispatch(new FetchTagsList());\n\n      _this.store.select(getTagsList).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        let flagData = data || [];\n\n        if (flagData.length > 0) {\n          _this.flagOptions = flagData.filter(flag => flag === null || flag === void 0 ? void 0 : flag.isActive).map(flag => {\n            const isActive = false;\n            return Object.assign(Object.assign({}, flag), {\n              isActive\n            });\n          });\n          _this.flagOptionsDefault = _this.flagOptions;\n        }\n\n        _this.sortFlags();\n\n        _this.updateFlags();\n      });\n\n      _this.foundData = (_b = _this.cardData) === null || _b === void 0 ? void 0 : _b.findIndex(item => _this.isDataEqual(item, _this.data));\n\n      if ((_this.foundData === ((_c = _this.cardData) === null || _c === void 0 ? void 0 : _c.length) - 3 || _this.foundData === ((_d = _this.cardData) === null || _d === void 0 ? void 0 : _d.length) - 1) && _this.isMobileView) {\n        _this.shareDataService.emitFetchNextLeads();\n      }\n\n      _this.getCardData();\n\n      const adminsAndReportees$ = _this.store.select(getAdminsAndReportees).pipe(takeUntil(_this.stopper));\n\n      const allUsers$ = _this.store.select(getUsersListForReassignment).pipe(takeUntil(_this.stopper));\n\n      const permissions$ = _this.store.select(getPermissions).pipe(takeUntil(_this.stopper));\n\n      _this.store.select(getLeadPreviewChanged).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        _this.isLeadPreviewChanged = data;\n      });\n\n      combineLatest({\n        adminsAndReportees: adminsAndReportees$,\n        allUsers: allUsers$,\n        permissions: permissions$\n      }).subscribe(({\n        adminsAndReportees,\n        allUsers,\n        permissions\n      }) => {\n        var _a, _b;\n\n        const permissionsSet = new Set(permissions);\n        if (permissions === null || permissions === void 0 ? void 0 : permissions.includes('Permissions.Leads.ViewLeadSource')) _this.canViewLeadSource = true;\n        if (permissions === null || permissions === void 0 ? void 0 : permissions.includes('Permissions.Leads.UpdateLeadStatus')) _this.canUpdateStatus = true;\n\n        if (permissions === null || permissions === void 0 ? void 0 : permissions.includes('Permissions.Users.AssignToAny')) {\n          _this.users = allUsers;\n        } else {\n          _this.users = adminsAndReportees;\n        }\n\n        if (permissions === null || permissions === void 0 ? void 0 : permissions.includes('Permissions.Leads.Update')) {\n          _this.canEditLead = true;\n        }\n\n        _this.canEditTags = permissionsSet.has('Permissions.Leads.UpdateTags');\n\n        if (permissions === null || permissions === void 0 ? void 0 : permissions.includes('Permissions.Invoice.Update')) {\n          _this.canUpdateInvoice = true;\n        }\n\n        if (permissions === null || permissions === void 0 ? void 0 : permissions.includes('Permissions.Invoice.View')) {\n          if (!_this.canUpdateInvoice && _this.currentPath === '/invoice' && ((_b = (_a = _this.data) === null || _a === void 0 ? void 0 : _a.status) === null || _b === void 0 ? void 0 : _b.status) === 'invoiced') {\n            _this.selectedSection = 'Overview';\n          }\n        }\n      });\n\n      if (_this.initialState) {\n        _this.selectedSection = _this.initialState.selectedSection;\n      }\n\n      _this.store.select(getGlobalSettingsAnonymous).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        _this.defaultCurrency = data.countries && data.countries.length > 0 ? data.countries[0].defaultCurrency : null;\n        _this.globalSettingsData = data;\n      });\n\n      _this.store.select(getPermissions).pipe(takeUntil(_this.stopper)).subscribe(permissions => {\n        if (!(permissions === null || permissions === void 0 ? void 0 : permissions.length)) return;\n\n        if (permissions === null || permissions === void 0 ? void 0 : permissions.includes('Permissions.Leads.UpdateBookedLead')) {\n          _this.canUpdateBookedLead = true;\n        }\n      });\n\n      _this.store.select(getUserBasicDetails).pipe(takeUntil(_this.stopper)).subscribe(data => {\n        _this.userData = data;\n      });\n    })();\n  }\n\n  ngOnChanges(changes) {\n    if (changes.clickedData && changes.clickedData.currentValue) {\n      this.data = this.clickedData; // ✅ Reset loading states when new data is received\n\n      this.isNextDataLoading = false;\n      this.isPreviousDataLoading = false;\n    }\n  }\n\n  ngAfterViewInit() {\n    const swipeArea = document.getElementById('swipeArea');\n    let startX, startY;\n    swipeArea.addEventListener('touchstart', event => {\n      startX = event.touches[0].clientX;\n      startY = event.touches[0].clientY;\n    });\n    swipeArea.addEventListener('touchend', event => {\n      const endX = event.changedTouches[0].clientX;\n      const endY = event.changedTouches[0].clientY;\n      const deltaX = endX - startX;\n      const deltaY = endY - startY; // left to right swipe\n\n      if (deltaX > 100 && Math.abs(deltaY) < 50) {\n        this.prevData();\n      } // right to left swipe\n      else if (deltaX < -100 && Math.abs(deltaY) < 50) {\n        this.nextData();\n      }\n    });\n  }\n\n  isSelectedFlag(f) {\n    var _a;\n\n    return (_a = this.selectedFlags) === null || _a === void 0 ? void 0 : _a.some(selectedFlag => (selectedFlag === null || selectedFlag === void 0 ? void 0 : selectedFlag.flag.id) === f.id);\n  }\n\n  filteredNavigationItems() {\n    var _a, _b, _c;\n\n    if ((_a = this.data) === null || _a === void 0 ? void 0 : _a.isArchived) {\n      return this.navigationItems.filter(item => ['Overview', 'History'].includes(item.section));\n    }\n\n    if (this.currentPath === '/invoice') {\n      return this.canUpdateInvoice ? this.navigationItems : this.navigationItems.filter(item => item.section !== 'Status');\n    } else {\n      if (((_c = (_b = this.data) === null || _b === void 0 ? void 0 : _b.status) === null || _c === void 0 ? void 0 : _c.status) === 'invoiced') {\n        return this.navigationItems.filter(item => item.section !== 'Status');\n      } else {\n        return this.canUpdateStatus ? this.navigationItems : this.navigationItems.filter(item => item.section !== 'Status');\n      }\n    }\n  }\n\n  getActiveBackgroundColor(flag) {\n    const bgColor = {};\n\n    if (flag.isActive) {\n      bgColor['border-black fw-600'] = true;\n      bgColor['bg-white'] = true;\n    }\n\n    return bgColor;\n  }\n\n  isDataEqual(obj1, obj2) {\n    return obj1.id === obj2.id;\n  } // ✅ Method to force reset loading states\n\n\n  forceResetLoadingStates() {\n    this.isNextDataLoading = false;\n    this.isPreviousDataLoading = false;\n    console.log('🔧 Force reset loading states called');\n  }\n\n  prevData() {\n    var _a, _b;\n\n    if (this.foundData > 0) {\n      this.foundData--;\n      this.data = (_a = this.cardData) === null || _a === void 0 ? void 0 : _a[this.foundData];\n      this.updateFlags();\n    } else if (this.foundData === 0 && !this.isMobileView) {\n      this.foundData = 0;\n      this.shareDataService.emitFetchPreviousLeads(false);\n      this.subscribeToDataUpdated();\n      this.isPreviousDataLoading = this.shareDataService.currentPageNumber === 1 ? false : true;\n    }\n\n    if (this.selectedSection === 'Status') this.isCustomStatusEnabled ? (_b = this.customStatusChangeComponent) === null || _b === void 0 ? void 0 : _b.cleanStatusForm() : this.statusChangeComponent.cleanStatusForm();\n    this.trackingService.trackFeature(`Web.Leads.Menu.Previouslead.Click`, this.data.id);\n  }\n\n  nextData() {\n    var _a, _b, _c, _d, _e, _f;\n\n    if (this.foundData < ((_a = this.cardData) === null || _a === void 0 ? void 0 : _a.length) - 1) {\n      this.foundData++;\n      this.data = (_b = this.cardData) === null || _b === void 0 ? void 0 : _b[this.foundData];\n      this.updateFlags();\n\n      if ((this.foundData === ((_c = this.cardData) === null || _c === void 0 ? void 0 : _c.length) - 3 || this.foundData === ((_d = this.cardData) === null || _d === void 0 ? void 0 : _d.length) - 1) && !this.isNextDataLoading && this.isMobileView) {\n        this.shareDataService.emitFetchNextLeads();\n        this.subscribeToDataUpdated();\n        this.isNextDataLoading = true;\n      }\n    } else if (this.foundData === ((_e = this.cardData) === null || _e === void 0 ? void 0 : _e.length) - 1 && !this.isMobileView) {\n      this.foundData = 0;\n      this.shareDataService.emitFetchNextLeads(false);\n      this.subscribeToDataUpdated();\n      this.isNextDataLoading = true;\n    }\n\n    if (this.selectedSection === 'Status') (_f = this.statusChangeComponent) === null || _f === void 0 ? void 0 : _f.cleanStatusForm();\n    this.trackingService.trackFeature(`Web.Leads.Menu.NextLead.Click`, this.data.id);\n  }\n\n  getNextLead() {\n    this.shareDataService.sendMiniBookingformData(this.data);\n  }\n\n  updateFlags() {\n    var _a, _b, _c;\n\n    this.flagOptions = this.flagOptionsDefault;\n    const flags = {};\n    (_b = (_a = this.data) === null || _a === void 0 ? void 0 : _a.customFlags) === null || _b === void 0 ? void 0 : _b.forEach(flag => {\n      var _a;\n\n      flags[(_a = flag === null || flag === void 0 ? void 0 : flag.flag) === null || _a === void 0 ? void 0 : _a.id] = true;\n    });\n    this.flagOptions = (_c = this.flagOptions) === null || _c === void 0 ? void 0 : _c.map(flag => {\n      if (flags[flag === null || flag === void 0 ? void 0 : flag.id]) {\n        return Object.assign(Object.assign({}, flag), {\n          isActive: true\n        });\n      }\n\n      return Object.assign({}, flag);\n    });\n  }\n\n  getUserName(id) {\n    return getAssignedToDetails(id, this.users, true) || '';\n  }\n\n  getProfession(profession) {\n    return Profession[profession];\n  }\n\n  onSectionSelect(section) {\n    this.selectedSection = section; // ✅ Force reset loading states when History section is selected\n\n    if (section === 'History') {\n      this.forceResetLoadingStates();\n    }\n\n    this.trackingService.trackFeature(`Web.Leads.Menu.${this.selectedSection.replace(/\\s+/g, '')}.Click`, this.data.id);\n  }\n\n  flagAction(flag) {\n    flag.isActive = !(flag === null || flag === void 0 ? void 0 : flag.isActive);\n    let payload = {\n      id: this.data.id,\n      flagId: flag === null || flag === void 0 ? void 0 : flag.id\n    };\n    this.flagOptions = this.flagOptions.map(f => f.id === flag.id ? Object.assign(Object.assign({}, f), {\n      isActive: flag.isActive\n    }) : f);\n    this.store.dispatch(new LeadPreviewChanged());\n    this.store.dispatch(new UpdateLeadsTagInfo(payload, this.data.id, false));\n    this.trackingService.trackFeature(`Web.Leads.Button.Tags.Click`, this.data.id);\n  }\n\n  sortFlags() {\n    this.flagOptions.sort((a, b) => {\n      if (a.isActive === b.isActive) {\n        const nameA = a.name.toUpperCase();\n        const nameB = b.name.toUpperCase();\n        return nameA.localeCompare(nameB);\n      } else {\n        return a.isActive ? -1 : 1;\n      }\n    });\n  }\n\n  updateNotes(data) {\n    this.data.notes = data;\n  }\n\n  checkStatus(section) {\n    this.onSectionSelect(section);\n    this.trackingService.trackFeature(`Web.Leads.Button.ChangeStatus.Click`, this.data.id);\n  }\n\n  openAudioPlayer(aP) {\n    var _a, _b;\n\n    if (this.modalRef) {\n      this.modalRef.hide();\n    }\n\n    if ((_a = this.data) === null || _a === void 0 ? void 0 : _a.callRecordingUrls) {\n      this.callRecordingDetails = Object.entries((_b = this.data) === null || _b === void 0 ? void 0 : _b.callRecordingUrls).map(item => {\n        return {\n          years: item[0],\n          yearData: Object.entries(item[1]).reverse().map(item1 => {\n            return {\n              months: item1[0],\n              monthData: Object.keys(item1[1]).reverse().map(key => {\n                let audioDate = key.toString();\n                let audioFile = item1[1][key];\n                return {\n                  date: audioDate,\n                  audioUrl: audioFile\n                };\n              })\n            };\n          })\n        };\n      }).reverse();\n    }\n\n    this.modalRef = this.modalService.show(aP, {\n      class: 'right-modal modal-350 ph-modal-unset'\n    });\n  }\n\n  getCardData() {\n    var _a, _b, _c;\n\n    if (this.foundData || ((_a = this.cardData) === null || _a === void 0 ? void 0 : _a.length)) {\n      return;\n    }\n\n    let {\n      cardData\n    } = this.shareDataService.getCardData();\n    this.cardData = cardData;\n    this.foundData = (_b = this.cardData) === null || _b === void 0 ? void 0 : _b.findIndex(item => this.isDataEqual(item, this.data)); // ✅ Force reset loading states when getting card data\n\n    this.isNextDataLoading = false;\n    this.isPreviousDataLoading = false;\n    console.log('🔧 Card data loaded - Loading states reset:', {\n      isNextDataLoading: this.isNextDataLoading,\n      isPreviousDataLoading: this.isPreviousDataLoading,\n      foundData: this.foundData,\n      cardDataLength: (_c = this.cardData) === null || _c === void 0 ? void 0 : _c.length\n    });\n  }\n\n  hasPlotSubType(propertyTypes) {\n    return propertyTypes === null || propertyTypes === void 0 ? void 0 : propertyTypes.some(type => {\n      var _a;\n\n      return ((_a = type === null || type === void 0 ? void 0 : type.childType) === null || _a === void 0 ? void 0 : _a.displayName) === 'Plot';\n    });\n  }\n\n  getSubtypesTitle() {\n    var _a, _b, _c;\n\n    return ((_c = (_b = (_a = this.data) === null || _a === void 0 ? void 0 : _a.enquiry) === null || _b === void 0 ? void 0 : _b.propertyTypes) === null || _c === void 0 ? void 0 : _c.map(type => {\n      var _a;\n\n      return (_a = type === null || type === void 0 ? void 0 : type.childType) === null || _a === void 0 ? void 0 : _a.displayName;\n    }).join(', ')) || '';\n  }\n\n  subscribeToDataUpdated() {\n    this.dataUpdated$ = this.shareDataService.dataUpdated.subscribe(({\n      cardData,\n      isPrevFetched\n    }) => {\n      if (cardData === null || cardData === void 0 ? void 0 : cardData.length) {\n        this.cardData = cardData;\n\n        if (!this.isMobileView) {\n          const index = isPrevFetched ? cardData.length - 1 : 0;\n          this.data = cardData[index];\n          this.foundData = index;\n        }\n\n        this.isNextDataLoading = false;\n        this.isPreviousDataLoading = false;\n        this.dataUpdated$.unsubscribe();\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    var _a, _b;\n\n    this.stopper.next();\n    this.stopper.complete();\n    this.shareDataService.gotoOverviewTab(null);\n\n    if (this.isLeadPreviewChanged || this.fetchLeadsWhenClosed) {\n      this.store.dispatch(new ClearCardData());\n      this.fetchLeadsWhenClosed = false;\n      this.store.dispatch(new FetchLeadList(true, (_a = location === null || location === void 0 ? void 0 : location.href) === null || _a === void 0 ? void 0 : _a.includes('invoice')));\n    }\n\n    this.store.dispatch(new LeadPreviewSaved());\n    (_b = this.dataUpdated$) === null || _b === void 0 ? void 0 : _b.unsubscribe();\n  }\n\n}\n\nLeadPreviewComponent.ɵfac = function LeadPreviewComponent_Factory(t) {\n  return new (t || LeadPreviewComponent)(i0.ɵɵdirectiveInject(i1.BsModalService), i0.ɵɵdirectiveInject(i1.BsModalRef), i0.ɵɵdirectiveInject(i2.Store), i0.ɵɵdirectiveInject(i3.GridOptionsService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.ShareDataService), i0.ɵɵdirectiveInject(i6.TrackingService));\n};\n\nLeadPreviewComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: LeadPreviewComponent,\n  selectors: [[\"lead-preview\"]],\n  viewQuery: function LeadPreviewComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.statusChangeComponent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.customStatusChangeComponent = _t.first);\n    }\n  },\n  inputs: {\n    clickedData: \"clickedData\",\n    whatsAppComp: \"whatsAppComp\",\n    showCommunicationCount: \"showCommunicationCount\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 53,\n  vars: 47,\n  consts: [[\"id\", \"swipeArea\"], [1, \"px-20\", 3, \"ngClass\"], [1, \"flex-between\", \"mt-10\"], [1, \"align-center\", \"mt-10\"], [1, \"d-flex\", \"cursor-pointer\"], [\"class\", \"icon ic-black ic-xs ic-circle-chevron-left mr-8\", 3, \"click\", 4, \"ngIf\"], [1, \"fw-600\"], [1, \"flex-end\", \"ph-d-none\", \"flex-grow-1\"], [4, \"ngIf\"], [1, \"w-70\"], [3, \"data\", \"isLeadPreviewOpen\", \"whatsAppComp\", \"showCommunicationCount\", \"changeSelection\"], [1, \"bg-secondary\", \"my-16\", \"p-16\", \"br-4\", \"text-mud\", \"text-sm\", \"ip-w-100-40\"], [1, \"fw-600\", \"text-coal\", \"text-truncate-1\", \"break-all\"], [1, \"align-center\", \"mt-8\"], [1, \"icon\", \"ic-location-circle\", \"ic-slate-90\", \"ic-xxs\", \"mr-6\"], [1, \"scrollbar\", \"scroll-hide\"], [1, \"text-truncate-1\", \"break-all\"], [1, \"d-flex\", \"mt-8\"], [1, \"icon\", \"ic-apartment\", \"ic-slate-90\", \"ic-xxs\", \"mr-6\"], [1, \"align-center\", \"text-nowrap\"], [\"class\", \"align-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"Horizontal-navbar\"], [4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\"], [\"audioOption\", \"\"], [1, \"ph-d-block\", \"d-none\", \"bottom-0\", \"p-10\", \"position-absolute\", \"bg-white\"], [1, \"d-flex\", \"ph-w-100-20\", \"mble-preview\"], [\"title\", \"Previous Lead\", 1, \"bg-coal\", \"icon-badge\", \"mt-4\", 3, \"ngClass\", \"click\"], [1, \"icon\", \"ic-chevron-left\", \"m-auto\", \"ic-xxs\"], [1, \"w-100\"], [\"title\", \"Next Lead\", 1, \"bg-coal\", \"icon-badge\", \"mt-4\", 3, \"ngClass\", \"click\"], [1, \"icon\", \"ic-chevron-right\", \"m-auto\", \"ic-xxs\"], [1, \"icon\", \"ic-black\", \"ic-xs\", \"ic-circle-chevron-left\", \"mr-8\", 3, \"click\"], [\"title\", \"Previous Lead\", 1, \"mt-4\", \"bg-coal\", \"icon-badge\", 3, \"ngClass\", \"click\"], [\"title\", \"Next Lead\", 1, \"mt-4\", \"bg-coal\", \"icon-badge\", 3, \"ngClass\", \"click\"], [1, \"align-center\"], [1, \"icon\", \"ic-Call\", \"mr-6\", \"ic-slate-90\", \"ic-xxxs\"], [1, \"align-center\", \"mr-20\"], [1, \"icon\", \"ic-Call\", \"ic-slate-90\", \"ic-xxxs\", \"mr-6\", \"ml-20\"], [1, \"icon\", \"ic-mail\", \"ic-slate-90\", \"ic-xxs\", \"mr-6\", \"ml-2\"], [1, \"text-accent-green\", \"text-sm\", \"fw-600\"], [\"class\", \"dot dot-xxs bg-accent-green mx-6\", 4, \"ngIf\"], [1, \"dot\", \"dot-xxs\", \"bg-accent-green\", \"mx-6\"], [1, \"dot\", \"dot-xxs\", \"bg-dark-700\", \"mx-6\"], [4, \"ngIf\", \"ngIfElse\"], [\"noSubtype\", \"\"], [1, \"nav-item\", 3, \"ngClass\", \"click\"], [1, \"ph-h-100-275\", \"scrollbar\", \"pr-12\", 3, \"ngClass\"], [\"class\", \"flex-between mt-8\", 4, \"ngIf\"], [\"class\", \"bg-secondary mt-12 mr-12 px-16 py-12 br-4\", 4, \"ngIf\"], [3, \"data\", \"isLastLead\", \"whatsAppComp\"], [1, \"field-label\"], [1, \"bg-secondary\", \"p-12\", \"br-4\", \"fw-semi-bold\", \"text-large\"], [1, \"align-center\", \"flex-wrap\"], [1, \"mr-40\", \"ip-mr-10\"], [1, \"mb-4\", \"text-black-200\", \"text-sm\", \"fv-sm-caps\"], [1, \"fw-600\", \"text-truncate-1\", \"break-all\"], [\"class\", \"mr-40 ip-mr-10\", 4, \"ngIf\"], [1, \"mr-40\", \"ip-mr-10\", 3, \"ngClass\"], [1, \"align-center\", \"mt-16\"], [1, \"icon\", \"ic-apartment\", \"ic-accent-green\", \"ic-xxs\", \"mr-8\"], [1, \"text-accent-green\", \"fw-700\"], [\"class\", \"mt-10\", 4, \"ngIf\"], [\"class\", \"mt-20\", 4, \"ngIf\"], [1, \"d-flex\", \"flex-wrap\", \"w-100\"], [1, \"w-25\", \"ip-w-33\", \"ph-w-50\", \"pr-10\"], [1, \"fv-sm-caps\", \"mb-4\", \"mt-16\", \"text-black-200\"], [1, \"fw-600\", \"mt-4\", \"mr-30\", \"text-truncate-1\", \"break-all\", \"ip-mr-10\", \"header-5\", \"cursor-pointer\"], [\"class\", \"align-center mt-16\", 4, \"ngIf\"], [\"class\", \"mb-10\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"icon\", \"ic-paper-clip\", \"ic-xxs\", \"ic-accent-green\", \"mr-8\"], [1, \"fw-700\", \"text-accent-green\"], [1, \"w-100\", \"d-flex\", \"flex-wrap\"], [1, \"w-33\", \"ip-w-50\", \"mt-12\"], [1, \"fv-sm-caps\", \"text-black-200\"], [\"noAgencies\", \"\"], [1, \"fw-600\", \"mt-4\", \"text-truncate-1\", \"break-all\", \"mr-30\", \"ip-mr-10\"], [1, \"fw-600\", \"mt-4\", \"text-truncate-1\", \"word-break\", \"line-break\", \"mr-30\", \"ip-mr-10\"], [\"class\", \"text-truncate-1 break-all text-sm\", 4, \"ngIf\"], [1, \"fw-600\", \"mt-4\", \"mr-30\", \"text-truncate-1\", \"break-all\", \"ip-mr-10\"], [1, \"fw-600\", \"mt-4\", \"mr-30\", \"text-truncate-1\", \"ip-mr-10\"], [\"empty\", \"\"], [\"noCampaign\", \"\"], [1, \"w-100\", \"ip-w-50\", \"mt-12\"], [1, \"fw-600\", \"mt-4\", \"mr-30\", \"text-truncate-1\", \"break-all\", \"text-wrap\", \"ip-mr-10\"], [1, \"border-bottom-slate-20\", \"mt-16\"], [1, \"w-100\", \"d-flex\", \"ph-flex-col\", \"mt-6\"], [1, \"fw-700\", \"text-mud\", \"mr-6\"], [1, \"text-sm\"], [1, \"fw-semi-bold\", \"mb-4\"], [1, \"text-black-200\"], [1, \"border-left\", \"mx-20\"], [1, \"align-center\", \"ph-mt-10\"], [1, \"flex-between\", \"mt-8\"], [1, \"field-label\", \"mt-0\"], [\"class\", \"btn btn-sm btn-linear-green text-nowrap br-8 mr-10\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-sm\", \"btn-linear-green\", \"text-nowrap\", \"br-8\", \"mr-10\", 3, \"click\"], [1, \"bg-secondary\", \"mt-12\", \"mr-12\", \"px-16\", \"py-12\", \"br-4\"], [1, \"align-center\", \"w-100\"], [1, \"align-center\", \"w-50\"], [1, \"icon\", \"ic-person-walking\", \"ic-slate-90\", \"ic-xxs\", \"mr-8\"], [1, \"fv-sm-caps\", \"fw-600\"], [\"class\", \"align-center w-50\", 4, \"ngIf\"], [\"class\", \"d-flex mt-16\", 4, \"ngIf\"], [1, \"icon\", \"ic-alarm\", \"ic-slate-90\", \"ic-xxs\", \"mr-8\"], [1, \"text-truncate-1\", \"break-all\", \"text-sm\"], [1, \"d-flex\", \"mt-16\"], [1, \"icon\", \"ic-message-lines\", \"ic-slate-90\", \"ic-xxs\", \"mr-8\"], [1, \"text-black-20\", \"text-sm\", \"word-break\", \"line-break\", \"text-truncate-2\", 3, \"innerHTML\"], [1, \"d-flex\", \"flex-wrap\", \"bg-white\", 3, \"ngClass\"], [1, \"align-center\", \"br-4\", \"cursor-pointer\", \"p-6\", \"mr-10\", \"mt-6\", \"border\", 3, \"ngClass\", \"click\"], [1, \"flex-center\", \"w-16\", \"h-16\", \"br-50\", \"mr-4\", 3, \"ngClass\"], [\"alt\", \"\", 1, \"w-16\", \"h-16\", 3, \"type\", \"appImage\"], [1, \"flex-between\", \"mr-12\", \"py-12\", \"px-10\", \"bg-secondary\", \"br-4\"], [1, \"icon\", \"ic-ivr-printer\", \"ic-black\", \"ic-large\", \"mr-16\"], [1, \"text-dark\", \"fw-semi-bold\"], [1, \"btn\", \"btn-sm\", \"btn-linear-green\", \"w-110\", \"flex-center\", 3, \"click\"], [1, \"fw-600\", \"text-wrap\"], [1, \"mt-10\"], [1, \"text-black-200\", \"fv-sm-caps\"], [1, \"d-flex\", \"flex-wrap\"], [\"class\", \"py-4 px-8 bg-black-100 br-20 text-gray-110 mr-4 mt-6\", 4, \"ngIf\"], [1, \"py-4\", \"px-8\", \"bg-black-100\", \"br-20\", \"text-gray-110\", \"mr-4\", \"mt-6\"], [1, \"mt-20\"], [3, \"title\"], [\"class\", \"w-25 ip-w-33 ph-w-50 pr-10\", 4, \"ngIf\"], [1, \"fw-600\", \"text-truncate-1\", \"break-all\", 3, \"title\"], [1, \"icon\", \"ic-location-circle\", \"ic-xxs\", \"ic-accent-green\", \"mr-8\"], [1, \"mb-10\", 3, \"ngClass\"], [1, \"align-center\", \"mt-12\"], [1, \"dot\", \"dot-xxs\", \"bg-slate-250\", \"mr-8\"], [1, \"fw-600\", \"mt-4\", \"text-truncate-1\", \"word-break\", \"line-break\", \"mr-30\", \"ip-mr-10\", 3, \"title\"], [1, \"w-100\", \"mt-20\"], [3, \"leadInfo\", \"canShowStatusPopupInPreview\", \"canUpdateStatus\", \"isLeadPreview\", \"isLastLead\", \"closeLeadPreviewModal\", \"whatsAppComp\", 4, \"ngIf\"], [3, \"leadInfo\", \"canShowStatusPopupInPreview\", \"canUpdateStatus\", \"isLeadPreview\", \"isLastLead\", \"closeLeadPreviewModal\", \"whatsAppComp\"], [\"statusChangeComponent\", \"\"], [\"customStatusChangeComponent\", \"\"], [3, \"data\", \"whatsAppComp\"], [3, \"data\", \"whatsAppComp\", \"notesAdded\"], [1, \"w-100\", 3, \"ngClass\"], [3, \"leadData\", \"whatsAppComp\"], [1, \"bg-coal\", \"px-24\", \"py-12\", \"text-white\", \"fw-semi-bold\"], [1, \"px-20\", \"h-100-80\", \"scrollbar\"], [1, \"align-center\", \"w-100\", \"mt-20\"], [1, \"text-accent-green\", \"fw-600\"], [1, \"flex-grow-1\", \"border-bottom\", \"ml-8\"], [1, \"bg-slate-150\", \"br-10\"], [1, \"mt-20\", \"bg-coal\", \"px-20\", \"py-10\", \"br-10\", \"text-white\", \"fw-600\"], [1, \"position-relative\"], [\"controls\", \"\", 3, \"play\", \"canplay\", \"loadedmetadata\"], [\"audioPlayer\", \"\"], [\"type\", \"audio/mp3\", 3, \"src\"], [\"width\", \"30px\", \"height\", \"30px\", 1, \"position-absolute\", \"top-10\", \"left-6\", 3, \"options\"]],\n  template: function LeadPreviewComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n      i0.ɵɵtemplate(5, LeadPreviewComponent_span_5_Template, 1, 0, \"span\", 5);\n      i0.ɵɵelementStart(6, \"h4\", 6);\n      i0.ɵɵtext(7);\n      i0.ɵɵpipe(8, \"translate\");\n      i0.ɵɵpipe(9, \"translate\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(10, \"div\", 7);\n      i0.ɵɵtemplate(11, LeadPreviewComponent_ng_container_11_Template, 3, 4, \"ng-container\", 8);\n      i0.ɵɵelementStart(12, \"div\", 9)(13, \"leads-actions\", 10);\n      i0.ɵɵlistener(\"changeSelection\", function LeadPreviewComponent_Template_leads_actions_changeSelection_13_listener($event) {\n        return ctx.onSectionSelect($event);\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(14, LeadPreviewComponent_ng_container_14_Template, 3, 4, \"ng-container\", 8);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(15, \"div\", 11)(16, \"h4\", 12);\n      i0.ɵɵtext(17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"div\", 13);\n      i0.ɵɵtemplate(19, LeadPreviewComponent_ng_container_19_Template, 17, 4, \"ng-container\", 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(20, \"div\", 13);\n      i0.ɵɵelement(21, \"span\", 14);\n      i0.ɵɵelementStart(22, \"drag-scroll\", 15)(23, \"div\", 16);\n      i0.ɵɵtext(24);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(25, \"div\", 17);\n      i0.ɵɵelement(26, \"span\", 18);\n      i0.ɵɵelementStart(27, \"drag-scroll\", 15)(28, \"div\", 19);\n      i0.ɵɵtemplate(29, LeadPreviewComponent_span_29_Template, 4, 2, \"span\", 20);\n      i0.ɵɵtemplate(30, LeadPreviewComponent_ng_container_30_Template, 4, 1, \"ng-container\", 8);\n      i0.ɵɵtemplate(31, LeadPreviewComponent_ng_container_31_Template, 5, 2, \"ng-container\", 8);\n      i0.ɵɵtemplate(32, LeadPreviewComponent_ng_container_32_Template, 4, 1, \"ng-container\", 8);\n      i0.ɵɵtemplate(33, LeadPreviewComponent_ng_container_33_Template, 4, 1, \"ng-container\", 8);\n      i0.ɵɵtemplate(34, LeadPreviewComponent_ng_container_34_Template, 5, 2, \"ng-container\", 8);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(35, \"div\", 21);\n      i0.ɵɵtemplate(36, LeadPreviewComponent_ng_container_36_Template, 4, 6, \"ng-container\", 22);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(37, LeadPreviewComponent_div_37_Template, 220, 129, \"div\", 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(38, \"span\", 23);\n      i0.ɵɵtemplate(39, LeadPreviewComponent_div_39_Template, 4, 2, \"div\", 8);\n      i0.ɵɵtemplate(40, LeadPreviewComponent_div_40_Template, 3, 2, \"div\", 8);\n      i0.ɵɵtemplate(41, LeadPreviewComponent_div_41_Template, 3, 2, \"div\", 8);\n      i0.ɵɵtemplate(42, LeadPreviewComponent_div_42_Template, 3, 5, \"div\", 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(43, LeadPreviewComponent_ng_template_43_Template, 5, 4, \"ng-template\", null, 24, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementStart(45, \"div\", 25)(46, \"div\", 26)(47, \"div\", 27);\n      i0.ɵɵlistener(\"click\", function LeadPreviewComponent_Template_div_click_47_listener() {\n        ctx.prevData();\n        return ctx.getNextLead();\n      });\n      i0.ɵɵelement(48, \"span\", 28);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(49, \"div\", 29)(50, \"leads-actions\", 10);\n      i0.ɵɵlistener(\"changeSelection\", function LeadPreviewComponent_Template_leads_actions_changeSelection_50_listener($event) {\n        return ctx.onSectionSelect($event);\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(51, \"div\", 30);\n      i0.ɵɵlistener(\"click\", function LeadPreviewComponent_Template_div_click_51_listener() {\n        return ctx.nextData();\n      });\n      i0.ɵɵelement(52, \"span\", 31);\n      i0.ɵɵelementEnd()()()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(36, _c10, (ctx.isNextDataLoading || ctx.isPreviousDataLoading) && !ctx.isMobileView));\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", !ctx.whatsAppComp);\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(8, 32, \"GLOBAL.lead\"), \" \", i0.ɵɵpipeBind1(9, 34, \"LEADS.preview\"), \"\");\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", !ctx.whatsAppComp);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"data\", ctx.data)(\"isLeadPreviewOpen\", true)(\"whatsAppComp\", ctx.whatsAppComp)(\"showCommunicationCount\", ctx.showCommunicationCount);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.whatsAppComp);\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate1(\" \", ctx.data == null ? null : ctx.data.name, \" \");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !(ctx.globalSettingsData == null ? null : ctx.globalSettingsData.isMaskedLeadContactNo));\n      i0.ɵɵadvance(5);\n      i0.ɵɵtextInterpolate1(\"\", ctx.addresses, \" \");\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngForOf\", (ctx.data == null ? null : ctx.data.enquiry == null ? null : ctx.data.enquiry.enquiryTypes) || i0.ɵɵpureFunction0(38, _c11));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.data == null ? null : ctx.data.enquiry == null ? null : ctx.data.enquiry.propertyTypes == null ? null : ctx.data.enquiry.propertyTypes.length);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.data == null ? null : ctx.data.enquiry == null ? null : ctx.data.enquiry.propertyTypes == null ? null : ctx.data.enquiry.propertyTypes.length);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.data == null ? null : ctx.data.enquiry == null ? null : ctx.data.enquiry.bhKs == null ? null : ctx.data.enquiry.bhKs.length);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !(ctx.globalSettingsData == null ? null : ctx.globalSettingsData.isCustomLeadFormEnabled));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", (ctx.data == null ? null : ctx.data.enquiry == null ? null : ctx.data.enquiry.lowerBudget) || (ctx.data == null ? null : ctx.data.enquiry == null ? null : ctx.data.enquiry.upperBudget));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.filteredNavigationItems());\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedSection == \"Overview\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(39, _c10, (ctx.isNextDataLoading || ctx.isPreviousDataLoading) && !ctx.isMobileView));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedSection == \"Status\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedSection == \"History\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedSection == \"Notes\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedSection == \"Document\");\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(41, _c2, ctx.foundData == 0 && ctx.isMobileView || !ctx.isMobileView && ctx.isFirstPage && ctx.foundData == 0, ctx.isPreviousDataLoading && !ctx.isMobileView));\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"data\", ctx.data)(\"isLeadPreviewOpen\", true)(\"whatsAppComp\", ctx.whatsAppComp)(\"showCommunicationCount\", ctx.showCommunicationCount);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(44, _c2, ctx.foundData == (ctx.cardData == null ? null : ctx.cardData.length) - 1 && ctx.isMobileView || ctx.totalPages === ctx.currentPage && ctx.foundData == (ctx.cardData == null ? null : ctx.cardData.length) - 1, ctx.isNextDataLoading && ctx.foundData == (ctx.cardData == null ? null : ctx.cardData.length) - 1 || ctx.isNextDataLoading && !ctx.isMobileView));\n    }\n  },\n  encapsulation: 2\n});", "map": {"version": 3, "mappings": ";AAAA,SAGEA,YAHF,EAOEC,aAPF,EAQEC,WARF,QAUO,eAVP;AAWA,SAASC,aAAT,QAAsC,iBAAtC;AAEA,OAAO,KAAKC,MAAZ,MAAwB,QAAxB;AAEA,SAAuBC,MAAvB,EAA+BC,GAA/B,EAAoCC,SAApC,EAA+CC,IAA/C,EAAqDC,SAArD,QAAsE,MAAtE;AACA,SAASC,aAAT,QAA8B,wCAA9B;AAEA,SAASC,UAAT,EAAqBC,wBAArB,QAAqD,uBAArD;AACA,SACEC,OADF,EAEEC,WAFF,EAGEC,aAHF,EAIEC,MAJF,EAKEC,UALF,EAMEC,iBANF,EAOEC,SAPF,EAQEC,UARF,EASEC,WATF,QAUO,kBAVP;AAYA,SACEC,kBADF,EAEEC,YAFF,EAGEC,oBAHF,EAIEC,mBAJF,EAMEC,uBANF,EAOEC,eAPF,QAQO,gCARP;AAWA,SAASC,aAAT,QAA8B,kDAA9B;AACA,SAASC,WAAT,QAA4B,kDAA5B;AACA,SACEC,2BADF,EAEEC,0BAFF,QAGO,0DAHP;AAIA,SACEC,aADF,EAEEC,aAFF,EAGEC,kBAHF,QAIO,oCAJP;AAKA,SAASC,4BAAT,EAAuCC,eAAvC,EAAwDC,QAAxD,QAAwE,oCAAxE;AACA,SACEC,kBADF,EAEEC,gBAFF,QAGO,wCAHP;AAIA,SAASC,qBAAT,QAAsC,wCAAtC;AACA,SAASC,cAAT,QAA+B,mDAA/B;AACA,SACEC,qBADF,EAEEC,mBAFF,EAGEC,2BAHF,QAIO,sCAJP;;;;;;;;;;;;;;;ICvDUC;IACEA;MAAAA;MAAA;MAAA,OAASA,uCAAT;IAAwB,CAAxB;IAA0BA;;;;;;;;;;;;;;;IAK9BA;IACEA;IAA2DA;MAAAA;MAAA;MAASC;MAAU,OAACD,qCAAD;IAAc,CAAjC;IAEzDA;IACFA;IACFA;;;;;IAHIA;IAAAA;;;;;;;;IAQJA;IACEA;IAAuDA;MAAAA;MAAA;MAASE;MAAU,OAACF,qCAAD;IAAc,CAAjC;IAErDA;IACFA;IACFA;;;;;IAHIA;IAAAA;;;;;;IAWJA;IACEA;IACEA;IACAA;IAAMA;IAA8CA;IAKtDA;IACEA;IACAA;IAAMA;IAAgEA;IAIxEA;IACEA;IACAA;IAAMA;IAA4CA;IAEpDA;IACEA;IACAA;IAAwCA;IAAsCA;IAIlFA;;;;;IArBUA;IAAAA;IAOAA;IAAAA;IAMAA;IAAAA;IAIkCA;IAAAA;;;;;;IAoBtCA;;;;;;IAHFA,iCAAwG,CAAxG,EAAwG,MAAxG,EAAwG,EAAxG;IACiDA;IACdA;IACjCA;IACFA;;;;;;;IAHiDA;IAAAA;IAExCA;IAAAA;;;;;;IAETA;IACEA;IACAA;IAAMA;IAAkDA;IAC1DA;;;;;IADQA;IAAAA;;;;;;IAO+BA;IAAoBA;IAAEA;;;;;;IADvDA;IACEA;IAA+BA;IACjCA;;;;;;;IADEA;IAAAA;IAAsCA;IAAAA;;;;;;IAF1CA;IACEA;IAGFA;;;;;IAHSA;IAAAA;;;;;;IAFXA;IACEA;IAKFA;;;;;IALiCA;IAAAA;;;;;;IAO/BA;IAAIA;IAAEA;;;;;;IAVVA;IACEA;IACAA;IAOAA;IAGFA;;;;;;;IAViBA;IAAAA,qLAA4C,UAA5C,EAA4CG,IAA5C;;;;;;IAWjBH;IACEA;IACAA;IAAMA;IAA6CA;IACrDA;;;;;IADQA;IAAAA;;;;;;IAERA;IACEA;IACAA;IAAMA;IAAcA;IACtBA;;;;;IADQA;IAAAA;;;;;;IAKNA;IAAuEA;IACqBA;;;;;IADrBA;IAAAA;;;;;;IAHzEA;IACEA;IACAA;IAAMA;IAA0FA;IAChGA;IAEFA;;;;;IAHQA;IAAAA;IACCA;IAAAA;;;;;;;;;;;;;;IAQfA;IACEA;IACEA;MAAA;MAAA;MAAA;MAAA,OAASA,yDAAT;IAAsC,CAAtC;IACAA;;IACFA;IACFA;;;;;;IAJwBA;IAAAA;IAEpBA;IAAAA;;;;;;;;IAUAA;IAAgEA;MAAAA;MAAA;MAAA,OAASA,mCAAY,QAAZ,EAAT;IAA8B,CAA9B;IAE9DA;;IACFA;;;;IADEA;IAAAA;;;;;;IANJA,gCAAuD,CAAvD,EAAuD,KAAvD,EAAuD,EAAvD;IAEIA;;;IACFA;IACAA;IAIFA;;;;;IANIA;IAAAA;IAGCA;IAAAA;;;;;;IAQ8DA;IACXA;IAAuCA;;;;;IAAvCA;IAAAA;;;;;;IAUhDA;IAEEA;IACFA;;;;;IADEA;IAAAA;;;;;;IAVNA;IACEA;IACAA,4BAAK,CAAL,EAAK,IAAL,EAAK,GAAL;IAEIA;IAGFA;IACAA;IAIFA;;;;;IARIA;IAAAA;IAKCA;IAAAA;;;;;;IAMTA;IACEA,6BAAmE,CAAnE,EAAmE,GAAnE,EAAmE,GAAnE;IAIFA;;;;;IAHKA;IAAAA;;;;;;IAxBPA,gCAA+E,CAA/E,EAA+E,KAA/E,EAA+E,EAA/E,EAA+E,CAA/E,EAA+E,KAA/E,EAA+E,EAA/E;IAGMA;IACAA;IAA8BA;IAA+BA;IACmCA;IAElGA;IAcFA;IACAA;IAMFA;;;;;IAxBoCA;IAAAA;IACzBA;IAAAA;IAEyBA;IAAAA;IAePA;IAAAA;;;;;;;;;;;;;;;;;;;;IAWzBA;IACEA;IAC6CA;MAAA;MAAA;MAAA;MAAA,OAASA,4CAAT;IAAyB,CAAzB;IAC3CA;IACEA;IAEFA;IACAA;IAAgDA;;IAA2BA;IAE/EA;;;;;;IAPIA;IAAAA;IAC8CA;IAAAA;IACvCA;IAAAA,iCAAkB,UAAlB,EAAkBI,6HAAlB;IAGDJ;IAAAA;IAA0CA;IAAAA;;;;;;;;;;;;IAXxDA,4BAC+J,CAD/J,EAC+J,KAD/J,EAC+J,EAD/J;IAE2BA;;IAA8BA;IACvDA;IACEA;IAUFA;;;;;IAZyBA;IAAAA;IACcA;IAAAA;IACNA;IAAAA;;;;;;;;IAcnCA,4BAAqC,CAArC,EAAqC,KAArC,EAAqC,EAArC;IAC2BA;;IAA6CA;IACtEA,iCAA8D,CAA9D,EAA8D,KAA9D,EAA8D,EAA9D;IAEIA;IACAA;IACEA;;IACFA;IAEFA;IAA2DA;MAAAA;MAAA;;MAAA;;MAAA,OACtCA,8DACGK,6BADH,GAEEA,2BAFF,CADsC;IAI1D,CAJ0D;IAKzDL;IACFA;;;;IAduBA;IAAAA;IAKnBA;IAAAA;;;;;;IAoCJA,gCAAsD,CAAtD,EAAsD,KAAtD,EAAsD,EAAtD;IAEIA;;;IACFA;IACAA;IAA6CA;IAEzCA;;;;;IAJFA;IAAAA;IAE2CA;IAAAA;;;;;;IAI/CA,gCAAsD,CAAtD,EAAsD,KAAtD,EAAsD,EAAtD;IAEIA;;IACFA;IACAA;IAA6BA;IAA6DA;;;;;IAFxFA;IAAAA;IAE2BA;IAAAA;;;;;;IAwB3BA;IACEA;IACFA;;;;;IADEA;IAAAA;;;;;;IAFJA;IACEA;IAGFA;;;;;IAHQA;IAAAA;;;;;;IAJZA,iCAAyD,CAAzD,EAAyD,KAAzD,EAAyD,GAAzD;IACyCA;;IAAkCA;IACzEA;IACEA;IAKFA;;;;;IAPuCA;IAAAA;IAEdA;IAAAA;;;;;;IAWrBA;IACEA;IACFA;;;;;IADEA;IAAAA;;;;;;IAFJA;IACEA;IAGFA;;;;;IAHQA;IAAAA;;;;;;IAJZA,iCAAuD,CAAvD,EAAuD,KAAvD,EAAuD,GAAvD;IACyCA;;IAAmCA;IAC1EA;IACEA;IAKFA;;;;;IAPuCA;IAAAA;IAEdA;IAAAA;;;;;;IAqBiBA;IAAoBA;IAAEA;;;;;;IAF1DA;IACEA;IACEA;IAAgCA;IAClCA;IACFA;;;;;;;IAHQA;IAAAA;IACJA;IAAAA;IAAuCA;IAAAA;;;;;;IAH7CA;IACEA;IAKFA;;;;;IALiCA;IAAAA;;;;;;IAQ/BA;;;;;;IAMJA,gCAA6F,CAA7F,EAA6F,KAA7F,EAA6F,EAA7F;IAEIA;IACFA;IACAA;IAAmBA;IAA6CA;;;;;IAF9DA;IAAAA;IAEiBA;IAAAA;;;;;;IAErBA,gCAA6F,CAA7F,EAA6F,KAA7F,EAA6F,EAA7F;IACoDA;;;IACpCA;IACdA;IAAmBA;IAAYA;;;;;IAFmBA;IAAAA;IAE/BA;IAAAA;;;;;;IAErBA;IACEA,gCAAwC,CAAxC,EAAwC,KAAxC,EAAwC,EAAxC;IACoDA;IAAIA;IACtDA;IAA4DA;IAA8BA;IAE5FA,gCAAwC,CAAxC,EAAwC,KAAxC,EAAwC,EAAxC;IACoDA;IAAKA;IACvDA;IAA4EA;IAEpEA;IAEZA;;;;;IARiDA;IAAAA;IAAeA;IAAAA;IAIfA;IAAAA;IAA+BA;IAAAA;;;;;;IApBlFA;IAEEA;IAMAA;IAKAA;IAYFA;;;;;IAvBQA;IAAAA;IAMmCA;IAAAA;IAK1BA;IAAAA;;;;;;IAcfA,gCAC4I,CAD5I,EAC4I,KAD5I,EAC4I,EAD5I;IAEoDA;IAAcA;IAChEA;IAAmBA;IAA6EA;;;;;IAA7EA;IAAAA;;;;;;IAErBA,gCAC4I,CAD5I,EAC4I,KAD5I,EAC4I,EAD5I;IAEoDA;IAAgBA;IAClEA;IAAmBA;IACaA;;;;;IADbA;IAAAA;;;;;;IATvBA;IACEA;IAKAA;IAMAA,gCAAwC,CAAxC,EAAwC,KAAxC,EAAwC,EAAxC;IACoDA;IAAaA;IAC/DA;IAAmBA;IAAyEA;IAEhGA;;;;;IAdKA;IAAAA;IAKAA;IAAAA;IAOkBA;IAAAA;;;;;;IAkBzBA;IACEA;IACAA;IAAqCA;;IAAqCA;;;;IAArCA;IAAAA;;;;;;;;;;;;IAEvCA,iCAC4C,CAD5C,EAC4C,KAD5C,EAC4C,GAD5C;IAGIA;IACAA;IAAMA;IACNA;;;;;;;IAJFA;IAGQA;IAAAA;;;;;;IAciBA;IAAoBA;IAAEA;;;;;;IADzCA;IACEA;IAAiBA;IACnBA;;;;;;IADEA;IAAAA;IAAwBA;IAAAA;;;;;;IAF5BA;IACEA;IAGFA;;;;;IAHmCA;IAAAA;;;;;;IAKjCA;;;;;;IAsCNA;IACEA,gCAAgC,CAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAaA;IACnDA;IAAkEA;IAIlEA;IAEFA,gCAAgC,CAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAQA;IAC9CA;IAAkEA;IAIlEA;IAEFA,iCAAgC,EAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAgBA;IACtDA;IAA8EA;IAE9EA;IAEFA,iCAAgC,EAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAYA;IAClDA;IACEA;IAEFA;IAEFA,iCAAgC,EAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IACtCA;IACAA;IAEEA;IAEFA;IAEJA;;;;;IApCsEA;IAAAA;IAQAA;IAAAA;IAQYA;IAAAA;IAO5EA;IAAAA;IAOEA;IAAAA;IAEFA;IAAAA;;;;;;IAaJA;IAEEA;IACFA;;;;;IADEA;IAAAA;;;;;;IAWFA;IAEEA;IACFA;;;;;IADEA;IAAAA;;;;;;IA2CyBA;IAA4BA;IACjDA;;;;;;IAFFA;IACEA;IAAqBA;IACNA;;;;;;IADfA;IAAAA;IAAoCA;IAAAA;;;;;;IAFxCA;IACEA;IAGFA;;;;;IAHoCA;IAAAA;;;;;;IAGDA;;;;;;IAQZA;IAAoBA;IAAEA;;;;;;IAD3CA;IACEA;IAAmBA;IACrBA;;;;;;IADEA;IAAAA;IAA0BA;IAAAA;;;;;;IAF9BA;IACEA;IAGFA;;;;;IAHqCA;IAAAA;;;;;;IAKnCA;;;;;;IAwBVA;IACEA;IAAyBA;IAAaA;IACtCA,gCAA4D,CAA5D,EAA4D,KAA5D,EAA4D,EAA5D,EAA4D,CAA5D,EAA4D,KAA5D,EAA4D,EAA5D,EAA4D,CAA5D,EAA4D,IAA5D,EAA4D,EAA5D;IAG4CA;IAAKA;IAC3CA;IACEA;IACFA;IAEFA,iCAAgC,EAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAOA;IAC7CA;IACEA;IACFA;IAEFA,iCAAgC,EAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAASA;IAC/CA;IACEA;IACFA;IAEFA,iCAAgC,EAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAWA;IACjDA;IACEA;IACFA;IAEFA,iCAAgC,EAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAOA;IAC7CA;IACEA;IACFA;IAQFA,iCAAgC,EAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAWA;IACjDA;IACEA;IACFA;IAEFA,iCAAgC,EAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAaA;IACnDA;IACEA;IACFA;IAEFA,iCAAgC,EAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAaA;IACnDA;IACEA;IACFA;IAEFA,iCAAgC,EAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAeA;IACrDA;IACEA;IACFA;IAEFA,iCAAgC,EAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAIA;IAC1CA;IACEA;IACFA;IAEFA,iCAAgC,EAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAWA;IACjDA;IACEA;IACFA;IAuBRA;;;;;IA1FUA;IAAAA;IAMAA;IAAAA;IAMAA;IAAAA;IAMAA;IAAAA;IAMAA;IAAAA;IAYAA;IAAAA;IAMAA;IAAAA;IAMAA;IAAAA;IAMAA;IAAAA;IAMAA;IAAAA;IAMAA;IAAAA;;;;;;IAmCJA;IAEEA;IACFA;;;;;IADEA;IAAAA;;;;;;IAYFA;IAEEA;IACFA;;;;;IADEA;IAAAA;;;;;;;;;;;;IAtjBZA,4BAA2C,CAA3C,EAA2C,KAA3C,EAA2C,EAA3C;IAEIA;IASAA;IA6BAA;IAgBAA;IAEAA;IAkBAA;IAAyBA;;;IAAsEA;IAC/FA,iCAA4D,EAA5D,EAA4D,KAA5D,EAA4D,EAA5D,EAA4D,EAA5D,EAA4D,KAA5D,EAA4D,EAA5D,EAA4D,EAA5D,EAA4D,KAA5D,EAA4D,EAA5D;IAIQA;IACFA;IACAA;IAA6CA;IAAiCA;IAEhFA,iCAA4B,EAA5B,EAA4B,KAA5B,EAA4B,EAA5B;IAEIA;IACFA;IACAA;IAA6CA;IAGpCA;IAEXA,iCAA4B,EAA5B,EAA4B,KAA5B,EAA4B,EAA5B;IAEIA;IACFA;IACAA;IAA6CA;IAAmDA;IAElGA;IAQAA;IAMAA,iCAAqE,EAArE,EAAqE,KAArE,EAAqE,EAArE;IACsDA;;IACpDA;IACAA;IAAmBA;IACnBA;IAEFA,iCAA8C,EAA9C,EAA8C,KAA9C,EAA8C,EAA9C;IACsDA;IACpDA;IACAA;IAAmBA;IAEnBA;IAGJA;IACEA;IACAA;IAAqCA;;IAAkCA;IAEzEA;IAUAA;IAUAA,iCAAoC,EAApC,EAAoC,KAApC,EAAoC,EAApC,EAAoC,EAApC,EAAoC,KAApC,EAAoC,EAApC;IAEsDA;;IAAuCA;IACzFA;IACEA;IAEFA;IAEFA,iCAAwC,EAAxC,EAAwC,KAAxC,EAAwC,EAAxC;IACoDA;IAAkBA;IACpEA;IACEA;IAQAA;IAGFA;IAEFA;IA0BAA;IAiBAA,iCAAwC,EAAxC,EAAwC,KAAxC,EAAwC,EAAxC;IACoDA;;IAAqCA;IACvFA;IACEA;IAGFA;IAEFA,iCAAwC,EAAxC,EAAwC,KAAxC,EAAwC,EAAxC;IACoDA;;IAAqCA;IACvFA;IAAmBA;IAEnBA;IAGJA;IAIAA;IAQAA;IACEA;IACAA;IAAqCA;;IAAkCA;IAEzEA,iCAAoC,EAApC,EAAoC,KAApC,EAAoC,EAApC,EAAoC,EAApC,EAAoC,IAApC,EAAoC,EAApC;IAE0CA;;IAAyCA;IAC/EA;IACEA;IAKAA;IAGFA;IAEFA,iCAAgC,EAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;;IAAoCA;IAC1EA;IAAiEA;IACzCA;IAE1BA,iCAAgC,EAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;;IAAmCA;IACzEA;IAA8EA;IAE9EA;IAEFA,kCAAgC,GAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;;IAAmCA;IACzEA;IAAkEA;IAGlEA;IAEFA,kCAAgC,GAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAaA;IACnDA;IAAkEA;IAIlEA;IAEFA,kCAAgC,GAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAaA;IACnDA;IAAkEA;IAIlEA;IAEFA;IAwCAA,kCAAgC,GAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAYA;IAClDA;IAAkEA;IAKlEA;IACAA;IAIFA;IACAA,kCAAgC,GAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAoBA;IAC1DA;IAAkEA;IAKlEA;IACAA;IAIFA;IACAA,kCAAgC,GAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAaA;IACnDA;IAA8EA;IAE9EA;IAEFA,kCAAgC,GAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAeA;IACrDA;IAAkEA;IAElEA;IAEFA,kCAAgC,GAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAcA;IACpDA;IAAkEA;IAElEA;IAEFA,kCAAgC,GAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;IAAaA;IACnDA;IAAkEA;IACvCA;IAE7BA,kCAAgC,GAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;;IAA0CA;IAChFA;IAAiEA;IAEzDA;IAEVA,kCAAgC,GAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;;IAAyCA;IAC/EA;IACEA;IACFA;IAEFA,kCAAgC,GAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;;IAAkDA;IACxFA;IACEA;IAIeA;IACjBA;IAEFA,kCAAgC,GAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;;IAAyCA;IAC/EA;IACEA;IAKAA;IAGFA;IAEFA,kCAAgC,GAAhC,EAAgC,IAAhC,EAAgC,EAAhC;IACwCA;;IAAoCA;IAC1EA;IACEA;IACFA;IAGFA,kCAAiC,GAAjC,EAAiC,KAAjC,EAAiC,EAAjC;IACyCA;;IAA2CA;IAClFA;IACEA;IAMFA;IAINA;IAkGAA;IACAA;IAAyBA;;IAAuCA;IAChEA,kCAA2C,GAA3C,EAA2C,KAA3C,EAA2C,EAA3C,EAA2C,GAA3C,EAA2C,IAA3C,EAA2C,EAA3C;IAEqCA;;IAAoCA;IACrEA,kCAAqB,GAArB,EAAqB,KAArB,EAAqB,EAArB;IACiCA;IACTA;IACtBA;IAA4BA;IACiDA;IAC7EA;IAIFA;IAEFA;IACAA,kCAAmC,GAAnC,EAAmC,IAAnC,EAAmC,EAAnC;IACmCA;;IAAqCA;IACtEA,kCAAqB,GAArB,EAAqB,KAArB,EAAqB,EAArB;IACiCA;IACTA;IACtBA;IAA4BA;IACuDA;IACnFA;IAIFA;;;;;;;;;;;;;;;IAvjBoCA;IAAAA;IACRA;IAAAA;IASwBA;IAAAA;IA8BrDA;IAAAA;IAekBA;IAAAA,oCAAa,YAAb,EAAaM,kBAAb,EAAa,cAAb,EAAaA,oBAAb;IAEfN;IAAAA;IAkBmBA;IAAAA;IAO0BA;IAAAA;IAMAA;IAAAA;IASAA;IAAAA;IAElBA;IAAAA;IAQAA;IAAAA;IAMDA;IAAAA;IAC0BA;IAAAA;IAEjCA;IAAAA;IAGhBA;IAAAA;IAGgBA;IAAAA;IAOgBA;IAAAA;IAEnBA;IAAAA;IAUAA;IAAAA;IAYkCA;IAAAA;IAEhDA;IAAAA;IAOeA;IAAAA,yLAA4C,UAA5C,EAA4CO,IAA5C;IAchBP;IAAAA;IAyBYA;IAAAA;IAkBqCA;IAAAA;IAEhDA;IAAAA;IAMgDA;IAAAA;IAC/BA;IAAAA;IAKjBA;IAAAA;IAIiCA;IAAAA;IAUAA;IAAAA;IAIGA;IAAAA;IAErBA;IAAAA,wLAAoD,UAApD,EAAoDQ,IAApD;IAWqBR;IAAAA;IAC2BA;IAAAA;IAI3BA;IAAAA;IACwCA;IAAAA;IAKxCA;IAAAA;IAC4BA;IAAAA;IAOAA;IAAAA;IAQAA;IAAAA;IAMrDA;IAAAA;IA0CqDA;IAAAA;IAO/DA;IAAAA;IAM+DA;IAAAA;IAO/DA;IAAAA;IAM2EA;IAAAA;IAMZA;IAAAA;IAMAA;IAAAA;IAMAA;IAAAA;IAI5BA;IAAAA;IAC2BA;IAAAA;IAK3BA;IAAAA;IAEpCA;IAAAA;IAIoCA;IAAAA;IAErBA;IAAAA,uIAAoC,UAApC,EAAoCS,IAApC;IAQqBT;IAAAA;IAErBA;IAAAA,2LAAsD,UAAtD,EAAsDU,IAAtD;IAWqBV;IAAAA;IAEpCA;IAAAA;IAKqCA;IAAAA;IAErCA;IAAAA;IAUOA;IAAAA;IAmGUA;IAAAA;IAGYA;IAAAA;IAEAA;IAAAA;IAEHA;IAAAA;IAGzBA;IAAAA;IAO4BA;IAAAA;IAEAA;IAAAA;IAEHA;IAAAA;IAGzBA;IAAAA;;;;;;IAcTA;;;;;IAAqEA,yCAAiB,6BAAjB,EAAiBW,8DAAjB,EAAiB,iBAAjB,EAAiBA,wBAAjB,EAAiB,eAAjB,EAAiB,IAAjB,EAAiB,YAAjB,EAAiBA,mBAAjB,EAAiB,uBAAjB,EAAiBA,8BAAjB,EAAiB,cAAjB,EAAiBA,qBAAjB;;;;;;IAIrEX;;;;;IAAiFA,yCAAiB,6BAAjB,EAAiBY,8DAAjB,EAAiB,iBAAjB,EAAiBA,wBAAjB,EAAiB,eAAjB,EAAiB,IAAjB,EAAiB,YAAjB,EAAiBA,mBAAjB,EAAiB,uBAAjB,EAAiBA,8BAAjB,EAAiB,cAAjB,EAAiBA,qBAAjB;;;;;;IANrFZ,4BAAyC,CAAzC,EAAyC,KAAzC,EAAyC,GAAzC;IAEIA;IAIAA;IAIFA;;;;;IARkBA;IAAAA;IAIOA;IAAAA;;;;;;IAO3BA,4BAA0C,CAA1C,EAA0C,KAA1C,EAA0C,EAA1C;IAEIA;IACFA;;;;;IADgBA;IAAAA,oCAAa,cAAb,EAAaa,oBAAb;;;;;;;;IAIlBb,4BAAwC,CAAxC,EAAwC,KAAxC,EAAwC,EAAxC,EAAwC,CAAxC,EAAwC,YAAxC,EAAwC,GAAxC;IAE8BA;MAAAA;MAAA;MAAA,OAAcA,4CAAd;IAAiC,CAAjC;IAAiEA;;;;;IAA/EA;IAAAA,oCAAa,cAAb,EAAac,oBAAb;;;;;;;;;;;;IAIhBd,4BAA2C,CAA3C,EAA2C,KAA3C,EAA2C,GAA3C;IAEIA;IACFA;;;;;IAFmBA;IAAAA;IACMA;IAAAA,wCAAiB,cAAjB,EAAiBe,oBAAjB;;;;;;IA4Bff;IACEA;IAEFA;;;;;IAFaA;IAAAA;;;;;;;;IAXnBA;IACEA,iCAAgC,CAAhC,EAAgC,IAAhC,EAAgC,GAAhC;IAEIA;IACFA;IACAA,iCAA+B,CAA/B,EAA+B,OAA/B,EAA+B,GAA/B,EAA+B,GAA/B;IAC+BA;MAAAA;;MAAA;;MAAA;MAAA,OAAQA,+CAAR;IAAoC,CAApC,EAAqC,SAArC,EAAqC;MAAAA;MAAA;MAAA,2CAAwB,KAAxB;IAA6B,CAAlE,EAAqC,gBAArC,EAAqC;MAAAA;MAAA;MAAA,2CAClC,KADkC;IAC7B,CADR;IAE3BA;IACFA;IACAA;IAIFA;IAEJA;;;;;;IAbMA;IAAAA;IAKUA;IAAAA;IAEJA;IAAAA;;;;;;IAjBdA;IACEA,iCAAsC,CAAtC,EAAsC,IAAtC,EAAsC,GAAtC;IAEIA;IACFA;IACAA;IACFA;IACAA;IAiBFA;;;;;;;IArBMA;IAAAA;IAI4BA;IAAAA;;;;;;IARpCA;IACEA;IAyBFA;;;;;IAzBkCA;IAAAA;;;;;;IALpCA;IACEA;;IACFA;IACAA;IACEA;IA2BFA;;;;;IA9BEA;IAAAA;IAG+BA;IAAAA;;;;;;;;;;;;;;ADppBrC,OAAM,MAAOgB,oBAAP,CAA2B;EAwJ/BC,YACSC,YADT,EAESC,QAFT,EAGUC,KAHV,EAISC,iBAJT,EAKSC,MALT,EAMUC,gBANV,EAOUC,eAPV,EAO0C;IANjC;IACA;IACC;IACD;IACA;IACC;IACA;IA9JD,mBAAmB,IAAnB;IACA,oBAAwB,KAAxB;IACA,8BAAkC,KAAlC;IACD,eAA8B,IAAIrE,YAAJ,EAA9B;IAQR,uBAAuB,UAAvB;IACA,qBAAyB,IAAzB;IAKA,cAASI,MAAT;IACA,oBAAemB,YAAf;IACA,4BAAuBC,oBAAvB;IACA,2BAAsBC,mBAAtB;IACA,+BAA0BC,uBAA1B;IACA,uBAAkBC,eAAlB;IACA,iBAAYR,SAAZ;IACA,qBAAgBJ,aAAhB;IACA,0BAAqBO,kBAArB;IACA,eAAUT,OAAV;IACA,kBAAaI,UAAb;IACA,mBAAcI,WAAd;IACA,mBAAcP,WAAd;IACA,kBAAaH,UAAb;IACA,uBAAkB,CAChB;MAAE2D,KAAK,EAAE,iBAAT;MAA4BC,OAAO,EAAE;IAArC,CADgB,EAEhB;MAAED,KAAK,EAAE,eAAT;MAA0BC,OAAO,EAAE;IAAnC,CAFgB,EAGhB;MAAED,KAAK,EAAE,gBAAT;MAA2BC,OAAO,EAAE;IAApC,CAHgB,EAIhB;MAAED,KAAK,EAAE,YAAT;MAAuBC,OAAO,EAAE;IAAhC,CAJgB,EAKhB;MAAED,KAAK,EAAE,iBAAT;MAA4BC,OAAO,EAAE;IAArC,CALgB,CAAlB;IAOA,mBAAqB,EAArB;IAEA,wBAA4B,KAA5B;IAGA,uBAA0B,KAA1B;IACA,4BAAgC,KAAhC;IACA,4BAAgC,KAAhC;IAEA,yBAA6B,KAA7B;IACA,6BAAiC,KAAjC;IACA,mBAAuB,KAAvB;IACA,mBAAuB,KAAvB;IACA,wBAA4B,KAA5B;IAGA,yBAA6B,KAA7B;IACA,uBAA2B,KAA3B;IACA,2BAA+B,KAA/B;IACA,0BAA0B,EAA1B;IAEA,6BAAiC,KAAjC;IACA,+BAAmC,IAAnC;IAEA,cAASvD,MAAT;IACA,yBAAoBE,iBAApB;EAiGK;;EA/F0B,IAA3BsD,2BAA2B;;;IAC7B,OACG,CAAC,YAAKC,IAAL,CAAUC,MAAV,MAAgB,IAAhB,IAAgBC,aAAhB,GAAgB,MAAhB,GAAgBA,GAAEC,WAAlB,MACAhE,wBAAwB,CAAC,mBAAD,CADxB,IAEA,YAAK6D,IAAL,CAAUC,MAAV,MAAgB,IAAhB,IAAgBG,aAAhB,GAAgB,MAAhB,GAAgBA,GAAED,WAAlB,MACAhE,wBAAwB,CAAC,iBAAD,CAHxB,IAIA,YAAK6D,IAAL,CAAUC,MAAV,MAAgB,IAAhB,IAAgBI,aAAhB,GAAgB,MAAhB,GAAgBA,GAAEF,WAAlB,MACAhE,wBAAwB,CAAC,oBAAD,CALzB,KAMC,CAAC,KAAKmE,qBANR,IAOC,KAAKA,qBAAL,IACC,KAAKC,kCAAL,CAAwC,WAAKP,IAAL,MAAS,IAAT,IAASQ,aAAT,GAAS,MAAT,GAASA,GAAEP,MAAnD,CATJ;EAWD;;EAEDM,kCAAkC,CAACN,MAAD,EAAY;;;IAC5C,OACE,OAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEQ,yBAAR,MAAqC,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEC,SAAR,MAAiB,IAAjB,IAAiBR,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEO,yBAAxD,KACC,aAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAEC,SAAR,MAAiB,IAAjB,IAAiBN,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEO,MAAnB,MAA6B,YAAM,SAAN,UAAM,WAAN,GAAM,MAAN,SAAM,CAAED,SAAR,MAAiB,IAAjB,IAAiBL,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEO,IAAF,CAAQF,SAAD,IAAoBA,SAAS,SAAT,aAAS,WAAT,GAAS,MAAT,YAAS,CAAED,yBAAtC,CAA9C,CADD,IAEA,KAHF;EAKD;;EAEe,IAAZI,YAAY;IACd,OAAOC,MAAM,CAACC,UAAP,IAAqB,GAA5B;EACD;;EACc,IAAXC,WAAW;IACb,OAAO,KAAKrB,gBAAL,CAAsBsB,iBAAtB,KAA4C,CAAnD;EACD;;EACc,IAAXC,WAAW;IACb,OAAO,KAAKvB,gBAAL,CAAsBsB,iBAA7B;EACD;;EACa,IAAVE,UAAU;IACZ,OAAO,KAAKxB,gBAAL,CAAsBwB,UAA7B;EACD;;EACa,IAAVC,UAAU;;;IACZ,OACG,KAAKC,SAAL,IAAkB,YAAKC,QAAL,MAAa,IAAb,IAAapB,aAAb,GAAa,MAAb,GAAaA,GAAES,MAAf,IAAwB,CAA1C,IAA+C,KAAKE,YAArD,IACC,KAAKM,UAAL,KAAoB,KAAKD,WAAzB,IACC,KAAKG,SAAL,IAAkB,YAAKC,QAAL,MAAa,IAAb,IAAalB,aAAb,GAAa,MAAb,GAAaA,GAAEO,MAAf,IAAwB,CAH9C;EAKD;;EAEe,IAAZY,YAAY;;;IACd,OACE,8BAAKvB,IAAL,MAAS,IAAT,IAASE,aAAT,GAAS,MAAT,GAASA,GAAEsB,OAAX,MAAkB,IAAlB,IAAkBpB,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEmB,YAApB,MAAgC,IAAhC,IAAgClB,aAAhC,GAAgC,MAAhC,GAAgCA,GAC5BxE,GAD4B,CACvB2F,OAAD,IAAkBnF,WAAW,CAACmF,OAAD,CADL,CAAhC,MAC+C,IAD/C,IAC+ChB,aAD/C,GAC+C,MAD/C,GAC+CA,GAC3CiB,IAD2C,CACtC,IADsC,CAD/C,KAEkB,IAHpB;EAKD;;EAEW,IAARC,QAAQ;;;IACV,OACE,8BAAK1B,IAAL,MAAS,IAAT,IAASE,aAAT,GAAS,MAAT,GAASA,GAAEsB,OAAX,MAAkB,IAAlB,IAAkBpB,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEsB,QAApB,MAA4B,IAA5B,IAA4BrB,aAA5B,GAA4B,MAA5B,GAA4BA,GACxBxE,GADwB,CACnB8F,IAAD,IAAevF,OAAO,CAACuF,IAAD,CADF,CAA5B,MACqC,IADrC,IACqCnB,aADrC,GACqC,MADrC,GACqCA,GACjCiB,IADiC,CAC5B,IAD4B,CADrC,KAEkB,IAHpB;EAKD;;EAEQ,IAALG,KAAK;uBAAA,CACP;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,OAAO,6BAAK5B,IAAL,MAAS,IAAT,IAASE,aAAT,GAAS,MAAT,GAASA,GAAEsB,OAAX,MAAkB,IAAlB,IAAkBpB,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEyB,IAApB,MAAwB,IAAxB,IAAwBxB,aAAxB,GAAwB,MAAxB,GAAwBA,GAC3BxE,GAD2B,CACtBiG,GAAD,IAAc9E,mBAAmB,CAAC8E,GAAD,CADV,CAAxB,MACwC,IADxC,IACwCtB,aADxC,GACwC,MADxC,GACwCA,GAC3CiB,IAD2C,CACtC,IADsC,CAD/C;EAGD;;EAGO,IAAJM,IAAI;;;IACN,OAAOC,KAAK,CAACC,OAAN,CAAc,WAAKjC,IAAL,CAAUwB,OAAV,MAAiB,IAAjB,IAAiBtB,aAAjB,GAAiB,MAAjB,GAAiBA,GAAE6B,IAAjC,IACH,WAAK/B,IAAL,CAAUwB,OAAV,MAAiB,IAAjB,IAAiBpB,aAAjB,GAAiB,MAAjB,GAAiBA,GAAE2B,IAAF,CAAOlG,GAAP,CAAYqG,GAAD,IAAeA,GAAG,KAAK,CAAR,IAAaA,GAAG,KAAK,GAArB,GAA2B,QAA3B,GAAsCA,GAAhE,CADd,GAEH,EAFJ;EAGD;;EAEY,IAATC,SAAS;;;IACX,OACE,8BAAKnC,IAAL,MAAS,IAAT,IAASE,aAAT,GAAS,MAAT,GAASA,GAAEsB,OAAX,MAAkB,IAAlB,IAAkBpB,aAAlB,GAAkB,MAAlB,GAAkBA,GAAE+B,SAApB,MAA6B,IAA7B,IAA6B9B,aAA7B,GAA6B,MAA7B,GAA6BA,GACzBxE,GADyB,CACpBuG,OAAD,IAAkBnF,uBAAuB,CAACmF,OAAD,CADpB,CAA7B,MAC2D,IAD3D,IAC2D5B,aAD3D,GAC2D,MAD3D,GAC2DA,GACvDiB,IADuD,CAClD,IADkD,CAD3D,KAEkB,IAHpB;EAKD;;EAaKY,QAAQ;IAAA;;IAAA;yBAAA,CACZ;;;MACA,KAAI,CAACC,iBAAL,GAAyB,KAAzB;MACA,KAAI,CAACC,qBAAL,GAA6B,KAA7B;MAEAC,OAAO,CAACC,GAAR,CAAY,8CAAZ,EAA4D;QAC1DH,iBAAiB,EAAE,KAAI,CAACA,iBADkC;QAE1DC,qBAAqB,EAAE,KAAI,CAACA,qBAF8B;QAG1D1B,YAAY,EAAE,KAAI,CAACA;MAHuC,CAA5D;;MAMA,KAAI,CAAClB,gBAAL,CAAsB+C,cAAtB,CAAqCC,SAArC,CAAgDC,KAAD,IAAU;QACvD,IAAIA,KAAJ,EAAW;UACT,KAAI,CAACC,eAAL,GAAuBD,KAAvB;QACD;MACF,CAJD,EAXY,CAiBZ;;;MACAE,UAAU,CAAC,MAAK;QACd,KAAI,CAACR,iBAAL,GAAyB,KAAzB;QACA,KAAI,CAACC,qBAAL,GAA6B,KAA7B;QACAC,OAAO,CAACC,GAAR,CAAY,iCAAZ,EAA+C;UAC7CH,iBAAiB,EAAE,KAAI,CAACA,iBADqB;UAE7CC,qBAAqB,EAAE,KAAI,CAACA;QAFiB,CAA/C;MAID,CAPS,EAOP,GAPO,CAAV;;MASA,KAAI,CAAC/C,KAAL,CAAWuD,MAAX,CAAkBnF,QAAlB,EACGoF,IADH,CACQhH,SAAS,CAAC,KAAI,CAACiH,OAAN,CADjB,EACiCnH,SAAS,CAAC,MAAM,KAAI,CAAC+E,YAAZ,CAD1C,EAEG8B,SAFH,CAEa3C,IAAI,IAAG;QAChB,KAAI,CAACsB,QAAL,GAAgBtB,IAAhB,CADgB,CAEhB;MACD,CALH;;MAOA,KAAI,CAACR,KAAL,CAAWuD,MAAX,CAAkBpF,eAAlB,EACGqF,IADH,CACQhH,SAAS,CAAC,KAAI,CAACiH,OAAN,CADjB,EACiCnH,SAAS,CAAC,MAAM,CAAC,KAAI,CAAC+E,YAAb,CAD1C,EAEG8B,SAFH;QAAA,6BAEa,WAAO3C,IAAP,EAAoB;UAC7B,KAAI,CAACsB,QAAL,GAAgBtB,IAAhB,CAD6B,CAE7B;QACD,CALH;;QAAA;UAAA;QAAA;MAAA;;MAOA,KAAI,CAACN,MAAL,CAAYwD,MAAZ,CACGF,IADH,CACQpH,MAAM,CAAEuH,KAAD,IAAWA,KAAK,YAAYzH,aAA7B,CADd,EAEGiH,SAFH,CAEa,MAAK;QACd,KAAI,CAACS,WAAL,GAAmB,KAAI,CAAC1D,MAAL,CAAY2D,GAA/B;MACD,CAJH;;MAKA,KAAI,CAACD,WAAL,GAAmB,KAAI,CAAC1D,MAAL,CAAY2D,GAA/B;MAEA,MAAM,KAAI,CAAC7D,KAAL,CACHuD,MADG,CACI1F,2BADJ,EAEH2F,IAFG,CAGFlH,SAAS,CAAEwH,SAAD,IAAuB;QAC/B,OAAOA,SAAP;MACD,CAFQ,CAHP,EAMFvH,IAAI,CAAC,CAAD,CANF,EAQHwH,SARG,EAAN;MASA,KAAI,CAACjD,qBAAL,SAAmC,KAAI,CAACd,KAAL,CAChCuD,MADgC,CACzBrF,4BADyB,EAEhCsF,IAFgC,CAG/BnH,GAAG,CAAEmE,IAAD,IAAeA,IAAhB,CAH4B,EAI/BjE,IAAI,CAAC,CAAD,CAJ2B,EAMhCwH,SANgC,EAAnC;MAQA,KAAI,CAACC,aAAL,GAAqB,WAAI,CAACxD,IAAL,MAAS,IAAT,IAASE,aAAT,GAAS,MAAT,GAASA,GAAEuD,WAAhC;;MACA,KAAI,CAACjE,KAAL,CAAWkE,QAAX,CAAoB,IAAIvG,aAAJ,EAApB;;MACA,KAAI,CAACqC,KAAL,CACGuD,MADH,CACU3F,WADV,EAEG4F,IAFH,CAEQhH,SAAS,CAAC,KAAI,CAACiH,OAAN,CAFjB,EAGGN,SAHH,CAGc3C,IAAD,IAAc;QACvB,IAAI2D,QAAQ,GAAG3D,IAAI,IAAI,EAAvB;;QACA,IAAI2D,QAAQ,CAAChD,MAAT,GAAkB,CAAtB,EAAyB;UACvB,KAAI,CAACiD,WAAL,GAAmBD,QAAQ,CACxB/H,MADgB,CACRiI,IAAD,IAAeA,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEC,QADZ,EAEhBjI,GAFgB,CAEXgI,IAAD,IAAc;YACjB,MAAMC,QAAQ,GAAG,KAAjB;YACA,uCAAYD,IAAZ,GAAgB;cAAEC;YAAF,CAAhB;UACD,CALgB,CAAnB;UAMA,KAAI,CAACC,kBAAL,GAA0B,KAAI,CAACH,WAA/B;QACD;;QACD,KAAI,CAACI,SAAL;;QACA,KAAI,CAACC,WAAL;MACD,CAhBH;;MAkBA,KAAI,CAAC5C,SAAL,GAAiB,WAAI,CAACC,QAAL,MAAa,IAAb,IAAalB,aAAb,GAAa,MAAb,GAAaA,GAAE8D,SAAF,CAAaC,IAAD,IACxC,KAAI,CAACC,WAAL,CAAiBD,IAAjB,EAAuB,KAAI,CAACnE,IAA5B,CAD4B,CAA9B;;MAGA,IACE,CAAC,KAAI,CAACqB,SAAL,KAAmB,YAAI,CAACC,QAAL,MAAa,IAAb,IAAajB,aAAb,GAAa,MAAb,GAAaA,GAAEM,MAAf,IAAwB,CAA3C,IACC,KAAI,CAACU,SAAL,KAAmB,YAAI,CAACC,QAAL,MAAa,IAAb,IAAad,aAAb,GAAa,MAAb,GAAaA,GAAEG,MAAf,IAAwB,CAD7C,KAEA,KAAI,CAACE,YAHP,EAIE;QACA,KAAI,CAAClB,gBAAL,CAAsB0E,kBAAtB;MACD;;MAGD,KAAI,CAACC,WAAL;;MAEA,MAAMC,mBAAmB,GAAG,KAAI,CAAC/E,KAAL,CACzBuD,MADyB,CAClB9E,qBADkB,EAEzB+E,IAFyB,CAEpBhH,SAAS,CAAC,KAAI,CAACiH,OAAN,CAFW,CAA5B;;MAGA,MAAMuB,SAAS,GAAG,KAAI,CAAChF,KAAL,CACfuD,MADe,CACR5E,2BADQ,EAEf6E,IAFe,CAEVhH,SAAS,CAAC,KAAI,CAACiH,OAAN,CAFC,CAAlB;;MAGA,MAAMwB,YAAY,GAAG,KAAI,CAACjF,KAAL,CAClBuD,MADkB,CACX/E,cADW,EAElBgF,IAFkB,CAEbhH,SAAS,CAAC,KAAI,CAACiH,OAAN,CAFI,CAArB;;MAGA,KAAI,CAACzD,KAAL,CACGuD,MADH,CACUhF,qBADV,EAEGiF,IAFH,CAEQhH,SAAS,CAAC,KAAI,CAACiH,OAAN,CAFjB,EAGGN,SAHH,CAGc3C,IAAD,IAAkB;QAC3B,KAAI,CAAC0E,oBAAL,GAA4B1E,IAA5B;MACD,CALH;;MAMA/D,aAAa,CAAC;QACZ0I,kBAAkB,EAAEJ,mBADR;QAEZK,QAAQ,EAAEJ,SAFE;QAGZK,WAAW,EAAEJ;MAHD,CAAD,CAAb,CAIG9B,SAJH,CAIa,CAAC;QAAEgC,kBAAF;QAAsBC,QAAtB;QAAgCC;MAAhC,CAAD,KAAkD;;;QAC7D,MAAMC,cAAc,GAAG,IAAIC,GAAJ,CAAQF,WAAR,CAAvB;QACA,IAAIA,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEG,QAAb,CAAsB,kCAAtB,CAAJ,EACE,KAAI,CAACC,iBAAL,GAAyB,IAAzB;QACF,IAAIJ,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEG,QAAb,CAAsB,oCAAtB,CAAJ,EACE,KAAI,CAACE,eAAL,GAAuB,IAAvB;;QACF,IAAIL,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEG,QAAb,CAAsB,+BAAtB,CAAJ,EAA4D;UAC1D,KAAI,CAACG,KAAL,GAAaP,QAAb;QACD,CAFD,MAEO;UACL,KAAI,CAACO,KAAL,GAAaR,kBAAb;QACD;;QACD,IAAIE,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEG,QAAb,CAAsB,0BAAtB,CAAJ,EAAuD;UACrD,KAAI,CAACI,WAAL,GAAmB,IAAnB;QACD;;QACD,KAAI,CAACC,WAAL,GAAmBP,cAAc,CAACQ,GAAf,CAAmB,8BAAnB,CAAnB;;QACA,IAAIT,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEG,QAAb,CAAsB,4BAAtB,CAAJ,EAAyD;UACvD,KAAI,CAACO,gBAAL,GAAwB,IAAxB;QACD;;QACD,IAAIV,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEG,QAAb,CAAsB,0BAAtB,CAAJ,EAAuD;UACrD,IACE,CAAC,KAAI,CAACO,gBAAN,IACA,KAAI,CAACnC,WAAL,KAAqB,UADrB,IAEA,kBAAI,CAACpD,IAAL,MAAS,IAAT,IAASE,aAAT,GAAS,MAAT,GAASA,GAAED,MAAX,MAAiB,IAAjB,IAAiBG,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEH,MAAnB,MAA8B,UAHhC,EAIE;YACA,KAAI,CAAC4C,eAAL,GAAuB,UAAvB;UACD;QACF;MACF,CA/BD;;MAgCA,IAAI,KAAI,CAAC2C,YAAT,EAAuB;QACrB,KAAI,CAAC3C,eAAL,GAAuB,KAAI,CAAC2C,YAAL,CAAkB3C,eAAzC;MACD;;MAED,KAAI,CAACrD,KAAL,CACGuD,MADH,CACUzF,0BADV,EAEG0F,IAFH,CAEQhH,SAAS,CAAC,KAAI,CAACiH,OAAN,CAFjB,EAGGN,SAHH,CAGc3C,IAAD,IAAc;QACvB,KAAI,CAACyF,eAAL,GACEzF,IAAI,CAAC0F,SAAL,IAAkB1F,IAAI,CAAC0F,SAAL,CAAe/E,MAAf,GAAwB,CAA1C,GACIX,IAAI,CAAC0F,SAAL,CAAe,CAAf,EAAkBD,eADtB,GAEI,IAHN;QAIA,KAAI,CAACE,kBAAL,GAA0B3F,IAA1B;MACD,CATH;;MAUA,KAAI,CAACR,KAAL,CACGuD,MADH,CACU/E,cADV,EAEGgF,IAFH,CAEQhH,SAAS,CAAC,KAAI,CAACiH,OAAN,CAFjB,EAGGN,SAHH,CAGckC,WAAD,IAAqB;QAC9B,IAAI,EAACA,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAElE,MAAd,CAAJ,EAA0B;;QAC1B,IAAIkE,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEG,QAAb,CAAsB,oCAAtB,CAAJ,EAAiE;UAC/D,KAAI,CAACY,mBAAL,GAA2B,IAA3B;QACD;MACF,CARH;;MASA,KAAI,CAACpG,KAAL,CACGuD,MADH,CACU7E,mBADV,EAEG8E,IAFH,CAEQhH,SAAS,CAAC,KAAI,CAACiH,OAAN,CAFjB,EAGGN,SAHH,CAGc3C,IAAD,IAAc;QACvB,KAAI,CAAC6F,QAAL,GAAgB7F,IAAhB;MACD,CALH;IAzKY;EA+Kb;;EAED8F,WAAW,CAACC,OAAD,EAAuB;IAChC,IAAIA,OAAO,CAACC,WAAR,IAAuBD,OAAO,CAACC,WAAR,CAAoBC,YAA/C,EAA6D;MAC3D,KAAKjG,IAAL,GAAY,KAAKgG,WAAjB,CAD2D,CAE3D;;MACA,KAAK1D,iBAAL,GAAyB,KAAzB;MACA,KAAKC,qBAAL,GAA6B,KAA7B;IACD;EACF;;EAED2D,eAAe;IACb,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAT,CAAwB,WAAxB,CAAlB;IACA,IAAIC,MAAJ,EAAiBC,MAAjB;IAEAJ,SAAS,CAACK,gBAAV,CAA2B,YAA3B,EAA0CrD,KAAD,IAAU;MACjDmD,MAAM,GAAGnD,KAAK,CAACsD,OAAN,CAAc,CAAd,EAAiBC,OAA1B;MACAH,MAAM,GAAGpD,KAAK,CAACsD,OAAN,CAAc,CAAd,EAAiBE,OAA1B;IACD,CAHD;IAKAR,SAAS,CAACK,gBAAV,CAA2B,UAA3B,EAAwCrD,KAAD,IAAU;MAC/C,MAAMyD,IAAI,GAAGzD,KAAK,CAAC0D,cAAN,CAAqB,CAArB,EAAwBH,OAArC;MACA,MAAMI,IAAI,GAAG3D,KAAK,CAAC0D,cAAN,CAAqB,CAArB,EAAwBF,OAArC;MAEA,MAAMI,MAAM,GAAGH,IAAI,GAAGN,MAAtB;MACA,MAAMU,MAAM,GAAGF,IAAI,GAAGP,MAAtB,CAL+C,CAM/C;;MACA,IAAIQ,MAAM,GAAG,GAAT,IAAgBE,IAAI,CAACC,GAAL,CAASF,MAAT,IAAmB,EAAvC,EAA2C;QACzC,KAAKG,QAAL;MACD,CAFD,CAGA;MAHA,KAIK,IAAIJ,MAAM,GAAG,CAAC,GAAV,IAAiBE,IAAI,CAACC,GAAL,CAASF,MAAT,IAAmB,EAAxC,EAA4C;QAC/C,KAAKI,QAAL;MACD;IACF,CAdD;EAeD;;EAEDC,cAAc,CAACC,CAAD,EAAO;;;IACnB,OAAO,WAAK9D,aAAL,MAAkB,IAAlB,IAAkBtD,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEU,IAAF,CACtB2G,YAAD,IAAuB,aAAY,SAAZ,gBAAY,WAAZ,GAAY,MAAZ,eAAY,CAAE1D,IAAd,CAAmB2D,EAAnB,MAA0BF,CAAC,CAACE,EAD5B,CAAzB;EAGD;;EAEDC,uBAAuB;;;IACrB,IAAI,WAAKzH,IAAL,MAAS,IAAT,IAASE,aAAT,GAAS,MAAT,GAASA,GAAEwH,UAAf,EAA2B;MACzB,OAAO,KAAKC,eAAL,CAAqB/L,MAArB,CAA6BuI,IAAD,IACjC,CAAC,UAAD,EAAa,SAAb,EAAwBa,QAAxB,CAAiCb,IAAI,CAACrE,OAAtC,CADK,CAAP;IAGD;;IACD,IAAI,KAAKsD,WAAL,KAAqB,UAAzB,EAAqC;MACnC,OAAO,KAAKmC,gBAAL,GACH,KAAKoC,eADF,GAEH,KAAKA,eAAL,CAAqB/L,MAArB,CAA6BuI,IAAD,IAAUA,IAAI,CAACrE,OAAL,KAAiB,QAAvD,CAFJ;IAGD,CAJD,MAIO;MACL,IAAI,kBAAKE,IAAL,MAAS,IAAT,IAASI,aAAT,GAAS,MAAT,GAASA,GAAEH,MAAX,MAAiB,IAAjB,IAAiBI,aAAjB,GAAiB,MAAjB,GAAiBA,GAAEJ,MAAnB,MAA8B,UAAlC,EAA8C;QAC5C,OAAO,KAAK0H,eAAL,CAAqB/L,MAArB,CAA6BuI,IAAD,IAAUA,IAAI,CAACrE,OAAL,KAAiB,QAAvD,CAAP;MACD,CAFD,MAEO;QACL,OAAO,KAAKoF,eAAL,GACH,KAAKyC,eADF,GAEH,KAAKA,eAAL,CAAqB/L,MAArB,CAA6BuI,IAAD,IAAUA,IAAI,CAACrE,OAAL,KAAiB,QAAvD,CAFJ;MAGD;IACF;EACF;;EAED8H,wBAAwB,CAAC/D,IAAD,EAAU;IAChC,MAAMgE,OAAO,GAAQ,EAArB;;IACA,IAAIhE,IAAI,CAACC,QAAT,EAAmB;MACjB+D,OAAO,CAAC,qBAAD,CAAP,GAAiC,IAAjC;MACAA,OAAO,CAAC,UAAD,CAAP,GAAsB,IAAtB;IACD;;IACD,OAAOA,OAAP;EACD;;EAEDzD,WAAW,CAAC0D,IAAD,EAAYC,IAAZ,EAAqB;IAC9B,OAAOD,IAAI,CAACN,EAAL,KAAYO,IAAI,CAACP,EAAxB;EACD,CA7Z8B,CA+Z/B;;;EACAQ,uBAAuB;IACrB,KAAK1F,iBAAL,GAAyB,KAAzB;IACA,KAAKC,qBAAL,GAA6B,KAA7B;IACAC,OAAO,CAACC,GAAR,CAAY,sCAAZ;EACD;;EAED0E,QAAQ;;;IACN,IAAI,KAAK9F,SAAL,GAAiB,CAArB,EAAwB;MACtB,KAAKA,SAAL;MACA,KAAKrB,IAAL,GAAY,WAAKsB,QAAL,MAAa,IAAb,IAAapB,aAAb,GAAa,MAAb,GAAaA,GAAG,KAAKmB,SAAR,CAAzB;MACA,KAAK4C,WAAL;IACD,CAJD,MAIO,IAAI,KAAK5C,SAAL,KAAmB,CAAnB,IAAwB,CAAC,KAAKR,YAAlC,EAAgD;MACrD,KAAKQ,SAAL,GAAiB,CAAjB;MACA,KAAK1B,gBAAL,CAAsBsI,sBAAtB,CAA6C,KAA7C;MACA,KAAKC,sBAAL;MACA,KAAK3F,qBAAL,GACE,KAAK5C,gBAAL,CAAsBsB,iBAAtB,KAA4C,CAA5C,GAAgD,KAAhD,GAAwD,IAD1D;IAED;;IACD,IAAI,KAAK4B,eAAL,KAAyB,QAA7B,EACE,KAAKvC,qBAAL,GACI,WAAK6H,2BAAL,MAAgC,IAAhC,IAAgC/H,aAAhC,GAAgC,MAAhC,GAAgCA,GAAEgI,eAAF,EADpC,GAEI,KAAKC,qBAAL,CAA2BD,eAA3B,EAFJ;IAGF,KAAKxI,eAAL,CAAqB0I,YAArB,CAAkC,mCAAlC,EAAuE,KAAKtI,IAAL,CAAUwH,EAAjF;EACD;;EAEDJ,QAAQ;;;IACN,IAAI,KAAK/F,SAAL,GAAiB,YAAKC,QAAL,MAAa,IAAb,IAAapB,aAAb,GAAa,MAAb,GAAaA,GAAES,MAAf,IAAwB,CAA7C,EAAgD;MAC9C,KAAKU,SAAL;MACA,KAAKrB,IAAL,GAAY,WAAKsB,QAAL,MAAa,IAAb,IAAalB,aAAb,GAAa,MAAb,GAAaA,GAAG,KAAKiB,SAAR,CAAzB;MACA,KAAK4C,WAAL;;MACA,IACE,CAAC,KAAK5C,SAAL,KAAmB,YAAKC,QAAL,MAAa,IAAb,IAAajB,aAAb,GAAa,MAAb,GAAaA,GAAEM,MAAf,IAAwB,CAA3C,IACC,KAAKU,SAAL,KAAmB,YAAKC,QAAL,MAAa,IAAb,IAAad,aAAb,GAAa,MAAb,GAAaA,GAAEG,MAAf,IAAwB,CAD7C,KAEA,CAAC,KAAK2B,iBAFN,IAGA,KAAKzB,YAJP,EAKE;QACA,KAAKlB,gBAAL,CAAsB0E,kBAAtB;QACA,KAAK6D,sBAAL;QACA,KAAK5F,iBAAL,GAAyB,IAAzB;MACD;IACF,CAdD,MAcO,IACL,KAAKjB,SAAL,KAAmB,YAAKC,QAAL,MAAa,IAAb,IAAaiH,aAAb,GAAa,MAAb,GAAaA,GAAE5H,MAAf,IAAwB,CAA3C,IACA,CAAC,KAAKE,YAFD,EAGL;MACA,KAAKQ,SAAL,GAAiB,CAAjB;MACA,KAAK1B,gBAAL,CAAsB0E,kBAAtB,CAAyC,KAAzC;MACA,KAAK6D,sBAAL;MACA,KAAK5F,iBAAL,GAAyB,IAAzB;IACD;;IACD,IAAI,KAAKO,eAAL,KAAyB,QAA7B,EACE,WAAKwF,qBAAL,MAA0B,IAA1B,IAA0BG,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEJ,eAAF,EAA1B;IACF,KAAKxI,eAAL,CAAqB0I,YAArB,CAAkC,+BAAlC,EAAmE,KAAKtI,IAAL,CAAUwH,EAA7E;EACD;;EAEDiB,WAAW;IACT,KAAK9I,gBAAL,CAAsB+I,uBAAtB,CAA8C,KAAK1I,IAAnD;EACD;;EAEDiE,WAAW;;;IACT,KAAKL,WAAL,GAAmB,KAAKG,kBAAxB;IACA,MAAM4E,KAAK,GAAQ,EAAnB;IACA,iBAAK3I,IAAL,MAAS,IAAT,IAASE,aAAT,GAAS,MAAT,GAASA,GAAEuD,WAAX,MAAsB,IAAtB,IAAsBrD,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEwI,OAAF,CAAW/E,IAAD,IAAc;;;MAC5C8E,KAAK,CAAC,UAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE9E,IAAN,MAAU,IAAV,IAAU3D,aAAV,GAAU,MAAV,GAAUA,GAAEsH,EAAb,CAAL,GAAwB,IAAxB;IACD,CAFqB,CAAtB;IAGA,KAAK5D,WAAL,GAAmB,WAAKA,WAAL,MAAgB,IAAhB,IAAgBvD,aAAhB,GAAgB,MAAhB,GAAgBA,GAAExE,GAAF,CAAOgI,IAAD,IAAc;MACrD,IAAI8E,KAAK,CAAC9E,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE2D,EAAP,CAAT,EAAqB;QACnB,uCAAY3D,IAAZ,GAAgB;UAAEC,QAAQ,EAAE;QAAZ,CAAhB;MACD;;MACD,yBAAYD,IAAZ;IACD,CALkC,CAAnC;EAMD;;EAEDgF,WAAW,CAACrB,EAAD,EAAW;IACpB,OAAOzK,oBAAoB,CAACyK,EAAD,EAAK,KAAKrC,KAAV,EAAiB,IAAjB,CAApB,IAA8C,EAArD;EACD;;EAED2D,aAAa,CAACC,UAAD,EAAuB;IAClC,OAAOpM,UAAU,CAACoM,UAAD,CAAjB;EACD;;EAEDC,eAAe,CAAClJ,OAAD,EAAgB;IAC7B,KAAK+C,eAAL,GAAuB/C,OAAvB,CAD6B,CAG7B;;IACA,IAAIA,OAAO,KAAK,SAAhB,EAA2B;MACzB,KAAKkI,uBAAL;IACD;;IAED,KAAKpI,eAAL,CAAqB0I,YAArB,CAAkC,kBAAkB,KAAKzF,eAAL,CAAqBoG,OAArB,CAA6B,MAA7B,EAAqC,EAArC,CAAwC,QAA5F,EAAsG,KAAKjJ,IAAL,CAAUwH,EAAhH;EACD;;EAED0B,UAAU,CAACrF,IAAD,EAAU;IAClBA,IAAI,CAACC,QAAL,GAAgB,EAACD,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEC,QAAP,CAAhB;IACA,IAAIqF,OAAO,GAAG;MACZ3B,EAAE,EAAE,KAAKxH,IAAL,CAAUwH,EADF;MAEZ4B,MAAM,EAAEvF,IAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAE2D;IAFF,CAAd;IAIA,KAAK5D,WAAL,GAAmB,KAAKA,WAAL,CAAiB/H,GAAjB,CAAsByL,CAAD,IACtCA,CAAC,CAACE,EAAF,KAAS3D,IAAI,CAAC2D,EAAd,GAAkB6B,gCAAM/B,CAAN,GAAO;MAAExD,QAAQ,EAAED,IAAI,CAACC;IAAjB,CAAP,CAAlB,GAAuDwD,CADtC,CAAnB;IAGA,KAAK9H,KAAL,CAAWkE,QAAX,CAAoB,IAAI7F,kBAAJ,EAApB;IACA,KAAK2B,KAAL,CAAWkE,QAAX,CAAoB,IAAIjG,kBAAJ,CAAuB0L,OAAvB,EAAgC,KAAKnJ,IAAL,CAAUwH,EAA1C,EAA8C,KAA9C,CAApB;IACA,KAAK5H,eAAL,CAAqB0I,YAArB,CAAkC,6BAAlC,EAAiE,KAAKtI,IAAL,CAAUwH,EAA3E;EACD;;EAEDxD,SAAS;IACP,KAAKJ,WAAL,CAAiB0F,IAAjB,CAAsB,CAACC,CAAD,EAAIC,CAAJ,KAAS;MAC7B,IAAID,CAAC,CAACzF,QAAF,KAAe0F,CAAC,CAAC1F,QAArB,EAA+B;QAC7B,MAAM2F,KAAK,GAAGF,CAAC,CAACG,IAAF,CAAOC,WAAP,EAAd;QACA,MAAMC,KAAK,GAAGJ,CAAC,CAACE,IAAF,CAAOC,WAAP,EAAd;QACA,OAAOF,KAAK,CAACI,aAAN,CAAoBD,KAApB,CAAP;MACD,CAJD,MAIO;QACL,OAAOL,CAAC,CAACzF,QAAF,GAAa,CAAC,CAAd,GAAkB,CAAzB;MACD;IACF,CARD;EASD;;EAEDgG,WAAW,CAAC9J,IAAD,EAAY;IACrB,KAAKA,IAAL,CAAU+J,KAAV,GAAkB/J,IAAlB;EACD;;EAEDgK,WAAW,CAAClK,OAAD,EAAgB;IACzB,KAAKkJ,eAAL,CAAqBlJ,OAArB;IACA,KAAKF,eAAL,CAAqB0I,YAArB,CAAkC,qCAAlC,EAAyE,KAAKtI,IAAL,CAAUwH,EAAnF;EACD;;EAEDyC,eAAe,CAACC,EAAD,EAAqB;;;IAClC,IAAI,KAAK3K,QAAT,EAAmB;MACjB,KAAKA,QAAL,CAAc4K,IAAd;IACD;;IACD,IAAI,WAAKnK,IAAL,MAAS,IAAT,IAASE,aAAT,GAAS,MAAT,GAASA,GAAEkK,iBAAf,EAAkC;MAChC,KAAKC,oBAAL,GAA4BhB,MAAM,CAACiB,OAAP,CAAe,WAAKtK,IAAL,MAAS,IAAT,IAASI,aAAT,GAAS,MAAT,GAASA,GAAEgK,iBAA1B,EACzBvO,GADyB,CACpBsI,IAAD,IAAc;QACjB,OAAO;UACLoG,KAAK,EAAEpG,IAAI,CAAC,CAAD,CADN;UAELqG,QAAQ,EAAEnB,MAAM,CAACiB,OAAP,CAAenG,IAAI,CAAC,CAAD,CAAnB,EACPsG,OADO,GAEP5O,GAFO,CAEF6O,KAAD,IAAe;YAClB,OAAO;cACLC,MAAM,EAAED,KAAK,CAAC,CAAD,CADR;cAELE,SAAS,EAAEvB,MAAM,CAACwB,IAAP,CAAYH,KAAK,CAAC,CAAD,CAAjB,EACRD,OADQ,GAER5O,GAFQ,CAEHiP,GAAD,IAAQ;gBACX,IAAIC,SAAS,GAAGD,GAAG,CAACE,QAAJ,EAAhB;gBACA,IAAIC,SAAS,GAAGP,KAAK,CAAC,CAAD,CAAL,CAASI,GAAT,CAAhB;gBACA,OAAO;kBAAEI,IAAI,EAAEH,SAAR;kBAAmBI,QAAQ,EAAEF;gBAA7B,CAAP;cACD,CANQ;YAFN,CAAP;UAUD,CAbO;QAFL,CAAP;MAiBD,CAnByB,EAoBzBR,OApByB,EAA5B;IAqBD;;IACD,KAAKlL,QAAL,GAAgB,KAAKD,YAAL,CAAkB8L,IAAlB,CAAuBlB,EAAvB,EAA2B;MACzCmB,KAAK,EAAE;IADkC,CAA3B,CAAhB;EAGD;;EAED/G,WAAW;;;IACT,IAAI,KAAKjD,SAAL,KAAkB,WAAKC,QAAL,MAAa,IAAb,IAAapB,aAAb,GAAa,MAAb,GAAaA,GAAES,MAAjC,CAAJ,EAA6C;MAC3C;IACD;;IACD,IAAI;MAAEW;IAAF,IAAe,KAAK3B,gBAAL,CAAsB2E,WAAtB,EAAnB;IACA,KAAKhD,QAAL,GAAgBA,QAAhB;IACA,KAAKD,SAAL,GAAiB,WAAKC,QAAL,MAAa,IAAb,IAAalB,aAAb,GAAa,MAAb,GAAaA,GAAE8D,SAAF,CAAaC,IAAD,IACxC,KAAKC,WAAL,CAAiBD,IAAjB,EAAuB,KAAKnE,IAA5B,CAD4B,CAA9B,CANS,CAST;;IACA,KAAKsC,iBAAL,GAAyB,KAAzB;IACA,KAAKC,qBAAL,GAA6B,KAA7B;IACAC,OAAO,CAACC,GAAR,CAAY,6CAAZ,EAA2D;MACzDH,iBAAiB,EAAE,KAAKA,iBADiC;MAEzDC,qBAAqB,EAAE,KAAKA,qBAF6B;MAGzDlB,SAAS,EAAE,KAAKA,SAHyC;MAIzDiK,cAAc,EAAE,WAAKhK,QAAL,MAAa,IAAb,IAAajB,aAAb,GAAa,MAAb,GAAaA,GAAEM;IAJ0B,CAA3D;EAMD;;EAED4K,cAAc,CAACC,aAAD,EAAqB;IACjC,OAAOA,aAAa,SAAb,iBAAa,WAAb,GAAa,MAAb,gBAAa,CAAE5K,IAAf,CAAoBe,IAAI,IAAG;MAAA;;MAAC,kBAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEjB,SAAN,MAAe,IAAf,IAAeR,aAAf,GAAe,MAAf,GAAeA,GAAEC,WAAjB,MAAiC,MAAjC;IAAuC,CAAnE,CAAP;EACD;;EAEDsL,gBAAgB;;;IACd,OAAO,wBAAKzL,IAAL,MAAS,IAAT,IAASE,aAAT,GAAS,MAAT,GAASA,GAAEsB,OAAX,MAAkB,IAAlB,IAAkBpB,aAAlB,GAAkB,MAAlB,GAAkBA,GAAEoL,aAApB,MAAiC,IAAjC,IAAiCnL,aAAjC,GAAiC,MAAjC,GAAiCA,GAAExE,GAAF,CAAO8F,IAAD,IAAc;MAAA;;MAAC,iBAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEjB,SAAN,MAAe,IAAf,IAAeR,aAAf,GAAe,MAAf,GAAeA,GAAEC,WAAjB;IAA4B,CAAjD,EAAmDsB,IAAnD,CAAwD,IAAxD,CAAjC,KAAkG,EAAzG;EACD;;EAEDyG,sBAAsB;IACpB,KAAKwD,YAAL,GAAoB,KAAK/L,gBAAL,CAAsBgM,WAAtB,CAAkChJ,SAAlC,CAClB,CAAC;MAAErB,QAAF;MAAYsK;IAAZ,CAAD,KAAgC;MAC9B,IAAItK,QAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAEX,MAAd,EAAsB;QACpB,KAAKW,QAAL,GAAgBA,QAAhB;;QACA,IAAI,CAAC,KAAKT,YAAV,EAAwB;UACtB,MAAMgL,KAAK,GAAGD,aAAa,GAAGtK,QAAQ,CAACX,MAAT,GAAkB,CAArB,GAAyB,CAApD;UACA,KAAKX,IAAL,GAAYsB,QAAQ,CAACuK,KAAD,CAApB;UACA,KAAKxK,SAAL,GAAiBwK,KAAjB;QACD;;QACD,KAAKvJ,iBAAL,GAAyB,KAAzB;QACA,KAAKC,qBAAL,GAA6B,KAA7B;QACA,KAAKmJ,YAAL,CAAkBI,WAAlB;MACD;IACF,CAbiB,CAApB;EAeD;;EAEDC,WAAW;;;IACT,KAAK9I,OAAL,CAAa+I,IAAb;IACA,KAAK/I,OAAL,CAAagJ,QAAb;IACA,KAAKtM,gBAAL,CAAsBuM,eAAtB,CAAsC,IAAtC;;IACA,IAAI,KAAKxH,oBAAL,IAA6B,KAAKyH,oBAAtC,EAA4D;MAC1D,KAAK3M,KAAL,CAAWkE,QAAX,CAAoB,IAAInG,aAAJ,EAApB;MACA,KAAK4O,oBAAL,GAA4B,KAA5B;MACA,KAAK3M,KAAL,CAAWkE,QAAX,CACE,IAAIlG,aAAJ,CAAkB,IAAlB,EAAwB,cAAQ,SAAR,YAAQ,WAAR,GAAQ,MAAR,WAAQ,CAAE4O,IAAV,MAAc,IAAd,IAAclM,aAAd,GAAc,MAAd,GAAcA,GAAE8E,QAAF,CAAW,SAAX,CAAtC,CADF;IAGD;;IACD,KAAKxF,KAAL,CAAWkE,QAAX,CAAoB,IAAI5F,gBAAJ,EAApB;IACA,WAAK4N,YAAL,MAAiB,IAAjB,IAAiBtL,aAAjB,GAAiB,MAAjB,GAAiBA,GAAE0L,WAAF,EAAjB;EACD;;AAznB8B;;;mBAApB1M,sBAAoBhB;AAAA;;;QAApBgB;EAAoBiN;EAAAC;IAAA;;;;;;;;;;;;;;;;;;;;;;;MCzEjClO,+BAAoB,CAApB,EAAoB,KAApB,EAAoB,CAApB,EAAoB,CAApB,EAAoB,KAApB,EAAoB,CAApB,EAAoB,CAApB,EAAoB,KAApB,EAAoB,CAApB,EAAoB,CAApB,EAAoB,KAApB,EAAoB,CAApB;MAKUA;MAEAA;MAAmBA;;;MAAiEA;MAGxFA;MACEA;MAMAA,gCAAkB,EAAlB,EAAkB,eAAlB,EAAkB,EAAlB;MAC0DA;QAAA,OAAmBmO,2BAAnB;MAA0C,CAA1C;MAC0BnO;MAEpFA;MAMFA;MAEFA,iCAAuE,EAAvE,EAAuE,IAAvE,EAAuE,EAAvE;MAEIA;MACFA;MACAA;MACEA;MAyBFA;MACAA;MACEA;MACAA,yCAA2C,EAA3C,EAA2C,KAA3C,EAA2C,EAA3C;MACyCA;MACvCA;MAGJA;MACEA;MACAA,yCAA2C,EAA3C,EAA2C,KAA3C,EAA2C,EAA3C;MAEIA;MAKAA;MAIAA;MAaAA;MAIAA;MAIAA;MAMFA;MAINA;MACEA;MAMFA;MACAA;MA6jBFA;MAGAA;MACEA;MAaAA;MAMAA;MAMAA;MAKFA;MAEAA;MAkCAA,iCAAwE,EAAxE,EAAwE,KAAxE,EAAwE,EAAxE,EAAwE,EAAxE,EAAwE,KAAxE,EAAwE,EAAxE;MAE+DA;QAASmO;QAAU,OAACA,iBAAD;MAAc,CAAjC;MAEzDnO;MACFA;MACAA,iCAAmB,EAAnB,EAAmB,eAAnB,EAAmB,EAAnB;MAC0DA;QAAA,OAAmBmO,2BAAnB;MAA0C,CAA1C;MAC0BnO;MAEpFA;MAAuDA;QAAA,OAASmO,cAAT;MAAmB,CAAnB;MAErDnO;MACFA;;;;MAtwBeA;MAAAA;MAIoDA;MAAAA;MAE5CA;MAAAA;MAINA;MAAAA;MAOEA;MAAAA,gCAAa,mBAAb,EAAa,IAAb,EAAa,cAAb,EAAamO,gBAAb,EAAa,wBAAb,EAAaA,0BAAb;MAGFnO;MAAAA;MAUfA;MAAAA;MAGeA;MAAAA;MA6B0BA;MAAAA;MAQUA;MAAAA;MAKhCA;MAAAA;MAIAA;MAAAA;MAaAA;MAAAA;MAIAA;MAAAA;MAIAA;MAAAA;MAWUA;MAAAA;MAO3BA;MAAAA;MAgkBFA;MAAAA;MACEA;MAAAA;MAaAA;MAAAA;MAMAA;MAAAA;MAMAA;MAAAA;MA4CFA;MAAAA;MAIeA;MAAAA,gCAAa,mBAAb,EAAa,IAAb,EAAa,cAAb,EAAamO,gBAAb,EAAa,wBAAb,EAAaA,0BAAb;MAIfnO;MAAAA", "names": ["EventEmitter", "SimpleChanges", "TemplateRef", "NavigationEnd", "moment", "filter", "map", "<PERSON><PERSON><PERSON><PERSON>", "take", "takeUntil", "combineLatest", "EMPTY_GUID", "UPDATE_STATUS_PAST_TENSE", "BHKType", "EnquiryType", "FurnishStatus", "Gender", "LeadSource", "MaritalStatusType", "OfferType", "Profession", "PurposeType", "convertUrlsToLinks", "formatBudget", "getAssignedToDetails", "getBHKDisplayString", "getLocationDetailsByObj", "getTimeZoneDate", "FetchTagsList", "getTagsList", "getGlobalAnonymousIsLoading", "getGlobalSettingsAnonymous", "ClearCardData", "FetchLeadList", "UpdateLeadsTagInfo", "getIsLeadCustomStatusEnabled", "getLeadCardData", "getLeads", "LeadPreviewChanged", "LeadPreviewSaved", "getLeadPreviewChanged", "getPermissions", "getAdminsAndReportees", "getUserBasicDetails", "getUsersListForReassignment", "i0", "ctx_r20", "ctx_r22", "_r28", "flag_r78", "ctx_r81", "ctx_r11", "_r49", "_r56", "_r62", "_r65", "ctx_r114", "ctx_r115", "ctx_r13", "ctx_r14", "ctx_r15", "LeadPreviewComponent", "constructor", "modalService", "modalRef", "store", "gridOptionService", "router", "shareDataService", "trackingService", "label", "section", "canShowStatusPopupInPreview", "data", "status", "_a", "displayName", "_b", "_c", "isCustomStatusEnabled", "shouldOpenAppointmentPageForStatus", "_d", "shouldOpenAppointmentPage", "childType", "length", "some", "isMobile<PERSON>iew", "window", "innerWidth", "isFirstPage", "currentPageNumber", "currentPage", "totalPages", "isLastLead", "foundData", "cardData", "enquiryTypes", "enquiry", "join", "bhkTypes", "type", "bhkNo", "bhKs", "bhk", "beds", "Array", "isArray", "bed", "addresses", "address", "ngOnInit", "isNextDataLoading", "isPreviousDataLoading", "console", "log", "backtoOverview", "subscribe", "value", "selectedSection", "setTimeout", "select", "pipe", "stopper", "events", "event", "currentPath", "url", "isLoading", "to<PERSON>romise", "selected<PERSON><PERSON><PERSON>", "customFlags", "dispatch", "flagData", "flagOptions", "flag", "isActive", "flagOptionsDefault", "sortFlags", "updateFlags", "findIndex", "item", "isDataEqual", "emitFetchNextLeads", "getCardData", "adminsAndReportees$", "allUsers$", "permissions$", "isLeadPreviewChanged", "adminsAndReportees", "allUsers", "permissions", "permissionsSet", "Set", "includes", "canViewLeadSource", "canUpdateStatus", "users", "canEditLead", "canEditTags", "has", "canUpdateInvoice", "initialState", "defaultCurrency", "countries", "globalSettingsData", "canUpdateBookedLead", "userData", "ngOnChanges", "changes", "clickedData", "currentValue", "ngAfterViewInit", "swipeArea", "document", "getElementById", "startX", "startY", "addEventListener", "touches", "clientX", "clientY", "endX", "changedTouches", "endY", "deltaX", "deltaY", "Math", "abs", "prevData", "nextData", "isSelectedFlag", "f", "selectedFlag", "id", "filteredNavigationItems", "isArchived", "navigationItems", "getActiveBackgroundColor", "bgColor", "obj1", "obj2", "forceResetLoadingStates", "emitFetchPreviousLeads", "subscribeToDataUpdated", "customStatusChangeComponent", "cleanStatusForm", "statusChangeComponent", "trackFeature", "_e", "_f", "getNextLead", "sendMiniBookingformData", "flags", "for<PERSON>ach", "getUserName", "getProfession", "profession", "onSectionSelect", "replace", "flagAction", "payload", "flagId", "Object", "sort", "a", "b", "nameA", "name", "toUpperCase", "nameB", "localeCompare", "updateNotes", "notes", "checkStatus", "openAudioPlayer", "aP", "hide", "callRecordingUrls", "callRecordingDetails", "entries", "years", "yearData", "reverse", "item1", "months", "monthData", "keys", "key", "audioDate", "toString", "audioFile", "date", "audioUrl", "show", "class", "cardData<PERSON>ength", "hasPlotSubType", "propertyTypes", "getSubtypesTitle", "dataUpdated$", "dataUpdated", "isPrevFetched", "index", "unsubscribe", "ngOnDestroy", "next", "complete", "gotoOverviewTab", "fetchLeadsWhenClosed", "href", "selectors", "viewQuery", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Desktop\\Translate\\Leadrat-Black-Web\\src\\app\\shared\\components\\lead-preview\\lead-preview.component.ts", "C:\\Users\\<USER>\\Desktop\\Translate\\Leadrat-Black-Web\\src\\app\\shared\\components\\lead-preview\\lead-preview.component.html"], "sourcesContent": ["import {\r\n  AfterViewInit,\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  OnDestroy,\r\n  OnInit,\r\n  SimpleChanges,\r\n  TemplateRef,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport { NavigationEnd, Router } from '@angular/router';\r\nimport { Store } from '@ngrx/store';\r\nimport * as moment from 'moment';\r\nimport { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';\r\nimport { Subscription, filter, map, skipWhile, take, takeUntil } from 'rxjs';\r\nimport { combineLatest } from 'rxjs/internal/observable/combineLatest';\r\n\r\nimport { EMPTY_GUID, UPDATE_STATUS_PAST_TENSE } from 'src/app/app.constants';\r\nimport {\r\n  BHKType,\r\n  EnquiryType,\r\n  FurnishStatus,\r\n  Gender,\r\n  LeadSource,\r\n  MaritalStatusType,\r\n  OfferType,\r\n  Profession,\r\n  PurposeType,\r\n} from 'src/app/app.enum';\r\nimport { AppState } from 'src/app/app.reducer';\r\nimport {\r\n  convertUrlsToLinks,\r\n  formatBudget,\r\n  getAssignedToDetails,\r\n  getBHKDisplayString,\r\n  getBRDisplayString,\r\n  getLocationDetailsByObj,\r\n  getTimeZoneDate\r\n} from 'src/app/core/utils/common.util';\r\nimport { CustomStatusChangeComponent } from 'src/app/features/leads/custom-status-change/custom-status-change.component';\r\nimport { StatusChangeComponent } from 'src/app/features/leads/status-change/status-change.component';\r\nimport { FetchTagsList } from 'src/app/reducers/custom-tags/custom-tags.actions';\r\nimport { getTagsList } from 'src/app/reducers/custom-tags/custom-tags.reducer';\r\nimport {\r\n  getGlobalAnonymousIsLoading,\r\n  getGlobalSettingsAnonymous,\r\n} from 'src/app/reducers/global-settings/global-settings.reducer';\r\nimport {\r\n  ClearCardData,\r\n  FetchLeadList,\r\n  UpdateLeadsTagInfo,\r\n} from 'src/app/reducers/lead/lead.actions';\r\nimport { getIsLeadCustomStatusEnabled, getLeadCardData, getLeads } from 'src/app/reducers/lead/lead.reducer';\r\nimport {\r\n  LeadPreviewChanged,\r\n  LeadPreviewSaved,\r\n} from 'src/app/reducers/loader/loader.actions';\r\nimport { getLeadPreviewChanged } from 'src/app/reducers/loader/loader.reducer';\r\nimport { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';\r\nimport {\r\n  getAdminsAndReportees,\r\n  getUserBasicDetails,\r\n  getUsersListForReassignment,\r\n} from 'src/app/reducers/teams/teams.reducer';\r\nimport { GridOptionsService } from 'src/app/services/shared/grid-options.service';\r\nimport { ShareDataService } from 'src/app/services/shared/share-data.service';\r\nimport { TrackingService } from 'src/app/services/shared/tracking.service';\r\n\r\n@Component({\r\n  selector: 'lead-preview',\r\n  templateUrl: './lead-preview.component.html',\r\n})\r\nexport class LeadPreviewComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  @Input() clickedData: any = null;\r\n  @Input() whatsAppComp: boolean = false;\r\n  @Input() showCommunicationCount: boolean = false;\r\n  private stopper: EventEmitter<void> = new EventEmitter<void>();\r\n  @ViewChild('statusChangeComponent', { static: false })\r\n  statusChangeComponent: StatusChangeComponent;\r\n  @ViewChild('customStatusChangeComponent', { static: false })\r\n  customStatusChangeComponent: CustomStatusChangeComponent;\r\n  initialState: any;\r\n  data: any;\r\n  users: any;\r\n  selectedSection: any = 'Overview';\r\n  showOnlyPopup: boolean = true;\r\n  callRecordingDetails: {\r\n    years: any;\r\n    yearData: { months: any; monthData: { date: string; audioUrl: any }[] }[];\r\n  }[];\r\n  moment = moment;\r\n  formatBudget = formatBudget;\r\n  getAssignedToDetails = getAssignedToDetails;\r\n  getBHKDisplayString = getBHKDisplayString;\r\n  getLocationDetailsByObj = getLocationDetailsByObj;\r\n  getTimeZoneDate = getTimeZoneDate;\r\n  OfferType = OfferType;\r\n  FurnishStatus = FurnishStatus;\r\n  convertUrlsToLinks = convertUrlsToLinks\r\n  BHKType = BHKType;\r\n  LeadSource = LeadSource;\r\n  PurposeType = PurposeType;\r\n  EnquiryType = EnquiryType;\r\n  EMPTY_GUID = EMPTY_GUID;\r\n  navigationItems = [\r\n    { label: 'GLOBAL.overview', section: 'Overview' },\r\n    { label: 'GLOBAL.status', section: 'Status' },\r\n    { label: 'GLOBAL.history', section: 'History' },\r\n    { label: 'TASK.notes', section: 'Notes' },\r\n    { label: 'GLOBAL.document', section: 'Document' },\r\n  ];\r\n  flagOptions: any[] = [];\r\n  selectedFlags: any;\r\n  toggleSelectFlag: boolean = false;\r\n  cardData: any;\r\n  foundData: any;\r\n  defaultCurrency: string = 'INR';\r\n  isLeadPreviewChanged: boolean = false;\r\n  fetchLeadsWhenClosed: boolean = false;\r\n  dataUpdated$: Subscription;\r\n  isNextDataLoading: boolean = false;\r\n  isPreviousDataLoading: boolean = false;\r\n  canEditLead: boolean = false;\r\n  canEditTags: boolean = false;\r\n  canUpdateInvoice: boolean = false;\r\n  closeLeadPreviewModal: any;\r\n  globalSettingsData: any;\r\n  canViewLeadSource: boolean = false;\r\n  canUpdateStatus: boolean = false;\r\n  canUpdateBookedLead: boolean = false;\r\n  flagOptionsDefault: any = [];\r\n  currentPath: string;\r\n  isCustomStatusEnabled: boolean = false;\r\n  isGlobalSettingsLoading: boolean = true;\r\n  userData: any;\r\n  Gender = Gender;\r\n  MaritalStatusType = MaritalStatusType;\r\n\r\n  get canShowStatusPopupInPreview(): boolean {\r\n    return (\r\n      ((this.data.status?.displayName ===\r\n        UPDATE_STATUS_PAST_TENSE['meeting-scheduled'] ||\r\n        this.data.status?.displayName ===\r\n        UPDATE_STATUS_PAST_TENSE['visit-scheduled'] ||\r\n        this.data.status?.displayName ===\r\n        UPDATE_STATUS_PAST_TENSE['referral-scheduled']) &&\r\n        !this.isCustomStatusEnabled) ||\r\n      (this.isCustomStatusEnabled &&\r\n        this.shouldOpenAppointmentPageForStatus(this.data?.status))\r\n    );\r\n  }\r\n\r\n  shouldOpenAppointmentPageForStatus(status: any): boolean {\r\n    return (\r\n      status?.shouldOpenAppointmentPage || status?.childType?.shouldOpenAppointmentPage ||\r\n      (status?.childType?.length && status?.childType?.some((childType: any) => childType?.shouldOpenAppointmentPage)) ||\r\n      false\r\n    );\r\n  }\r\n\r\n  get isMobileView(): boolean {\r\n    return window.innerWidth <= 480;\r\n  }\r\n  get isFirstPage(): boolean {\r\n    return this.shareDataService.currentPageNumber === 1;\r\n  }\r\n  get currentPage(): number {\r\n    return this.shareDataService.currentPageNumber;\r\n  }\r\n  get totalPages(): number {\r\n    return this.shareDataService.totalPages;\r\n  }\r\n  get isLastLead(): boolean {\r\n    return (\r\n      (this.foundData == this.cardData?.length - 1 && this.isMobileView) ||\r\n      (this.totalPages === this.currentPage &&\r\n        this.foundData == this.cardData?.length - 1)\r\n    );\r\n  }\r\n\r\n  get enquiryTypes(): string {\r\n    return (\r\n      this.data?.enquiry?.enquiryTypes\r\n        ?.map((enquiry: any) => EnquiryType[enquiry])\r\n        ?.join(', ') || '--'\r\n    );\r\n  }\r\n\r\n  get bhkTypes(): string {\r\n    return (\r\n      this.data?.enquiry?.bhkTypes\r\n        ?.map((type: any) => BHKType[type])\r\n        ?.join(', ') || '--'\r\n    );\r\n  }\r\n\r\n  get bhkNo(): string {\r\n    // return this.globalSettingsData?.isCustomLeadFormEnabled\r\n    //   ? this.data?.enquiry?.bhKs\r\n    //     /* BR field commented out\r\n    //     ?.map((bhk: any) => getBRDisplayString(bhk))\r\n    //     */\r\n    //     ?.join(', ')\r\n    //   :\r\n    return this.data?.enquiry?.bhKs\r\n      ?.map((bhk: any) => getBHKDisplayString(bhk))\r\n      ?.join(', ');\r\n  }\r\n\r\n\r\n  get beds(): string {\r\n    return Array.isArray(this.data.enquiry?.beds)\r\n      ? this.data.enquiry?.beds.map((bed: any) => (bed === 0 || bed === '0' ? 'Studio' : bed))\r\n      : [];\r\n  }\r\n\r\n  get addresses(): string {\r\n    return (\r\n      this.data?.enquiry?.addresses\r\n        ?.map((address: any) => getLocationDetailsByObj(address))\r\n        ?.join('; ') || '--'\r\n    );\r\n  }\r\n\r\n  constructor(\r\n    public modalService: BsModalService,\r\n    public modalRef: BsModalRef,\r\n    private store: Store<AppState>,\r\n    public gridOptionService: GridOptionsService,\r\n    public router: Router,\r\n    private shareDataService: ShareDataService,\r\n    private trackingService: TrackingService\r\n\r\n  ) { }\r\n\r\n  async ngOnInit(): Promise<void> {\r\n    // ✅ Reset loading states when component initializes\r\n    this.isNextDataLoading = false;\r\n    this.isPreviousDataLoading = false;\r\n\r\n    console.log('🔧 Lead Preview Init - Loading states reset:', {\r\n      isNextDataLoading: this.isNextDataLoading,\r\n      isPreviousDataLoading: this.isPreviousDataLoading,\r\n      isMobileView: this.isMobileView\r\n    });\r\n\r\n    this.shareDataService.backtoOverview.subscribe((value) => {\r\n      if (value) {\r\n        this.selectedSection = value;\r\n      }\r\n    });\r\n\r\n    // ✅ Force reset loading states after a short delay to ensure all initialization is complete\r\n    setTimeout(() => {\r\n      this.isNextDataLoading = false;\r\n      this.isPreviousDataLoading = false;\r\n      console.log('🔧 Delayed loading state reset:', {\r\n        isNextDataLoading: this.isNextDataLoading,\r\n        isPreviousDataLoading: this.isPreviousDataLoading\r\n      });\r\n    }, 100);\r\n\r\n    this.store.select(getLeads)\r\n      .pipe(takeUntil(this.stopper), skipWhile(() => this.isMobileView))\r\n      .subscribe(data => {\r\n        this.cardData = data;\r\n        // this.data = this.cardData?.find((data: any) => data?.id === this.data?.id)\r\n      });\r\n\r\n    this.store.select(getLeadCardData)\r\n      .pipe(takeUntil(this.stopper), skipWhile(() => !this.isMobileView))\r\n      .subscribe(async (data: any) => {\r\n        this.cardData = data;\r\n        // this.data = this.cardData?.find((data: any) => data?.id === this.data?.id)\r\n      });\r\n\r\n    this.router.events\r\n      .pipe(filter((event) => event instanceof NavigationEnd))\r\n      .subscribe(() => {\r\n        this.currentPath = this.router.url;\r\n      });\r\n    this.currentPath = this.router.url;\r\n\r\n    await this.store\r\n      .select(getGlobalAnonymousIsLoading)\r\n      .pipe(\r\n        skipWhile((isLoading: boolean) => {\r\n          return isLoading;\r\n        }),\r\n        take(1)\r\n      )\r\n      .toPromise();\r\n    this.isCustomStatusEnabled = await this.store\r\n      .select(getIsLeadCustomStatusEnabled)\r\n      .pipe(\r\n        map((data: any) => data),\r\n        take(1)\r\n      )\r\n      .toPromise();\r\n\r\n    this.selectedFlags = this.data?.customFlags;\r\n    this.store.dispatch(new FetchTagsList());\r\n    this.store\r\n      .select(getTagsList)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        let flagData = data || [];\r\n        if (flagData.length > 0) {\r\n          this.flagOptions = flagData\r\n            .filter((flag: any) => flag?.isActive)\r\n            .map((flag: any) => {\r\n              const isActive = false;\r\n              return { ...flag, isActive };\r\n            });\r\n          this.flagOptionsDefault = this.flagOptions;\r\n        }\r\n        this.sortFlags();\r\n        this.updateFlags();\r\n      });\r\n\r\n    this.foundData = this.cardData?.findIndex((item: any) =>\r\n      this.isDataEqual(item, this.data)\r\n    );\r\n    if (\r\n      (this.foundData === this.cardData?.length - 3 ||\r\n        this.foundData === this.cardData?.length - 1) &&\r\n      this.isMobileView\r\n    ) {\r\n      this.shareDataService.emitFetchNextLeads();\r\n    }\r\n\r\n\r\n    this.getCardData();\r\n\r\n    const adminsAndReportees$ = this.store\r\n      .select(getAdminsAndReportees)\r\n      .pipe(takeUntil(this.stopper));\r\n    const allUsers$ = this.store\r\n      .select(getUsersListForReassignment)\r\n      .pipe(takeUntil(this.stopper));\r\n    const permissions$ = this.store\r\n      .select(getPermissions)\r\n      .pipe(takeUntil(this.stopper));\r\n    this.store\r\n      .select(getLeadPreviewChanged)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: boolean) => {\r\n        this.isLeadPreviewChanged = data;\r\n      });\r\n    combineLatest({\r\n      adminsAndReportees: adminsAndReportees$,\r\n      allUsers: allUsers$,\r\n      permissions: permissions$,\r\n    }).subscribe(({ adminsAndReportees, allUsers, permissions }) => {\r\n      const permissionsSet = new Set(permissions);\r\n      if (permissions?.includes('Permissions.Leads.ViewLeadSource'))\r\n        this.canViewLeadSource = true;\r\n      if (permissions?.includes('Permissions.Leads.UpdateLeadStatus'))\r\n        this.canUpdateStatus = true;\r\n      if (permissions?.includes('Permissions.Users.AssignToAny')) {\r\n        this.users = allUsers;\r\n      } else {\r\n        this.users = adminsAndReportees;\r\n      }\r\n      if (permissions?.includes('Permissions.Leads.Update')) {\r\n        this.canEditLead = true;\r\n      }\r\n      this.canEditTags = permissionsSet.has('Permissions.Leads.UpdateTags');\r\n      if (permissions?.includes('Permissions.Invoice.Update')) {\r\n        this.canUpdateInvoice = true;\r\n      }\r\n      if (permissions?.includes('Permissions.Invoice.View')) {\r\n        if (\r\n          !this.canUpdateInvoice &&\r\n          this.currentPath === '/invoice' &&\r\n          this.data?.status?.status === 'invoiced'\r\n        ) {\r\n          this.selectedSection = 'Overview';\r\n        }\r\n      }\r\n    });\r\n    if (this.initialState) {\r\n      this.selectedSection = this.initialState.selectedSection;\r\n    }\r\n\r\n    this.store\r\n      .select(getGlobalSettingsAnonymous)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        this.defaultCurrency =\r\n          data.countries && data.countries.length > 0\r\n            ? data.countries[0].defaultCurrency\r\n            : null;\r\n        this.globalSettingsData = data;\r\n      });\r\n    this.store\r\n      .select(getPermissions)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((permissions: any) => {\r\n        if (!permissions?.length) return;\r\n        if (permissions?.includes('Permissions.Leads.UpdateBookedLead')) {\r\n          this.canUpdateBookedLead = true;\r\n        }\r\n      });\r\n    this.store\r\n      .select(getUserBasicDetails)\r\n      .pipe(takeUntil(this.stopper))\r\n      .subscribe((data: any) => {\r\n        this.userData = data;\r\n      });\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes.clickedData && changes.clickedData.currentValue) {\r\n      this.data = this.clickedData;\r\n      // ✅ Reset loading states when new data is received\r\n      this.isNextDataLoading = false;\r\n      this.isPreviousDataLoading = false;\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    const swipeArea = document.getElementById('swipeArea');\r\n    let startX: any, startY: any;\r\n\r\n    swipeArea.addEventListener('touchstart', (event) => {\r\n      startX = event.touches[0].clientX;\r\n      startY = event.touches[0].clientY;\r\n    });\r\n\r\n    swipeArea.addEventListener('touchend', (event) => {\r\n      const endX = event.changedTouches[0].clientX;\r\n      const endY = event.changedTouches[0].clientY;\r\n\r\n      const deltaX = endX - startX;\r\n      const deltaY = endY - startY;\r\n      // left to right swipe\r\n      if (deltaX > 100 && Math.abs(deltaY) < 50) {\r\n        this.prevData();\r\n      }\r\n      // right to left swipe\r\n      else if (deltaX < -100 && Math.abs(deltaY) < 50) {\r\n        this.nextData();\r\n      }\r\n    });\r\n  }\r\n\r\n  isSelectedFlag(f: any): boolean {\r\n    return this.selectedFlags?.some(\r\n      (selectedFlag: any) => selectedFlag?.flag.id === f.id\r\n    );\r\n  }\r\n\r\n  filteredNavigationItems() {\r\n    if (this.data?.isArchived) {\r\n      return this.navigationItems.filter((item) =>\r\n        ['Overview', 'History'].includes(item.section)\r\n      );\r\n    }\r\n    if (this.currentPath === '/invoice') {\r\n      return this.canUpdateInvoice\r\n        ? this.navigationItems\r\n        : this.navigationItems.filter((item) => item.section !== 'Status');\r\n    } else {\r\n      if (this.data?.status?.status === 'invoiced') {\r\n        return this.navigationItems.filter((item) => item.section !== 'Status');\r\n      } else {\r\n        return this.canUpdateStatus\r\n          ? this.navigationItems\r\n          : this.navigationItems.filter((item) => item.section !== 'Status');\r\n      }\r\n    }\r\n  }\r\n\r\n  getActiveBackgroundColor(flag: any): any {\r\n    const bgColor: any = {};\r\n    if (flag.isActive) {\r\n      bgColor['border-black fw-600'] = true;\r\n      bgColor['bg-white'] = true;\r\n    }\r\n    return bgColor;\r\n  }\r\n\r\n  isDataEqual(obj1: any, obj2: any): boolean {\r\n    return obj1.id === obj2.id;\r\n  }\r\n\r\n  // ✅ Method to force reset loading states\r\n  forceResetLoadingStates() {\r\n    this.isNextDataLoading = false;\r\n    this.isPreviousDataLoading = false;\r\n    console.log('🔧 Force reset loading states called');\r\n  }\r\n\r\n  prevData() {\r\n    if (this.foundData > 0) {\r\n      this.foundData--;\r\n      this.data = this.cardData?.[this.foundData];\r\n      this.updateFlags();\r\n    } else if (this.foundData === 0 && !this.isMobileView) {\r\n      this.foundData = 0;\r\n      this.shareDataService.emitFetchPreviousLeads(false);\r\n      this.subscribeToDataUpdated()\r\n      this.isPreviousDataLoading =\r\n        this.shareDataService.currentPageNumber === 1 ? false : true;\r\n    }\r\n    if (this.selectedSection === 'Status')\r\n      this.isCustomStatusEnabled\r\n        ? this.customStatusChangeComponent?.cleanStatusForm()\r\n        : this.statusChangeComponent.cleanStatusForm();\r\n    this.trackingService.trackFeature(`Web.Leads.Menu.Previouslead.Click`, this.data.id);\r\n  }\r\n\r\n  nextData() {\r\n    if (this.foundData < this.cardData?.length - 1) {\r\n      this.foundData++;\r\n      this.data = this.cardData?.[this.foundData];\r\n      this.updateFlags();\r\n      if (\r\n        (this.foundData === this.cardData?.length - 3 ||\r\n          this.foundData === this.cardData?.length - 1) &&\r\n        !this.isNextDataLoading &&\r\n        this.isMobileView\r\n      ) {\r\n        this.shareDataService.emitFetchNextLeads();\r\n        this.subscribeToDataUpdated()\r\n        this.isNextDataLoading = true;\r\n      }\r\n    } else if (\r\n      this.foundData === this.cardData?.length - 1 &&\r\n      !this.isMobileView\r\n    ) {\r\n      this.foundData = 0;\r\n      this.shareDataService.emitFetchNextLeads(false);\r\n      this.subscribeToDataUpdated()\r\n      this.isNextDataLoading = true;\r\n    }\r\n    if (this.selectedSection === 'Status')\r\n      this.statusChangeComponent?.cleanStatusForm();\r\n    this.trackingService.trackFeature(`Web.Leads.Menu.NextLead.Click`, this.data.id);\r\n  }\r\n\r\n  getNextLead() {\r\n    this.shareDataService.sendMiniBookingformData(this.data);\r\n  }\r\n\r\n  updateFlags() {\r\n    this.flagOptions = this.flagOptionsDefault;\r\n    const flags: any = {};\r\n    this.data?.customFlags?.forEach((flag: any) => {\r\n      flags[flag?.flag?.id] = true;\r\n    });\r\n    this.flagOptions = this.flagOptions?.map((flag: any) => {\r\n      if (flags[flag?.id]) {\r\n        return { ...flag, isActive: true };\r\n      }\r\n      return { ...flag };\r\n    });\r\n  }\r\n\r\n  getUserName(id: string): string {\r\n    return getAssignedToDetails(id, this.users, true) || '';\r\n  }\r\n\r\n  getProfession(profession: Profession): string {\r\n    return Profession[profession];\r\n  }\r\n\r\n  onSectionSelect(section: string) {\r\n    this.selectedSection = section;\r\n\r\n    // ✅ Force reset loading states when History section is selected\r\n    if (section === 'History') {\r\n      this.forceResetLoadingStates();\r\n    }\r\n\r\n    this.trackingService.trackFeature(`Web.Leads.Menu.${this.selectedSection.replace(/\\s+/g, '')}.Click`, this.data.id);\r\n  }\r\n\r\n  flagAction(flag: any): void {\r\n    flag.isActive = !flag?.isActive;\r\n    let payload = {\r\n      id: this.data.id,\r\n      flagId: flag?.id,\r\n    };\r\n    this.flagOptions = this.flagOptions.map((f: any) =>\r\n      f.id === flag.id ? { ...f, isActive: flag.isActive } : f\r\n    );\r\n    this.store.dispatch(new LeadPreviewChanged());\r\n    this.store.dispatch(new UpdateLeadsTagInfo(payload, this.data.id, false));\r\n    this.trackingService.trackFeature(`Web.Leads.Button.Tags.Click`, this.data.id);\r\n  }\r\n\r\n  sortFlags() {\r\n    this.flagOptions.sort((a, b) => {\r\n      if (a.isActive === b.isActive) {\r\n        const nameA = a.name.toUpperCase();\r\n        const nameB = b.name.toUpperCase();\r\n        return nameA.localeCompare(nameB);\r\n      } else {\r\n        return a.isActive ? -1 : 1;\r\n      }\r\n    });\r\n  }\r\n\r\n  updateNotes(data: any[]): void {\r\n    this.data.notes = data;\r\n  }\r\n\r\n  checkStatus(section: string) {\r\n    this.onSectionSelect(section);\r\n    this.trackingService.trackFeature(`Web.Leads.Button.ChangeStatus.Click`, this.data.id);\r\n  }\r\n\r\n  openAudioPlayer(aP: TemplateRef<any>) {\r\n    if (this.modalRef) {\r\n      this.modalRef.hide();\r\n    }\r\n    if (this.data?.callRecordingUrls) {\r\n      this.callRecordingDetails = Object.entries(this.data?.callRecordingUrls)\r\n        .map((item: any) => {\r\n          return {\r\n            years: item[0],\r\n            yearData: Object.entries(item[1])\r\n              .reverse()\r\n              .map((item1: any) => {\r\n                return {\r\n                  months: item1[0],\r\n                  monthData: Object.keys(item1[1])\r\n                    .reverse()\r\n                    .map((key) => {\r\n                      let audioDate = key.toString();\r\n                      let audioFile = item1[1][key];\r\n                      return { date: audioDate, audioUrl: audioFile };\r\n                    }),\r\n                };\r\n              }),\r\n          };\r\n        })\r\n        .reverse();\r\n    }\r\n    this.modalRef = this.modalService.show(aP, {\r\n      class: 'right-modal modal-350 ph-modal-unset',\r\n    });\r\n  }\r\n\r\n  getCardData() {\r\n    if (this.foundData || this.cardData?.length) {\r\n      return;\r\n    }\r\n    let { cardData } = this.shareDataService.getCardData();\r\n    this.cardData = cardData;\r\n    this.foundData = this.cardData?.findIndex((item: any) =>\r\n      this.isDataEqual(item, this.data)\r\n    );\r\n    // ✅ Force reset loading states when getting card data\r\n    this.isNextDataLoading = false;\r\n    this.isPreviousDataLoading = false;\r\n    console.log('🔧 Card data loaded - Loading states reset:', {\r\n      isNextDataLoading: this.isNextDataLoading,\r\n      isPreviousDataLoading: this.isPreviousDataLoading,\r\n      foundData: this.foundData,\r\n      cardDataLength: this.cardData?.length\r\n    });\r\n  }\r\n\r\n  hasPlotSubType(propertyTypes: any[]): boolean {\r\n    return propertyTypes?.some(type => type?.childType?.displayName === 'Plot');\r\n  }\r\n\r\n  getSubtypesTitle(): string {\r\n    return this.data?.enquiry?.propertyTypes?.map((type: any) => type?.childType?.displayName).join(', ') || '';\r\n  }\r\n\r\n  subscribeToDataUpdated() {\r\n    this.dataUpdated$ = this.shareDataService.dataUpdated.subscribe(\r\n      ({ cardData, isPrevFetched }) => {\r\n        if (cardData?.length) {\r\n          this.cardData = cardData;\r\n          if (!this.isMobileView) {\r\n            const index = isPrevFetched ? cardData.length - 1 : 0;\r\n            this.data = cardData[index];\r\n            this.foundData = index;\r\n          }\r\n          this.isNextDataLoading = false;\r\n          this.isPreviousDataLoading = false;\r\n          this.dataUpdated$.unsubscribe();\r\n        }\r\n      }\r\n    );\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.stopper.next();\r\n    this.stopper.complete();\r\n    this.shareDataService.gotoOverviewTab(null);\r\n    if (this.isLeadPreviewChanged || this.fetchLeadsWhenClosed) {\r\n      this.store.dispatch(new ClearCardData())\r\n      this.fetchLeadsWhenClosed = false;\r\n      this.store.dispatch(\r\n        new FetchLeadList(true, location?.href?.includes('invoice'))\r\n      );\r\n    }\r\n    this.store.dispatch(new LeadPreviewSaved());\r\n    this.dataUpdated$?.unsubscribe();\r\n  }\r\n}\r\n", "<div id=\"swipeArea\">\r\n  <div class=\"px-20\" [ngClass]=\"{'blinking': (isNextDataLoading || isPreviousDataLoading) && !isMobileView}\">\r\n    <div class=\"flex-between mt-10\">\r\n      <div class=\"align-center mt-10\">\r\n        <div class=\"d-flex cursor-pointer\">\r\n          <span class=\"icon ic-black ic-xs ic-circle-chevron-left mr-8\" *ngIf=\"!whatsAppComp\"\r\n            (click)=\"modalRef.hide()\"></span>\r\n          <h4 class=\"fw-600\">{{ \"GLOBAL.lead\" | translate }} {{ \"LEADS.preview\" | translate }}</h4>\r\n        </div>\r\n      </div>\r\n      <div class=\"flex-end ph-d-none flex-grow-1\">\r\n        <ng-container *ngIf=\"!whatsAppComp\">\r\n          <div title=\"Previous Lead\" class=\"mt-4 bg-coal icon-badge\" (click)=\"prevData();getNextLead()\"\r\n            [ngClass]=\"{'bg-black-200 pe-none' : (foundData == 0 && isMobileView) || (!isMobileView && isFirstPage && foundData == 0), 'blinking': (isPreviousDataLoading && !isMobileView)}\">\r\n            <span class=\"icon ic-chevron-left m-auto ic-xxs\"></span>\r\n          </div>\r\n        </ng-container>\r\n        <div class=\"w-70\">\r\n          <leads-actions [data]=\"data\" [isLeadPreviewOpen]=\"true\" (changeSelection)=\"onSectionSelect($event)\"\r\n            [whatsAppComp]=\"whatsAppComp\" [showCommunicationCount]=\"showCommunicationCount\"></leads-actions>\r\n        </div>\r\n        <ng-container *ngIf=\"!whatsAppComp\">\r\n          <div title=\"Next Lead\" class=\"mt-4 bg-coal icon-badge\" (click)=\"nextData();getNextLead()\"\r\n            [ngClass]=\"{'bg-black-200 pe-none' : (foundData == (cardData?.length - 1) && isMobileView) || (totalPages === currentPage && foundData == (cardData?.length - 1)), 'blinking': (isNextDataLoading && foundData == (cardData?.length - 1)) || (isNextDataLoading && !isMobileView)}\">\r\n            <span class=\"icon ic-chevron-right m-auto ic-xxs\"></span>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </div>\r\n    <div class=\"bg-secondary my-16 p-16 br-4 text-mud text-sm ip-w-100-40\">\r\n      <h4 class=\"fw-600 text-coal text-truncate-1 break-all\">\r\n        {{ data?.name }}\r\n      </h4>\r\n      <div class=\"align-center mt-8\">\r\n        <ng-container *ngIf=\"!globalSettingsData?.isMaskedLeadContactNo\">\r\n          <span class=\"align-center\">\r\n            <span class=\"icon ic-Call mr-6 ic-slate-90 ic-xxxs\"></span>\r\n            <span>{{data?.contactNo ? data?.contactNo : \"---\" }}</span>\r\n            <!-- <a [href]=\"data.contactNo ? 'tel:' + data.contactNo : ''\">{{\r\n              data?.contactNo\r\n              }}</a> -->\r\n          </span>\r\n          <span class=\"align-center mr-20\">\r\n            <span class=\"icon ic-Call ic-slate-90 ic-xxxs mr-6 ml-20\"></span>\r\n            <span>{{data?.alternateContactNo ? data?.alternateContactNo : \"---\" }}</span>\r\n            <!-- <a [ngClass]=\"{ 'pe-none': !data.alternateContactNo }\" [href]=\"'tel:' + data.alternateContactNo\">{{\r\n              data?.alternateContactNo ? data?.alternateContactNo : \"---\" }}</a> -->\r\n          </span>\r\n          <span class=\"align-center mr-20\">\r\n            <span class=\"icon ic-Call ic-slate-90 ic-xxxs mr-6 ml-20\"></span>\r\n            <span>{{data?.landLine ? data?.landLine : \"---\" }}</span>\r\n          </span>\r\n          <span class=\"align-center\">\r\n            <span class=\"icon ic-mail ic-slate-90 ic-xxs mr-6 ml-2\"></span>\r\n            <span class=\"text-truncate-1 break-all\">{{data?.email ? data?.email : \"---\" }}</span>\r\n            <!-- <a [href]=\"'mailto:' + data.email\" [ngClass]=\"{ 'pe-none': !data.email }\">\r\n              {{ data?.email ? data.email : \"---\" }}</a> -->\r\n          </span>\r\n        </ng-container>\r\n      </div>\r\n      <div class=\"align-center mt-8\">\r\n        <span class=\"icon ic-location-circle ic-slate-90 ic-xxs mr-6\"></span>\r\n        <drag-scroll class=\"scrollbar scroll-hide\">\r\n          <div class=\"text-truncate-1 break-all\">{{addresses}}\r\n          </div>\r\n        </drag-scroll>\r\n      </div>\r\n      <div class=\"d-flex mt-8\">\r\n        <span class=\"icon ic-apartment ic-slate-90 ic-xxs mr-6\"></span>\r\n        <drag-scroll class=\"scrollbar scroll-hide\">\r\n          <div class=\"align-center text-nowrap\">\r\n            <span class=\"align-center\" *ngFor=\"let enquiry of data?.enquiry?.enquiryTypes || ['']; let last = last\">\r\n              <span class=\"text-accent-green text-sm fw-600\">{{enquiry\r\n                ? EnquiryType[enquiry] : \"--\"}}</span>\r\n              <span *ngIf=\"!last\" class=\"dot dot-xxs bg-accent-green mx-6\"></span>\r\n            </span>\r\n            <ng-container *ngIf=\"data?.enquiry?.propertyTypes?.length\">\r\n              <span class=\"dot dot-xxs bg-dark-700 mx-6\"></span>\r\n              <span>{{ data.enquiry.propertyTypes?.[0]?.displayName }}</span>\r\n            </ng-container>\r\n            <ng-container *ngIf=\"data?.enquiry?.propertyTypes?.length\">\r\n              <span class=\"dot dot-xxs bg-dark-700 mx-6\"></span>\r\n              <ng-container *ngIf=\"data?.enquiry?.propertyTypes?.length; else noSubtype\">\r\n                <ng-container *ngFor=\"let type of data?.enquiry?.propertyTypes; let last = last\">\r\n                  <span *ngIf=\"type?.childType?.displayName\">\r\n                    {{ type.childType.displayName}}<span *ngIf=\"!last\">, </span>\r\n                  </span>\r\n                </ng-container>\r\n              </ng-container>\r\n              <ng-template #noSubtype>\r\n                <h6>--</h6>\r\n              </ng-template>\r\n            </ng-container>\r\n            <ng-container *ngIf=\"data?.enquiry?.bhKs?.length\">\r\n              <span class=\"dot dot-xxs bg-dark-700 mx-6\"></span>\r\n              <span>{{ bhkNo && bhkNo?.length > 0 ? bhkNo : '' }}</span>\r\n            </ng-container>\r\n            <ng-container *ngIf=\"!globalSettingsData?.isCustomLeadFormEnabled\">\r\n              <span class=\"dot dot-xxs bg-dark-700 mx-6\"></span>\r\n              <span>{{ bhkTypes }}</span>\r\n            </ng-container>\r\n            <ng-container *ngIf=\"data?.enquiry?.lowerBudget || data?.enquiry?.upperBudget\">\r\n              <span class=\"dot dot-xxs bg-dark-700 mx-6\"></span>\r\n              <span>{{ formatBudget(data?.enquiry?.lowerBudget, data?.enquiry?.currency || defaultCurrency) }}</span>\r\n              <span *ngIf=\"data?.enquiry?.lowerBudget && data?.enquiry?.upperBudget\">-\r\n                {{ formatBudget(data?.enquiry?.upperBudget, data?.enquiry?.currency || defaultCurrency) }}</span>\r\n            </ng-container>\r\n          </div>\r\n        </drag-scroll>\r\n      </div>\r\n    </div>\r\n    <div class=\"Horizontal-navbar\">\r\n      <ng-container *ngFor=\"let item of filteredNavigationItems()\">\r\n        <div class=\"nav-item\" [ngClass]=\"{ active: selectedSection === item.section }\"\r\n          (click)=\"onSectionSelect(item.section)\">\r\n          {{ item.label | translate }}\r\n        </div>\r\n      </ng-container>\r\n    </div>\r\n    <div *ngIf=\"selectedSection == 'Overview'\">\r\n      <div class=\"ph-h-100-275 scrollbar pr-12\" [ngClass]=\"whatsAppComp ? 'h-100-377' : 'h-100-250'\">\r\n        <div class=\"flex-between mt-8\" *ngIf=\"canUpdateStatus\">\r\n          <div class=\"field-label mt-0\">\r\n            {{ \"GLOBAL.lead\" | translate }} {{ \"GLOBAL.status\" | translate }}\r\n          </div>\r\n          <div class=\"btn btn-sm btn-linear-green text-nowrap br-8 mr-10\" (click)=\"checkStatus('Status')\"\r\n            *ngIf=\"currentPath === '/invoice' ? (canUpdateInvoice && !data?.isArchived) : canEditLead && !(!canUpdateBookedLead && data?.status?.displayName == 'Booked') && !data?.isArchived && data?.status?.status !== 'invoiced' && data?.assignTo !== EMPTY_GUID\">\r\n            {{ \"LEADS.change-status\" | translate }}\r\n          </div>\r\n        </div>\r\n        <div class=\"bg-secondary mt-12 mr-12 px-16 py-12 br-4\" *ngIf=\"canUpdateStatus\">\r\n          <div class=\"align-center w-100\">\r\n            <div class=\"align-center w-50\">\r\n              <span class=\"icon ic-person-walking ic-slate-90 ic-xxs mr-8\"></span>\r\n              <h5 class=\"fv-sm-caps fw-600\">{{ data?.status?.displayName }}<span\r\n                  *ngIf=\"data?.status?.childType?.displayName\"> - {{data.status.childType.displayName}}</span></h5>\r\n            </div>\r\n            <div class=\"align-center w-50\" *ngIf=\"data.scheduledDate\">\r\n              <span class=\"icon ic-alarm ic-slate-90 ic-xxs mr-8\"></span>\r\n              <div>\r\n                <h5 class=\"fv-sm-caps fw-600\">\r\n                  {{data.scheduledDate ? getTimeZoneDate(data.scheduledDate,userData?.timeZoneInfo?.baseUTcOffset,\r\n                  'dateWithTime') :\r\n                  \"---\"}}\r\n                </h5>\r\n                <div class=\"text-truncate-1 break-all text-sm\"\r\n                  *ngIf=\"userData?.timeZoneInfo?.timeZoneName && data.scheduledDate && userData?.shouldShowTimeZone\">\r\n                  ({{userData?.timeZoneInfo?.timeZoneName }})\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"d-flex mt-16\" *ngIf=\"data?.notes\">\r\n            <span class=\"icon ic-message-lines ic-slate-90 ic-xxs mr-8\"></span>\r\n            <p [innerHTML]=\"data?.notes ? convertUrlsToLinks(data.notes) : '---'\"\r\n              class=\"text-black-20 text-sm word-break line-break text-truncate-2\">\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div\r\n          *ngIf=\"currentPath === '/invoice' ? (canUpdateInvoice && !data?.isArchived) : (data?.status?.actionName !== 'Invoiced' && canEditTags && !data?.isArchived)\">\r\n          <div class=\"field-label\">{{ \"LEADS.tags\" | translate }}</div>\r\n          <div class=\"d-flex flex-wrap bg-white\" [ngClass]=\"{'pe-none': !canEditTags}\">\r\n            <ng-container *ngFor=\"let flag of flagOptions\">\r\n              <div class=\"align-center br-4 cursor-pointer p-6 mr-10 mt-6 border\"\r\n                [ngClass]=\"getActiveBackgroundColor(flag)\" (click)=\"flagAction(flag)\">\r\n                <div class=\"flex-center w-16 h-16 br-50 mr-4\" [ngClass]=\"{'bg-white': flag.isActive }\">\r\n                  <img [type]=\"'leadrat'\" [appImage]=\"flag.isActive ? flag?.activeImagePath : flag?.inactiveImagePath\"\r\n                    class=\"w-16 h-16\" alt=\"\">\r\n                </div>\r\n                <span [ngClass]=\"{'opacity-5': !flag.isActive}\">{{ flag.name | translate }}</span>\r\n              </div>\r\n            </ng-container>\r\n          </div>\r\n        </div>\r\n        <individual-reassign [data]=\"data\" [isLastLead]=\"isLastLead\"\r\n          [whatsAppComp]=\"whatsAppComp\"></individual-reassign>\r\n        <div *ngIf=\"data?.callRecordingUrls\">\r\n          <div class=\"field-label\">{{ \"LEADS.ivr-call-recordings\" | translate }}</div>\r\n          <div class=\"flex-between mr-12 py-12 px-10 bg-secondary br-4\">\r\n            <div class=\"align-center\">\r\n              <span class=\"icon ic-ivr-printer ic-black ic-large mr-16\"></span>\r\n              <h5 class=\"text-dark fw-semi-bold\">\r\n                {{ \"LEADS.call-are-recorded\" | translate }}\r\n              </h5>\r\n            </div>\r\n            <div class=\"btn btn-sm btn-linear-green w-110 flex-center\" (click)=\"\r\n                data.assignTo !== EMPTY_GUID\r\n                  ? openAudioPlayer(audioOption)\r\n                  : openUnassignModal()\r\n              \">\r\n              View all\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"field-label\">{{ \"GLOBAL.lead\" | translate }} {{ \"LEADS.enquiry-info\" | translate }}</div>\r\n        <div class=\"bg-secondary p-12 br-4 fw-semi-bold text-large\">\r\n          <div class=\"align-center flex-wrap\">\r\n            <div class=\"mr-40 ip-mr-10\">\r\n              <div class=\"mb-4 text-black-200 text-sm fv-sm-caps\">\r\n                Gender\r\n              </div>\r\n              <h5 class=\"fw-600 text-truncate-1 break-all\">{{ Gender[data.gender] ?? '--' }}</h5>\r\n            </div>\r\n            <div class=\"mr-40 ip-mr-10\">\r\n              <div class=\"mb-4 text-black-200 text-sm fv-sm-caps\">\r\n                Date of Birth\r\n              </div>\r\n              <h5 class=\"fw-600 text-truncate-1 break-all\">{{ data.dateOfBirth ?\r\n                getTimeZoneDate(data.dateOfBirth,'00:00:00',\r\n                'dayMonthYear') :\r\n                '--' }}</h5>\r\n            </div>\r\n            <div class=\"mr-40 ip-mr-10\">\r\n              <div class=\"mb-4 text-black-200 text-sm fv-sm-caps\">\r\n                Marital Status\r\n              </div>\r\n              <h5 class=\"fw-600 text-truncate-1 break-all\">{{ MaritalStatusType[data.maritalStatus] ?? '--' }}</h5>\r\n            </div>\r\n            <div class=\"mr-40 ip-mr-10\" *ngIf=\"canViewLeadSource\">\r\n              <div class=\"mb-4 text-black-200 text-sm fv-sm-caps\">\r\n                {{ \"GLOBAL.lead\" | translate }} {{ \"LEADS.source\" | translate }}\r\n              </div>\r\n              <h5 class=\"fw-600 text-truncate-1 break-all\">{{ LeadSource[data.enquiry?.leadSource] ?\r\n                LeadSource[data.enquiry?.leadSource] : '---'\r\n                }}</h5>\r\n            </div>\r\n            <div class=\"mr-40 ip-mr-10\" *ngIf=\"canViewLeadSource\">\r\n              <div class=\"mb-4 text-black-200 text-sm fv-sm-caps\">\r\n                {{ \"LEADS.sub-source\" | translate }}\r\n              </div>\r\n              <h5 class=\"fw-600 text-wrap\">{{ data.enquiry?.subSource ? data.enquiry?.subSource :'--' }}</h5>\r\n            </div>\r\n            <div class=\"mr-40 ip-mr-10\" [ngClass]=\"{'mt-12': canViewLeadSource}\">\r\n              <div class=\"mb-4 text-black-200 text-sm fv-sm-caps\">{{ \"LEAD_FORM.enquired-for\" | translate }}\r\n              </div>\r\n              <h5 class=\"fw-600\">{{ enquiryTypes }}\r\n              </h5>\r\n            </div>\r\n            <div [ngClass]=\"{'mt-12': canViewLeadSource}\">\r\n              <div class=\"mb-4 text-black-200 text-sm fv-sm-caps\">Purpose\r\n              </div>\r\n              <h5 class=\"fw-600\">{{ PurposeType[data.enquiry?.purpose] ?\r\n                PurposeType[data.enquiry?.purpose] : '---' }}\r\n              </h5>\r\n            </div>\r\n          </div>\r\n          <div class=\"align-center mt-16\">\r\n            <span class=\"icon ic-apartment ic-accent-green ic-xxs mr-8\"></span>\r\n            <h5 class=\"text-accent-green fw-700\">{{ \"GLOBAL.details\" | translate }}</h5>\r\n          </div>\r\n          <div class=\"mt-10\" *ngIf=\"data?.properties?.length != 0\">\r\n            <div class=\"text-black-200 fv-sm-caps\">{{ \"LABEL.property\" | translate }}</div>\r\n            <div class=\"d-flex flex-wrap\">\r\n              <div *ngFor=\"let value of data?.properties\">\r\n                <div *ngIf=\"value?.title\" class=\"py-4 px-8 bg-black-100 br-20 text-gray-110 mr-4 mt-6\">\r\n                  {{ value?.title }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"mt-20\" *ngIf=\"data?.projects?.length != 0\">\r\n            <div class=\"text-black-200 fv-sm-caps\">{{ \"SIDEBAR.project\" | translate }}</div>\r\n            <div class=\"d-flex flex-wrap\">\r\n              <div *ngFor=\"let value of data?.projects\">\r\n                <div *ngIf=\"value?.name\" class=\"py-4 px-8 bg-black-100 br-20 text-gray-110 mr-4 mt-6\">\r\n                  {{ value?.name }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"d-flex flex-wrap w-100\">\r\n            <div class=\"w-25 ip-w-33 ph-w-50 pr-10\">\r\n              <div class=\"fv-sm-caps mb-4 mt-16 text-black-200\">Property {{ \"LABEL.type\" | translate }}</div>\r\n              <h5 class=\"fw-600\">\r\n                {{data?.enquiry?.propertyTypes?.[0]?.displayName ? data?.enquiry?.propertyTypes?.[0]?.displayName :\r\n                \"--\"}}\r\n              </h5>\r\n            </div>\r\n            <div class=\"w-25 ip-w-33 ph-w-50 pr-10\">\r\n              <div class=\"fv-sm-caps mb-4 mt-16 text-black-200\">Property Sub-type </div>\r\n              <div class=\"fw-600 mt-4 mr-30 text-truncate-1 break-all ip-mr-10 header-5 cursor-pointer\">\r\n                <ng-container *ngIf=\"data?.enquiry?.propertyTypes?.length; else noSubtype\">\r\n                  <ng-container *ngFor=\"let type of data?.enquiry?.propertyTypes; let last = last\">\r\n                    <span [title]=\"getSubtypesTitle()\">\r\n                      {{ type.childType.displayName }}<span *ngIf=\"!last\">, </span>\r\n                    </span>\r\n                  </ng-container>\r\n                </ng-container>\r\n\r\n                <ng-template #noSubtype>\r\n                  --\r\n                </ng-template>\r\n              </div>\r\n            </div>\r\n            <ng-container\r\n              *ngIf=\"data?.enquiry?.propertyTypes?.[0]?.displayName == 'Residential' &&  !hasPlotSubType(data?.enquiry?.propertyTypes)\">\r\n              <div *ngIf=\"!globalSettingsData?.isCustomLeadFormEnabled\" class=\"w-25 ip-w-33 ph-w-50 pr-10\">\r\n                <div class=\"fv-sm-caps mb-4 mt-16 text-black-200\">\r\n                  {{ 'BHK' }}\r\n                </div>\r\n                <h5 class=\"fw-600\">{{bhkNo && bhkNo?.length > 0 ? bhkNo : '--'}}</h5>\r\n              </div>\r\n              <div class=\"w-25 ip-w-33 ph-w-50 pr-10\" *ngIf=\"!globalSettingsData?.isCustomLeadFormEnabled\">\r\n                <div class=\"fv-sm-caps mb-4 mt-16 text-black-200\">{{ \"PROPERTY.bhk\" | translate }} {{ \"LABEL.type\" |\r\n                  translate }}</div>\r\n                <h5 class=\"fw-600\">{{bhkTypes}}</h5>\r\n              </div>\r\n              <ng-container *ngIf=\"globalSettingsData?.isCustomLeadFormEnabled\">\r\n                <div class=\"w-25 ip-w-33 ph-w-50 pr-10\">\r\n                  <div class=\"fv-sm-caps mb-4 mt-16 text-black-200\">Beds</div>\r\n                  <h5 class=\"fw-600 text-truncate-1 break-all\" [title]=\"beds\">{{beds?.length ? beds : '--'}}</h5>\r\n                </div>\r\n                <div class=\"w-25 ip-w-33 ph-w-50 pr-10\">\r\n                  <div class=\"fv-sm-caps mb-4 mt-16 text-black-200\">Baths</div>\r\n                  <h5 class=\"fw-600 text-truncate-1 break-all\" [title]=\"data?.enquiry?.baths\">{{data?.enquiry?.baths &&\r\n                    data?.enquiry?.baths?.length > 0 ? data?.enquiry?.baths :\r\n                    '--'}}</h5>\r\n                </div>\r\n              </ng-container>\r\n            </ng-container>\r\n            <ng-container *ngIf=\"globalSettingsData?.isCustomLeadFormEnabled\">\r\n              <div class=\"w-25 ip-w-33 ph-w-50 pr-10\"\r\n                *ngIf=\"data?.enquiry?.propertyTypes?.[0]?.displayName == 'Commerical' || data?.enquiry?.propertyTypes?.[0]?.displayName == 'Residential'\">\r\n                <div class=\"fv-sm-caps mb-4 mt-16 text-black-200\">Furnish Status</div>\r\n                <h5 class=\"fw-600\">{{data?.enquiry?.furnished ? FurnishStatus[data?.enquiry?.furnished] : '--'}}</h5>\r\n              </div>\r\n              <div class=\"w-25 ip-w-33 ph-w-50 pr-10\"\r\n                *ngIf=\"data?.enquiry?.propertyTypes?.[0]?.displayName == 'Commerical' || data?.enquiry?.propertyTypes?.[0]?.displayName == 'Residential'\">\r\n                <div class=\"fv-sm-caps mb-4 mt-16 text-black-200\">Preferred Floors</div>\r\n                <h5 class=\"fw-600\">{{data?.enquiry?.floors && data?.enquiry?.floors?.length > 0 ?\r\n                  data?.enquiry?.floors : '--'}}</h5>\r\n              </div>\r\n              <div class=\"w-25 ip-w-33 ph-w-50 pr-10\">\r\n                <div class=\"fv-sm-caps mb-4 mt-16 text-black-200\">Offering Type</div>\r\n                <h5 class=\"fw-600\">{{data?.enquiry?.offerType ? OfferType[data?.enquiry?.offerType] : '--'}}</h5>\r\n              </div>\r\n            </ng-container>\r\n            <div class=\"w-25 ip-w-33 ph-w-50 pr-10\">\r\n              <div class=\"fv-sm-caps mb-4 mt-16 text-black-200\">Min. {{ \"LABEL.budget\" | translate }}</div>\r\n              <h5 class=\"fw-600\">\r\n                {{ data?.enquiry?.lowerBudget ? formatBudget(data?.enquiry?.lowerBudget, data?.enquiry?.currency) :\r\n                \"--\"\r\n                }}\r\n              </h5>\r\n            </div>\r\n            <div class=\"w-25 ip-w-33 ph-w-50 pr-10\">\r\n              <div class=\"fv-sm-caps mb-4 mt-16 text-black-200\">Max. {{ \"LABEL.budget\" | translate }}</div>\r\n              <h5 class=\"fw-600\">{{ data?.enquiry?.upperBudget ? formatBudget(data?.enquiry?.upperBudget,\r\n                data?.enquiry?.currency) : \"--\" }}\r\n              </h5>\r\n            </div>\r\n          </div>\r\n          <div *ngIf=\"data?.enquiry?.addresses?.length\" class=\"align-center mt-16\">\r\n            <span class=\"icon ic-location-circle ic-xxs ic-accent-green mr-8\"></span>\r\n            <h5 class=\"fw-700 text-accent-green\">{{ \"LOCATION.location\" | translate }}</h5>\r\n          </div>\r\n          <div class=\"mb-10\" *ngFor=\"let address of data?.enquiry?.addresses; let last = last\"\r\n            [ngClass]=\"{'border-bottom pb-4': !last}\">\r\n            <div class=\"align-center mt-12\">\r\n              <span class=\"dot dot-xxs bg-slate-250 mr-8\"> </span>\r\n              <span>{{getLocationDetailsByObj(address)}}\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div class=\"align-center mt-16\">\r\n            <span class=\"icon ic-paper-clip ic-xxs ic-accent-green mr-8\"></span>\r\n            <h5 class=\"fw-700 text-accent-green\">{{ \"SIDEBAR.others\" | translate }}</h5>\r\n          </div>\r\n          <div class=\"w-100 d-flex flex-wrap\">\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">{{'INTEGRATION.agency-name' | translate}}</h5>\r\n              <div class=\"fw-600 mt-4 mr-30 text-truncate-1 break-all ip-mr-10 header-5 cursor-pointer\">\r\n                <ng-container *ngIf=\"data?.agencies && data?.agencies?.length > 0; else noAgencies\">\r\n                  <ng-container *ngFor=\"let agency of data?.agencies; let last = last\">\r\n                    {{ agency.name }}<span *ngIf=\"!last\">, </span>\r\n                  </ng-container>\r\n                </ng-container>\r\n                <ng-template #noAgencies>\r\n                  --\r\n                </ng-template>\r\n              </div>\r\n            </div>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">{{'GLOBAL.assign-from' | translate}}</h5>\r\n              <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\">{{getAssignedToDetails(data.assignedFrom,\r\n                users, true) || '--'}}</h5>\r\n            </div>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">{{'AUTH.company-name' | translate}}</h5>\r\n              <h5 class=\"fw-600 mt-4 text-truncate-1 word-break line-break mr-30 ip-mr-10\"> {{data.companyName ?\r\n                data.companyName: \"--\"}}\r\n              </h5>\r\n            </div>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">{{'LEADS.carpet-area' | translate}}</h5>\r\n              <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\"> {{data?.enquiry?.carpetArea ?\r\n                data?.enquiry?.carpetArea: \"--\"}} {{data?.enquiry?.carpetArea && data?.enquiry?.carpetAreaUnit ?\r\n                data?.enquiry?.carpetAreaUnit : ''}}\r\n              </h5>\r\n            </div>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">Built-up Area</h5>\r\n              <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\"> {{data?.enquiry?.builtUpArea ?\r\n                data?.enquiry?.builtUpArea:\r\n                \"--\"}} {{data?.enquiry?.builtUpArea && data?.enquiry?.builtUpAreaUnit ?\r\n                data?.enquiry?.builtUpAreaUnit : ''}}\r\n              </h5>\r\n            </div>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">Saleable Area</h5>\r\n              <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\"> {{data?.enquiry?.saleableArea ?\r\n                data?.enquiry?.saleableArea:\r\n                \"--\"}} {{data?.enquiry?.saleableArea && data?.enquiry?.saleableAreaUnit ?\r\n                data?.enquiry?.saleableAreaUnit : ''}}\r\n              </h5>\r\n            </div>\r\n            <ng-container *ngIf=\"globalSettingsData?.isCustomLeadFormEnabled\">\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Property Area</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\"> {{data?.enquiry?.propertyArea ?\r\n                  data?.enquiry?.propertyArea:\r\n                  \"--\"}} {{data?.enquiry?.propertyArea && data?.enquiry?.propertyAreaUnit ?\r\n                  data?.enquiry?.propertyAreaUnit : ''}}\r\n                </h5>\r\n              </div>\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Net Area</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\"> {{data?.enquiry?.netArea ?\r\n                  data?.enquiry?.netArea:\r\n                  \"--\"}} {{data?.enquiry?.netArea && data?.enquiry?.netAreaUnit ?\r\n                  data?.enquiry?.netAreaUnit : ''}}\r\n                </h5>\r\n              </div>\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Unit Number/Name</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 word-break line-break mr-30 ip-mr-10\"> {{data.enquiry?.unitName ?\r\n                  data?.enquiry?.unitName:\"--\" }}\r\n                </h5>\r\n              </div>\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Cluster Name</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 word-break line-break mr-30 ip-mr-10\">\r\n                  {{data.enquiry?.clusterName ?\r\n                  data?.enquiry?.clusterName:\"--\" }}\r\n                </h5>\r\n              </div>\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Nationality\r\n                </h5>\r\n                <h5 [title]=\"data?.nationality\"\r\n                  class=\"fw-600 mt-4 text-truncate-1 word-break line-break mr-30 ip-mr-10\">\r\n                  {{data?.nationality ?\r\n                  data?.nationality:\"--\" }}\r\n                </h5>\r\n              </div>\r\n            </ng-container>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">Deleted Date</h5>\r\n              <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\"> {{\r\n                data.archivedOn\r\n                ? getTimeZoneDate( data.archivedOn, userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYearText')\r\n                : \"---\"\r\n                }}\r\n              </h5>\r\n              <div class=\"text-truncate-1 break-all text-sm\"\r\n                *ngIf=\"userData?.timeZoneInfo?.timeZoneName && data.archivedOn && userData?.shouldShowTimeZone\">\r\n                ({{userData?.timeZoneInfo?.timeZoneName }})\r\n              </div>\r\n            </div>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">Possession Needed By</h5>\r\n              <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\"> {{data.enquiry?.possessionDate ?\r\n                getTimeZoneDate(data.enquiry?.possessionDate, userData?.timeZoneInfo?.baseUTcOffset,\r\n                'dayMonthYearText')\r\n                : \"---\"\r\n                }}\r\n              </h5>\r\n              <div class=\"text-truncate-1 break-all text-sm\"\r\n                *ngIf=\"userData?.timeZoneInfo?.timeZoneName && data.enquiry?.possessionDate && userData?.shouldShowTimeZone\">\r\n                ({{userData?.timeZoneInfo?.timeZoneName }})\r\n              </div>\r\n            </div>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">Referral Name</h5>\r\n              <h5 class=\"fw-600 mt-4 text-truncate-1 word-break line-break mr-30 ip-mr-10\"> {{data.referralName ?\r\n                data.referralName:\"--\" }}\r\n              </h5>\r\n            </div>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">Referral Number</h5>\r\n              <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\"> {{data.referralContactNo ?\r\n                data.referralContactNo:\"--\" }}\r\n              </h5>\r\n            </div>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">Referral Email</h5>\r\n              <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\"> {{data.referralEmail ?\r\n                data.referralEmail : \"--\" }}\r\n              </h5>\r\n            </div>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">Serial Number</h5>\r\n              <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\"> {{data.serialNumber ?\r\n                data.serialNumber: \"--\"}}</h5>\r\n            </div>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">{{ \"LEADS.sourcing-manager\" | translate }}</h5>\r\n              <h5 class=\"fw-600 mt-4 mr-30 text-truncate-1 break-all ip-mr-10\">{{data?.sourcingManager ?\r\n                getUserName(data.sourcingManager) :\r\n                \"--\"}}</h5>\r\n            </div>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">{{ \"LEADS.closing-manager\" | translate }}</h5>\r\n              <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\">\r\n                {{data?.closingManager ? getUserName(data?.closingManager) : \"--\"}}\r\n              </h5>\r\n            </div>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">{{ \"LEAD_FORM.channel-partner-name\" | translate }}</h5>\r\n              <h5 class=\"fw-600 mt-4 mr-30 text-truncate-1 ip-mr-10\">\r\n                <ng-container *ngIf=\"data?.channelPartners?.length else empty\">\r\n                  <ng-container *ngFor=\"let partner of data?.channelPartners; let last = last\">\r\n                    {{partner?.firmName}}<ng-container *ngIf=\"!last\">,\r\n                    </ng-container></ng-container>\r\n                </ng-container><ng-template #empty>--</ng-template>\r\n              </h5>\r\n            </div>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">{{'LEAD_FORM.campaign-name' | translate}}</h5>\r\n              <div class=\"fw-600 mt-4 mr-30 text-truncate-1 break-all ip-mr-10 header-5 cursor-pointer\">\r\n                <ng-container *ngIf=\"data?.campaigns && data?.campaigns?.length > 0; else noCampaign\">\r\n                  <ng-container *ngFor=\"let campaign of data?.campaigns; let last = last\">\r\n                    {{ campaign.name }}<span *ngIf=\"!last\">, </span>\r\n                  </ng-container>\r\n                </ng-container>\r\n                <ng-template #noCampaign>\r\n                  --\r\n                </ng-template>\r\n              </div>\r\n            </div>\r\n            <div class=\"w-33 ip-w-50 mt-12\">\r\n              <h5 class=\"fv-sm-caps text-black-200\">{{ \"LEADS.profession\" | translate }}</h5>\r\n              <h5 class=\"fw-600 mt-4 mr-30 text-truncate-1 break-all ip-mr-10\">\r\n                {{data?.profession ? getProfession(data.profession) : \"--\"}}\r\n              </h5>\r\n            </div>\r\n\r\n            <div class=\"w-100 ip-w-50 mt-12\">\r\n              <div class=\"fv-sm-caps text-black-200\">{{ \"LEADS.customer-location\" | translate }}</div>\r\n              <h5 class=\"fw-600 mt-4 mr-30 text-truncate-1 break-all text-wrap ip-mr-10\">\r\n                {{ data?.address?.subLocality && data?.address?.city && data?.address?.state ?\r\n                data?.address?.subLocality\r\n                +\r\n                \", \" +\r\n                data?.address?.city + \", \" + data?.address?.state : data?.address?.city\r\n                ? data?.address?.city : \"--\"}}\r\n              </h5>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <ng-container *ngIf=\"data?.additionalProperties\">\r\n          <div class=\"field-label\">Facebook Info</div>\r\n          <div class=\"bg-secondary p-12 br-4 fw-semi-bold text-large\">\r\n            <div class=\"w-100 d-flex flex-wrap\">\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Ad ID</h5>\r\n                <div class=\"fw-600 mt-4 mr-30 text-truncate-1 break-all ip-mr-10 header-5 cursor-pointer\">\r\n                  {{data?.additionalProperties?.AdId ? data?.additionalProperties?.AdId : '--'}}\r\n                </div>\r\n              </div>\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Ad Name</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\">\r\n                  {{data?.additionalProperties?.AdName ? data?.additionalProperties?.AdName : '--'}}\r\n                </h5>\r\n              </div>\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Ad Set ID</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\">\r\n                  {{data?.additionalProperties?.AdSetId ? data?.additionalProperties?.AdSetId : '--'}}\r\n                </h5>\r\n              </div>\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Ad Set Name</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\">\r\n                  {{data?.additionalProperties?.AdSetName ? data?.additionalProperties?.AdSetName : '--'}}\r\n                </h5>\r\n              </div>\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Page ID</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 word-break line-break mr-30 ip-mr-10\">\r\n                  {{data?.additionalProperties?.PageId ? data?.additionalProperties?.PageId : '--'}}\r\n                </h5>\r\n              </div>\r\n              <!-- <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Status</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\">\r\n                  {{data?.additionalProperties?.Status ? data?.additionalProperties?.Status : '--'}}\r\n                </h5>\r\n              </div> -->\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Campaign ID</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\">\r\n                  {{data?.additionalProperties?.CampaignId ? data?.additionalProperties?.CampaignId : '--'}}\r\n                </h5>\r\n              </div>\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Campaign Name</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\">\r\n                  {{data?.additionalProperties?.CampaignName ? data?.additionalProperties?.CampaignName : '--'}}\r\n                </h5>\r\n              </div>\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Ad Account ID</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\">\r\n                  {{data?.additionalProperties?.AdAccountId ? data?.additionalProperties?.AdAccountId : '--'}}\r\n                </h5>\r\n              </div>\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Ad Account Name</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\">\r\n                  {{data?.additionalProperties?.AdAccountName ? data?.additionalProperties?.AdAccountName : '--'}}\r\n                </h5>\r\n              </div>\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Name</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\">\r\n                  {{data?.additionalProperties?.Name ? data?.additionalProperties?.Name : '--'}}\r\n                </h5>\r\n              </div>\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Facebook ID</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\">\r\n                  {{data?.additionalProperties?.FacebookId ? data?.additionalProperties?.FacebookId : '--'}}\r\n                </h5>\r\n              </div>\r\n              <!-- <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">IsAutomated</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\">\r\n                  {{data?.additionalProperties?.IsAutomated ? data?.additionalProperties?.IsAutomated : '--'}}\r\n                </h5>\r\n              </div>\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Automation ID</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\">\r\n                  {{data?.additionalProperties?.AutomationId !== EMPTY_GUID ? data?.additionalProperties?.AutomationId :\r\n                  '--'}}\r\n                </h5>\r\n              </div>\r\n              <div class=\"w-33 ip-w-50 mt-12\">\r\n                <h5 class=\"fv-sm-caps text-black-200\">Is Subscribed</h5>\r\n                <h5 class=\"fw-600 mt-4 text-truncate-1 break-all mr-30 ip-mr-10\">\r\n                  {{data?.additionalProperties?.IsSubscribed ? data?.additionalProperties?.IsSubscribed : '--'}}\r\n                </h5>\r\n              </div> -->\r\n            </div>\r\n          </div>\r\n        </ng-container>\r\n        <div class=\"border-bottom-slate-20 mt-16\"></div>\r\n        <div class=\"field-label\">{{ \"LEADS.last-activity\" | translate }}</div>\r\n        <div class=\"w-100 d-flex ph-flex-col mt-6\">\r\n          <div class=\"align-center\">\r\n            <h5 class=\"fw-700 text-mud mr-6\">{{ \"LEADS.created-by\" | translate }}</h5>\r\n            <div class=\"text-sm\">\r\n              <div class=\"fw-semi-bold mb-4\">{{getAssignedToDetails(data.createdBy,\r\n                users, true) || ''}}</div>\r\n              <div class=\"text-black-200\">At\r\n                {{ getTimeZoneDate(data.createdOn,userData?.timeZoneInfo?.baseUTcOffset) }}</div>\r\n              <div class=\"text-truncate-1 break-all text-sm\"\r\n                *ngIf=\"userData?.timeZoneInfo?.timeZoneName && data.createdOn && userData?.shouldShowTimeZone\">\r\n                ({{userData?.timeZoneInfo?.timeZoneName }})\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"border-left mx-20\"></div>\r\n          <div class=\"align-center ph-mt-10\">\r\n            <h5 class=\"fw-700 text-mud mr-6\">{{ \"LEADS.modified-by\" | translate }}</h5>\r\n            <div class=\"text-sm\">\r\n              <div class=\"fw-semi-bold mb-4\">{{getAssignedToDetails(data?.lastModifiedBy,\r\n                users, true) || ''}}</div>\r\n              <div class=\"text-black-200\">At\r\n                {{ getTimeZoneDate(data.lastModifiedOn, userData?.timeZoneInfo?.baseUTcOffset) }}</div>\r\n              <div class=\"text-truncate-1 break-all text-sm\"\r\n                *ngIf=\"userData?.timeZoneInfo?.timeZoneName && data.lastModifiedOn && userData?.shouldShowTimeZone\">\r\n                ({{userData?.timeZoneInfo?.timeZoneName }})\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- DEBUG: Next={{isNextDataLoading}}, Prev={{isPreviousDataLoading}}, Mobile={{isMobileView}}, Blinking={{(isNextDataLoading || isPreviousDataLoading) && !isMobileView}} -->\r\n  <span [ngClass]=\"{'blinking': (isNextDataLoading || isPreviousDataLoading) && !isMobileView}\">\r\n    <div *ngIf=\"selectedSection == 'Status'\">\r\n      <div class=\"w-100 mt-20\">\r\n        <status-change *ngIf=\"!isCustomStatusEnabled\" #statusChangeComponent [leadInfo]=\"data\"\r\n          [canShowStatusPopupInPreview]=\"canShowStatusPopupInPreview && showOnlyPopup\"\r\n          [canUpdateStatus]=\"canUpdateStatus\" [isLeadPreview]=\"true\" [isLastLead]=\"isLastLead\"\r\n          [closeLeadPreviewModal]=\"closeLeadPreviewModal\" [whatsAppComp]=\"whatsAppComp\"></status-change>\r\n        <custom-status-change *ngIf=\"isCustomStatusEnabled\" #customStatusChangeComponent [leadInfo]=\"data\"\r\n          [canShowStatusPopupInPreview]=\"canShowStatusPopupInPreview && showOnlyPopup\"\r\n          [canUpdateStatus]=\"canUpdateStatus\" [isLeadPreview]=\"true\" [isLastLead]=\"isLastLead\"\r\n          [closeLeadPreviewModal]=\"closeLeadPreviewModal\" [whatsAppComp]=\"whatsAppComp\"></custom-status-change>\r\n      </div>\r\n    </div>\r\n\r\n    <div *ngIf=\"selectedSection == 'History'\">\r\n      <div class=\"w-100\">\r\n        <lead-history [data]=\"data\" [whatsAppComp]=\"whatsAppComp\"></lead-history>\r\n      </div>\r\n    </div>\r\n\r\n    <div *ngIf=\"selectedSection == 'Notes'\">\r\n      <div class=\"w-100\">\r\n        <lead-notes [data]=\"data\" (notesAdded)=\"updateNotes($event)\" [whatsAppComp]=\"whatsAppComp\"></lead-notes>\r\n      </div>\r\n    </div>\r\n\r\n    <div *ngIf=\"selectedSection == 'Document'\">\r\n      <div class=\"w-100\" [ngClass]=\"{'position-relative' : whatsAppComp }\">\r\n        <leads-document-upload [leadData]=\"data\" [whatsAppComp]=\"whatsAppComp\"></leads-document-upload>\r\n      </div>\r\n    </div>\r\n  </span>\r\n\r\n  <ng-template #audioOption>\r\n    <h3 class=\"bg-coal px-24 py-12 text-white fw-semi-bold\">\r\n      {{ \"LEADS.lead-call-recordings\" | translate }}\r\n    </h3>\r\n    <div class=\"px-20 h-100-80 scrollbar\">\r\n      <ng-container *ngFor=\"let year of callRecordingDetails\">\r\n        <ng-container *ngFor=\"let month of year.yearData\">\r\n          <div class=\"align-center w-100 mt-20\">\r\n            <h5 class=\"text-accent-green fw-600\">\r\n              {{ moment(month?.months).format(\"MMM\") }} {{ year?.years }}\r\n            </h5>\r\n            <div class=\"flex-grow-1 border-bottom ml-8\"></div>\r\n          </div>\r\n          <ng-container *ngFor=\"let audio of month.monthData\">\r\n            <div class=\"bg-slate-150 br-10\">\r\n              <h5 class=\"mt-20 bg-coal px-20 py-10 br-10 text-white fw-600\">\r\n                {{ getTimeZoneDate(audio?.date,userData?.timeZoneInfo?.baseUTcOffset) }}\r\n              </h5>\r\n              <div class=\"position-relative\">\r\n                <audio #audioPlayer controls (play)=\"pauseOtherAudio(audioPlayer)\" (canplay)=\"isLoading = false\"\r\n                  (loadedmetadata)=\"isLoading = false\">\r\n                  <source [src]=\"audio?.audioUrl\" type=\"audio/mp3\" />\r\n                </audio>\r\n                <div *ngIf=\"isLoading\">\r\n                  <ng-lottie [options]=\"loader\" width=\"30px\" height=\"30px\" class=\"position-absolute top-10 left-6\">\r\n                  </ng-lottie>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </ng-container>\r\n        </ng-container>\r\n      </ng-container>\r\n    </div>\r\n  </ng-template>\r\n  <div class=\"ph-d-block d-none bottom-0 p-10 position-absolute bg-white\">\r\n    <div class=\"d-flex ph-w-100-20 mble-preview\">\r\n      <div title=\"Previous Lead\" class=\"bg-coal icon-badge mt-4\" (click)=\"prevData();getNextLead()\"\r\n        [ngClass]=\"{'bg-black-200 pe-none' : (foundData == 0 && isMobileView) || (!isMobileView && isFirstPage && foundData == 0), 'blinking': (isPreviousDataLoading && !isMobileView)}\">\r\n        <span class=\"icon ic-chevron-left m-auto ic-xxs\"></span>\r\n      </div>\r\n      <div class=\"w-100\">\r\n        <leads-actions [data]=\"data\" [isLeadPreviewOpen]=\"true\" (changeSelection)=\"onSectionSelect($event)\"\r\n          [whatsAppComp]=\"whatsAppComp\" [showCommunicationCount]=\"showCommunicationCount\"></leads-actions>\r\n      </div>\r\n      <div title=\"Next Lead\" class=\"bg-coal icon-badge mt-4\" (click)=\"nextData()\"\r\n        [ngClass]=\"{'bg-black-200 pe-none' : (foundData == (cardData?.length - 1) && isMobileView) || (totalPages === currentPage && foundData == (cardData?.length - 1)), 'blinking': (isNextDataLoading && foundData == (cardData?.length - 1)) || (isNextDataLoading && !isMobileView)}\">\r\n        <span class=\"icon ic-chevron-right m-auto ic-xxs\"></span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"]}, "metadata": {}, "sourceType": "module"}