<form [formGroup]="unitInfoDetails" (keydown.enter)="$event.preventDefault()" autocomplete="off">
    <div class="bg-dark text-white flex-between p-16">
        <h3>
            {{ selectEditUnitData ? 'Edit ' + (projectSubTypeData ? projectSubTypeData : 'Unit') : 'Add New ' +
            (projectSubTypeData ? projectSubTypeData : 'Unit') }}
        </h3>
        <div (click)="closeForm()" class="icon ic-close-secondary ic-large cursor-pointer"></div>
    </div>
    <div class="pl-20 pt-10 h-100-150 scrollbar position-relative">
        <div class="align-center">
            <h4 class="text-accent-green fw-semi-bold">{{ projectSubTypeData ? projectSubTypeData : 'Unit' }} Details</h4>
            <hr class="flex-grow-1 mx-3">
        </div>
        <div class="d-flex flex-wrap">
            <div class="w-33 ip-w-100 tb-w-50">
                <div class="mr-20 ip-mr-10">
                    <div class="field-label-req"> {{ projectSubTypeData ? projectSubTypeData : 'Unit' }} Name</div>
                    <form-errors-wrapper [control]="unitInfoDetails.controls['unitName']" label="{{ projectSubTypeData ? projectSubTypeData : 'Unit' }} name">
                        <input type="text" id="inpPropTitle" data-automate-id="inpPropTitle"
                            placeholder="ex. East luxury" formControlName="unitName">
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-33 ip-w-100 tb-w-50">
                <div [ngClass]="unitInfoDetails.controls['unitArea'].value ? 'field-label-req' : 'field-label'">{{
                    projectSubTypeData ? projectSubTypeData : 'Unit' }}
                    Area</div>
                <div class="align-center mr-20 ip-mr-10">
                    <div class="w-60pr">
                        <div class="mr-6">
                            <form-errors-wrapper [control]="unitInfoDetails.controls['unitArea']" label="Unit Area">
                                <input type="number" min="0" placeholder="ex. 503" formControlName="unitArea">
                            </form-errors-wrapper>
                        </div>
                    </div>
                    <div class="w-40pr">
                        <form-errors-wrapper [control]="unitInfoDetails.controls['unitAreaSize']" label="Area unit">
                            <ng-select [virtualScroll]="true" formControlName="unitAreaSize" placeholder="ex. sq. feet."
                                [items]="areaUnit" bindValue="id" bindLabel="unit"
                                (change)="onUnitChange('unitAreaSize')"
                                [readonly]="!unitInfoDetails.controls['unitArea'].value"></ng-select>
                        </form-errors-wrapper>
                    </div>
                </div>
            </div>
            <div class="w-33 ip-w-100 tb-w-50" *ngIf="projectSubTypeData !== 'Plot'">
                <div class="mr-20 ip-mr-10">
                    <div [ngClass]="unitInfoDetails.controls['carpetArea'].value ? 'field-label-req' : 'field-label'">
                        Carpet Area</div>
                    <div class="align-center">
                        <div class="w-60pr">
                            <div class="mr-6">
                                <form-errors-wrapper label="{{'LEADS.carpet-area' | translate}}"
                                    [control]="unitInfoDetails.controls['carpetArea']">
                                    <input type="number" min="0" placeholder="ex. 503" formControlName="carpetArea">
                                </form-errors-wrapper>
                            </div>
                        </div>
                        <div class="w-40pr">
                            <form-errors-wrapper label="Area unit"
                                [control]="unitInfoDetails.controls['carpetAreaUnit']">
                                <ng-select [virtualScroll]="true" formControlName="carpetAreaUnit"
                                    (change)="onUnitChange('carpetAreaUnit')" placeholder="ex. sq. feet."
                                    [items]="areaUnit" bindValue="id" bindLabel="unit"
                                    [readonly]="!unitInfoDetails.controls['carpetArea'].value"></ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-33 ip-w-100 tb-w-50"  *ngIf="projectSubTypeData !== 'Plot'">
                <div class="mr-20 ip-mr-10">
                    <div [ngClass]="unitInfoDetails.controls['builtupArea'].value ? 'field-label-req' : 'field-label'">
                        Built-up Area</div>
                    <div class="align-center">
                        <div class="w-60pr mr-6">
                            <form-errors-wrapper label="Built-up Area"
                                [control]="unitInfoDetails.controls['builtupArea']">
                                <input type="number" min="0" placeholder="ex. 503" formControlName="builtupArea">
                            </form-errors-wrapper>
                        </div>
                        <div class="w-40pr">
                            <form-errors-wrapper label="Area unit"
                                [control]="unitInfoDetails.controls['builtupAreaUnit']">
                                <ng-select [virtualScroll]="true" formControlName="builtupAreaUnit"
                                    (change)="onUnitChange('builtupAreaUnit')" placeholder="ex. sq. feet."
                                    [items]="areaUnit" bindValue="id" bindLabel="unit"
                                    [readonly]="!unitInfoDetails.controls['builtupArea'].value"></ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-33 ip-w-100 tb-w-50"  *ngIf="projectSubTypeData !== 'Plot'">
                <div class="mr-20 ip-mr-10">
                    <div
                        [ngClass]="unitInfoDetails.controls['superBuiltupArea'].value ? 'field-label-req' : 'field-label'">
                        Super Built-up Area</div>
                    <div class="align-center">
                        <div class="w-60pr mr-6">
                            <form-errors-wrapper label="Super Built-up Area"
                                [control]="unitInfoDetails.controls['superBuiltupArea']">
                                <input type="number" min="0" placeholder="ex. 503" formControlName="superBuiltupArea">
                            </form-errors-wrapper>
                        </div>
                        <div class="w-40pr">
                            <form-errors-wrapper label="Area unit"
                                [control]="unitInfoDetails.controls['superBuiltupAreaUnit']">
                                <ng-select [virtualScroll]="true" formControlName="superBuiltupAreaUnit"
                                    (change)="onUnitChange('superBuiltupAreaUnit')" [items]="areaUnit"
                                    placeholder="ex. sq. feet." bindValue="id" bindLabel="unit"
                                    [readonly]="!unitInfoDetails.controls['superBuiltupArea'].value"></ng-select>
                            </form-errors-wrapper>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-33 ip-w-100 tb-w-50">
                <div class="mr-20 ip-mr-10">
                    <div class="field-rupees-tag">
                        <div class="field-label">Maintenance Cost</div>
                        <div class="position-relative budget-dropdown">
                            <form-errors-wrapper [control]="unitInfoDetails.controls['maintenanceCost']"
                                label="Maintenance Cost">
                                <input type="number" id="inpPropAmt" data-automate-id="inpPropAmt"
                                    placeholder="ex. 200000" formControlName="maintenanceCost"
                                    [max]="unitInfoDetails.controls['totalPrice'].value" (keydown)="onlyNumbers($event)"
                                    maxlength="10" autocomplete="off">
                                <div class="no-validation">
                                    <ng-container *ngIf="currencyList?.length > 1; else showCurrencySymbol">
                                        <ng-select formControlName="currency"
                                            class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                                            <ng-option *ngFor="let curr of currencyList" [value]="curr.currency">
                                                <span [title]="curr.currency">{{curr.currency}}</span>
                                            </ng-option>
                                        </ng-select>
                                    </ng-container>
                                    <ng-template #showCurrencySymbol>
                                        <h5 class="rupees px-12 py-8 fw-600 m-4">{{ selectEditUnitData?.currency ||
                                            defaultCurrency }}</h5>
                                    </ng-template>
                                </div>
                            </form-errors-wrapper>
                            <div *ngIf="unitInfoDetails.controls['maintenanceCost'].value"
                                class="position-absolute right-12 bottom-12 text-accent-green fw-semi-bold text-sm">
                                {{maintenanceCost}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-33 ip-w-100 tb-w-50">
                <div class="mr-20 ip-mr-10">
                    <div class="field-rupees-tag">
                        <div class="field-label"> Price per {{ projectSubTypeData ? projectSubTypeData : 'Unit' }} area
                        </div>
                        <div class="position-relative budget-dropdown">
                            <form-errors-wrapper [control]="unitInfoDetails.controls['pricePerUnit']"
                                label="Price per unit">
                                <input type="number" id="inpPropAmt" data-automate-id="inpPropAmt"
                                    placeholder="ex. 4000000" formControlName="pricePerUnit"
                                    (keydown)="onlyNumbers($event)" maxlength="10" autocomplete="off">
                                <div class="no-validation">
                                    <ng-container *ngIf="currencyList?.length > 1; else showCurrencySymbol">
                                        <ng-select formControlName="currency"
                                            class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                                            <ng-option *ngFor="let curr of currencyList" [value]="curr.currency">
                                                <span [title]="curr.currency">{{curr.currency}}</span>
                                            </ng-option>
                                        </ng-select>
                                    </ng-container>
                                </div>
                            </form-errors-wrapper>
                            <div *ngIf="unitInfoDetails.controls['pricePerUnit'].value"
                                class="position-absolute right-12 bottom-12 text-accent-green fw-semi-bold text-sm">
                                {{pricePerUnit}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-33 ip-w-100 tb-w-50">
                <div class="flex-between">
                    <div class="field-label text-nowrap">Total Price</div>
                    <div *ngIf="!globalSettingsDetails?.shouldEnablePropertyListing" class="d-flex mt-16">
                        <ng-container *ngFor="let subOption of taxationModeList">
                            <div>
                                <label class="form-check form-check-inline p-0">
                                    <input class="radio-check-input" type="radio" formControlName="taxationMode"
                                        [value]="subOption.value">
                                    <div class="text-large fw-semi-bold cursor-pointer"
                                        [ngClass]="unitInfoDetails?.get('taxationMode')?.value === subOption.value ? 'text-black-200': 'text-dark-gray'">
                                        {{ subOption.label }}</div>
                                </label>
                            </div>
                        </ng-container>
                    </div>
                </div>
                <div class="field-rupees-tag">
                    <div class="mr-20 ip-mr-10">
                        <div class="position-relative budget-dropdown">
                            <form-errors-wrapper [control]="unitInfoDetails.controls['totalPrice']"
                                label="{{ 'PROPERTY.PROPERTY_DETAIL.total-price' | translate }}">
                                <input type="number" id="inpPropAmt" data-automate-id="inpPropAmt"
                                    placeholder="ex. 4000000" formControlName="totalPrice"
                                    (keydown)="onlyNumbers($event)" maxlength="10" autocomplete="off">
                                <div class="no-validation">
                                    <ng-container *ngIf="currencyList?.length > 1; else showCurrencySymbol">
                                        <ng-select formControlName="currency"
                                            class="ml-4 mt-4 position-absolute top-0 manage-dropdown">
                                            <ng-option *ngFor="let curr of currencyList" [value]="curr.currency">
                                                <span [title]="curr.currency">{{curr.currency}}</span>
                                            </ng-option>
                                        </ng-select>
                                    </ng-container>
                                </div>
                            </form-errors-wrapper>
                            <div *ngIf="unitInfoDetails.controls['totalPrice'].value"
                                class="position-absolute right-12 bottom-12 text-accent-green fw-semi-bold text-sm">
                                {{totalPrice}}
                            </div>
                            <div *ngIf="unitInfoDetails.controls['totalPrice'].value?unitInfoDetails.controls['totalPrice'].value<unitInfoDetails.controls['pricePerUnit'].value:false"
                                class="mt-6 text-xs text-red position-absolute right-0 fw-semi-bold text-nowrap">
                                Total price cannot be less than price per unit
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-33 ip-w-100 tb-w-50">
                <div class="mr-20 ip-mr-10">
                    <div class="field-label">{{ projectSubTypeData ? projectSubTypeData : 'Unit' }} Type</div>
                    <form-errors-wrapper [control]="unitInfoDetails.controls['unitType']" label="Unit type">
                        <ng-select [virtualScroll]="true" class="bg-white" [items]="allUnitTypes" [closeOnSelect]="true"
                            bindLabel="displayName" bindValue="displayName" name="projectsList"
                            formControlName="unitType" (change)="isAllFieldsValid($event)" placeholder="ex. Residential"
                            addTagText="Create New Project">
                        </ng-select>
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-33 ip-w-100 tb-w-50">
                <div class="mr-20 ip-mr-10">
                    <div class="field-label">{{ projectSubTypeData ? projectSubTypeData : 'Unit' }} Sub-Type</div>
                    <form-errors-wrapper [control]="unitInfoDetails.controls['unitSubType']" label="Unit sub type">
                        <ng-select [virtualScroll]="true" [items]="unitSubTypes" [closeOnSelect]="true"
                            bindLabel="displayName" name="projectsList"
                            (click)="unitInfoDetails.controls['unitType'].value?'':errMessage()"
                            [readonly]="unitInfoDetails.controls['unitType'].value ? false : true"
                            [ngClass]="unitInfoDetails.controls['unitType'].value ? 'bg-white' : 'bg-gray'"
                            formControlName="unitSubType" placeholder="ex. Plot">
                        </ng-select>
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-33 ip-w-100 tb-w-50" *ngIf="projectSubTypeData !== 'Plot'">
                <div class="mr-20 ip-mr-0">
                    <div class="oval-radio">
                        <div class="field-label">
                            {{'PROPERTY.bhk' | translate}}</div>
                        <form-errors-wrapper label="BHK" [control]="unitInfoDetails.controls['noOfBHK']">
                            <ng-select [virtualScroll]="true" [items]="bhkNoList" class="bg-white"
                                (click)="unitInfoDetails.controls['unitType'].value?'':errMessage()"
                                [readonly]="unitInfoDetails.controls['unitType'].value ? false : true"
                                [closeOnSelect]="true" placeholder="ex. 1 BHK" formControlName="noOfBHK">
                                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                    <span class="ng-value-label"> {{getBHKDisplayString(item)}}</span>
                                </ng-template>
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    {{getBHKDisplayString(item)}}
                                </ng-template>
                            </ng-select>
                            <!-- </div> -->
                        </form-errors-wrapper>
                    </div>
                </div>
            </div>
            <div class="w-33 ip-w-100 tb-w-50" *ngIf="projectSubTypeData !== 'Plot'">
                <div class="mr-20 ip-mr-10 oval-radio">
                    <div class="field-label">
                        {{'PROPERTY.bhk' | translate }} {{'LABEL.type' | translate}}</div>
                    <form-errors-wrapper label="BHK Type" [control]="unitInfoDetails.controls['bhkType']">
                        <div class="d-flex flex-wrap">
                            <ng-container *ngFor="let bhkType of bhkTypes">
                                <input type="radio" class="btn-check" name="bhkType" [id]="bhkType" autocomplete="off"
                                    formControlName="bhkType" [value]="bhkType">
                                <label class="btn-outline mr-10 mb-10" [for]="bhkType">{{bhkType}}</label>
                            </ng-container>
                        </div>
                    </form-errors-wrapper>
                </div>
            </div>
            <div class="w-33 tb-w-50 ip-w-100">
                <div class="mr-20 ip-mr-10">
                    <div class="box-radio black">
                        <div class="field-label">{{'PROPERTY.PROPERTY_DETAIL.facing' | translate }}</div>
                        <div class="d-flex flex-wrap">
                            <ng-container *ngFor="let facing of facingList">
                                <!-- <input type="radio" class="btn-check" name="facing" [id]="facing.displayName"
                                    autocomplete="off" [value]="facing.displayName" formControlName="facing"> -->
                                <input type="checkbox" class="btn-check" name="facing" [id]="facing.displayName"
                                    (change)="selectFacing($event.target.checked, facing)"
                                    [checked]="facingArray?.includes(facing.value)" autocomplete="off"
                                    [value]="facing.displayName">
                                <label class="btn-outline" [for]="facing.displayName">{{facing.displayName}}</label>
                            </ng-container>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-33 ip-w-100 tb-w-50"  *ngIf="projectSubTypeData !== 'Plot'">
                <div class="mr-20 ip-mr-10">
                    <div class="oval-radio">
                        <div class="field-label">Furnishing Status</div>
                        <div class="d-flex flex-wrap">
                            <ng-container *ngFor="let furnishingStatus of furnishingStatusList">
                                <input type="radio" class="btn-check" name="furnishingStatus"
                                    [id]="furnishingStatus.dispName" autocomplete="off"
                                    [value]="furnishingStatus.dispName" formControlName="furnishingStatus">
                                <label class="btn-outline"
                                    [for]="furnishingStatus.dispName">{{furnishingStatus.dispName}}</label>
                            </ng-container>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Attributes section ----------------- -->
        <ng-container *ngIf="getAttributesByType('basic')?.length  && projectSubTypeData !== 'Plot'">
            <div class="align-center">
                <h4 class="text-accent-green fw-600">Attributes</h4>
                <hr class="flex-grow-1 mx-3">
            </div>
            <div class="d-flex flex-wrap">
                <ng-container *ngFor="let attr of getAttributesByType('basic')">
                    <form-errors-wrapper [control]="unitInfoDetails.controls[attr.attributeName]"
                        [label]="attr.attributeDisplayName" class="position-relative mr-20 error">
                        <div class="field-label">{{attr.attributeDisplayName}}</div>
                        <div class="spin-btn-container-gray">
                            <div class="spin-btn" (click)="onMinus(attr.attributeName)"><span
                                    class="spin-ic ic-minus"></span>
                            </div>
                            <div class="no-validation px-6">
                                <input type="number" placeholder="ex. 3" [min]="0" [max]="99"
                                    [formControlName]="attr.attributeName" id="inpAttr{{attr.attributeName}}"
                                    data-automate-id="inpAttr{{attr.attributeName}}" autocomplete="off" />
                            </div>
                            <div class="spin-btn" (click)="onPlus(attr.attributeName)"><span
                                    class="spin-ic ic-plus"></span>
                            </div>
                        </div>
                    </form-errors-wrapper>
                </ng-container>
            </div>
            <!-- Attributes section ----------------- -->
            <div class="align-center mt-4">
                <h4 class="text-accent-green fw-600">Attributes</h4>
                <hr class="flex-grow-1 mx-3">
            </div>
            <div class="mt-8 px-2">
                <label class="checkbox-container text-secondary fw-semi-bold">{{ 'PROPERTY.select-all' |
                    translate }}
                    <input type="checkbox" class="mr-10" id="selectAllAdditionalAttrs"
                        [checked]="allAttributeSelectionCheck()?'checked': null"
                        (change)="changeAllAttributeSelection($event.target.checked)" />
                    <span class="checkmark" [class.select-all]="allAttributeSelectionCheck()"></span>
                </label>
            </div>
            <div class="d-flex flex-wrap flex-grow-1 mt-4">
                <ng-container *ngFor="let attribute of filteredAttributes">
                    <div class="w-20 tb-w-25 ip-w-33 ph-w-100 position-relative tag-card">
                        <div [ngClass]="selectedAdditionalAttr.includes(attribute?.id) ? 'border-bottom-black' : 'border'"
                            class="box-shadow-40 d-flex bg-white h-80 mt-10 mr-10 br-4 p-8">
                            <label class="checkbox-container mb-4">
                                <input type="checkbox"
                                    [checked]="selectedAdditionalAttr.includes(attribute?.id)?'checked':null" (change)="onAttributeSelectionChange(attribute.id , $event.target.checked,attribute.attributeDisplayName); 
                                emitAttributeSelection(); attribute.selected = $event.target.checked">
                                <span class="checkmark"></span>
                            </label>
                            <div class="flex-center-col w-100 mr-4">
                                <img *ngIf="attribute?.activeImageURL && attribute?.inActiveImageURL; else dummy"
                                    [src]="selectedAdditionalAttr.includes(attribute?.id) ? attribute?.activeImageURL: attribute?.inActiveImageURL"
                                    class="obj-fill" width="23px" height="15px">
                                <div [ngClass]="selectedAdditionalAttr.includes(attribute?.id) ? 'text-black-100 fw-semi-bold': ' text-light-gray fw-400'"
                                    class="mt-4 text-center">
                                    {{attribute?.attributeDisplayName}}
                                </div>
                                <ng-template #dummy>
                                    <span class="icon ic-black ic-sm">
                                        {{getFirstCharacter(attribute?.attributeDisplayName)}}</span>
                                </ng-template>
                            </div>
                        </div>
                    </div>
                </ng-container>
            </div>
            <!-- <div class="d-flex flex-wrap mt-20 w-100 px-10">
                <ng-container *ngFor="let attribute of getAttributesByType('additional'); let i = index">
                    <label class="checkbox-container d-flex cursor-pointer mb-20 ml-20 fw-semi-bold"
                        for="inpFAttr{{i}}">
                        {{attribute.attributeDisplayName }}
                        <input type="checkbox" class="mr-10" [id]="'inpFAttr'+i" [data-automate-id]="'inpFAttr'+i"
                            [checked]="selectedAdditionalAttr.includes(attribute.id)?'checked':null"
                            (change)="onAttributeSelectionChange(attribute.id, $event.target.checked,attribute.attributeDisplayName)">
                        <span class="checkmark bg-white"></span>
                    </label>
                </ng-container>
            </div> -->
        </ng-container>
        <!-- Uploads Section --------------- -->
        <div class="align-center mt-10">
            <h4 class="text-accent-green fw-600">Uploads</h4>
            <hr class="flex-grow-1 mx-3 ">
            <label *ngIf="waterMarkSettingsObj.isWaterMarkEnabled"
                class="checkbox-container w-fit-content tb-w-33 ip-w-50 ph-w-100 text-secondary mr-30 mt-0">
                Add water mark
                <input type="checkbox" class="mr-10" formControlName="isWaterMark" (change)="isAddWaterMark($event)">
                <span class="checkmark"></span>
            </label>
        </div>
        <div class="d-flex flex-wrap">
            <div *ngFor="let url of galleryS3Paths; let i = index" class="position-relative mr-20 mb-16 br-5">
                <div
                    *ngIf="url.imageFilePath.includes('jpeg') || url.imageFilePath.includes('jpg') || url.imageFilePath.includes('png') || url.imageFilePath.includes('gif')">
                    <a [href]="url?.imageFilePath?.includes(s3BucketUrl)?url?.imageFilePath:getAWSImagePath(url?.imageFilePath)"
                        target="_blank">
                        <img [type]="'leadrat'"
                            [appImage]="url?.imageFilePath?.includes(s3BucketUrl)?url?.imageFilePath:getAWSImagePath(url.imageFilePath)"
                            width="120" height="120" class="obj-cover cursor-pointer br-5" alt="img" />
                    </a>
                    <span (click)="deleteFile(i)"
                        class="dot bg-black-4 cursor-pointer bg-hover-red position-absolute top-4 right-4">
                        <span class="icon ic-delete ic-xxxs" id="clkDeleteImage"
                            data-automate-id="clkDeleteImage"></span>
                    </span>
                    <div class="position-absolute w-100 bottom-0 image-dropdown">
                        <div class="p-1 flex-between bg-white-100 brbr-6 brbl-6">
                            <div class="text-truncate-1 break-all fw-semi-bold">{{url.name}}</div>
                        </div>
                    </div>
                </div>
                <div *ngIf="url.imageFilePath.includes('mp4')" class="box-shadow-1 br-5">
                    <video width="120" height="120" controls class="br-5" alt="video">
                        <source
                            [src]="url?.imageFilePath?.includes(s3BucketUrl)?url?.imageFilePath:getAWSImagePath(url.imageFilePath)"
                            type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    <span (click)="deleteFile(i)"
                        class="dot bg-black-4 cursor-pointer bg-hover-red position-absolute top-4 right-4">
                        <span class="icon ic-delete ic-xxxs" id="clkDeleteImage"
                            data-automate-id="clkDeleteImage"></span>
                    </span>
                    <div class="position-absolute w-100 bottom-0 image-dropdown">
                        <div class="p-1 flex-between bg-white-100 brbr-6 brbl-6">
                            <div class="text-truncate-1 break-all fw-semi-bold">{{url.name}}</div>
                        </div>
                    </div>
                </div>
                <div *ngIf="url.imageFilePath.includes('pdf')">
                    <a [href]="url?.imageFilePath?.includes(s3BucketUrl)?url?.imageFilePath:getAWSImagePath(url.imageFilePath)"
                        target="_blank">
                        <img src="../../../../assets/images/pdf.svg" alt="" class="h-88">
                    </a>
                    <div class="p-10 flex-between bg-white-100 brbr-6 brbl-6">
                        <div class="text-truncate-1 break-all fw-semi-bold">{{url.name}}</div>
                        <span class="dot bg-light-red cursor-pointer" (click)="deleteFile(i)">
                            <div title="Delete" class="ic-delete icon ic-xxxs cursor-pointer" id="clkDeletePdf"
                                data-automate-id="clkDeletePdf"></div>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="version-two">
            <div class="text-gray text-xs fw-semi-bold">(max upload limit 15 MB)</div>
            <div class="position-relative tb-w-100 w-50">
                <div class="image-container">
                    <div class="icon ic-download text-dark ic-large w-20px position-absolute top-55 right-50"></div>
                </div>
                <browse-drop-upload [allowedFileType]="'media'" [isExcelFile]="false" [allowedFileFormat]="fileFormat"
                    [fileMessage]="fileMessage" (WaterMarkImage)="addWaterMarkImages($event)"
                    (uploadedFileSize)="fileSize = $event" (uploadedFileWithName)="onFileSelection($event)">
                </browse-drop-upload>
            </div>
        </div>
    </div>
    <div class="modal-footer bg-white justify-end position-sticky bottom-0">
        <div (click)="closeForm()" class="btn-gray">Close</div>
        <div class="btn-coal" (click)="onSubmit()">Save</div>
    </div>
</form>