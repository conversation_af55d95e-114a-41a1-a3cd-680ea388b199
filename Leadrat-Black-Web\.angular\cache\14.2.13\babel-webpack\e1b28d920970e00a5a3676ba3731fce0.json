{"ast": null, "code": "import createMathOperation from './_createMathOperation.js';\n/**\n * Divide two numbers.\n *\n * @static\n * @memberOf _\n * @since 4.7.0\n * @category Math\n * @param {number} dividend The first number in a division.\n * @param {number} divisor The second number in a division.\n * @returns {number} Returns the quotient.\n * @example\n *\n * _.divide(6, 4);\n * // => 1.5\n */\n\nvar divide = createMathOperation(function (dividend, divisor) {\n  return dividend / divisor;\n}, 1);\nexport default divide;", "map": {"version": 3, "names": ["createMathOperation", "divide", "dividend", "divisor"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/lodash-es/divide.js"], "sourcesContent": ["import createMathOperation from './_createMathOperation.js';\n\n/**\n * Divide two numbers.\n *\n * @static\n * @memberOf _\n * @since 4.7.0\n * @category Math\n * @param {number} dividend The first number in a division.\n * @param {number} divisor The second number in a division.\n * @returns {number} Returns the quotient.\n * @example\n *\n * _.divide(6, 4);\n * // => 1.5\n */\nvar divide = createMathOperation(function(dividend, divisor) {\n  return dividend / divisor;\n}, 1);\n\nexport default divide;\n"], "mappings": "AAAA,OAAOA,mBAAP,MAAgC,2BAAhC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAIC,MAAM,GAAGD,mBAAmB,CAAC,UAASE,QAAT,EAAmBC,OAAnB,EAA4B;EAC3D,OAAOD,QAAQ,GAAGC,OAAlB;AACD,CAF+B,EAE7B,CAF6B,CAAhC;AAIA,eAAeF,MAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}