{"ast": null, "code": "import { switchMap } from './switchMap';\nimport { identity } from '../util/identity';\nexport function switchAll() {\n  return switchMap(identity);\n}", "map": {"version": 3, "names": ["switchMap", "identity", "switchAll"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/@angular-slider/ngx-slider/node_modules/rxjs/_esm2015/internal/operators/switchAll.js"], "sourcesContent": ["import { switchMap } from './switchMap';\nimport { identity } from '../util/identity';\nexport function switchAll() {\n    return switchMap(identity);\n}\n"], "mappings": "AAAA,SAASA,SAAT,QAA0B,aAA1B;AACA,SAASC,QAAT,QAAyB,kBAAzB;AACA,OAAO,SAASC,SAAT,GAAqB;EACxB,OAAOF,SAAS,CAACC,QAAD,CAAhB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}