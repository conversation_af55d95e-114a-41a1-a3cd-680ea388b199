{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.buffer = void 0;\n\nvar lift_1 = require(\"../util/lift\");\n\nvar noop_1 = require(\"../util/noop\");\n\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\n\nfunction buffer(closingNotifier) {\n  return lift_1.operate(function (source, subscriber) {\n    var currentBuffer = [];\n    source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) {\n      return currentBuffer.push(value);\n    }, function () {\n      subscriber.next(currentBuffer);\n      subscriber.complete();\n    }));\n    closingNotifier.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n      var b = currentBuffer;\n      currentBuffer = [];\n      subscriber.next(b);\n    }, noop_1.noop));\n    return function () {\n      currentBuffer = null;\n    };\n  });\n}\n\nexports.buffer = buffer;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "buffer", "lift_1", "require", "noop_1", "OperatorSubscriber_1", "closingNotifier", "operate", "source", "subscriber", "current<PERSON><PERSON><PERSON>", "subscribe", "createOperatorSubscriber", "push", "next", "complete", "b", "noop"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/rxjs/dist/cjs/internal/operators/buffer.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.buffer = void 0;\nvar lift_1 = require(\"../util/lift\");\nvar noop_1 = require(\"../util/noop\");\nvar OperatorSubscriber_1 = require(\"./OperatorSubscriber\");\nfunction buffer(closingNotifier) {\n    return lift_1.operate(function (source, subscriber) {\n        var currentBuffer = [];\n        source.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function (value) { return currentBuffer.push(value); }, function () {\n            subscriber.next(currentBuffer);\n            subscriber.complete();\n        }));\n        closingNotifier.subscribe(OperatorSubscriber_1.createOperatorSubscriber(subscriber, function () {\n            var b = currentBuffer;\n            currentBuffer = [];\n            subscriber.next(b);\n        }, noop_1.noop));\n        return function () {\n            currentBuffer = null;\n        };\n    });\n}\nexports.buffer = buffer;\n"], "mappings": "AAAA;;AACAA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;EAAEC,KAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACE,MAAR,GAAiB,KAAK,CAAtB;;AACA,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAD,CAApB;;AACA,IAAIC,MAAM,GAAGD,OAAO,CAAC,cAAD,CAApB;;AACA,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,sBAAD,CAAlC;;AACA,SAASF,MAAT,CAAgBK,eAAhB,EAAiC;EAC7B,OAAOJ,MAAM,CAACK,OAAP,CAAe,UAAUC,MAAV,EAAkBC,UAAlB,EAA8B;IAChD,IAAIC,aAAa,GAAG,EAApB;IACAF,MAAM,CAACG,SAAP,CAAiBN,oBAAoB,CAACO,wBAArB,CAA8CH,UAA9C,EAA0D,UAAUT,KAAV,EAAiB;MAAE,OAAOU,aAAa,CAACG,IAAd,CAAmBb,KAAnB,CAAP;IAAmC,CAAhH,EAAkH,YAAY;MAC3IS,UAAU,CAACK,IAAX,CAAgBJ,aAAhB;MACAD,UAAU,CAACM,QAAX;IACH,CAHgB,CAAjB;IAIAT,eAAe,CAACK,SAAhB,CAA0BN,oBAAoB,CAACO,wBAArB,CAA8CH,UAA9C,EAA0D,YAAY;MAC5F,IAAIO,CAAC,GAAGN,aAAR;MACAA,aAAa,GAAG,EAAhB;MACAD,UAAU,CAACK,IAAX,CAAgBE,CAAhB;IACH,CAJyB,EAIvBZ,MAAM,CAACa,IAJgB,CAA1B;IAKA,OAAO,YAAY;MACfP,aAAa,GAAG,IAAhB;IACH,CAFD;EAGH,CAdM,CAAP;AAeH;;AACDX,OAAO,CAACE,MAAR,GAAiBA,MAAjB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}