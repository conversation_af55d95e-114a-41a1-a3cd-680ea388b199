import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
@Component({
  selector: 'file-upload',
  templateUrl: './file-upload.component.html',
})
export class FileUploadComponent {
  @Input() labelText: string;
  @Input() requiredFileType: string;
  @Input() buttonClass: string;
  @Input() allowMultipleFiles: boolean = true;
  @Input() isImageGallery: boolean = false;
  @Input() isExcelUpload: boolean = false;
  @Input() prevUploadedImages: Array<string> = [];
  @ViewChild('fileUpload') fileInputRef!: ElementRef;
  @Input() showFileUploader: string = 'first';
  @Input() isUploadingImage: boolean = true;
  @Input() isFormData: boolean = false;

  @Output() imageFilesWithSize: EventEmitter<any> = new EventEmitter<any>();
  @Output() imageFiles: EventEmitter<any> = new EventEmitter<any>();
  @Output() imageFileName: EventEmitter<any> = new EventEmitter<any>();
  @Output() uploadedFile: EventEmitter<any> = new EventEmitter<any>();
  @Output() uploadedFileName: EventEmitter<any> = new EventEmitter<any>();
  @Output() uploadedFileSize: EventEmitter<any> = new EventEmitter<any>();
  @Output() addWaterMarkImage: EventEmitter<any> = new EventEmitter<any>();

  private files: File[] = [];
  galleryImageArray: any = [];
  projectGalleryFiles: any = [];
  sizes: Object;
  imageUpload: any;
  waterMarkFileNameList: any = [];
  progress: number = 0;

  constructor(
    private sharedDataService: ShareDataService,
    private imageUploadService: BlobStorageService
  ) { }

  @Output() imageDimensions: EventEmitter<any> = new EventEmitter<any>();

  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    let waterMarkFileName = event.target.files;
    this.waterMarkFileNameList = [...waterMarkFileName];
    this.addWaterMarkImage.emit(this.waterMarkFileNameList);

    const currentDate = new Date();
    const uploadDate = currentDate.toISOString();
    if (file) {
      this.sharedDataService.shareFileSize(file.size);
      this.uploadedFileSize.emit(file.size);
    }
    const selectedFiles = event.target.files;
    this.galleryImageArray = [];
    const imgNames: string[] = [];
    this.projectGalleryFiles = [];
    this.files = []; // Clear the files array to prevent accumulation
    if (selectedFiles && selectedFiles.length) {
      if (this.isImageGallery) {
        for (let i = 0; i < event.target.files.length; i++) {
          this.files.push(event.target.files[i]);
          imgNames.push(event.target.files[i].name);
          let reader = new FileReader();
          reader.readAsDataURL(event.target.files[i]);
          reader.onload = (eventOnload: any) => {
            if (reader.result) {
              const fileType = event.target.files[i]?.type;
              const isVideo = fileType && fileType.startsWith('video/');
              const isPDF = fileType && fileType === 'application/pdf';
              if (isVideo || isPDF) {
                this.galleryImageArray.push(eventOnload.target.result);
                this.projectGalleryFiles.push([eventOnload.target.result, event.target.files[i]?.name, event.target.files[i]?.size, event.target.files[i]]);
                if (this.galleryImageArray.length === selectedFiles.length) {
                  setTimeout(() => {
                    this.uploadedFileName.emit(event.target.files[0]?.name);
                    this.imageFiles.emit([this.galleryImageArray, imgNames, uploadDate, this.files]);
                    this.imageFilesWithSize.emit({ images: this.galleryImageArray, size: file?.size });
                    this.imageFileName.emit(this.projectGalleryFiles);
                    event.target.value = '';
                  }, 100);
                }
              } else {
                const image = new Image();
                image.src = eventOnload.target.result;
                image.onload = () => {
                  const dimensions = {
                    width: image.width,
                    height: image.height,
                    fileName: event.target.files[i]?.name,
                    dataUrl: eventOnload.target.result
                  };
                  this.imageDimensions.emit(dimensions);
                  this.galleryImageArray.push(eventOnload.target.result);
                  this.projectGalleryFiles.push([eventOnload.target.result, event.target.files[i]?.name, event.target.files[i]?.size, event.target.files[i]]);
                  if (this.galleryImageArray.length === selectedFiles.length) {
                    // Small delay to ensure all dimension checks have been processed by the parent component
                    setTimeout(() => {
                      this.uploadedFileName.emit(event.target.files[0]?.name);
                      this.imageFiles.emit([this.galleryImageArray, imgNames, uploadDate, this.files]);
                      this.imageFilesWithSize.emit({ images: this.galleryImageArray, size: file?.size });
                      this.imageFileName.emit(this.projectGalleryFiles);
                      event.target.value = '';
                    }, 300); // Longer delay to ensure dimension checks complete
                  }
                };
              }
            }
          };
        }
      } else if (this.isExcelUpload || this.isFormData) {
        this.uploadedFile.emit(selectedFiles[0]);
        this.uploadedFileName.emit(selectedFiles[0].name);
      } else {
        let uploadedFile: any[] = [];
        if (selectedFiles[0]) {
          let reader = new FileReader();
          reader.onload = (eventOnload: any) => {
            uploadedFile.push(eventOnload.target.result);
            this.uploadedFile.emit(uploadedFile);
            event.target.value = '';
          };
          reader.readAsDataURL(event.target.files[0]);
          this.uploadedFileName.emit(event.target.files[0]?.name);
        }
      }
    }
  }
  cancelUpload($event: any) {
    this.isUploadingImage = false;
    this.imageUploadService.cancelUpload();
  }

}
