{"ast": null, "code": "import Lazy<PERSON>rapper from './_LazyWrapper.js';\nimport copyArray from './_copyArray.js';\n/**\n * Creates a clone of the lazy wrapper object.\n *\n * @private\n * @name clone\n * @memberOf LazyWrapper\n * @returns {Object} Returns the cloned `<PERSON><PERSON>Wrapper` object.\n */\n\nfunction lazyClone() {\n  var result = new LazyWrapper(this.__wrapped__);\n  result.__actions__ = copyArray(this.__actions__);\n  result.__dir__ = this.__dir__;\n  result.__filtered__ = this.__filtered__;\n  result.__iteratees__ = copyArray(this.__iteratees__);\n  result.__takeCount__ = this.__takeCount__;\n  result.__views__ = copyArray(this.__views__);\n  return result;\n}\n\nexport default lazyClone;", "map": {"version": 3, "names": ["LazyWrapper", "copyArray", "lazy<PERSON>lone", "result", "__wrapped__", "__actions__", "__dir__", "__filtered__", "__iteratees__", "__takeCount__", "__views__"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/lodash-es/_lazyClone.js"], "sourcesContent": ["import Lazy<PERSON>rapper from './_LazyWrapper.js';\nimport copyArray from './_copyArray.js';\n\n/**\n * Creates a clone of the lazy wrapper object.\n *\n * @private\n * @name clone\n * @memberOf LazyWrapper\n * @returns {Object} Returns the cloned `<PERSON><PERSON>Wrapper` object.\n */\nfunction lazyClone() {\n  var result = new LazyWrapper(this.__wrapped__);\n  result.__actions__ = copyArray(this.__actions__);\n  result.__dir__ = this.__dir__;\n  result.__filtered__ = this.__filtered__;\n  result.__iteratees__ = copyArray(this.__iteratees__);\n  result.__takeCount__ = this.__takeCount__;\n  result.__views__ = copyArray(this.__views__);\n  return result;\n}\n\nexport default lazyClone;\n"], "mappings": "AAAA,OAAOA,WAAP,MAAwB,mBAAxB;AACA,OAAOC,SAAP,MAAsB,iBAAtB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,SAAT,GAAqB;EACnB,IAAIC,MAAM,GAAG,IAAIH,WAAJ,CAAgB,KAAKI,WAArB,CAAb;EACAD,MAAM,CAACE,WAAP,GAAqBJ,SAAS,CAAC,KAAKI,WAAN,CAA9B;EACAF,MAAM,CAACG,OAAP,GAAiB,KAAKA,OAAtB;EACAH,MAAM,CAACI,YAAP,GAAsB,KAAKA,YAA3B;EACAJ,MAAM,CAACK,aAAP,GAAuBP,SAAS,CAAC,KAAKO,aAAN,CAAhC;EACAL,MAAM,CAACM,aAAP,GAAuB,KAAKA,aAA5B;EACAN,MAAM,CAACO,SAAP,GAAmBT,SAAS,CAAC,KAAKS,SAAN,CAA5B;EACA,OAAOP,MAAP;AACD;;AAED,eAAeD,SAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}