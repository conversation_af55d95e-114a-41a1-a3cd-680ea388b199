{"ast": null, "code": "import baseToString from './_baseToString.js';\nimport castSlice from './_castSlice.js';\nimport charsStartIndex from './_charsStartIndex.js';\nimport stringToArray from './_stringToArray.js';\nimport toString from './toString.js';\n/** Used to match leading whitespace. */\n\nvar reTrimStart = /^\\s+/;\n/**\n * Removes leading whitespace or specified characters from `string`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to trim.\n * @param {string} [chars=whitespace] The characters to trim.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {string} Returns the trimmed string.\n * @example\n *\n * _.trimStart('  abc  ');\n * // => 'abc  '\n *\n * _.trimStart('-_-abc-_-', '_-');\n * // => 'abc-_-'\n */\n\nfunction trimStart(string, chars, guard) {\n  string = toString(string);\n\n  if (string && (guard || chars === undefined)) {\n    return string.replace(reTrimStart, '');\n  }\n\n  if (!string || !(chars = baseToString(chars))) {\n    return string;\n  }\n\n  var strSymbols = stringToArray(string),\n      start = charsStartIndex(strSymbols, stringToArray(chars));\n  return castSlice(strSymbols, start).join('');\n}\n\nexport default trimStart;", "map": {"version": 3, "names": ["baseToString", "castSlice", "charsStartIndex", "stringToArray", "toString", "reTrimStart", "trimStart", "string", "chars", "guard", "undefined", "replace", "strSymbols", "start", "join"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/lodash-es/trimStart.js"], "sourcesContent": ["import baseToString from './_baseToString.js';\nimport castSlice from './_castSlice.js';\nimport charsStartIndex from './_charsStartIndex.js';\nimport stringToArray from './_stringToArray.js';\nimport toString from './toString.js';\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * Removes leading whitespace or specified characters from `string`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to trim.\n * @param {string} [chars=whitespace] The characters to trim.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {string} Returns the trimmed string.\n * @example\n *\n * _.trimStart('  abc  ');\n * // => 'abc  '\n *\n * _.trimStart('-_-abc-_-', '_-');\n * // => 'abc-_-'\n */\nfunction trimStart(string, chars, guard) {\n  string = toString(string);\n  if (string && (guard || chars === undefined)) {\n    return string.replace(reTrimStart, '');\n  }\n  if (!string || !(chars = baseToString(chars))) {\n    return string;\n  }\n  var strSymbols = stringToArray(string),\n      start = charsStartIndex(strSymbols, stringToArray(chars));\n\n  return castSlice(strSymbols, start).join('');\n}\n\nexport default trimStart;\n"], "mappings": "AAAA,OAAOA,YAAP,MAAyB,oBAAzB;AACA,OAAOC,SAAP,MAAsB,iBAAtB;AACA,OAAOC,eAAP,MAA4B,uBAA5B;AACA,OAAOC,aAAP,MAA0B,qBAA1B;AACA,OAAOC,QAAP,MAAqB,eAArB;AAEA;;AACA,IAAIC,WAAW,GAAG,MAAlB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,SAAT,CAAmBC,MAAnB,EAA2BC,KAA3B,EAAkCC,KAAlC,EAAyC;EACvCF,MAAM,GAAGH,QAAQ,CAACG,MAAD,CAAjB;;EACA,IAAIA,MAAM,KAAKE,KAAK,IAAID,KAAK,KAAKE,SAAxB,CAAV,EAA8C;IAC5C,OAAOH,MAAM,CAACI,OAAP,CAAeN,WAAf,EAA4B,EAA5B,CAAP;EACD;;EACD,IAAI,CAACE,MAAD,IAAW,EAAEC,KAAK,GAAGR,YAAY,CAACQ,KAAD,CAAtB,CAAf,EAA+C;IAC7C,OAAOD,MAAP;EACD;;EACD,IAAIK,UAAU,GAAGT,aAAa,CAACI,MAAD,CAA9B;EAAA,IACIM,KAAK,GAAGX,eAAe,CAACU,UAAD,EAAaT,aAAa,CAACK,KAAD,CAA1B,CAD3B;EAGA,OAAOP,SAAS,CAACW,UAAD,EAAaC,KAAb,CAAT,CAA6BC,IAA7B,CAAkC,EAAlC,CAAP;AACD;;AAED,eAAeR,SAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}