{"ast": null, "code": "import { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { arrRemove } from '../util/arrRemove';\nexport function bufferToggle(openings, closingSelector) {\n  return operate((source, subscriber) => {\n    const buffers = [];\n    innerFrom(openings).subscribe(createOperatorSubscriber(subscriber, openValue => {\n      const buffer = [];\n      buffers.push(buffer);\n      const closingSubscription = new Subscription();\n\n      const emitBuffer = () => {\n        arrRemove(buffers, buffer);\n        subscriber.next(buffer);\n        closingSubscription.unsubscribe();\n      };\n\n      closingSubscription.add(innerFrom(closingSelector(openValue)).subscribe(createOperatorSubscriber(subscriber, emitBuffer, noop)));\n    }, noop));\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      for (const buffer of buffers) {\n        buffer.push(value);\n      }\n    }, () => {\n      while (buffers.length > 0) {\n        subscriber.next(buffers.shift());\n      }\n\n      subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["Subscription", "operate", "innerFrom", "createOperatorSubscriber", "noop", "arr<PERSON><PERSON><PERSON>", "bufferToggle", "openings", "closingSelector", "source", "subscriber", "buffers", "subscribe", "openValue", "buffer", "push", "closingSubscription", "emitB<PERSON>er", "next", "unsubscribe", "add", "value", "length", "shift", "complete"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/rxjs/dist/esm/internal/operators/bufferToggle.js"], "sourcesContent": ["import { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { arrRemove } from '../util/arrRemove';\nexport function bufferToggle(openings, closingSelector) {\n    return operate((source, subscriber) => {\n        const buffers = [];\n        innerFrom(openings).subscribe(createOperatorSubscriber(subscriber, (openValue) => {\n            const buffer = [];\n            buffers.push(buffer);\n            const closingSubscription = new Subscription();\n            const emitBuffer = () => {\n                arrRemove(buffers, buffer);\n                subscriber.next(buffer);\n                closingSubscription.unsubscribe();\n            };\n            closingSubscription.add(innerFrom(closingSelector(openValue)).subscribe(createOperatorSubscriber(subscriber, emitBuffer, noop)));\n        }, noop));\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            for (const buffer of buffers) {\n                buffer.push(value);\n            }\n        }, () => {\n            while (buffers.length > 0) {\n                subscriber.next(buffers.shift());\n            }\n            subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,YAAT,QAA6B,iBAA7B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,IAAT,QAAqB,cAArB;AACA,SAASC,SAAT,QAA0B,mBAA1B;AACA,OAAO,SAASC,YAAT,CAAsBC,QAAtB,EAAgCC,eAAhC,EAAiD;EACpD,OAAOP,OAAO,CAAC,CAACQ,MAAD,EAASC,UAAT,KAAwB;IACnC,MAAMC,OAAO,GAAG,EAAhB;IACAT,SAAS,CAACK,QAAD,CAAT,CAAoBK,SAApB,CAA8BT,wBAAwB,CAACO,UAAD,EAAcG,SAAD,IAAe;MAC9E,MAAMC,MAAM,GAAG,EAAf;MACAH,OAAO,CAACI,IAAR,CAAaD,MAAb;MACA,MAAME,mBAAmB,GAAG,IAAIhB,YAAJ,EAA5B;;MACA,MAAMiB,UAAU,GAAG,MAAM;QACrBZ,SAAS,CAACM,OAAD,EAAUG,MAAV,CAAT;QACAJ,UAAU,CAACQ,IAAX,CAAgBJ,MAAhB;QACAE,mBAAmB,CAACG,WAApB;MACH,CAJD;;MAKAH,mBAAmB,CAACI,GAApB,CAAwBlB,SAAS,CAACM,eAAe,CAACK,SAAD,CAAhB,CAAT,CAAsCD,SAAtC,CAAgDT,wBAAwB,CAACO,UAAD,EAAaO,UAAb,EAAyBb,IAAzB,CAAxE,CAAxB;IACH,CAVqD,EAUnDA,IAVmD,CAAtD;IAWAK,MAAM,CAACG,SAAP,CAAiBT,wBAAwB,CAACO,UAAD,EAAcW,KAAD,IAAW;MAC7D,KAAK,MAAMP,MAAX,IAAqBH,OAArB,EAA8B;QAC1BG,MAAM,CAACC,IAAP,CAAYM,KAAZ;MACH;IACJ,CAJwC,EAItC,MAAM;MACL,OAAOV,OAAO,CAACW,MAAR,GAAiB,CAAxB,EAA2B;QACvBZ,UAAU,CAACQ,IAAX,CAAgBP,OAAO,CAACY,KAAR,EAAhB;MACH;;MACDb,UAAU,CAACc,QAAX;IACH,CATwC,CAAzC;EAUH,CAvBa,CAAd;AAwBH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}