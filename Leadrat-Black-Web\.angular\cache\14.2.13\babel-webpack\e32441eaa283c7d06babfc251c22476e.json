{"ast": null, "code": "import { concat } from '../observable/concat';\nimport { take } from './take';\nimport { ignoreElements } from './ignoreElements';\nimport { mapTo } from './mapTo';\nimport { mergeMap } from './mergeMap';\nexport function delayWhen(delayDurationSelector, subscriptionDelay) {\n  if (subscriptionDelay) {\n    return source => concat(subscriptionDelay.pipe(take(1), ignoreElements()), source.pipe(delayWhen(delayDurationSelector)));\n  }\n\n  return mergeMap((value, index) => delayDurationSelector(value, index).pipe(take(1), mapTo(value)));\n}", "map": {"version": 3, "names": ["concat", "take", "ignoreElements", "mapTo", "mergeMap", "<PERSON><PERSON>hen", "delayDurationSelector", "subscriptionDelay", "source", "pipe", "value", "index"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/rxjs/dist/esm/internal/operators/delayWhen.js"], "sourcesContent": ["import { concat } from '../observable/concat';\nimport { take } from './take';\nimport { ignoreElements } from './ignoreElements';\nimport { mapTo } from './mapTo';\nimport { mergeMap } from './mergeMap';\nexport function delayWhen(delayDurationSelector, subscriptionDelay) {\n    if (subscriptionDelay) {\n        return (source) => concat(subscriptionDelay.pipe(take(1), ignoreElements()), source.pipe(delayWhen(delayDurationSelector)));\n    }\n    return mergeMap((value, index) => delayDurationSelector(value, index).pipe(take(1), mapTo(value)));\n}\n"], "mappings": "AAAA,SAASA,MAAT,QAAuB,sBAAvB;AACA,SAASC,IAAT,QAAqB,QAArB;AACA,SAASC,cAAT,QAA+B,kBAA/B;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,SAASC,QAAT,QAAyB,YAAzB;AACA,OAAO,SAASC,SAAT,CAAmBC,qBAAnB,EAA0CC,iBAA1C,EAA6D;EAChE,IAAIA,iBAAJ,EAAuB;IACnB,OAAQC,MAAD,IAAYR,MAAM,CAACO,iBAAiB,CAACE,IAAlB,CAAuBR,IAAI,CAAC,CAAD,CAA3B,EAAgCC,cAAc,EAA9C,CAAD,EAAoDM,MAAM,CAACC,IAAP,CAAYJ,SAAS,CAACC,qBAAD,CAArB,CAApD,CAAzB;EACH;;EACD,OAAOF,QAAQ,CAAC,CAACM,KAAD,EAAQC,KAAR,KAAkBL,qBAAqB,CAACI,KAAD,EAAQC,KAAR,CAArB,CAAoCF,IAApC,CAAyCR,IAAI,CAAC,CAAD,CAA7C,EAAkDE,KAAK,CAACO,KAAD,CAAvD,CAAnB,CAAf;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}