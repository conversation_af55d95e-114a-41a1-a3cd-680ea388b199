{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Catalan [ca]\n//! author : <PERSON> : https://github.com/juanghurtado\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var ca = moment.defineLocale('ca', {\n    months: {\n      standalone: 'gener_febrer_març_abril_maig_juny_juliol_agost_setembre_octubre_novembre_desembre'.split('_'),\n      format: \"de gener_de febrer_de març_d'abril_de maig_de juny_de juliol_d'agost_de setembre_d'octubre_de novembre_de desembre\".split('_'),\n      isFormat: /D[oD]?(\\s)+MMMM/\n    },\n    monthsShort: 'gen._febr._març_abr._maig_juny_jul._ag._set._oct._nov._des.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'diumenge_dilluns_dimarts_dimecres_dijous_divendres_dissabte'.split('_'),\n    weekdaysShort: 'dg._dl._dt._dc._dj._dv._ds.'.split('_'),\n    weekdaysMin: 'dg_dl_dt_dc_dj_dv_ds'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM [de] YYYY',\n      ll: 'D MMM YYYY',\n      LLL: 'D MMMM [de] YYYY [a les] H:mm',\n      lll: 'D MMM YYYY, H:mm',\n      LLLL: 'dddd D MMMM [de] YYYY [a les] H:mm',\n      llll: 'ddd D MMM YYYY, H:mm'\n    },\n    calendar: {\n      sameDay: function () {\n        return '[avui a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n      },\n      nextDay: function () {\n        return '[demà a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n      },\n      nextWeek: function () {\n        return 'dddd [a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n      },\n      lastDay: function () {\n        return '[ahir a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n      },\n      lastWeek: function () {\n        return '[el] dddd [passat a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: \"d'aquí %s\",\n      past: 'fa %s',\n      s: 'uns segons',\n      ss: '%d segons',\n      m: 'un minut',\n      mm: '%d minuts',\n      h: 'una hora',\n      hh: '%d hores',\n      d: 'un dia',\n      dd: '%d dies',\n      M: 'un mes',\n      MM: '%d mesos',\n      y: 'un any',\n      yy: '%d anys'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(r|n|t|è|a)/,\n    ordinal: function (number, period) {\n      var output = number === 1 ? 'r' : number === 2 ? 'n' : number === 3 ? 'r' : number === 4 ? 't' : 'è';\n\n      if (period === 'w' || period === 'W') {\n        output = 'a';\n      }\n\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n\n    }\n  });\n  return ca;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "ca", "defineLocale", "months", "standalone", "split", "format", "isFormat", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "ll", "LLL", "lll", "LLLL", "llll", "calendar", "sameDay", "hours", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "period", "output", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/moment/locale/ca.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Catalan [ca]\n//! author : <PERSON> : https://github.com/juanghurtado\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var ca = moment.defineLocale('ca', {\n        months: {\n            standalone:\n                'gener_febrer_març_abril_maig_juny_juliol_agost_setembre_octubre_novembre_desembre'.split(\n                    '_'\n                ),\n            format: \"de gener_de febrer_de març_d'abril_de maig_de juny_de juliol_d'agost_de setembre_d'octubre_de novembre_de desembre\".split(\n                '_'\n            ),\n            isFormat: /D[oD]?(\\s)+MMMM/,\n        },\n        monthsShort:\n            'gen._febr._març_abr._maig_juny_jul._ag._set._oct._nov._des.'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays:\n            'diumenge_dilluns_dimarts_dimecres_dijous_divendres_dissabte'.split(\n                '_'\n            ),\n        weekdaysShort: 'dg._dl._dt._dc._dj._dv._ds.'.split('_'),\n        weekdaysMin: 'dg_dl_dt_dc_dj_dv_ds'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM [de] YYYY',\n            ll: 'D MMM YYYY',\n            LLL: 'D MMMM [de] YYYY [a les] H:mm',\n            lll: 'D MMM YYYY, H:mm',\n            LLLL: 'dddd D MMMM [de] YYYY [a les] H:mm',\n            llll: 'ddd D MMM YYYY, H:mm',\n        },\n        calendar: {\n            sameDay: function () {\n                return '[avui a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n            },\n            nextDay: function () {\n                return '[demà a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n            },\n            nextWeek: function () {\n                return 'dddd [a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n            },\n            lastDay: function () {\n                return '[ahir a ' + (this.hours() !== 1 ? 'les' : 'la') + '] LT';\n            },\n            lastWeek: function () {\n                return (\n                    '[el] dddd [passat a ' +\n                    (this.hours() !== 1 ? 'les' : 'la') +\n                    '] LT'\n                );\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: \"d'aquí %s\",\n            past: 'fa %s',\n            s: 'uns segons',\n            ss: '%d segons',\n            m: 'un minut',\n            mm: '%d minuts',\n            h: 'una hora',\n            hh: '%d hores',\n            d: 'un dia',\n            dd: '%d dies',\n            M: 'un mes',\n            MM: '%d mesos',\n            y: 'un any',\n            yy: '%d anys',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(r|n|t|è|a)/,\n        ordinal: function (number, period) {\n            var output =\n                number === 1\n                    ? 'r'\n                    : number === 2\n                      ? 'n'\n                      : number === 3\n                        ? 'r'\n                        : number === 4\n                          ? 't'\n                          : 'è';\n            if (period === 'w' || period === 'W') {\n                output = 'a';\n            }\n            return number + output;\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return ca;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AAEA;;AAAE,WAAUA,MAAV,EAAkBC,OAAlB,EAA2B;EAC1B,OAAOC,OAAP,KAAmB,QAAnB,IAA+B,OAAOC,MAAP,KAAkB,WAAjD,IACO,OAAOC,OAAP,KAAmB,UAD1B,GACuCH,OAAO,CAACG,OAAO,CAAC,WAAD,CAAR,CAD9C,GAEA,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAAvC,GAA6CD,MAAM,CAAC,CAAC,WAAD,CAAD,EAAgBJ,OAAhB,CAAnD,GACAA,OAAO,CAACD,MAAM,CAACO,MAAR,CAHP;AAIF,CALC,EAKA,IALA,EAKO,UAAUA,MAAV,EAAkB;EAAE,aAAF,CAEvB;;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAP,CAAoB,IAApB,EAA0B;IAC/BC,MAAM,EAAE;MACJC,UAAU,EACN,oFAAoFC,KAApF,CACI,GADJ,CAFA;MAKJC,MAAM,EAAE,qHAAqHD,KAArH,CACJ,GADI,CALJ;MAQJE,QAAQ,EAAE;IARN,CADuB;IAW/BC,WAAW,EACP,8DAA8DH,KAA9D,CACI,GADJ,CAZ2B;IAe/BI,gBAAgB,EAAE,IAfa;IAgB/BC,QAAQ,EACJ,8DAA8DL,KAA9D,CACI,GADJ,CAjB2B;IAoB/BM,aAAa,EAAE,8BAA8BN,KAA9B,CAAoC,GAApC,CApBgB;IAqB/BO,WAAW,EAAE,uBAAuBP,KAAvB,CAA6B,GAA7B,CArBkB;IAsB/BQ,kBAAkB,EAAE,IAtBW;IAuB/BC,cAAc,EAAE;MACZC,EAAE,EAAE,MADQ;MAEZC,GAAG,EAAE,SAFO;MAGZC,CAAC,EAAE,YAHS;MAIZC,EAAE,EAAE,kBAJQ;MAKZC,EAAE,EAAE,YALQ;MAMZC,GAAG,EAAE,+BANO;MAOZC,GAAG,EAAE,kBAPO;MAQZC,IAAI,EAAE,oCARM;MASZC,IAAI,EAAE;IATM,CAvBe;IAkC/BC,QAAQ,EAAE;MACNC,OAAO,EAAE,YAAY;QACjB,OAAO,cAAc,KAAKC,KAAL,OAAiB,CAAjB,GAAqB,KAArB,GAA6B,IAA3C,IAAmD,MAA1D;MACH,CAHK;MAINC,OAAO,EAAE,YAAY;QACjB,OAAO,cAAc,KAAKD,KAAL,OAAiB,CAAjB,GAAqB,KAArB,GAA6B,IAA3C,IAAmD,MAA1D;MACH,CANK;MAONE,QAAQ,EAAE,YAAY;QAClB,OAAO,cAAc,KAAKF,KAAL,OAAiB,CAAjB,GAAqB,KAArB,GAA6B,IAA3C,IAAmD,MAA1D;MACH,CATK;MAUNG,OAAO,EAAE,YAAY;QACjB,OAAO,cAAc,KAAKH,KAAL,OAAiB,CAAjB,GAAqB,KAArB,GAA6B,IAA3C,IAAmD,MAA1D;MACH,CAZK;MAaNI,QAAQ,EAAE,YAAY;QAClB,OACI,0BACC,KAAKJ,KAAL,OAAiB,CAAjB,GAAqB,KAArB,GAA6B,IAD9B,IAEA,MAHJ;MAKH,CAnBK;MAoBNK,QAAQ,EAAE;IApBJ,CAlCqB;IAwD/BC,YAAY,EAAE;MACVC,MAAM,EAAE,WADE;MAEVC,IAAI,EAAE,OAFI;MAGVC,CAAC,EAAE,YAHO;MAIVC,EAAE,EAAE,WAJM;MAKVC,CAAC,EAAE,UALO;MAMVC,EAAE,EAAE,WANM;MAOVC,CAAC,EAAE,UAPO;MAQVC,EAAE,EAAE,UARM;MASVC,CAAC,EAAE,QATO;MAUVC,EAAE,EAAE,SAVM;MAWVC,CAAC,EAAE,QAXO;MAYVC,EAAE,EAAE,UAZM;MAaVC,CAAC,EAAE,QAbO;MAcVC,EAAE,EAAE;IAdM,CAxDiB;IAwE/BC,sBAAsB,EAAE,oBAxEO;IAyE/BC,OAAO,EAAE,UAAUC,MAAV,EAAkBC,MAAlB,EAA0B;MAC/B,IAAIC,MAAM,GACNF,MAAM,KAAK,CAAX,GACM,GADN,GAEMA,MAAM,KAAK,CAAX,GACE,GADF,GAEEA,MAAM,KAAK,CAAX,GACE,GADF,GAEEA,MAAM,KAAK,CAAX,GACE,GADF,GAEE,GAThB;;MAUA,IAAIC,MAAM,KAAK,GAAX,IAAkBA,MAAM,KAAK,GAAjC,EAAsC;QAClCC,MAAM,GAAG,GAAT;MACH;;MACD,OAAOF,MAAM,GAAGE,MAAhB;IACH,CAxF8B;IAyF/BC,IAAI,EAAE;MACFC,GAAG,EAAE,CADH;MACM;MACRC,GAAG,EAAE,CAFH,CAEM;;IAFN;EAzFyB,CAA1B,CAAT;EA+FA,OAAOrD,EAAP;AAEH,CA1GC,CAAD", "ignoreList": []}, "metadata": {}, "sourceType": "script"}