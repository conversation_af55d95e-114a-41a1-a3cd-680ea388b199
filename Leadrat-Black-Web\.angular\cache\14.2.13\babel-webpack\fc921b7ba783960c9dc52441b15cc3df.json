{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Deferred } from '@firebase/util';\n/**\r\n * Component for service name T, e.g. `auth`, `auth-internal`\r\n */\n\nclass Component {\n  /**\r\n   *\r\n   * @param name The public service name, e.g. app, auth, firestore, database\r\n   * @param instanceFactory Service factory responsible for creating the public interface\r\n   * @param type whether the service provided by the component is public or private\r\n   */\n  constructor(name, instanceFactory, type) {\n    this.name = name;\n    this.instanceFactory = instanceFactory;\n    this.type = type;\n    this.multipleInstances = false;\n    /**\r\n     * Properties to be added to the service namespace\r\n     */\n\n    this.serviceProps = {};\n    this.instantiationMode = \"LAZY\"\n    /* InstantiationMode.LAZY */\n    ;\n    this.onInstanceCreated = null;\n  }\n\n  setInstantiationMode(mode) {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances) {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props) {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback) {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\r\n * NameServiceMapping[T] is an alias for the type of the instance\r\n */\n\nclass Provider {\n  constructor(name, container) {\n    this.name = name;\n    this.container = container;\n    this.component = null;\n    this.instances = new Map();\n    this.instancesDeferred = new Map();\n    this.instancesOptions = new Map();\n    this.onInitCallbacks = new Map();\n  }\n  /**\r\n   * @param identifier A provider can provide mulitple instances of a service\r\n   * if this.component.multipleInstances is true.\r\n   */\n\n\n  get(identifier) {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n\n    if (!this.instancesDeferred.has(normalizedIdentifier)) {\n      const deferred = new Deferred();\n      this.instancesDeferred.set(normalizedIdentifier, deferred);\n\n      if (this.isInitialized(normalizedIdentifier) || this.shouldAutoInitialize()) {\n        // initialize the service if it can be auto-initialized\n        try {\n          const instance = this.getOrInitializeService({\n            instanceIdentifier: normalizedIdentifier\n          });\n\n          if (instance) {\n            deferred.resolve(instance);\n          }\n        } catch (e) {// when the instance factory throws an exception during get(), it should not cause\n          // a fatal error. We just return the unresolved promise in this case.\n        }\n      }\n    }\n\n    return this.instancesDeferred.get(normalizedIdentifier).promise;\n  }\n\n  getImmediate(options) {\n    var _a; // if multipleInstances is not supported, use the default name\n\n\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(options === null || options === void 0 ? void 0 : options.identifier);\n    const optional = (_a = options === null || options === void 0 ? void 0 : options.optional) !== null && _a !== void 0 ? _a : false;\n\n    if (this.isInitialized(normalizedIdentifier) || this.shouldAutoInitialize()) {\n      try {\n        return this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n      } catch (e) {\n        if (optional) {\n          return null;\n        } else {\n          throw e;\n        }\n      }\n    } else {\n      // In case a component is not initialized and should/can not be auto-initialized at the moment, return null if the optional flag is set, or throw\n      if (optional) {\n        return null;\n      } else {\n        throw Error(`Service ${this.name} is not available`);\n      }\n    }\n  }\n\n  getComponent() {\n    return this.component;\n  }\n\n  setComponent(component) {\n    if (component.name !== this.name) {\n      throw Error(`Mismatching Component ${component.name} for Provider ${this.name}.`);\n    }\n\n    if (this.component) {\n      throw Error(`Component for ${this.name} has already been provided`);\n    }\n\n    this.component = component; // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\n\n    if (!this.shouldAutoInitialize()) {\n      return;\n    } // if the service is eager, initialize the default instance\n\n\n    if (isComponentEager(component)) {\n      try {\n        this.getOrInitializeService({\n          instanceIdentifier: DEFAULT_ENTRY_NAME\n        });\n      } catch (e) {// when the instance factory for an eager Component throws an exception during the eager\n        // initialization, it should not cause a fatal error.\n        // TODO: Investigate if we need to make it configurable, because some component may want to cause\n        // a fatal error in this case?\n      }\n    } // Create service instances for the pending promises and resolve them\n    // NOTE: if this.multipleInstances is false, only the default instance will be created\n    // and all promises with resolve with it regardless of the identifier.\n\n\n    for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()) {\n      const normalizedIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);\n\n      try {\n        // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\n        const instance = this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n        instanceDeferred.resolve(instance);\n      } catch (e) {// when the instance factory throws an exception, it should not cause\n        // a fatal error. We just leave the promise unresolved.\n      }\n    }\n  }\n\n  clearInstance(identifier = DEFAULT_ENTRY_NAME) {\n    this.instancesDeferred.delete(identifier);\n    this.instancesOptions.delete(identifier);\n    this.instances.delete(identifier);\n  } // app.delete() will call this method on every provider to delete the services\n  // TODO: should we mark the provider as deleted?\n\n\n  delete() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      const services = Array.from(_this.instances.values());\n      yield Promise.all([...services.filter(service => 'INTERNAL' in service) // legacy services\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      .map(service => service.INTERNAL.delete()), ...services.filter(service => '_delete' in service) // modularized services\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      .map(service => service._delete())]);\n    })();\n  }\n\n  isComponentSet() {\n    return this.component != null;\n  }\n\n  isInitialized(identifier = DEFAULT_ENTRY_NAME) {\n    return this.instances.has(identifier);\n  }\n\n  getOptions(identifier = DEFAULT_ENTRY_NAME) {\n    return this.instancesOptions.get(identifier) || {};\n  }\n\n  initialize(opts = {}) {\n    const {\n      options = {}\n    } = opts;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(opts.instanceIdentifier);\n\n    if (this.isInitialized(normalizedIdentifier)) {\n      throw Error(`${this.name}(${normalizedIdentifier}) has already been initialized`);\n    }\n\n    if (!this.isComponentSet()) {\n      throw Error(`Component ${this.name} has not been registered yet`);\n    }\n\n    const instance = this.getOrInitializeService({\n      instanceIdentifier: normalizedIdentifier,\n      options\n    }); // resolve any pending promise waiting for the service instance\n\n    for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()) {\n      const normalizedDeferredIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);\n\n      if (normalizedIdentifier === normalizedDeferredIdentifier) {\n        instanceDeferred.resolve(instance);\n      }\n    }\n\n    return instance;\n  }\n  /**\r\n   *\r\n   * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\r\n   * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\r\n   *\r\n   * @param identifier An optional instance identifier\r\n   * @returns a function to unregister the callback\r\n   */\n\n\n  onInit(callback, identifier) {\n    var _a;\n\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    const existingCallbacks = (_a = this.onInitCallbacks.get(normalizedIdentifier)) !== null && _a !== void 0 ? _a : new Set();\n    existingCallbacks.add(callback);\n    this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\n    const existingInstance = this.instances.get(normalizedIdentifier);\n\n    if (existingInstance) {\n      callback(existingInstance, normalizedIdentifier);\n    }\n\n    return () => {\n      existingCallbacks.delete(callback);\n    };\n  }\n  /**\r\n   * Invoke onInit callbacks synchronously\r\n   * @param instance the service instance`\r\n   */\n\n\n  invokeOnInitCallbacks(instance, identifier) {\n    const callbacks = this.onInitCallbacks.get(identifier);\n\n    if (!callbacks) {\n      return;\n    }\n\n    for (const callback of callbacks) {\n      try {\n        callback(instance, identifier);\n      } catch (_a) {// ignore errors in the onInit callback\n      }\n    }\n  }\n\n  getOrInitializeService({\n    instanceIdentifier,\n    options = {}\n  }) {\n    let instance = this.instances.get(instanceIdentifier);\n\n    if (!instance && this.component) {\n      instance = this.component.instanceFactory(this.container, {\n        instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\n        options\n      });\n      this.instances.set(instanceIdentifier, instance);\n      this.instancesOptions.set(instanceIdentifier, options);\n      /**\r\n       * Invoke onInit listeners.\r\n       * Note this.component.onInstanceCreated is different, which is used by the component creator,\r\n       * while onInit listeners are registered by consumers of the provider.\r\n       */\n\n      this.invokeOnInitCallbacks(instance, instanceIdentifier);\n      /**\r\n       * Order is important\r\n       * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\r\n       * makes `isInitialized()` return true.\r\n       */\n\n      if (this.component.onInstanceCreated) {\n        try {\n          this.component.onInstanceCreated(this.container, instanceIdentifier, instance);\n        } catch (_a) {// ignore errors in the onInstanceCreatedCallback\n        }\n      }\n    }\n\n    return instance || null;\n  }\n\n  normalizeInstanceIdentifier(identifier = DEFAULT_ENTRY_NAME) {\n    if (this.component) {\n      return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\n    } else {\n      return identifier; // assume multiple instances are supported before the component is provided.\n    }\n  }\n\n  shouldAutoInitialize() {\n    return !!this.component && this.component.instantiationMode !== \"EXPLICIT\"\n    /* InstantiationMode.EXPLICIT */\n    ;\n  }\n\n} // undefined should be passed to the service factory for the default instance\n\n\nfunction normalizeIdentifierForFactory(identifier) {\n  return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\n}\n\nfunction isComponentEager(component) {\n  return component.instantiationMode === \"EAGER\"\n  /* InstantiationMode.EAGER */\n  ;\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n\n/**\r\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\r\n */\n\n\nclass ComponentContainer {\n  constructor(name) {\n    this.name = name;\n    this.providers = new Map();\n  }\n  /**\r\n   *\r\n   * @param component Component being added\r\n   * @param overwrite When a component with the same name has already been registered,\r\n   * if overwrite is true: overwrite the existing component with the new component and create a new\r\n   * provider with the new component. It can be useful in tests where you want to use different mocks\r\n   * for different tests.\r\n   * if overwrite is false: throw an exception\r\n   */\n\n\n  addComponent(component) {\n    const provider = this.getProvider(component.name);\n\n    if (provider.isComponentSet()) {\n      throw new Error(`Component ${component.name} has already been registered with ${this.name}`);\n    }\n\n    provider.setComponent(component);\n  }\n\n  addOrOverwriteComponent(component) {\n    const provider = this.getProvider(component.name);\n\n    if (provider.isComponentSet()) {\n      // delete the existing provider from the container, so we can register the new component\n      this.providers.delete(component.name);\n    }\n\n    this.addComponent(component);\n  }\n  /**\r\n   * getProvider provides a type safe interface where it can only be called with a field name\r\n   * present in NameServiceMapping interface.\r\n   *\r\n   * Firebase SDKs providing services should extend NameServiceMapping interface to register\r\n   * themselves.\r\n   */\n\n\n  getProvider(name) {\n    if (this.providers.has(name)) {\n      return this.providers.get(name);\n    } // create a Provider for a service that hasn't registered with Firebase\n\n\n    const provider = new Provider(name, this);\n    this.providers.set(name, provider);\n    return provider;\n  }\n\n  getProviders() {\n    return Array.from(this.providers.values());\n  }\n\n}\n\nexport { Component, ComponentContainer, Provider };", "map": {"version": 3, "names": ["Deferred", "Component", "constructor", "name", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "DEFAULT_ENTRY_NAME", "Provider", "container", "component", "instances", "Map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instancesOptions", "onInitCallbacks", "get", "identifier", "normalizedIdentifier", "normalizeInstanceIdentifier", "has", "deferred", "set", "isInitialized", "shouldAutoInitialize", "instance", "getOrInitializeService", "instanceIdentifier", "resolve", "e", "promise", "getImmediate", "options", "_a", "optional", "Error", "getComponent", "setComponent", "isComponentEager", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entries", "clearInstance", "delete", "services", "Array", "from", "values", "Promise", "all", "filter", "service", "map", "INTERNAL", "_delete", "isComponentSet", "getOptions", "initialize", "opts", "normalizedDeferredIdentifier", "onInit", "existingCallbacks", "Set", "add", "existingInstance", "invokeOnInitCallbacks", "callbacks", "normalizeIdentifierForFactory", "undefined", "ComponentContainer", "providers", "addComponent", "provider", "get<PERSON><PERSON><PERSON>", "addOrOverwriteComponent", "getProviders"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/@firebase/component/dist/esm/index.esm2017.js"], "sourcesContent": ["import { Deferred } from '@firebase/util';\n\n/**\r\n * Component for service name T, e.g. `auth`, `auth-internal`\r\n */\r\nclass Component {\r\n    /**\r\n     *\r\n     * @param name The public service name, e.g. app, auth, firestore, database\r\n     * @param instanceFactory Service factory responsible for creating the public interface\r\n     * @param type whether the service provided by the component is public or private\r\n     */\r\n    constructor(name, instanceFactory, type) {\r\n        this.name = name;\r\n        this.instanceFactory = instanceFactory;\r\n        this.type = type;\r\n        this.multipleInstances = false;\r\n        /**\r\n         * Properties to be added to the service namespace\r\n         */\r\n        this.serviceProps = {};\r\n        this.instantiationMode = \"LAZY\" /* InstantiationMode.LAZY */;\r\n        this.onInstanceCreated = null;\r\n    }\r\n    setInstantiationMode(mode) {\r\n        this.instantiationMode = mode;\r\n        return this;\r\n    }\r\n    setMultipleInstances(multipleInstances) {\r\n        this.multipleInstances = multipleInstances;\r\n        return this;\r\n    }\r\n    setServiceProps(props) {\r\n        this.serviceProps = props;\r\n        return this;\r\n    }\r\n    setInstanceCreatedCallback(callback) {\r\n        this.onInstanceCreated = callback;\r\n        return this;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\r\n * NameServiceMapping[T] is an alias for the type of the instance\r\n */\r\nclass Provider {\r\n    constructor(name, container) {\r\n        this.name = name;\r\n        this.container = container;\r\n        this.component = null;\r\n        this.instances = new Map();\r\n        this.instancesDeferred = new Map();\r\n        this.instancesOptions = new Map();\r\n        this.onInitCallbacks = new Map();\r\n    }\r\n    /**\r\n     * @param identifier A provider can provide mulitple instances of a service\r\n     * if this.component.multipleInstances is true.\r\n     */\r\n    get(identifier) {\r\n        // if multipleInstances is not supported, use the default name\r\n        const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\r\n        if (!this.instancesDeferred.has(normalizedIdentifier)) {\r\n            const deferred = new Deferred();\r\n            this.instancesDeferred.set(normalizedIdentifier, deferred);\r\n            if (this.isInitialized(normalizedIdentifier) ||\r\n                this.shouldAutoInitialize()) {\r\n                // initialize the service if it can be auto-initialized\r\n                try {\r\n                    const instance = this.getOrInitializeService({\r\n                        instanceIdentifier: normalizedIdentifier\r\n                    });\r\n                    if (instance) {\r\n                        deferred.resolve(instance);\r\n                    }\r\n                }\r\n                catch (e) {\r\n                    // when the instance factory throws an exception during get(), it should not cause\r\n                    // a fatal error. We just return the unresolved promise in this case.\r\n                }\r\n            }\r\n        }\r\n        return this.instancesDeferred.get(normalizedIdentifier).promise;\r\n    }\r\n    getImmediate(options) {\r\n        var _a;\r\n        // if multipleInstances is not supported, use the default name\r\n        const normalizedIdentifier = this.normalizeInstanceIdentifier(options === null || options === void 0 ? void 0 : options.identifier);\r\n        const optional = (_a = options === null || options === void 0 ? void 0 : options.optional) !== null && _a !== void 0 ? _a : false;\r\n        if (this.isInitialized(normalizedIdentifier) ||\r\n            this.shouldAutoInitialize()) {\r\n            try {\r\n                return this.getOrInitializeService({\r\n                    instanceIdentifier: normalizedIdentifier\r\n                });\r\n            }\r\n            catch (e) {\r\n                if (optional) {\r\n                    return null;\r\n                }\r\n                else {\r\n                    throw e;\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            // In case a component is not initialized and should/can not be auto-initialized at the moment, return null if the optional flag is set, or throw\r\n            if (optional) {\r\n                return null;\r\n            }\r\n            else {\r\n                throw Error(`Service ${this.name} is not available`);\r\n            }\r\n        }\r\n    }\r\n    getComponent() {\r\n        return this.component;\r\n    }\r\n    setComponent(component) {\r\n        if (component.name !== this.name) {\r\n            throw Error(`Mismatching Component ${component.name} for Provider ${this.name}.`);\r\n        }\r\n        if (this.component) {\r\n            throw Error(`Component for ${this.name} has already been provided`);\r\n        }\r\n        this.component = component;\r\n        // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\r\n        if (!this.shouldAutoInitialize()) {\r\n            return;\r\n        }\r\n        // if the service is eager, initialize the default instance\r\n        if (isComponentEager(component)) {\r\n            try {\r\n                this.getOrInitializeService({ instanceIdentifier: DEFAULT_ENTRY_NAME });\r\n            }\r\n            catch (e) {\r\n                // when the instance factory for an eager Component throws an exception during the eager\r\n                // initialization, it should not cause a fatal error.\r\n                // TODO: Investigate if we need to make it configurable, because some component may want to cause\r\n                // a fatal error in this case?\r\n            }\r\n        }\r\n        // Create service instances for the pending promises and resolve them\r\n        // NOTE: if this.multipleInstances is false, only the default instance will be created\r\n        // and all promises with resolve with it regardless of the identifier.\r\n        for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()) {\r\n            const normalizedIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);\r\n            try {\r\n                // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\r\n                const instance = this.getOrInitializeService({\r\n                    instanceIdentifier: normalizedIdentifier\r\n                });\r\n                instanceDeferred.resolve(instance);\r\n            }\r\n            catch (e) {\r\n                // when the instance factory throws an exception, it should not cause\r\n                // a fatal error. We just leave the promise unresolved.\r\n            }\r\n        }\r\n    }\r\n    clearInstance(identifier = DEFAULT_ENTRY_NAME) {\r\n        this.instancesDeferred.delete(identifier);\r\n        this.instancesOptions.delete(identifier);\r\n        this.instances.delete(identifier);\r\n    }\r\n    // app.delete() will call this method on every provider to delete the services\r\n    // TODO: should we mark the provider as deleted?\r\n    async delete() {\r\n        const services = Array.from(this.instances.values());\r\n        await Promise.all([\r\n            ...services\r\n                .filter(service => 'INTERNAL' in service) // legacy services\r\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n                .map(service => service.INTERNAL.delete()),\r\n            ...services\r\n                .filter(service => '_delete' in service) // modularized services\r\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n                .map(service => service._delete())\r\n        ]);\r\n    }\r\n    isComponentSet() {\r\n        return this.component != null;\r\n    }\r\n    isInitialized(identifier = DEFAULT_ENTRY_NAME) {\r\n        return this.instances.has(identifier);\r\n    }\r\n    getOptions(identifier = DEFAULT_ENTRY_NAME) {\r\n        return this.instancesOptions.get(identifier) || {};\r\n    }\r\n    initialize(opts = {}) {\r\n        const { options = {} } = opts;\r\n        const normalizedIdentifier = this.normalizeInstanceIdentifier(opts.instanceIdentifier);\r\n        if (this.isInitialized(normalizedIdentifier)) {\r\n            throw Error(`${this.name}(${normalizedIdentifier}) has already been initialized`);\r\n        }\r\n        if (!this.isComponentSet()) {\r\n            throw Error(`Component ${this.name} has not been registered yet`);\r\n        }\r\n        const instance = this.getOrInitializeService({\r\n            instanceIdentifier: normalizedIdentifier,\r\n            options\r\n        });\r\n        // resolve any pending promise waiting for the service instance\r\n        for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()) {\r\n            const normalizedDeferredIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);\r\n            if (normalizedIdentifier === normalizedDeferredIdentifier) {\r\n                instanceDeferred.resolve(instance);\r\n            }\r\n        }\r\n        return instance;\r\n    }\r\n    /**\r\n     *\r\n     * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\r\n     * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\r\n     *\r\n     * @param identifier An optional instance identifier\r\n     * @returns a function to unregister the callback\r\n     */\r\n    onInit(callback, identifier) {\r\n        var _a;\r\n        const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\r\n        const existingCallbacks = (_a = this.onInitCallbacks.get(normalizedIdentifier)) !== null && _a !== void 0 ? _a : new Set();\r\n        existingCallbacks.add(callback);\r\n        this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\r\n        const existingInstance = this.instances.get(normalizedIdentifier);\r\n        if (existingInstance) {\r\n            callback(existingInstance, normalizedIdentifier);\r\n        }\r\n        return () => {\r\n            existingCallbacks.delete(callback);\r\n        };\r\n    }\r\n    /**\r\n     * Invoke onInit callbacks synchronously\r\n     * @param instance the service instance`\r\n     */\r\n    invokeOnInitCallbacks(instance, identifier) {\r\n        const callbacks = this.onInitCallbacks.get(identifier);\r\n        if (!callbacks) {\r\n            return;\r\n        }\r\n        for (const callback of callbacks) {\r\n            try {\r\n                callback(instance, identifier);\r\n            }\r\n            catch (_a) {\r\n                // ignore errors in the onInit callback\r\n            }\r\n        }\r\n    }\r\n    getOrInitializeService({ instanceIdentifier, options = {} }) {\r\n        let instance = this.instances.get(instanceIdentifier);\r\n        if (!instance && this.component) {\r\n            instance = this.component.instanceFactory(this.container, {\r\n                instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\r\n                options\r\n            });\r\n            this.instances.set(instanceIdentifier, instance);\r\n            this.instancesOptions.set(instanceIdentifier, options);\r\n            /**\r\n             * Invoke onInit listeners.\r\n             * Note this.component.onInstanceCreated is different, which is used by the component creator,\r\n             * while onInit listeners are registered by consumers of the provider.\r\n             */\r\n            this.invokeOnInitCallbacks(instance, instanceIdentifier);\r\n            /**\r\n             * Order is important\r\n             * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\r\n             * makes `isInitialized()` return true.\r\n             */\r\n            if (this.component.onInstanceCreated) {\r\n                try {\r\n                    this.component.onInstanceCreated(this.container, instanceIdentifier, instance);\r\n                }\r\n                catch (_a) {\r\n                    // ignore errors in the onInstanceCreatedCallback\r\n                }\r\n            }\r\n        }\r\n        return instance || null;\r\n    }\r\n    normalizeInstanceIdentifier(identifier = DEFAULT_ENTRY_NAME) {\r\n        if (this.component) {\r\n            return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\r\n        }\r\n        else {\r\n            return identifier; // assume multiple instances are supported before the component is provided.\r\n        }\r\n    }\r\n    shouldAutoInitialize() {\r\n        return (!!this.component &&\r\n            this.component.instantiationMode !== \"EXPLICIT\" /* InstantiationMode.EXPLICIT */);\r\n    }\r\n}\r\n// undefined should be passed to the service factory for the default instance\r\nfunction normalizeIdentifierForFactory(identifier) {\r\n    return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\r\n}\r\nfunction isComponentEager(component) {\r\n    return component.instantiationMode === \"EAGER\" /* InstantiationMode.EAGER */;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\r\n */\r\nclass ComponentContainer {\r\n    constructor(name) {\r\n        this.name = name;\r\n        this.providers = new Map();\r\n    }\r\n    /**\r\n     *\r\n     * @param component Component being added\r\n     * @param overwrite When a component with the same name has already been registered,\r\n     * if overwrite is true: overwrite the existing component with the new component and create a new\r\n     * provider with the new component. It can be useful in tests where you want to use different mocks\r\n     * for different tests.\r\n     * if overwrite is false: throw an exception\r\n     */\r\n    addComponent(component) {\r\n        const provider = this.getProvider(component.name);\r\n        if (provider.isComponentSet()) {\r\n            throw new Error(`Component ${component.name} has already been registered with ${this.name}`);\r\n        }\r\n        provider.setComponent(component);\r\n    }\r\n    addOrOverwriteComponent(component) {\r\n        const provider = this.getProvider(component.name);\r\n        if (provider.isComponentSet()) {\r\n            // delete the existing provider from the container, so we can register the new component\r\n            this.providers.delete(component.name);\r\n        }\r\n        this.addComponent(component);\r\n    }\r\n    /**\r\n     * getProvider provides a type safe interface where it can only be called with a field name\r\n     * present in NameServiceMapping interface.\r\n     *\r\n     * Firebase SDKs providing services should extend NameServiceMapping interface to register\r\n     * themselves.\r\n     */\r\n    getProvider(name) {\r\n        if (this.providers.has(name)) {\r\n            return this.providers.get(name);\r\n        }\r\n        // create a Provider for a service that hasn't registered with Firebase\r\n        const provider = new Provider(name, this);\r\n        this.providers.set(name, provider);\r\n        return provider;\r\n    }\r\n    getProviders() {\r\n        return Array.from(this.providers.values());\r\n    }\r\n}\n\nexport { Component, ComponentContainer, Provider };\n"], "mappings": ";AAAA,SAASA,QAAT,QAAyB,gBAAzB;AAEA;AACA;AACA;;AACA,MAAMC,SAAN,CAAgB;EACZ;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAW,CAACC,IAAD,EAAOC,eAAP,EAAwBC,IAAxB,EAA8B;IACrC,KAAKF,IAAL,GAAYA,IAAZ;IACA,KAAKC,eAAL,GAAuBA,eAAvB;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKC,iBAAL,GAAyB,KAAzB;IACA;AACR;AACA;;IACQ,KAAKC,YAAL,GAAoB,EAApB;IACA,KAAKC,iBAAL,GAAyB;IAAO;IAAhC;IACA,KAAKC,iBAAL,GAAyB,IAAzB;EACH;;EACDC,oBAAoB,CAACC,IAAD,EAAO;IACvB,KAAKH,iBAAL,GAAyBG,IAAzB;IACA,OAAO,IAAP;EACH;;EACDC,oBAAoB,CAACN,iBAAD,EAAoB;IACpC,KAAKA,iBAAL,GAAyBA,iBAAzB;IACA,OAAO,IAAP;EACH;;EACDO,eAAe,CAACC,KAAD,EAAQ;IACnB,KAAKP,YAAL,GAAoBO,KAApB;IACA,OAAO,IAAP;EACH;;EACDC,0BAA0B,CAACC,QAAD,EAAW;IACjC,KAAKP,iBAAL,GAAyBO,QAAzB;IACA,OAAO,IAAP;EACH;;AAlCW;AAqChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,kBAAkB,GAAG,WAA3B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;AACA,MAAMC,QAAN,CAAe;EACXhB,WAAW,CAACC,IAAD,EAAOgB,SAAP,EAAkB;IACzB,KAAKhB,IAAL,GAAYA,IAAZ;IACA,KAAKgB,SAAL,GAAiBA,SAAjB;IACA,KAAKC,SAAL,GAAiB,IAAjB;IACA,KAAKC,SAAL,GAAiB,IAAIC,GAAJ,EAAjB;IACA,KAAKC,iBAAL,GAAyB,IAAID,GAAJ,EAAzB;IACA,KAAKE,gBAAL,GAAwB,IAAIF,GAAJ,EAAxB;IACA,KAAKG,eAAL,GAAuB,IAAIH,GAAJ,EAAvB;EACH;EACD;AACJ;AACA;AACA;;;EACII,GAAG,CAACC,UAAD,EAAa;IACZ;IACA,MAAMC,oBAAoB,GAAG,KAAKC,2BAAL,CAAiCF,UAAjC,CAA7B;;IACA,IAAI,CAAC,KAAKJ,iBAAL,CAAuBO,GAAvB,CAA2BF,oBAA3B,CAAL,EAAuD;MACnD,MAAMG,QAAQ,GAAG,IAAI/B,QAAJ,EAAjB;MACA,KAAKuB,iBAAL,CAAuBS,GAAvB,CAA2BJ,oBAA3B,EAAiDG,QAAjD;;MACA,IAAI,KAAKE,aAAL,CAAmBL,oBAAnB,KACA,KAAKM,oBAAL,EADJ,EACiC;QAC7B;QACA,IAAI;UACA,MAAMC,QAAQ,GAAG,KAAKC,sBAAL,CAA4B;YACzCC,kBAAkB,EAAET;UADqB,CAA5B,CAAjB;;UAGA,IAAIO,QAAJ,EAAc;YACVJ,QAAQ,CAACO,OAAT,CAAiBH,QAAjB;UACH;QACJ,CAPD,CAQA,OAAOI,CAAP,EAAU,CACN;UACA;QACH;MACJ;IACJ;;IACD,OAAO,KAAKhB,iBAAL,CAAuBG,GAAvB,CAA2BE,oBAA3B,EAAiDY,OAAxD;EACH;;EACDC,YAAY,CAACC,OAAD,EAAU;IAClB,IAAIC,EAAJ,CADkB,CAElB;;;IACA,MAAMf,oBAAoB,GAAG,KAAKC,2BAAL,CAAiCa,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACf,UAA3F,CAA7B;IACA,MAAMiB,QAAQ,GAAG,CAACD,EAAE,GAAGD,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACE,QAAhE,MAA8E,IAA9E,IAAsFD,EAAE,KAAK,KAAK,CAAlG,GAAsGA,EAAtG,GAA2G,KAA5H;;IACA,IAAI,KAAKV,aAAL,CAAmBL,oBAAnB,KACA,KAAKM,oBAAL,EADJ,EACiC;MAC7B,IAAI;QACA,OAAO,KAAKE,sBAAL,CAA4B;UAC/BC,kBAAkB,EAAET;QADW,CAA5B,CAAP;MAGH,CAJD,CAKA,OAAOW,CAAP,EAAU;QACN,IAAIK,QAAJ,EAAc;UACV,OAAO,IAAP;QACH,CAFD,MAGK;UACD,MAAML,CAAN;QACH;MACJ;IACJ,CAfD,MAgBK;MACD;MACA,IAAIK,QAAJ,EAAc;QACV,OAAO,IAAP;MACH,CAFD,MAGK;QACD,MAAMC,KAAK,CAAE,WAAU,KAAK1C,IAAK,mBAAtB,CAAX;MACH;IACJ;EACJ;;EACD2C,YAAY,GAAG;IACX,OAAO,KAAK1B,SAAZ;EACH;;EACD2B,YAAY,CAAC3B,SAAD,EAAY;IACpB,IAAIA,SAAS,CAACjB,IAAV,KAAmB,KAAKA,IAA5B,EAAkC;MAC9B,MAAM0C,KAAK,CAAE,yBAAwBzB,SAAS,CAACjB,IAAK,iBAAgB,KAAKA,IAAK,GAAnE,CAAX;IACH;;IACD,IAAI,KAAKiB,SAAT,EAAoB;MAChB,MAAMyB,KAAK,CAAE,iBAAgB,KAAK1C,IAAK,4BAA5B,CAAX;IACH;;IACD,KAAKiB,SAAL,GAAiBA,SAAjB,CAPoB,CAQpB;;IACA,IAAI,CAAC,KAAKc,oBAAL,EAAL,EAAkC;MAC9B;IACH,CAXmB,CAYpB;;;IACA,IAAIc,gBAAgB,CAAC5B,SAAD,CAApB,EAAiC;MAC7B,IAAI;QACA,KAAKgB,sBAAL,CAA4B;UAAEC,kBAAkB,EAAEpB;QAAtB,CAA5B;MACH,CAFD,CAGA,OAAOsB,CAAP,EAAU,CACN;QACA;QACA;QACA;MACH;IACJ,CAvBmB,CAwBpB;IACA;IACA;;;IACA,KAAK,MAAM,CAACF,kBAAD,EAAqBY,gBAArB,CAAX,IAAqD,KAAK1B,iBAAL,CAAuB2B,OAAvB,EAArD,EAAuF;MACnF,MAAMtB,oBAAoB,GAAG,KAAKC,2BAAL,CAAiCQ,kBAAjC,CAA7B;;MACA,IAAI;QACA;QACA,MAAMF,QAAQ,GAAG,KAAKC,sBAAL,CAA4B;UACzCC,kBAAkB,EAAET;QADqB,CAA5B,CAAjB;QAGAqB,gBAAgB,CAACX,OAAjB,CAAyBH,QAAzB;MACH,CAND,CAOA,OAAOI,CAAP,EAAU,CACN;QACA;MACH;IACJ;EACJ;;EACDY,aAAa,CAACxB,UAAU,GAAGV,kBAAd,EAAkC;IAC3C,KAAKM,iBAAL,CAAuB6B,MAAvB,CAA8BzB,UAA9B;IACA,KAAKH,gBAAL,CAAsB4B,MAAtB,CAA6BzB,UAA7B;IACA,KAAKN,SAAL,CAAe+B,MAAf,CAAsBzB,UAAtB;EACH,CAvHU,CAwHX;EACA;;;EACMyB,MAAM,GAAG;IAAA;;IAAA;MACX,MAAMC,QAAQ,GAAGC,KAAK,CAACC,IAAN,CAAW,KAAI,CAAClC,SAAL,CAAemC,MAAf,EAAX,CAAjB;MACA,MAAMC,OAAO,CAACC,GAAR,CAAY,CACd,GAAGL,QAAQ,CACNM,MADF,CACSC,OAAO,IAAI,cAAcA,OADlC,EAC2C;MAC1C;MAFD,CAGEC,GAHF,CAGMD,OAAO,IAAIA,OAAO,CAACE,QAAR,CAAiBV,MAAjB,EAHjB,CADW,EAKd,GAAGC,QAAQ,CACNM,MADF,CACSC,OAAO,IAAI,aAAaA,OADjC,EAC0C;MACzC;MAFD,CAGEC,GAHF,CAGMD,OAAO,IAAIA,OAAO,CAACG,OAAR,EAHjB,CALW,CAAZ,CAAN;IAFW;EAYd;;EACDC,cAAc,GAAG;IACb,OAAO,KAAK5C,SAAL,IAAkB,IAAzB;EACH;;EACDa,aAAa,CAACN,UAAU,GAAGV,kBAAd,EAAkC;IAC3C,OAAO,KAAKI,SAAL,CAAeS,GAAf,CAAmBH,UAAnB,CAAP;EACH;;EACDsC,UAAU,CAACtC,UAAU,GAAGV,kBAAd,EAAkC;IACxC,OAAO,KAAKO,gBAAL,CAAsBE,GAAtB,CAA0BC,UAA1B,KAAyC,EAAhD;EACH;;EACDuC,UAAU,CAACC,IAAI,GAAG,EAAR,EAAY;IAClB,MAAM;MAAEzB,OAAO,GAAG;IAAZ,IAAmByB,IAAzB;IACA,MAAMvC,oBAAoB,GAAG,KAAKC,2BAAL,CAAiCsC,IAAI,CAAC9B,kBAAtC,CAA7B;;IACA,IAAI,KAAKJ,aAAL,CAAmBL,oBAAnB,CAAJ,EAA8C;MAC1C,MAAMiB,KAAK,CAAE,GAAE,KAAK1C,IAAK,IAAGyB,oBAAqB,gCAAtC,CAAX;IACH;;IACD,IAAI,CAAC,KAAKoC,cAAL,EAAL,EAA4B;MACxB,MAAMnB,KAAK,CAAE,aAAY,KAAK1C,IAAK,8BAAxB,CAAX;IACH;;IACD,MAAMgC,QAAQ,GAAG,KAAKC,sBAAL,CAA4B;MACzCC,kBAAkB,EAAET,oBADqB;MAEzCc;IAFyC,CAA5B,CAAjB,CATkB,CAalB;;IACA,KAAK,MAAM,CAACL,kBAAD,EAAqBY,gBAArB,CAAX,IAAqD,KAAK1B,iBAAL,CAAuB2B,OAAvB,EAArD,EAAuF;MACnF,MAAMkB,4BAA4B,GAAG,KAAKvC,2BAAL,CAAiCQ,kBAAjC,CAArC;;MACA,IAAIT,oBAAoB,KAAKwC,4BAA7B,EAA2D;QACvDnB,gBAAgB,CAACX,OAAjB,CAAyBH,QAAzB;MACH;IACJ;;IACD,OAAOA,QAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIkC,MAAM,CAACrD,QAAD,EAAWW,UAAX,EAAuB;IACzB,IAAIgB,EAAJ;;IACA,MAAMf,oBAAoB,GAAG,KAAKC,2BAAL,CAAiCF,UAAjC,CAA7B;IACA,MAAM2C,iBAAiB,GAAG,CAAC3B,EAAE,GAAG,KAAKlB,eAAL,CAAqBC,GAArB,CAAyBE,oBAAzB,CAAN,MAA0D,IAA1D,IAAkEe,EAAE,KAAK,KAAK,CAA9E,GAAkFA,EAAlF,GAAuF,IAAI4B,GAAJ,EAAjH;IACAD,iBAAiB,CAACE,GAAlB,CAAsBxD,QAAtB;IACA,KAAKS,eAAL,CAAqBO,GAArB,CAAyBJ,oBAAzB,EAA+C0C,iBAA/C;IACA,MAAMG,gBAAgB,GAAG,KAAKpD,SAAL,CAAeK,GAAf,CAAmBE,oBAAnB,CAAzB;;IACA,IAAI6C,gBAAJ,EAAsB;MAClBzD,QAAQ,CAACyD,gBAAD,EAAmB7C,oBAAnB,CAAR;IACH;;IACD,OAAO,MAAM;MACT0C,iBAAiB,CAAClB,MAAlB,CAAyBpC,QAAzB;IACH,CAFD;EAGH;EACD;AACJ;AACA;AACA;;;EACI0D,qBAAqB,CAACvC,QAAD,EAAWR,UAAX,EAAuB;IACxC,MAAMgD,SAAS,GAAG,KAAKlD,eAAL,CAAqBC,GAArB,CAAyBC,UAAzB,CAAlB;;IACA,IAAI,CAACgD,SAAL,EAAgB;MACZ;IACH;;IACD,KAAK,MAAM3D,QAAX,IAAuB2D,SAAvB,EAAkC;MAC9B,IAAI;QACA3D,QAAQ,CAACmB,QAAD,EAAWR,UAAX,CAAR;MACH,CAFD,CAGA,OAAOgB,EAAP,EAAW,CACP;MACH;IACJ;EACJ;;EACDP,sBAAsB,CAAC;IAAEC,kBAAF;IAAsBK,OAAO,GAAG;EAAhC,CAAD,EAAuC;IACzD,IAAIP,QAAQ,GAAG,KAAKd,SAAL,CAAeK,GAAf,CAAmBW,kBAAnB,CAAf;;IACA,IAAI,CAACF,QAAD,IAAa,KAAKf,SAAtB,EAAiC;MAC7Be,QAAQ,GAAG,KAAKf,SAAL,CAAehB,eAAf,CAA+B,KAAKe,SAApC,EAA+C;QACtDkB,kBAAkB,EAAEuC,6BAA6B,CAACvC,kBAAD,CADK;QAEtDK;MAFsD,CAA/C,CAAX;MAIA,KAAKrB,SAAL,CAAeW,GAAf,CAAmBK,kBAAnB,EAAuCF,QAAvC;MACA,KAAKX,gBAAL,CAAsBQ,GAAtB,CAA0BK,kBAA1B,EAA8CK,OAA9C;MACA;AACZ;AACA;AACA;AACA;;MACY,KAAKgC,qBAAL,CAA2BvC,QAA3B,EAAqCE,kBAArC;MACA;AACZ;AACA;AACA;AACA;;MACY,IAAI,KAAKjB,SAAL,CAAeX,iBAAnB,EAAsC;QAClC,IAAI;UACA,KAAKW,SAAL,CAAeX,iBAAf,CAAiC,KAAKU,SAAtC,EAAiDkB,kBAAjD,EAAqEF,QAArE;QACH,CAFD,CAGA,OAAOQ,EAAP,EAAW,CACP;QACH;MACJ;IACJ;;IACD,OAAOR,QAAQ,IAAI,IAAnB;EACH;;EACDN,2BAA2B,CAACF,UAAU,GAAGV,kBAAd,EAAkC;IACzD,IAAI,KAAKG,SAAT,EAAoB;MAChB,OAAO,KAAKA,SAAL,CAAed,iBAAf,GAAmCqB,UAAnC,GAAgDV,kBAAvD;IACH,CAFD,MAGK;MACD,OAAOU,UAAP,CADC,CACkB;IACtB;EACJ;;EACDO,oBAAoB,GAAG;IACnB,OAAQ,CAAC,CAAC,KAAKd,SAAP,IACJ,KAAKA,SAAL,CAAeZ,iBAAf,KAAqC;IAAW;IADpD;EAEH;;AAxPU,C,CA0Pf;;;AACA,SAASoE,6BAAT,CAAuCjD,UAAvC,EAAmD;EAC/C,OAAOA,UAAU,KAAKV,kBAAf,GAAoC4D,SAApC,GAAgDlD,UAAvD;AACH;;AACD,SAASqB,gBAAT,CAA0B5B,SAA1B,EAAqC;EACjC,OAAOA,SAAS,CAACZ,iBAAV,KAAgC;EAAQ;EAA/C;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMsE,kBAAN,CAAyB;EACrB5E,WAAW,CAACC,IAAD,EAAO;IACd,KAAKA,IAAL,GAAYA,IAAZ;IACA,KAAK4E,SAAL,GAAiB,IAAIzD,GAAJ,EAAjB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACI0D,YAAY,CAAC5D,SAAD,EAAY;IACpB,MAAM6D,QAAQ,GAAG,KAAKC,WAAL,CAAiB9D,SAAS,CAACjB,IAA3B,CAAjB;;IACA,IAAI8E,QAAQ,CAACjB,cAAT,EAAJ,EAA+B;MAC3B,MAAM,IAAInB,KAAJ,CAAW,aAAYzB,SAAS,CAACjB,IAAK,qCAAoC,KAAKA,IAAK,EAApF,CAAN;IACH;;IACD8E,QAAQ,CAAClC,YAAT,CAAsB3B,SAAtB;EACH;;EACD+D,uBAAuB,CAAC/D,SAAD,EAAY;IAC/B,MAAM6D,QAAQ,GAAG,KAAKC,WAAL,CAAiB9D,SAAS,CAACjB,IAA3B,CAAjB;;IACA,IAAI8E,QAAQ,CAACjB,cAAT,EAAJ,EAA+B;MAC3B;MACA,KAAKe,SAAL,CAAe3B,MAAf,CAAsBhC,SAAS,CAACjB,IAAhC;IACH;;IACD,KAAK6E,YAAL,CAAkB5D,SAAlB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI8D,WAAW,CAAC/E,IAAD,EAAO;IACd,IAAI,KAAK4E,SAAL,CAAejD,GAAf,CAAmB3B,IAAnB,CAAJ,EAA8B;MAC1B,OAAO,KAAK4E,SAAL,CAAerD,GAAf,CAAmBvB,IAAnB,CAAP;IACH,CAHa,CAId;;;IACA,MAAM8E,QAAQ,GAAG,IAAI/D,QAAJ,CAAaf,IAAb,EAAmB,IAAnB,CAAjB;IACA,KAAK4E,SAAL,CAAe/C,GAAf,CAAmB7B,IAAnB,EAAyB8E,QAAzB;IACA,OAAOA,QAAP;EACH;;EACDG,YAAY,GAAG;IACX,OAAO9B,KAAK,CAACC,IAAN,CAAW,KAAKwB,SAAL,CAAevB,MAAf,EAAX,CAAP;EACH;;AA/CoB;;AAkDzB,SAASvD,SAAT,EAAoB6E,kBAApB,EAAwC5D,QAAxC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}