import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import {
  BHK_NO_ALL,
  BHK_TYPE,
  FACING,
  FURNISH_STATUS,
  NUMBER_5,
} from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getBHKDisplayString, getPages, getProjectSubTypeDisplayName, onlyNumbersWithDecimal } from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import {
  FetchAreaUnitList
} from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'unit-info-advance-filters',
  templateUrl: './unit-info-advance-filters.component.html',
})
export class UnitInfoAdvanceFiltersComponent implements OnInit {
  @Output() filterEmitter: EventEmitter<any> = new EventEmitter<any>();
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  appliedFilter: any = {};
  areaUnit: Array<any>;
  currencyList: any[] = [];
  defaultCurrency: string;
  allUnitTypes: Array<any> = JSON.parse(localStorage.getItem('projectType') || '[]');
  unitSubTypes: any[];
  bhkNoList: Array<string> = BHK_NO_ALL;
  bhkType: Array<string> = BHK_TYPE;
  furnishingStatusList: Array<{ dispName: string; value: string }> =
    FURNISH_STATUS;
  facingList: Array<{ displayName: string; value: string }> = FACING;
  facingPayload: any = [];
  facingArray: any = [];
  numbers: Array<{ display: string; value: string }> = NUMBER_5;
  getPages = getPages;
  getBHKDisplayString = getBHKDisplayString;
  onlyNumbersWithDecimal = onlyNumbersWithDecimal
  areaValidations: boolean = true;
  carpetAreaValidations: boolean = true;
  builtupAreaValidations: boolean = true;
  superBuiltupAreaValidations: boolean = true;
  projectSubType: any;
  projectTypeList: Array<any> = JSON.parse(localStorage.getItem('projectType') || '[]');
  getProjectSubTypeDisplayName = getProjectSubTypeDisplayName
  constructor(
    private _store: Store<AppState>,
    public modalService: BsModalService,
    private sharedDataService: ShareDataService

  ) { }

  ngOnInit(): void {
    this._store.dispatch(new FetchAreaUnitList());
    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaUnit = units || [];
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.currencyList = data?.countries?.length
          ? data.countries[0].currencies
          : null;
        this.defaultCurrency = data?.countries?.length
          ? data.countries[0].defaultCurrency
          : null;
      });

    this.unitSubTypes = [];
    this.allUnitTypes?.map((item: any) => {
      item.childTypes?.map((item: any) => {
        this.unitSubTypes.push(item);
      });
    });
    this.sharedDataService.projectSubType$.subscribe((subTypeId: any) => {
      this.projectSubType = getProjectSubTypeDisplayName(subTypeId);
    });
  }

  changeUnitType(data: any) {
    this.unitSubTypes = [];
    if (!data?.length) {
      this.allUnitTypes?.map((item: any) => {
        item.childTypes?.map((item: any) => {
          this.unitSubTypes.push(item);
        });
      });
    } else {
      this.allUnitTypes?.map((item: any) => {
        if (data.includes(item?.id)) {
          item.childTypes?.map((item: any) => {
            this.unitSubTypes.push(item);
          });
        }
      });
    }
  }

  applyAdvancedFilter() {
    this.modalService.hide();
    const updatedData = {
      appliedFilter: this.appliedFilter,
    };
    this.filterEmitter.emit(updatedData);
  }

  validateArea(): void {
    const minArea = this.appliedFilter.MinArea;
    const maxArea = this.appliedFilter.MaxArea;

    if (minArea !== null && maxArea !== null && maxArea < minArea) {
      this.areaValidations = false;
    } else {
      this.areaValidations = true;
    }
  }

  validateCarpetArea(): void {
    const minArea = this.appliedFilter.MinCarpetArea;
    const maxArea = this.appliedFilter.MaxCarpetArea;

    if (minArea !== null && maxArea !== null && maxArea < minArea) {
      this.carpetAreaValidations = false;
    } else {
      this.carpetAreaValidations = true;
    }
  }

  validateBuiltupArea(): void {
    const minArea = this.appliedFilter.MinBuiltupArea;
    const maxArea = this.appliedFilter.MaxBuiltupArea;

    if (minArea !== null && maxArea !== null && maxArea < minArea) {
      this.builtupAreaValidations = false;
    } else {
      this.builtupAreaValidations = true;
    }
  }

  validateSuperBuiltupArea(): void {
    const minArea = this.appliedFilter.MinSuperBuiltupArea;
    const maxArea = this.appliedFilter.MaxSuperBuiltupArea;

    if (minArea !== null && maxArea !== null && maxArea < minArea) {
      this.superBuiltupAreaValidations = false;
    } else {
      this.superBuiltupAreaValidations = true;
    }
  }

  reset() {
    for (let key in this.appliedFilter) {
      if (this.appliedFilter.hasOwnProperty(key)) {
        this.appliedFilter[key] = null;
        this.facingPayload = [];
        this.facingArray = [];
      }
    }
    const updatedData = {
      appliedFilter: this.appliedFilter,
    };
    this.filterEmitter.emit(updatedData);
  }
}
