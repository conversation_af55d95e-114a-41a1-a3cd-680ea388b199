import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  HostListener,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  TemplateRef
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject, takeUntil } from 'rxjs';
import {
  EVENT_TYPE,
  PAGE_SIZE,
  PF_REQUEST_TYPES,
  REQUEST_TYPE_MAPPING,
  REQUEST_TYPES,
  SHOW_ENTRIES,
  VALIDATION_CLEAR,
  VALIDATION_SET,
} from 'src/app/app.constants';
import { EventType, IntegrationSource, PFRequestType, RequestType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  getAppName,
  getPages,
  toggleValidation,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import {
  AddBayut,
  AddCommonFloor,
  AddDubizzle,
  AddIntegration,
  AddJustLead,
  AddPropertyFinder,
  AssignPageSize,
  DeleteIntegration,
  DoesExistAccountName,
  FetchAgencyNameList,
  FetchIntegrationById,
  FetchIntegrationByIdSuccess,
  FetchIntegrationList,
  UpdateBayut,
  UpdateCommonFloor,
  UpdateDubizzle,
  UpdateJustLead,
  UpdatePropertyFinder,
} from 'src/app/reducers/Integration/integration.actions';
import {
  doesAccountNameExists,
  getAgencyNameList,
  getAgencyNameListIsLoading,
  getExcelFileLink,
  getIntegrationDetails,
  getIntegrationDetailsIsLoading,
} from 'src/app/reducers/Integration/integration.reducer';
import {
  FetchIntegrationAssignment,
  FetchPriorityList,
  updateIntegrationAssignment
} from 'src/app/reducers/automation/automation.actions';
import {
  getIntegrationAssignment,
  getPriorityList,
  getUserAssignmentByEntity,
} from 'src/app/reducers/automation/automation.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchCampaignList, FetchChannelPartnerList, FetchPropertyList } from 'src/app/reducers/lead/lead.actions';
import { getCampaignList, getCampaignListIsLoading, getChannelPartnerList, getChannelPartnerListIsLoading, getPropertyList, getPropertyListIsLoading } from 'src/app/reducers/lead/lead.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchProjectIdWithName } from 'src/app/reducers/project/project.action';
import { getProjectsIDWithName } from 'src/app/reducers/project/project.reducer';
import { FetchAllLocations } from 'src/app/reducers/site/site.actions';
import { getAllLocations } from 'src/app/reducers/site/site.reducer';
import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { IntegrationAssignmentV2Component } from '../integration-assignment-v2/integration-assignment-v2.component';

@Component({
  selector: 'common-floor',
  templateUrl: './common-floor.component.html',
})
// Common Floor , Just Lead , Property Finder , Bayut //
export class CommonFloorComponent implements OnInit, OnDestroy {
  public searchTermSubject = new Subject<string>();
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  integrateForm: FormGroup;
  addJustLeadForm: FormGroup;
  propertyFinderForm: FormGroup;
  bayutForm: FormGroup;
  dubizzleForm: FormGroup;
  // accountName: FormControl = new FormControl('', Validators.required);
  assignedUser: any[] = [];
  assignedDuplicateUser: any[] = [];
  project: FormControl = new FormControl(null);
  property: FormControl = new FormControl(null);
  location: FormControl = new FormControl(null);
  countryCode: FormControl = new FormControl(null);
  agencyName: FormControl = new FormControl(null);
  campaign: FormControl = new FormControl(null);
  channelPartner: FormControl = new FormControl(null);
  name: string = '';
  domainName: URL = new URL(location.href);
  updatedIntegrationList: Array<any> = [];
  assignedUserDetails: Array<string> = [];
  userList: Array<any> = [];
  allUserList: Array<any> = [];
  activeUsers: Array<any> = [];
  allActiveUsers: Array<any> = [];
  allProjectList: Array<any> = [];
  placesList: Array<any> = [];
  moduleId: string;
  searchTerm: string;
  selectedAccountName: string;
  leadSource: number;
  selectedAccountId: string = '';
  selectedLeadSource: string;
  canAdd: boolean = false;
  canDelete: boolean = false;
  canAssign: boolean = false;
  canAssignToAny: boolean = false;
  isShowContentAndAdd: boolean = true;
  isShowAddAccountBtn: boolean = true;
  isShowAddAtListing: boolean = true;
  isShowAssignModal: boolean = false;
  isShowProjectAndLocationModal: boolean = false;
  selectedCount: number = 0;
  isBulkAssignModel: boolean = false;
  editCred: boolean = false;
  selectedIntegrations: any[] = [];
  isManualChange: boolean = true;
  displayName: string;
  lastClickedOption: any;
  cities: any = [];
  canAllowDuplicates: boolean = false;
  canEnableAllowDuplicates: boolean = false;
  canAllowSecondaryUsers: boolean = false;
  message: string = '';
  notes: string = '';
  canEnableAllowSecondaryUsers: boolean = false;
  assignedSecondaryUsers: any[] = [];
  assignedPrimaryUsers: any[] = [];
  sameAsPrimaryUsers: boolean = false;
  sameAsSelectedUsers: boolean = false;
  sameAsAbove: boolean = false;
  integrationDuplicateForm: FormGroup;
  integrationDualOwnerForm: FormGroup;
  isIntegrationLoading: boolean = true;
  isShowCountryCodeModal: boolean = false;
  preferredCountries: any[] = [];
  integrationAssignmentData: any;
  // @ViewChild('contactNoInput') contactNoInput!: NgxMatIntlTelInputComponent;
  requestTypes = REQUEST_TYPES;
  pfRequestType = PF_REQUEST_TYPES;
  eventTypes = EVENT_TYPE;
  doesAccountNameExist: any;
  canBulkAssignment: boolean;
  canBulkReassign: boolean;
  getAppName = getAppName;
  receivedData: any;
  image: any;
  accountDetails: any;
  count: any;
  canViewForFilter: boolean;
  currOffset: number = 0;
  currPageNumber: number = 1;
  PageSize: number = PAGE_SIZE;
  totalCount: number;
  getPages = getPages;
  showEntriesSize: number[] = SHOW_ENTRIES;
  pageEntry: FormControl = new FormControl(this.PageSize);
  rowData: any[];
  filtersPayload: any;
  agencyListIsLoading: boolean;
  channelPartnerList: any;
  channelPartnerListIsLoading: boolean;
  campaignList: any;
  campaignListIsLoading: boolean;
  agencyNameList: any;
  propertyList: any;
  propertyListIsLoading: boolean;

  getRequestTypeDisplay(type: string): string {
    return REQUEST_TYPE_MAPPING[type] || type;
  }

  constructor(
    private store: Store<AppState>,
    public modalService: BsModalService,
    private modalRef: BsModalRef,
    private fb: FormBuilder,
    public router: Router,
    private cdr: ChangeDetectorRef,
    private _notificationService: NotificationsService,
    private _translateService: TranslateService,
    private headerTitle: HeaderTitleService,
  ) {
    this.headerTitle.setLangTitle('Integration');
    this.integrationDuplicateForm = this.fb.group({
      assignedUser: [null, [Validators.required]],
      assignedDuplicateUser: [null, [Validators.required]],
    });
    const storedData = localStorage.getItem('integrationData');
    if (storedData) {
      this.receivedData = JSON.parse(storedData);
      this.image = this.receivedData.image;
      this.displayName = this.receivedData.displayName;
      this.name = this.receivedData.name;
      this.count = this.receivedData.count;
    }

    this.integrationDualOwnerForm = this.fb.group({
      assignedPrimaryUsers: [null, [Validators.required]],
      assignedSecondaryUsers: [null, [Validators.required]],
      assignedDuplicateUser: [null, [Validators.required]],
      selectedUserType: ['Primary User(s)', [Validators.required]],
    });

    this.integrateForm = this.fb.group({
      accountName: ['', [Validators.required, ValidationUtil.cannotBeBlank]],
      loginEmail: [null, ValidationUtil.emailValidatorMinLength],
      secretId: [''],
      secretKey: [''],
      apiType: ['push'],
      toRecipients: this.fb.array([], [Validators.required, Validators.minLength(1)]),
      ccRecipients: this.fb.array([]),
      bccRecipients: this.fb.array([])
    });

    this.addJustLeadForm = this.fb.group({
      accountName: ['', [Validators.required, ValidationUtil.cannotBeBlank]],
      loginEmail: [null, ValidationUtil.emailValidatorMinLength],
      userId: [''],
      userName: [''],
      password: [''],
      apiType: ['push'],
      toRecipients: this.fb.array([], [Validators.required, Validators.minLength(1)]),
      ccRecipients: this.fb.array([]),
      bccRecipients: this.fb.array([])
    });

    this.propertyFinderForm = this.fb.group({
      accountName: ['', [Validators.required, ValidationUtil.cannotBeBlank]],
      loginEmail: [null, ValidationUtil.emailValidatorMinLength],
      apiKey: [''],
      requestType: [null],
      secretKey: [''],
      apiType: ['push'],
      event: [null],
      pullApiKey: [null],
      pullSecretKey: [null],
      // toRecipients: this.fb.array([], [Validators.required, Validators.minLength(1)]),
      // ccRecipients: this.fb.array([]),
      // bccRecipients: this.fb.array([])
    });

    this.bayutForm = this.fb.group({
      accountName: ['', [Validators.required, ValidationUtil.cannotBeBlank]],
      loginEmail: [null, ValidationUtil.emailValidatorMinLength],
      requestType: [null],
      apiKey: [''],
      apiType: ['push'],
      // toRecipients: this.fb.array([], [Validators.required, Validators.minLength(1)]),
      // ccRecipients: this.fb.array([]),
      // bccRecipients: this.fb.array([])
    });

    this.dubizzleForm = this.fb.group({
      accountName: ['', [Validators.required, ValidationUtil.cannotBeBlank]],
      loginEmail: [null, ValidationUtil.emailValidatorMinLength],
      requestType: [null],
      apiKey: [''],
      apiType: ['push'],
      // toRecipients: this.fb.array([], [Validators.required, Validators.minLength(1)]),
      // ccRecipients: this.fb.array([]),
      // bccRecipients: this.fb.array([])
    });

    this.addJustLeadForm.get('apiType').valueChanges.subscribe((val: any) => {
      if (val == 'pull') {
        toggleValidation(VALIDATION_SET, this.addJustLeadForm, 'userId', [
          Validators.required,
        ]);
        toggleValidation(VALIDATION_SET, this.addJustLeadForm, 'userName', [
          Validators.pattern(/^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+$/),
        ]);
        toggleValidation(VALIDATION_SET, this.addJustLeadForm, 'password', [
          Validators.required,
        ]);
      } else {
        toggleValidation(VALIDATION_CLEAR, this.addJustLeadForm, 'userId');
        toggleValidation(VALIDATION_CLEAR, this.addJustLeadForm, 'userName');
        toggleValidation(VALIDATION_CLEAR, this.addJustLeadForm, 'password');
      }
      this.addJustLeadForm.patchValue({
        userId: null,
        userName: null,
        password: null,
      });
    });

    this.integrateForm.get('apiType').valueChanges.subscribe((val: any) => {
      if (val == 'pull') {
        toggleValidation(VALIDATION_SET, this.integrateForm, 'secretId', [
          Validators.required,
        ]);
        toggleValidation(VALIDATION_SET, this.integrateForm, 'secretKey', [
          Validators.required,
        ]);
      } else {
        toggleValidation(VALIDATION_CLEAR, this.integrateForm, 'secretId');
        toggleValidation(VALIDATION_CLEAR, this.integrateForm, 'secretKey');
      }
      this.integrateForm.patchValue({
        secretId: null,
        secretKey: null,
      });
    });

    this.propertyFinderForm
      .get('apiType')
      .valueChanges.subscribe((val: any) => {
        if (val == 'pull') {
          toggleValidation(
            VALIDATION_SET,
            this.propertyFinderForm,
            'secretKey',
            [Validators.required]
          );
          toggleValidation(VALIDATION_SET, this.propertyFinderForm, 'apiKey', [
            Validators.required,
          ]);
          toggleValidation(VALIDATION_SET, this.propertyFinderForm, 'requestType', [
            Validators.required,
          ]);
        } else {
          toggleValidation(
            VALIDATION_CLEAR,
            this.propertyFinderForm,
            'secretKey'
          );
          toggleValidation(VALIDATION_CLEAR, this.propertyFinderForm, 'apiKey');
          toggleValidation(VALIDATION_CLEAR, this.propertyFinderForm, 'requestType');
        }
        this.propertyFinderForm.patchValue({
          secretKey: null,
          apiKey: null,
          requestType: null,
        });
      });

    this.bayutForm.get('apiType').valueChanges.subscribe((val: any) => {
      if (val == 'pull') {
        toggleValidation(VALIDATION_SET, this.bayutForm, 'apiKey', [
          Validators.required,
        ]);
        toggleValidation(VALIDATION_SET, this.bayutForm, 'requestType', [
          Validators.required,
        ]);
      } else {
        toggleValidation(VALIDATION_CLEAR, this.bayutForm, 'apiKey');
        toggleValidation(VALIDATION_CLEAR, this.bayutForm, 'requestType');
      }
      this.bayutForm.patchValue({
        requestType: null,
        apiKey: null,
      });
    });

    this.dubizzleForm.get('apiType').valueChanges.subscribe((val: any) => {
      if (val == 'pull') {
        toggleValidation(VALIDATION_SET, this.dubizzleForm, 'apiKey', [
          Validators.required,
        ]);
        toggleValidation(VALIDATION_SET, this.dubizzleForm, 'requestType', [
          Validators.required,
        ]);
      } else {
        toggleValidation(VALIDATION_CLEAR, this.dubizzleForm, 'apiKey');
        toggleValidation(VALIDATION_CLEAR, this.dubizzleForm, 'requestType');
      }
      this.dubizzleForm.patchValue({
        requestType: null,
        apiKey: null,
      });
    });

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canBulkAssignment = permissionsSet.has('Permissions.GlobalSettings.BulkAssignment');
        this.canBulkReassign = permissionsSet.has('Permissions.GlobalSettings.BulkReassign');
        this.canAdd = permissionsSet.has('Permissions.Integration.Create');
        this.canDelete = permissionsSet.has('Permissions.Integration.Delete');
        this.canAssign = permissionsSet.has('Permissions.Integration.Assign');

        if (permissions?.includes('Permissions.Users.ViewForFilter')) {
          this.canViewForFilter = true;
          if (permissionsSet.has('Permissions.Users.AssignToAny')) {
            this.canAssignToAny = true;
            this.store.dispatch(new FetchUsersListForReassignment());
          } else {
            this.store.dispatch(new FetchAdminsAndReportees());
          }
        }
      });

    this.store
      .select(getIntegrationDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.leadSource = data?.leadSource;
        this.updatedIntegrationList = data?.items?.filter(
          (item: any) =>
            item.leadSource ==
            IntegrationSource[this.name as keyof typeof IntegrationSource]
        )
          .map((item: any) => ({ ...item, isSelected: false }));
        this.rowData = this?.updatedIntegrationList;
        this.totalCount = data?.totalCount;
      });

    this.store
      .select(getIntegrationDetailsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isIntegrationLoading = isLoading;
      });

    this.store
      .select(getAllLocations)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.placesList = data?.items
          ?.slice()
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
      });

    this.store
      .select(getProjectsIDWithName)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any[]) => {
        this.allProjectList = res
          ?.filter((data: any) => data.name)
          .slice()
          .sort((a: any, b: any) => a.name.localeCompare(b.name));
      });

    this.store
      .select(getPropertyList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.propertyList = data
          .filter((item: any) => !item?.isArchived && item?.title != null)
          .slice()
          .sort((a: any, b: any) => {
            const titleA = a?.title ?? '';
            const titleB = b?.title ?? '';
            return titleA.localeCompare(titleB);
          });
      });

    this.store
      .select(getPropertyListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.propertyListIsLoading = isLoading;
      });

    this.store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userList = data;
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');
        this.selectAllForDropdownItems(this.activeUsers);
      });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUserList = data;
        this.allActiveUsers = data?.filter((user: any) => user.isActive);
        this.allActiveUsers = this.allActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allActiveUsers = assignToSort(this.allActiveUsers, '');
        this.selectAllForDropdownItems(this.allActiveUsers);
      });
  }

  ngOnInit() {
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.canEnableAllowDuplicates =
          data?.duplicateFeatureInfo?.isFeatureAdded;
        this.canEnableAllowSecondaryUsers = data?.isDualOwnershipEnabled;
      });
    this.store
      .select(getIntegrationAssignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (Object.keys(data).length) {
          // this.countryCode.setValue(data?.countryCode);
          const projectId =
            !data.project?.isDeleted && !data.project?.isArchived
              ? data.project?.id
              : null;
          this.project.patchValue(projectId);
          this.location.patchValue(data?.location?.id);
          this.property.patchValue(!data.property?.isDeleted && !data.property?.isArchived
            ? data.property?.title
            : null);
          this.location.patchValue(data?.location?.id);
          this.agencyName.patchValue(!data.agency?.isDeleted
            ? data.agency?.name
            : null);
          this.campaign.patchValue(!data.campaign?.isDeleted
            ? data.campaign?.name
            : null);
          this.channelPartner.patchValue(!data.channelPartner?.isDeleted
            ? data.channelPartner?.firmName
            : null);
          // this.updatePreferredCountry(data?.countryCode);
        } else {
          this.project.patchValue(null);
          this.property.patchValue(null);
          this.location.patchValue(null);
          this.agencyName.patchValue(null);
          this.campaign.patchValue(null);
          this.channelPartner.patchValue(null);
          // this.updatePreferredCountry(null);
        }
      });

    this.store
      .select(getAgencyNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        this.agencyNameList = item
          .filter((data: any) => data)
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.store
      .select(getAgencyNameListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.agencyListIsLoading = loading
      });

    this.store
      .select(getChannelPartnerList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.channelPartnerList = data
          .slice()
          .filter((item: any) => item != null)
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.store
      .select(getChannelPartnerListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.channelPartnerListIsLoading = loading
      });

    this.store
      .select(getCampaignList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.campaignList = data
          .slice()
          .sort((a: any, b: any) => a.localeCompare(b));
      });

    this.store
      .select(getCampaignListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.campaignListIsLoading = loading
      });

    this.searchTermSubject.subscribe((data: any) => {
      this.searchTerm = data;
      this.dispatchFetchIntegrationList();
    });
    this.dispatchFetchIntegrationList();
  }

  @HostListener('document:keydown.enter', ['$event'])
  onEnterKey(event: KeyboardEvent) {
    if (event.target instanceof HTMLElement) {
      const tagName = event.target.tagName.toLowerCase();
      if (tagName === 'input' || tagName === 'textarea') {
        this.saveData();
      }
    }
  }

  dispatchFetchIntegrationList() {
    this.PageSize = this.pageEntry.value;
    this.currOffset = 0;
    this.currPageNumber = 1
    const payload = {
      LeadSource:
        IntegrationSource[this.name as keyof typeof IntegrationSource],
      SearchByName: this.searchTerm,
      PageSize: this.PageSize,
      PageNumber: this.currPageNumber,
    };

    this.store.dispatch(new FetchIntegrationList(payload));
  }

  handleEmailValidation(form: FormGroup, emailInput: HTMLInputElement, fieldName: string) {
    const control = form.get(fieldName);
    if (!control) return;
    if (emailInput.value && !this.validateEmail(emailInput.value)) {
      control.clearValidators();
    } else if (!emailInput.value) {
      control.setValidators([Validators.required]);
    }
    control.updateValueAndValidity();
  }

  saveData() {
    let integrateData;
    this.PageSize = this.pageEntry.value;
    this.currOffset = 0;
    this.currPageNumber = 1;
    switch (this.displayName) {
      case 'Common Floor':
        integrateData = this.integrateForm.value;

        const emailToInput = document.getElementById("emailToInput") as HTMLInputElement;
        if (emailToInput?.value && this.validateEmail(emailToInput.value)) {
          this.addInputField(emailToInput.value, emailToInput, this.integrationToRecipients());
        }

        const emailBccInput = document.getElementById("emailBccInput") as HTMLInputElement;
        if (emailBccInput?.value && this.validateEmail(emailBccInput.value)) {
          this.addInputField(emailBccInput.value, emailBccInput, this.integrationBccRecipients());
        }

        const emailCcInput = document.getElementById("emailCcInput") as HTMLInputElement;
        if (emailCcInput?.value && this.validateEmail(emailCcInput.value)) {
          this.addInputField(emailCcInput.value, emailCcInput, this.integrationCcRecipients());
        }
        if (this.editCred && this.integrateForm.contains('toRecipients')) {
          this.integrateForm.get('toRecipients')?.clearValidators();
          this.integrateForm.get('toRecipients')?.updateValueAndValidity();
        }

        if (!this.integrateForm.valid || this.doesAccountNameExist) {
          this.integrateForm.markAllAsTouched();
          return;
        }

        if (
          ((emailToInput?.value && !this.validateEmail(emailToInput.value)) ||
            (emailBccInput?.value && !this.validateEmail(emailBccInput.value)) ||
            (emailCcInput?.value && !this.validateEmail(emailCcInput.value))) && !this.editCred
        ) {
          return;
        }
        const toEmails = this.integrationToRecipients().value;
        const ccEmails = this.integrationCcRecipients().value;
        const bccEmails = this.integrationBccRecipients().value;

        if (integrateData.apiType == 'pull') {
          let payload: any = {
            accountName: integrateData.accountName,
            loginEmail: integrateData.loginEmail,
            secretId: integrateData.secretId,
            secretKey: integrateData.secretKey.toString(),
            toRecipients: toEmails,
            ccRecipients: ccEmails,
            bccRecipients: bccEmails,
            PageSize: this.PageSize,
            PageNumber: this.currPageNumber,
            source: IntegrationSource[this.name as keyof typeof IntegrationSource],
          };

          if (this.editCred) {
            payload = {
              ...payload,
              id: this.selectedAccountId,
            };
            this.store.dispatch(new UpdateCommonFloor(payload));
          } else {
            this.store.dispatch(new AddCommonFloor(payload));
          }
        } else {
          let payload: any = {
            accountName: integrateData.accountName,
            loginEmail: integrateData?.loginEmail,
            source:
              IntegrationSource[this.name as keyof typeof IntegrationSource],
            toRecipients: toEmails,
            ccRecipients: ccEmails,
            bccRecipients: bccEmails,
            PageSize: this.PageSize,
            PageNumber: this.currPageNumber,
          };
          this.store.dispatch(new FetchIntegrationByIdSuccess(''));
          this.store.dispatch(new AddIntegration(payload));
        }
        break;

      case 'Just Lead':
        integrateData = this.addJustLeadForm.value;

        const emailJustLeadToInput = document.getElementById("emailJustLeadToInput") as HTMLInputElement;
        if (emailJustLeadToInput?.value && this.validateEmail(emailJustLeadToInput.value)) {
          this.addInputField(emailJustLeadToInput.value, emailJustLeadToInput, this.justLeadToRecipients());
        }
        const emailJustLeadBccInput = document.getElementById("emailJustLeadBccInput") as HTMLInputElement;
        if (emailJustLeadBccInput?.value && this.validateEmail(emailJustLeadBccInput.value)) {
          this.addInputField(emailJustLeadBccInput.value, emailJustLeadBccInput, this.justLeadBccRecipients());
        }

        const emailJustLeadCcInput = document.getElementById("emailJustLeadCcInput") as HTMLInputElement;
        if (emailJustLeadCcInput?.value && this.validateEmail(emailJustLeadCcInput.value)) {
          this.addInputField(emailJustLeadCcInput.value, emailJustLeadCcInput, this.justLeadCcRecipients());
        }

        if (this.editCred && this.addJustLeadForm.contains('toRecipients')) {
          this.addJustLeadForm.get('toRecipients')?.clearValidators();
          this.addJustLeadForm.get('toRecipients')?.updateValueAndValidity();
        }

        if (!this.addJustLeadForm.valid || this.doesAccountNameExist) {
          this.addJustLeadForm.markAllAsTouched();
          return;
        }

        if (
          ((emailJustLeadToInput?.value && !this.validateEmail(emailJustLeadToInput.value)) ||
            (emailJustLeadBccInput?.value && !this.validateEmail(emailJustLeadBccInput.value)) ||
            (emailJustLeadCcInput?.value && !this.validateEmail(emailJustLeadCcInput.value))) && !this.editCred
        ) {
          return;
        }

        const toJustLeadEmails = this.justLeadToRecipients().value;
        const ccJustLeadEmails = this.justLeadCcRecipients().value;
        const bccJustLeadEmails = this.justLeadBccRecipients().value;


        if (integrateData.apiType == 'pull') {
          let payload = {
            userId: this.addJustLeadForm.value.userId.toString(),
            userName: this.addJustLeadForm.value.userName,
            password: this.addJustLeadForm.value.password,
            source:
              IntegrationSource[this.name as keyof typeof IntegrationSource],
            toRecipients: toJustLeadEmails,
            ccRecipients: ccJustLeadEmails,
            bccRecipients: bccJustLeadEmails,
            PageSize: this.PageSize,
            PageNumber: this.currPageNumber,
          };

          if (this.editCred) {
            this.store.dispatch(new UpdateJustLead(payload));
          } else {
            this.store.dispatch(new AddJustLead(payload));
          }
        } else {
          let payload: any = {
            accountName: integrateData.accountName,
            loginEmail: integrateData?.loginEmail,
            source:
              IntegrationSource[this.name as keyof typeof IntegrationSource],
            toRecipients: toJustLeadEmails,
            ccRecipients: ccJustLeadEmails,
            bccRecipients: bccJustLeadEmails,
            PageSize: this.PageSize,
            PageNumber: this.currPageNumber,
          };

          this.store.dispatch(new FetchIntegrationByIdSuccess(''));
          this.store.dispatch(new AddIntegration(payload));
        }
        break;
      case 'Property Finder':
        integrateData = this.propertyFinderForm.value;
        // const emailPropertyFinderToInput = document.getElementById("emailPropertyFinderToInput") as HTMLInputElement;
        // if (emailPropertyFinderToInput?.value && this.validateEmail(emailPropertyFinderToInput.value)) {
        //   this.addInputField(emailPropertyFinderToInput.value, emailPropertyFinderToInput, this.propertyFinderToRecipients());
        // }

        // const emailPropertyFinderBccInput = document.getElementById("emailPropertyFinderBccInput") as HTMLInputElement;
        // if (emailPropertyFinderBccInput?.value && this.validateEmail(emailPropertyFinderBccInput.value)) {
        //   this.addInputField(emailPropertyFinderBccInput.value, emailPropertyFinderBccInput, this.propertyFinderBccRecipients());
        // }

        // const emailPropertyFinderCcInput = document.getElementById("emailPropertyFinderCcInput") as HTMLInputElement;
        // if (emailPropertyFinderCcInput?.value && this.validateEmail(emailPropertyFinderCcInput.value)) {
        //   this.addInputField(emailPropertyFinderCcInput.value, emailPropertyFinderCcInput, this.propertyFinderCcRecipients());
        // }
        // if (this.editCred && this.propertyFinderForm.contains('toRecipients')) {
        //   this.propertyFinderForm.get('toRecipients')?.clearValidators();
        //   this.propertyFinderForm.get('toRecipients')?.updateValueAndValidity();
        // }
        if (!this.propertyFinderForm.valid || this.doesAccountNameExist) {
          this.propertyFinderForm.markAllAsTouched();
          return;
        }
        // if (
        //   ((emailPropertyFinderToInput?.value && !this.validateEmail(emailPropertyFinderToInput.value)) ||
        //     (emailPropertyFinderBccInput?.value && !this.validateEmail(emailPropertyFinderBccInput.value)) ||
        //     (emailPropertyFinderCcInput?.value && !this.validateEmail(emailPropertyFinderCcInput.value))) && !this.editCred
        // ) {
        //   return;
        // }

        // const toPropertyFinderEmails = this.propertyFinderToRecipients().value;
        // const ccPropertyFinderEmails = this.propertyFinderCcRecipients().value;
        // const bccPropertyFinderEmails = this.propertyFinderBccRecipients().value;

        if (!this.propertyFinderForm.valid) {
          validateAllFormFields(
            this.propertyFinderForm || this.doesAccountNameExist
          );
          return;
        }

        if (integrateData.apiType == 'pull') {
          let payload: any = {
            name: this.propertyFinderForm.value.accountName,
            loginEmail: this.propertyFinderForm.value.loginEmail,
            apiKey: this.propertyFinderForm.value.apiKey,
            secretKey: this.propertyFinderForm.value.secretKey,
            source:
              IntegrationSource[this.name as keyof typeof IntegrationSource],
            PageSize: this.PageSize,
            PageNumber: this.currPageNumber,
            // toRecipients: toPropertyFinderEmails,
            // ccRecipients: ccPropertyFinderEmails,
            // bccRecipients: bccPropertyFinderEmails
          };

          if (this.editCred) {
            payload = {
              ...payload,
              id: this.selectedAccountId,
            };
            this.store.dispatch(new UpdatePropertyFinder(payload));
          } else {
            payload = {
              ...payload,
              requestType: PFRequestType[this.propertyFinderForm.value.requestType],
            };
            this.store.dispatch(new AddPropertyFinder(payload));
          }
        } else {
          let payload: any = {
            accountName: integrateData.accountName,
            loginEmail: integrateData.loginEmail,
            apiKey: integrateData.pullApiKey,
            secretKey: integrateData.pullSecretKey,
            event: EventType[integrateData.event],
            source:
              IntegrationSource[this.name as keyof typeof IntegrationSource],
            PageSize: this.PageSize,
            PageNumber: this.currPageNumber,
            // toRecipients: toPropertyFinderEmails,
            // ccRecipients: ccPropertyFinderEmails,
            // bccRecipients: bccPropertyFinderEmails
          };

          this.store.dispatch(new FetchIntegrationByIdSuccess(''));
          this.store.dispatch(new AddIntegration(payload));
        }
        break;
      case 'Bayut':
        integrateData = this.bayutForm.value;

        // const emailBayutToInput = document.getElementById("emailBayutToInput") as HTMLInputElement;
        // if (emailBayutToInput?.value && this.validateEmail(emailBayutToInput.value)) {
        //   this.addInputField(emailBayutToInput.value, emailBayutToInput, this.bayutToRecipients());
        // }
        // const emailBayutBccInput = document.getElementById("emailBayutBccInput") as HTMLInputElement;
        // if (emailBayutBccInput?.value && this.validateEmail(emailBayutBccInput.value)) {
        //   this.addInputField(emailBayutBccInput.value, emailBayutBccInput, this.bayutToRecipients());
        // }

        // const emailBayutCcInput = document.getElementById("emailBayutCcInput") as HTMLInputElement;
        // if (emailBayutCcInput?.value && this.validateEmail(emailBayutCcInput.value)) {
        //   this.addInputField(emailBayutCcInput.value, emailBayutCcInput, this.bayutCcRecipients());
        // }

        // if (this.editCred && this.bayutForm.contains('toRecipients')) {
        //   this.bayutForm.get('toRecipients')?.clearValidators();
        //   this.bayutForm.get('toRecipients')?.updateValueAndValidity();
        // }

        if (!this.bayutForm.valid || this.doesAccountNameExist) {
          this.bayutForm.markAllAsTouched();
          return;
        }
        // if (
        //   ((emailBayutToInput?.value && !this.validateEmail(emailBayutToInput.value)) ||
        //     (emailBayutBccInput?.value && !this.validateEmail(emailBayutBccInput.value)) ||
        //     (emailBayutCcInput?.value && !this.validateEmail(emailBayutCcInput.value))) && !this.editCred
        // ) {
        //   return;
        // }
        // const toBayutEmails = this.bayutToRecipients().value;
        // const ccBayutEmails = this.bayutCcRecipients().value;
        // const bccBayutEmails = this.bayutBccRecipients().value;

        if (integrateData.apiType == 'pull') {
          let payload: any = {
            name: integrateData?.accountName,
            loginEmail: integrateData.loginEmail,
            apiKey: integrateData?.apiKey,
            source:
              IntegrationSource[this.name as keyof typeof IntegrationSource],
            PageSize: this.PageSize,
            PageNumber: this.currPageNumber,
            // toRecipients: toBayutEmails,
            // ccRecipients: ccBayutEmails,
            // bccRecipients: bccBayutEmails
          };

          if (this.editCred) {
            payload = {
              ...payload,
              id: this.selectedAccountId,
            };
            this.store.dispatch(new UpdateBayut(payload));
          } else {
            payload = {
              ...payload,
              requestType: RequestType[integrateData?.requestType],
            };
            this.store.dispatch(new AddBayut(payload));
          }
        } else {
          let payload: any = {
            accountName: integrateData.accountName,
            loginEmail: integrateData.loginEmail,
            source:
              IntegrationSource[this.name as keyof typeof IntegrationSource],
            PageSize: this.PageSize,
            PageNumber: this.currPageNumber,
            // toRecipients: toBayutEmails,
            // ccRecipients: ccBayutEmails,
            // bccRecipients: bccBayutEmails
          };

          this.store.dispatch(new FetchIntegrationByIdSuccess(''));
          this.store.dispatch(new AddIntegration(payload));
        }
        break;
      case 'Dubizzle':
        integrateData = this.dubizzleForm.value;

        // const emailDubizzleToInput = document.getElementById("emailDubizzleToInput") as HTMLInputElement;
        // if (emailDubizzleToInput?.value && this.validateEmail(emailDubizzleToInput.value)) {
        //   this.addInputField(emailDubizzleToInput.value, emailDubizzleToInput, this.dubizzleToRecipients());
        // }

        // const emailDubizzleBccInput = document.getElementById("emailDubizzleBccInput") as HTMLInputElement;
        // if (emailDubizzleBccInput?.value && this.validateEmail(emailDubizzleBccInput.value)) {
        //   this.addInputField(emailDubizzleBccInput.value, emailDubizzleBccInput, this.dubizzleBccRecipients());
        // }

        // const emailDubizzleCcInput = document.getElementById("emailDubizzleCcInput") as HTMLInputElement;
        // if (emailDubizzleCcInput?.value && this.validateEmail(emailDubizzleCcInput.value)) {
        //   this.addInputField(emailDubizzleCcInput.value, emailDubizzleCcInput, this.dubizzleCcRecipients());
        // }
        // if (this.editCred && this.dubizzleForm.contains('toRecipients')) {
        //   this.dubizzleForm.get('toRecipients')?.clearValidators();
        //   this.dubizzleForm.get('toRecipients')?.updateValueAndValidity();
        // }

        if (!this.dubizzleForm.valid || this.doesAccountNameExist) {
          this.dubizzleForm.markAllAsTouched();
          return;
        }

        // if (
        //   ((emailDubizzleToInput?.value && !this.validateEmail(emailDubizzleToInput.value)) ||
        //     (emailDubizzleBccInput?.value && !this.validateEmail(emailDubizzleBccInput.value)) ||
        //     (emailDubizzleCcInput?.value && !this.validateEmail(emailDubizzleCcInput.value))) && !this.editCred
        // ) {
        //   return;
        // }
        // const toDubizzleEmails = this.dubizzleToRecipients().value;
        // const ccDubizzleEmails = this.dubizzleCcRecipients().value;
        // const bccDubizzleEmails = this.dubizzleBccRecipients().value;

        if (integrateData.apiType == 'pull') {
          let payload: any = {
            name: integrateData?.accountName,
            loginEmail: integrateData.loginEmail,
            apiKey: integrateData?.apiKey,
            source:
              IntegrationSource[this.name as keyof typeof IntegrationSource],
            PageSize: this.PageSize,
            PageNumber: this.currPageNumber,
            // toRecipients: toDubizzleEmails,
            // ccRecipients: ccDubizzleEmails,
            // bccRecipients: bccDubizzleEmails
          };

          if (this.editCred) {
            payload = {
              ...payload,
              id: this.selectedAccountId,
            };
            this.store.dispatch(new UpdateDubizzle(payload));
          } else {
            payload = {
              ...payload,
              requestType: RequestType[integrateData?.requestType],
            };
            this.store.dispatch(new AddDubizzle(payload));
          }
        } else {
          let payload: any = {
            accountName: integrateData.accountName,
            loginEmail: integrateData.loginEmail,
            source:
              IntegrationSource[this.name as keyof typeof IntegrationSource],
            PageSize: this.PageSize,
            PageNumber: this.currPageNumber,
            // toRecipients: toDubizzleEmails,
            // ccRecipients: ccDubizzleEmails,
            // bccRecipients: bccDubizzleEmails
          };

          this.store.dispatch(new FetchIntegrationByIdSuccess(''));
          this.store.dispatch(new AddIntegration(payload));
        }
        break;

      default:
        break;
    }

    this.isShowContentAndAdd = false;
    this.isShowAddAtListing = true;
    this.editCred = false;
    this.reset();
    this.modalService.hide();
  }

  editData(addAccount: TemplateRef<any>, accountDetails: any) {
    this.editCred = true;
    this.selectedAccountId = accountDetails.accountId;
    switch (this.displayName) {
      case 'Common Floor':
        this.integrateForm.controls['apiType'].setValue('pull');
        this.integrateForm.patchValue({
          accountName: accountDetails.accountName,
          loginEmail: accountDetails.loginEmail,
          secretId: accountDetails.credentials?.id,
          secretKey: accountDetails.credentials?.key,
        });
        const toRecipientsArray = this.integrateForm.get('toRecipients') as FormArray;
        toRecipientsArray.clear();

        accountDetails?.toRecipients?.forEach((email: any) => {
          toRecipientsArray.push(new FormControl(email));
        });

        const ccRecipientsArray = this.integrateForm.get('ccRecipients') as FormArray;
        ccRecipientsArray.clear();

        accountDetails?.ccRecipients?.forEach((email: any) => {
          ccRecipientsArray.push(new FormControl(email));
        });

        const bccRecipientsArray = this.integrateForm.get('bccRecipients') as FormArray;
        bccRecipientsArray.clear();

        accountDetails?.bccRecipients?.forEach((email: any) => {
          bccRecipientsArray.push(new FormControl(email));
        });
        break;

      case 'Just Lead':
        this.addJustLeadForm.controls['apiType'].setValue('pull');
        this.addJustLeadForm.patchValue({
          accountName: accountDetails.accountName,
          loginEmail: accountDetails.loginEmail,
          userId: accountDetails.credentials?.userid,
          userName: accountDetails.credentials?.user_name,
        });
        const toJustLeadRecipientsArray = this.addJustLeadForm.get('toRecipients') as FormArray;
        toJustLeadRecipientsArray.clear();

        accountDetails?.toRecipients?.forEach((email: any) => {
          toJustLeadRecipientsArray.push(new FormControl(email));
        });

        const ccJustLeadRecipientsArray = this.addJustLeadForm.get('ccRecipients') as FormArray;
        ccJustLeadRecipientsArray.clear();

        accountDetails?.ccRecipients?.forEach((email: any) => {
          ccJustLeadRecipientsArray.push(new FormControl(email));
        });

        const bccJustLeadRecipientsArray = this.addJustLeadForm.get('bccRecipients') as FormArray;
        bccJustLeadRecipientsArray.clear();

        accountDetails?.bccRecipients?.forEach((email: any) => {
          bccJustLeadRecipientsArray.push(new FormControl(email));
        });
        break;

      case 'Property Finder':
        this.propertyFinderForm.controls['apiType'].setValue('pull');
        this.propertyFinderForm.patchValue({
          accountName: accountDetails.accountName,
          loginEmail: accountDetails.loginEmail,
          apiKey: accountDetails?.credentials?.apiKey,
          secretKey: accountDetails?.credentials?.secretKey,
          requestType:
            PFRequestType[this.getPFRequestType(accountDetails?.credentials?.requestType)],
        });
        // const toPropertyFinderRecipientsArray = this.propertyFinderForm.get('toRecipients') as FormArray;
        // toPropertyFinderRecipientsArray.clear();

        // accountDetails?.toRecipients?.forEach((email: any) => {
        //   toPropertyFinderRecipientsArray.push(new FormControl(email));
        // });

        // const ccPropertyFinderRecipientsArray = this.propertyFinderForm.get('ccRecipients') as FormArray;
        // ccPropertyFinderRecipientsArray.clear();

        // accountDetails?.ccRecipients?.forEach((email: any) => {
        //   ccPropertyFinderRecipientsArray.push(new FormControl(email));
        // });

        // const bccPropertyFinderRecipientsArray = this.propertyFinderForm.get('bccRecipients') as FormArray;
        // bccPropertyFinderRecipientsArray.clear();

        // accountDetails?.bccRecipients?.forEach((email: any) => {
        //   bccPropertyFinderRecipientsArray.push(new FormControl(email));
        // });
        break;

      case 'Bayut':
        this.bayutForm.controls['apiType'].setValue('pull');
        this.bayutForm.patchValue({
          accountName: accountDetails.accountName,
          loginEmail: accountDetails.loginEmail,
          requestType:
            RequestType[this.getRequestType(accountDetails?.credentials?.type)],
          apiKey: accountDetails?.credentials?.api_key,
        });
        // const toBayutRecipientsArray = this.bayutForm.get('toRecipients') as FormArray;
        // toBayutRecipientsArray.clear();

        // accountDetails?.toRecipients?.forEach((email: any) => {
        //   toBayutRecipientsArray.push(new FormControl(email));
        // });

        // const ccBayutRecipientsArray = this.bayutForm.get('ccRecipients') as FormArray;
        // ccBayutRecipientsArray.clear();

        // accountDetails?.ccRecipients?.forEach((email: any) => {
        //   ccBayutRecipientsArray.push(new FormControl(email));
        // });

        // const bccBayutRecipientsArray = this.bayutForm.get('bccRecipients') as FormArray;
        // bccBayutRecipientsArray.clear();

        // accountDetails?.bccRecipients?.forEach((email: any) => {
        //   bccBayutRecipientsArray.push(new FormControl(email));
        // });
        break;
      case 'Dubizzle':
        this.dubizzleForm.controls['apiType'].setValue('pull');
        this.dubizzleForm.patchValue({
          accountName: accountDetails.accountName,
          loginEmail: accountDetails.loginEmail,
          requestType:
            RequestType[this.getRequestType(accountDetails?.credentials?.type)],
          apiKey: accountDetails?.credentials?.api_key,
        });
        // const toDubizzleRecipientsArray = this.dubizzleForm.get('toRecipients') as FormArray;
        // toDubizzleRecipientsArray.clear();

        // accountDetails?.toRecipients?.forEach((email: any) => {
        //   toDubizzleRecipientsArray.push(new FormControl(email));
        // });

        // const ccDubizzleRecipientsArray = this.dubizzleForm.get('ccRecipients') as FormArray;
        // ccDubizzleRecipientsArray.clear();

        // accountDetails?.ccRecipients?.forEach((email: any) => {
        //   ccDubizzleRecipientsArray.push(new FormControl(email));
        // });

        // const bccDubizzleRecipientsArray = this.dubizzleForm.get('bccRecipients') as FormArray;
        // bccDubizzleRecipientsArray.clear();

        // accountDetails?.bccRecipients?.forEach((email: any) => {
        //   bccDubizzleRecipientsArray.push(new FormControl(email));
        // });
        break;
      default:
        break;
    }
    let initialState: any = {
      class: 'modal-550 right-modal ip-modal-unset',
    };
    this.modalRef = this.modalService.show(
      addAccount,
      initialState
    );
  }

  getPFRequestType(type: string): PFRequestType {
    switch (type) {
      case 'Whatsapp':
        return PFRequestType.Whatsapp;
      case 'Call':
        return PFRequestType.Call;
      case 'Email':
        return PFRequestType.Email;
      default:
        return PFRequestType.None;
    }
  }

  getRequestType(type: string): RequestType {
    switch (type) {
      case 'whatsapp_leads':
        return RequestType.Whatsapp;
      case 'call_logs':
        return RequestType.IVR;
      case 'leads':
        return RequestType.Email;
      default:
        return RequestType.None;
    }
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      $event.preventDefault();
      if (this.searchTerm === '' || this.searchTerm === null) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
      this.isBulkAssignModel = false;
      this.isShowAssignModal = false;
      this.deselectAllRowsAndAdd();
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  reDownloadExcel(id: string) {
    this.store.dispatch(new FetchIntegrationByIdSuccess(''));
    this.store.dispatch(new FetchIntegrationById(id));
    this.store
      .select(getExcelFileLink)
      .pipe(takeUntil(this.stopper))
      .subscribe((res: any) => {
        if (res != '' && res != undefined) {
          window.open(res, '_self');
        }
      });
  }

  doesAccountNameExists() {
    let formGroup: FormGroup;
    switch (this.displayName) {
      case 'Common Floor':
        formGroup = this.integrateForm;
        break;
      case 'Just Lead':
        formGroup = this.addJustLeadForm;
        break;
      case 'Property Finder':
        formGroup = this.propertyFinderForm;
        break;
      case 'Bayut':
        formGroup = this.bayutForm;
        break;
      case 'Dubizzle':
        formGroup = this.dubizzleForm;
        break;
      default:
        return;
    }
    if (!formGroup) return;

    const accountNameControl = formGroup.get('accountName');
    if (!accountNameControl) return;

    let accountName = accountNameControl.value;

    accountNameControl.setErrors(null);

    if (!accountName) {
      accountNameControl.setErrors({ required: true });
      return;
    }

    if (!accountName.trim()) {
      accountNameControl.setErrors({ blank: true });
      return;
    }

    const source = IntegrationSource[this.name as keyof typeof IntegrationSource];

    this.store.dispatch(new DoesExistAccountName(accountName.trim(), source));

    this.store
      .select(doesAccountNameExists)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        accountNameControl.setErrors(data ? { alreadyExist: true } : null);
      });
  }

  initDeleteIntegration(id: string, accountName: string) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: accountName,
      fieldType: 'account',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.deleteAccount(id);
        }
      });
    }
  }

  deleteAccount(id: string) {
    this.PageSize = this.pageEntry.value;
    this.currOffset = 0;
    this.currPageNumber = 1;
    let payload: any = {
      id: id,
      LeadSource: IntegrationSource[this.name as keyof typeof IntegrationSource],
      PageSize: this.PageSize,
      PageNumber: this.currPageNumber,
    }
    this.store.dispatch(new DeleteIntegration(payload));
    this.modalRef.hide();
  }

  selectAllRows(event: any) {
    this.updatedIntegrationList.forEach((integration: any) => {
      integration.isSelected = event.target.checked;
    });
    this.onCheckboxChange(event);
    this.selectedCount = event.target.checked
      ? this.updatedIntegrationList?.length
      : 0;
  }

  selectedCountReset() {
    this.selectedCount = 0;
    this.updatedIntegrationList.forEach((integration: any) => {
      if (integration.isSelected) this.selectedCount += 1;
    });
    if (this.selectedCount) {
      this.isBulkAssignModel = true;
    }
  }

  deselectAllRowsAndAdd() {
    this.selectAllRows({ target: { checked: false } });
    this.reset();
  }

  isAllSelected(): boolean {
    return this.updatedIntegrationList?.length > 0 &&
      this.updatedIntegrationList.every(item => item.isSelected);
  }


  onCheckboxChange(event: any) {
    const isChecked = event.target.checked;
    this.isBulkAssignModel = true;
    if (isChecked) {
      this.selectedCount++;
    } else {
      this.selectedCount--;
    }
  }

  openBulkReassignModel() {
    this.selectedIntegrations = this.updatedIntegrationList.filter(
      (item) => item.isSelected
    );
    this.isBulkAssignModel = false;
    this.isShowAssignModal = true;

    this.store
      .select(getUserAssignmentByEntity)
      .pipe(takeUntil(this.stopper))
      .subscribe((res) => {
        this.assignedUserDetails = res?.id;
      });
    this.store.dispatch(new FetchPriorityList());
    this.store
      .select(getPriorityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any[]) => {
        const filteredData = data.filter((item) => item.name === 'SubSource');
        this.moduleId = filteredData.map((item) => item.id).join(', ');
      });
    let initialState: any = {
      class: 'modal-700 right-modal ip-modal-unset',
      initialState: {
        selectedIntegrations: this.selectedIntegrations,
        isBulkAssignModel: true,
        integration: this.selectedIntegrations,
        updatedIntegrationList: this.updatedIntegrationList,
      }
    };
    this.modalRef = this.modalService.show(
      IntegrationAssignmentV2Component,
      initialState
    );
  }

  openBulkProjectLocationModel(ProjectAndLocationModal: TemplateRef<any>) {
    this.project.reset();
    this.property.reset();
    this.agencyName.reset();
    this.campaign.reset();
    this.channelPartner.reset();
    this.location.reset();
    this.selectedIntegrations = this.updatedIntegrationList.filter(
      (item) => item.isSelected
    );
    this.store.dispatch(new FetchProjectIdWithName());
    this.store.dispatch(new FetchAllLocations());
    this.isShowProjectAndLocationModal = true;
    this.isBulkAssignModel = false;
    let initialState: any = {
      class: 'modal-350 right-modal ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      ProjectAndLocationModal,
      initialState
    );
  }

  openProjectAndLocationModal(ProjectAndLocationModal: TemplateRef<any>, id: string, source: any, accountName: string) {
    this.store.dispatch(new FetchProjectIdWithName());
    this.store.dispatch(new FetchAllLocations());
    this.store.dispatch(new FetchIntegrationAssignment({ id, source }));
    this.store.dispatch(new FetchAgencyNameList());
    this.store.dispatch(new FetchCampaignList());
    this.store.dispatch(new FetchChannelPartnerList());
    this.store.dispatch(new FetchPropertyList(true));
    this.isShowProjectAndLocationModal = true;
    this.isBulkAssignModel = true;
    this.selectedAccountName = accountName;
    this.selectedAccountId = id;
    this.selectedLeadSource = source;
    let initialState: any = {
      class: 'modal-350 right-modal ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      ProjectAndLocationModal,
      initialState
    );
  }

  updateProjectAndLocation(ProjectAndLocationModal: TemplateRef<any>) {
    this.selectedIntegrations = this.updatedIntegrationList.filter(
      (item) => item.isSelected
    );
    let selectedIds: any = this.selectedIntegrations?.map(
      (node: any) => node?.accountId
    );
    if (selectedIds.length > 0) {
      const payload = {
        ids: selectedIds,
        projectId: this.project.value,
        locationId: this.location.value,
        source: this.selectedLeadSource,
        agency: this.agencyName.value,
        camapign: this.campaign.value,
        channelPartner: this.channelPartner.value,
        property: this.property.value,
      };
      this.store.dispatch(new updateIntegrationAssignment(payload, true));
    } else {
      const payload = {
        id: this.selectedAccountId,
        projectId: this.project.value,
        locationId: this.location.value,
        source: this.selectedLeadSource,
        agency: this.agencyName.value,
        camapign: this.campaign.value,
        channelPartner: this.channelPartner.value,
        property: this.property.value,
      };
      this.store.dispatch(new updateIntegrationAssignment(payload, false));
    }
    this.reset();
    this.isShowProjectAndLocationModal = false;
    this.modalService.hide();
    let initialState: any = {
      class: 'modal-350 right-modal ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      ProjectAndLocationModal,
      initialState
    );
  }
  // updateCountryCode(CountryCodeModal: TemplateRef<any>) {
  //   this.selectedIntegrations = this.updatedIntegrationList.filter(
  //     (item) => item.isSelected
  //   );
  //   let selectedIds: any = this.selectedIntegrations?.map(
  //     (node: any) => node?.accountId
  //   );
  //   if (selectedIds.length > 0) {
  //     let payload: any = {
  //       ids: selectedIds,
  //       countryCode: this.contactNoInput?.selectedCountry?.dialCode
  //         ? '+' + this.contactNoInput.selectedCountry.dialCode
  //         : null,
  //       source: this.selectedLeadSource,
  //     };
  //     this.store.dispatch(new updateCountryCodeAssignment(payload));
  //   } else {
  //     let payload: any = {
  //       ids: [this.selectedAccountId],
  //       countryCode: this.contactNoInput?.selectedCountry?.dialCode
  //         ? '+' + this.contactNoInput.selectedCountry.dialCode
  //         : null,
  //       source: this.selectedLeadSource,
  //     };
  //     this.store.dispatch(new updateCountryCodeAssignment(payload));
  //   }
  //   this.reset();
  //   this.isShowCountryCodeModal = false;
  //   this.modalService.hide();
  //   let initialState: any = {
  //     class: 'modal-350 right-modal ph-modal-unset',
  //   };
  //   this.modalRef = this.modalService.show(
  //     CountryCodeModal,
  //     initialState
  //   );
  // }
  openAssignmentModal(integration: any) {
    this.isBulkAssignModel = true;
    this.isShowAssignModal = true;
    this.selectedAccountName = integration.accountName;
    this.selectedAccountId = integration.accountId;
    this.store.dispatch(new FetchPriorityList());
    this.store
      .select(getPriorityList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any[]) => {
        const filteredData = data.filter((item) => item.name === 'SubSource');
        this.moduleId = filteredData.map((item) => item.id).join(', ');
      });
    let initialState: any = {
      class: 'modal-700 right-modal ip-modal-unset',
      initialState: {
        integration: integration
      }
    };
    this.modalRef = this.modalService.show(
      IntegrationAssignmentV2Component,
      initialState
    );
  }

  closeModal() {
    this.modalService.hide();
    this.reset();
  }

  reset() {
    this.updatedIntegrationList.forEach((item) => (item.isSelected = false));
    this.selectedCount = 0;
    this.integrateForm.reset();
    this.addJustLeadForm.reset();
    this.propertyFinderForm.reset();
    this.bayutForm.reset();
    this.dubizzleForm.reset();
    this.integrateForm.controls['apiType'].setValue('push');
    this.addJustLeadForm.controls['apiType'].setValue('push');
    this.propertyFinderForm.controls['apiType'].setValue('push');
    this.bayutForm.controls['apiType'].setValue('push');
    this.dubizzleForm.controls['apiType'].setValue('push');
    this.assignedUser = [];
    this.project.reset();
    this.property.reset();
    this.agencyName.reset();
    this.campaign.reset();
    this.channelPartner.reset();
    this.location.reset();
    this.resetIntegrationRecipients()
    this.resetJustLeadRecipients()
    // this.resetDubizzleRecipients()
    // this.resetBayutRecipients()
    // this.resetPropertyFinderFormRecipients()
  }

  selectAllForDropdownItems(items: any[]) {
    let allSelect = (items: any) => {
      items.forEach((element: any) => {
        element['selectedAllGroup'] = 'selectedAllGroup';
      });
    };

    allSelect(items);
  }

  openCountryCode(CountryCodeModal: TemplateRef<any>, id: string, source: any, accountName: string) {
    this.store.dispatch(new FetchIntegrationAssignment({ id, source }));
    this.isBulkAssignModel = true;
    this.isShowCountryCodeModal = true;
    this.selectedAccountName = accountName;
    this.selectedAccountId = id;
    this.selectedLeadSource = source;
    let initialState: any = {
      class: 'modal-350 right-modal ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      CountryCodeModal,
      initialState
    );
  }

  openBulkCountryCodeModal(CountryCodeModal: TemplateRef<any>) {
    // this.updatePreferredCountry(null);
    this.selectedIntegrations = this.updatedIntegrationList.filter(
      (item) => item.isSelected
    );
    this.isShowCountryCodeModal = true;
    this.isBulkAssignModel = false;
    let initialState: any = {
      class: 'modal-350 right-modal ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      CountryCodeModal,
      initialState
    );
  }

  copyUrl(endPointUrl: any): void {
    navigator.clipboard?.writeText(endPointUrl);
    this._notificationService.success(
      this._translateService.instant('GLOBAL.link-copied')
    );
  }

  addNewAccount(addAccount: TemplateRef<any>) {
    // this.isShowAddAccountBtn = false;
    this.deselectAllRowsAndAdd();
    this.isShowAddAtListing = false;
    this.editCred = false
    let initialState: any = {
      class: 'modal-550 right-modal ip-modal-unset',
    };
    this.modalRef = this.modalService.show(
      addAccount,
      initialState
    );

  }

  integrationToRecipients(): FormArray {
    return this.integrateForm.get('toRecipients') as FormArray;
  }

  integrationCcRecipients(): FormArray {
    return this.integrateForm.get('ccRecipients') as FormArray;
  }

  integrationBccRecipients(): FormArray {
    return this.integrateForm.get('bccRecipients') as FormArray;
  }

  justLeadToRecipients(): FormArray {
    return this.addJustLeadForm.get('toRecipients') as FormArray;
  }

  justLeadCcRecipients(): FormArray {
    return this.addJustLeadForm.get('ccRecipients') as FormArray;
  }

  justLeadBccRecipients(): FormArray {
    return this.addJustLeadForm.get('bccRecipients') as FormArray;
  }

  // propertyFinderToRecipients(): FormArray {
  //   return this.propertyFinderForm.get('toRecipients') as FormArray;
  // }

  // propertyFinderCcRecipients(): FormArray {
  //   return this.propertyFinderForm.get('ccRecipients') as FormArray;
  // }

  // propertyFinderBccRecipients(): FormArray {
  //   return this.propertyFinderForm.get('bccRecipients') as FormArray;
  // }

  // bayutToRecipients(): FormArray {
  //   return this.bayutForm.get('toRecipients') as FormArray;
  // }

  // bayutCcRecipients(): FormArray {
  //   return this.bayutForm.get('ccRecipients') as FormArray;
  // }

  // bayutBccRecipients(): FormArray {
  //   return this.bayutForm.get('bccRecipients') as FormArray;
  // }

  // dubizzleToRecipients(): FormArray {
  //   return this.dubizzleForm.get('toRecipients') as FormArray;
  // }

  // dubizzleCcRecipients(): FormArray {
  //   return this.dubizzleForm.get('ccRecipients') as FormArray;
  // }

  // dubizzleBccRecipients(): FormArray {
  //   return this.dubizzleForm.get('bccRecipients') as FormArray;
  // }

  resetIntegrationRecipients() {
    this.integrateForm.setControl('toRecipients', this.fb.array([], [Validators.required, Validators.minLength(1)]));
    this.integrateForm.setControl('ccRecipients', this.fb.array([]));
    this.integrateForm.setControl('bccRecipients', this.fb.array([]));
  }

  resetJustLeadRecipients() {
    this.addJustLeadForm.setControl('toRecipients', this.fb.array([], [Validators.required, Validators.minLength(1)]));
    this.addJustLeadForm.setControl('ccRecipients', this.fb.array([]));
    this.addJustLeadForm.setControl('bccRecipients', this.fb.array([]));
  }

  // resetDubizzleRecipients() {
  //   this.dubizzleForm.setControl('toRecipients', this.fb.array([], [Validators.required, Validators.minLength(1)]));
  //   this.dubizzleForm.setControl('ccRecipients', this.fb.array([]));
  //   this.dubizzleForm.setControl('bccRecipients', this.fb.array([]));
  // }

  // resetBayutRecipients() {
  //   this.bayutForm.setControl('toRecipients', this.fb.array([], [Validators.required, Validators.minLength(1)]));
  //   this.bayutForm.setControl('ccRecipients', this.fb.array([]));
  //   this.bayutForm.setControl('bccRecipients', this.fb.array([]));
  // }

  // resetPropertyFinderFormRecipients() {
  //   this.propertyFinderForm.setControl('toRecipients', this.fb.array([], [Validators.required, Validators.minLength(1)]));
  //   this.propertyFinderForm.setControl('ccRecipients', this.fb.array([]));
  //   this.propertyFinderForm.setControl('bccRecipients', this.fb.array([]));
  // }

  addInputField(email: string, emailInput: HTMLInputElement, formArray: FormArray) {
    const trimmedEmail = email?.trim();
    if (!trimmedEmail || !this.validateEmail(trimmedEmail)) {
      return;
    }

    formArray.push(new FormControl(trimmedEmail));
    emailInput.value = '';
    formArray.markAsUntouched();
    formArray.updateValueAndValidity();

    const parentForm = formArray === this.integrationToRecipients() ?
      this.integrateForm : formArray === this.justLeadToRecipients() ?
        this.addJustLeadForm :
        // formArray === this.dubizzleToRecipients() ?
        //   this.dubizzleForm : formArray === this.bayutToRecipients() ?
        //     this.bayutForm : formArray === this.propertyFinderToRecipients() ?
        //       this.propertyFinderForm :
        null;

    if (parentForm) {
      parentForm.updateValueAndValidity();
    }
  }

  removeEmail(index: number, formArray: FormArray, emailInput?: HTMLInputElement) {
    formArray.removeAt(index);
    const parentForm = formArray === this.integrationToRecipients() ?
      this.integrateForm :
      formArray === this.justLeadToRecipients() ?
        this.addJustLeadForm :
        // formArray === 
        // this.dubizzleToRecipients() ? this.dubizzleForm :
        //  formArray === this.bayutToRecipients() ?
        //   this.bayutForm : formArray === this.propertyFinderToRecipients() ?
        //     this.propertyFinderForm : 
        null;

    const isInputEmpty = !emailInput.value.trim();
    const isFormArrayEmpty = formArray.length === 0;
    if (parentForm) {
      const control = parentForm.get('toRecipients');
      control?.setValidators([Validators.required]);
      if (isFormArrayEmpty && isInputEmpty) {
        control?.markAsTouched();
      } else {
        control?.markAsUntouched();
      }

      control?.updateValueAndValidity();
    }
  }

  validateEmail(email: string): boolean {
    if (!email || !email.trim()) return false;
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailPattern.test(email.trim());
  }

  assignPageSize() {
    this.PageSize = this.pageEntry.value;
    this.currOffset = 0;
    this.currPageNumber = 1;
    this.store.dispatch(
      new AssignPageSize(this.PageSize, this.currPageNumber)
    );
    this.filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.PageSize,
      PageNumber: this.currPageNumber,
      LeadSource: IntegrationSource[this.name as keyof typeof IntegrationSource],
      SearchByName: this.searchTerm,
    };
    this.store.dispatch(new FetchIntegrationList(this.filtersPayload));
    this.deselectAllRowsAndAdd();
  }

  onPageChange(offset: number) {
    this.currOffset = offset;
    this.currPageNumber = offset + 1;
    this.store.dispatch(
      new AssignPageSize(this.PageSize, this.currPageNumber)
    );
    this.filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.PageSize,
      PageNumber: this.currPageNumber,
      LeadSource: IntegrationSource[this.name as keyof typeof IntegrationSource],
      SearchByName: this.searchTerm,

    };
    this.store.dispatch(new FetchIntegrationList(this.filtersPayload));
    this.deselectAllRowsAndAdd();

  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
