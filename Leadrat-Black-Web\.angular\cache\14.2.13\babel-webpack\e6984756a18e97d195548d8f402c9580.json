{"ast": null, "code": "/**\n * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license\n */\n\n/**\n * @module watchdog\n */\nexport { default as ContextWatchdog } from './contextwatchdog.js';\nexport { default as EditorWatchdog } from './editorwatchdog.js';\nexport { default as Watchdog } from './watchdog.js';\nimport './augmentation.js';", "map": {"version": 3, "names": ["default", "ContextWatchdog", "EditorWatchdog", "Watchdog"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/@ckeditor/ckeditor5-watchdog/src/index.js"], "sourcesContent": ["/**\n * @license Copyright (c) 2003-2024, CKSource Holding sp. z o.o. All rights reserved.\n * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license\n */\n/**\n * @module watchdog\n */\nexport { default as ContextWatchdog } from './contextwatchdog.js';\nexport { default as EditorWatchdog } from './editorwatchdog.js';\nexport { default as Watchdog } from './watchdog.js';\nimport './augmentation.js';\n"], "mappings": "AAAA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA,SAASA,OAAO,IAAIC,eAApB,QAA2C,sBAA3C;AACA,SAASD,OAAO,IAAIE,cAApB,QAA0C,qBAA1C;AACA,SAASF,OAAO,IAAIG,QAApB,QAAoC,eAApC;AACA,OAAO,mBAAP", "ignoreList": []}, "metadata": {}, "sourceType": "module"}