<div class="bg-white px-24 py-6" *ngIf="topFilters?.length || firstLevelFilter?.length">
  <div class="flex-between flex-grow-1">
    <ul
      class="ph-d-none align-center top-nav-bar text-nowrap scrollbar ip-w-100-260 tb-w-100-540 w-100-680 scroll-hide"
      *ngIf="topFilters?.length">
      <ng-container *ngFor="let parentFilter of topFilters">
        <ng-container *ngIf="canViewUnassigned || parentFilter.displayName !== 'Unassigned'">
          <div [title]="parentFilter?.displayName" class="cursor-pointer"
            (click)="parentFilterChanged(parentFilter, true);trackerFeatures(parentFilter.displayName)">
            <div class="align-center ph-mb-4">
              <a [class.active]="filtersPayload[parentFilter.filterPayloadKey] === parentFilter?.enumValue">
                <img [type]="'leadrat'" [appImage]="s3BucketUrl + parentFilter?.logoUrl" alt="logo" width="20"
                  height="20"></a>
              <span [class.active]="filtersPayload[parentFilter.filterPayloadKey] === parentFilter?.enumValue"
                class="text-large fw-semi-bold mx-8 d-flex">{{ parentFilter?.displayName }}
                <ng-container *ngIf="showFilterCount">
                  <ng-container *ngIf="!isLoadingCustomData && !isLoadingTopFilters else loader">
                    ({{parentFilter?.count || 0}})
                  </ng-container>
                </ng-container>
              </span>
            </div>
          </div>
        </ng-container>
      </ng-container>
    </ul>
    <div class="ph-d-flex ng-select-sm d-none" *ngIf="topFilters?.length">
      <ng-select [items]="filteredTopFilters" bindLabel="displayName" [(ngModel)]="selectedParentFilter"
        (change)="parentFilterChanged($event, true)" [clearable]="false" placeholder="Select Filter" class="w-170">
        <ng-template ng-label-tmp let-item="item">
          <img [appImage]="s3BucketUrl + item.logoUrl" [type]="'orgProfileLogo'" alt="logo" height="15px" width="20px">
          <span class="mx-2">{{ item.displayName }} <span *ngIf="showFilterCount">({{ item.count || 0 }})</span></span>
        </ng-template>
        <ng-template ng-option-tmp let-item="item" let-index="index">
          <a [class.active]="filtersPayload[item.filterPayloadKey] === item.enumValue">
            <img [appImage]="s3BucketUrl + item.logoUrl" [type]="'orgProfileLogo'" alt="logo" height="15px"
              width="20px">
            <span class="mx-2">{{ item.displayName }} <span *ngIf="showFilterCount">({{ item.count || 0 }})</span> </span>
          </a>
        </ng-template>
      </ng-select>
    </div>
    <div class="align-center flex-end" *ngIf="firstLevelFilter?.length">
      <div class="ph-align-center-unset ph-flex-col align-center">
        <div class="bg-white align-center cursor-pointer mr-6 text-nowrap"
          [ngClass]="{'pe-none': showFilterCount, 'disabled': showFilterCount}" (click)="onshowLeadCountData()">
          <span class="icon ic-xxs ic-eye-solid ic-accent-green mr-4 text-nowrap"></span><span
            class="fw-semi-bold text-large text-accent-green ip-d-none"> Data Count </span><span
            class="ip-d-block d-none icon ic-xxs ic-accent-green ic-secondary-filter-solid"> </span>

        </div>
        <div class="bg-white align-center cursor-pointer pl-6 mr-10 text-nowrap ph-mt-4 border-left ph-border-0"
          [ngClass]="{'pe-none': showCommunicationCount, 'disabled': showCommunicationCount}"
          (click)="onshowcommunicationCountData()">
          <span class="icon ic-xxs ic-eye-solid ic-accent-green mr-4 text-nowrap"></span><span
            class="fw-semi-bold text-large text-accent-green ip-d-none"> Communication Count </span> <span
            class="ip-d-block d-none icon ic-xxs ic-accent-green ic-social-profile"> </span>
        </div>
      </div>
      <div class="btn-full-dropdown btn-w-100">
        <div class="position-absolute top-9 left-9 ip-top-11 align-center z-index-2">
          <span class="ic-tracker icon ic-xxs"></span>
          <span class="ml-8 ip-d-none">Tracker</span>
        </div>
        <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false"
          [(ngModel)]="selectedDataTrackerOption"
          (click)="openDataTracker();trackingService.trackFeature('Web.Data.Dropdown.Tracker.Visit')">
          <ng-option (click)="selectedDataTrackerOption = null" value="bulkUpload" *ngIf="canBulkUpload">
            <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
            {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }} Tracker</ng-option>
          <ng-option (click)="selectedDataTrackerOption = null" value="export" *ngIf="canExport">
            <span class="ic-download icon ic-xxs ic-dark mr-8"></span>
            Export Tracker</ng-option>
          <ng-option (click)="selectedDataTrackerOption = null" value="bulk">
            <span class="ic-double-upload icon ic-xxs ic-dark mr-8"></span>
            Bulk Operation</ng-option>
        </ng-select>
      </div>
      <ng-container *ngIf="canBulkUpload|| canAdd">
        <div class="ml-10" [ngClass]="canBulkUpload ? 'btn-left-dropdown' : 'btn-coal'"
          (click)="canAdd ? navigateToAddData() : ''">
          <span class="ic-add icon ic-xxs"></span>
          <span class="ml-8 ip-d-none">{{ 'BUTTONS.add-data' | translate }}</span>
        </div>
        <div class="btn-right-dropdown btn-w-30 black-100" *ngIf="canBulkUpload">
          <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false" [(ngModel)]="selectedAddDataOption"
            (click)="openDataBulkUploadQR()">
            <ng-option (click)="selectedAddDataOption = null" value="bulkUpload">
              <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
              {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}</ng-option>
          </ng-select>
        </div>
      </ng-container>
    </div>
  </div>
</div>
<div class="border-top border-bottom px-24 bg-white" *ngIf="firstLevelFilter?.length">
  <div class="flex-between">
    <div class="align-center">
      <div class="top-navbar-black align-center no-validation">
        <div class="ml-6 lead-dropdown d-dropdown">
          <ng-select [virtualScroll]="true" bindValue="id" bindLabel="name" [(ngModel)]="selectedSecondFilter"
            ResizableDropdown (change)="firstLevelFilterChanged($event)" [searchable]="false" [clearable]="false"
            class="no-validation">
            <ng-option name="showEntriesSize" *ngFor="let firstLevelFilter of firstLevelFilter"
              [value]="firstLevelFilter?.id">
              <div class="value">
                <div class="position-relative mr-10 text-nowrap">{{ firstLevelFilter?.name }}</div>
                <ng-container *ngIf="showFilterCount">
                  <div class="count" *ngIf="!isLoadingCustomData && !isLoadingTopFilters">
                    ({{firstLevelFilter?.count || 0}})
                  </div>
                </ng-container>
                <ng-container *ngIf="showFilterCount">
                  <span *ngIf="isLoadingCustomData || isLoadingTopFilters" class="count">
                    (<div class="container px-4 d-inline">
                      <ng-container *ngFor="let dot of [1,2,3]">
                        <div class="dot-falling"></div>
                      </ng-container>
                    </div>)
                  </span>
                </ng-container>
              </div>
            </ng-option>
          </ng-select>
        </div>
        <drag-scroll class="d-flex scrollbar scroll-hide tb-w-100-210 ph-w-100-160"
          [ngClass]="showLeftNav ? 'w-100-350' : 'w-100-250'">
          <ul class="py-12 user-select-none">
            <li *ngFor="let secondLevelFilter of secondLevelFilterList">
              <a class="position-relative header-5 ml-30 fw-semi-bold"
                [class.active]="activeSecondLevelFilter(secondLevelFilter)">
                <span (click)="secondLevelFilterChanged(secondLevelFilter)"> {{secondLevelFilter?.name }}
                  <ng-container *ngIf="showFilterCount">
                    <span *ngIf="!isLoadingCustomData && !isLoadingTopFilters">({{
                      secondLevelFilter?.count
                      || 0}})</span>
                  </ng-container>
                  <ng-container *ngIf="showFilterCount">
                    <span *ngIf="isLoadingCustomData || isLoadingTopFilters">
                      (<div class="container px-4 d-inline">
                        <ng-container *ngFor="let dot of [1,2,3]">
                          <div class="dot-falling"></div>
                        </ng-container>
                      </div>)
                    </span>
                  </ng-container>
                </span>
                <span class="icon ic-cancel ic-x-xs ic-black cursor-pointer ml-10"
                  *ngIf="activeSecondLevelFilter(secondLevelFilter)"
                  (click)="secondLevelFilterChanged(secondLevelFilter, true)">
                </span>
                <a [class.active]="activeSecondLevelFilter(secondLevelFilter)"></a>
              </a>
            </li>
          </ul>
        </drag-scroll>
      </div>
    </div>
    <div [ngClass]="{'blinking pe-none': isGridDataLoading}" class="btn-coal ph-d-none ml-10 min-w-32"
      title="Refresh Data" (click)="filterFunction();trackingService.trackFeature('Web.Data.Button.DataRefresh.Click')">
      <span class="icon ic-refresh ic-xxs"></span>
    </div>
  </div>
</div>
<div class="w-100 bg-brick-pattern" *ngIf="isMobileView && showWebUI"
  [ngClass]="showWebUI ? 'hidden-phone' : 'hidden-web'">
  <div class="pt-10 px-24 pr-10">
    <div class="align-center">
      <div class="align-center flex-grow-1 no-validation p-8 bg-white border-gray br-8 position-relative"
        id="search-dropdown">
        <input placeholder="type to search" autocomplete="off" name="search" class="border-0 outline-0 w-100"
          [(ngModel)]="searchTerm"
          (keydown.enter)="onSearch();trackingService.trackFeature('Web.Data.DataEntry.Search.DataEntry')"
          (input)="isEmptyInput()" />
        <span class="icon ic-triangle ic-xxxs ic-dark cursor-pointer" (click)="toggleSearchDropdown()"></span>

        <!-- Mobile Search Enhancement Dropdown -->
        <div [ngClass]="{'pe-none': isModuleWiseSearchPropertiesLoading}" *ngIf="showSearchDropdown"
          class="position-absolute w-100 bg-white brbr-20 brbl-20 left-0 top-40 border shadow-sm z-index-1001"
          (clickOutside)="showSearchDropdown = false">
          <div class="scrollbar scroll-hide max-h-100-360">
            <div class="d-flex flex-wrap p-10">
              <h6 *ngFor="let filter of getDisplayedFilters()"
                class="px-10 fw-semi-bold br-6 py-6 m-4 cursor-pointer border"
                [ngClass]="{'text-muted': selectedSearchFilters.length >= 5 && !isSelectedFilter(filter)}" [ngStyle]="{'pointer-events': selectedSearchFilters.length >= 5 && !isSelectedFilter(filter) ? 'none' : 'auto',
                           'opacity': selectedSearchFilters.length >= 5 && !isSelectedFilter(filter) ? '0.5' : '1'}"
                (click)="toggleSearchFilter(filter);$event.stopPropagation()">
                {{ filter?.displayName }}
              </h6>
            </div>
          </div>
          <div *ngIf="getSelectedFilters().length" class="p-10">
            <h5 class="fw-300 mb-6">{{recentSearches?.length ? 'Recent Searches' : 'Selected'}}</h5>
            <div class="d-flex flex-wrap">
              <div *ngFor="let filter of getSelectedFilters()"
                class="px-10 py-6 m-4 d-flex bg-white-100 align-center cursor-pointer rounded-pill bg-slate">
                <div (click)="toggleSearchFilter(filter)">
                  {{ filter?.displayName }}
                </div>
                <div class="ml-8 bg-black-900 align-center p-4 br-20"
                  (click)="toggleSearchFilter(filter); $event.stopPropagation();">
                  <span class="icon ic-cancel ic-xx-xs"></span>
                </div>
              </div>
            </div>
          </div>
          <div class="p-10 d-flex align-center">
            <span *ngIf="selectedSearchFilters.length >= 5" class="text-danger">Users can select up to five (5)
              parameters at a time</span>
          </div>
        </div>
      </div>
      <div class="d-flex border-gray bg-white mx-10 cursor-pointer br-8">
        <div class="p-8" (click)="openAdvFiltersModal()">
          <span class="icon ic-right-left ic-sm ic-slate-90"></span>
        </div>
        <div class="flex-end bg-white brtr-8 brbr-8 p-8 position-relative" id="saved-filter"
          (click)="openSavedFilter($event)">
          <span class="icon ic-triangle ic-dark ic-x-xs mx-2"></span>
          <saved-filter [isMobileView]="isMobileView" [showFilters]="showFilters" (closeFilter)="closeSavedFilter()"
            (selectFilter)="handleSelectFilter($event)" (editFilter)="handleEditFilter($event)" [filters]="filters"
            *ngIf="isSavedFilterOpen"></saved-filter>
        </div>
      </div>
    </div>
    <div class="bg-white p-4 tb-w-100-40 w-100-190 ph-w-100-50 mt-10" *ngIf="showFilters">
      <div class="bg-secondary flex-between">
        <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
          <div class="d-flex" *ngFor="let filter of filtersPayload | keyvalue">
            <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
              *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
              {{
              ((dataFiltersKeyLabel[filter.key]
              == 'NoOfBHKs' || filter.key == 'NoOfBHKs') &&
              globalSettingsData?.isCustomLeadFormEnabled) ? 'BR' :
              dataFiltersKeyLabel[filter.key] || filter.key }}:
              {{ filter.key === 'NoOfBHKs' && !globalSettingsData?.isCustomLeadFormEnabled ?
              getBHKDisplayString(value) :
              filter.key === 'DateOfBirth' ? getTimeZoneDate(value,
              '00:00:00', 'dayMonthYear') :
              filter.key === 'Gender' ? Gender[value] ?? '' :
              filter.key === 'MaritalStatus' ? MaritalStatusType[value] ?? '' :
              filter.key === 'NoOfBHKs' && globalSettingsData?.isCustomLeadFormEnabled ? getBRDisplayString(value) :
              filter.key === 'Beds'? getBedsDisplay(value) :
              filter.key === 'AssignTo' ? getUserName(value) :
              filter.key === 'AssignedFromIds' ? getUserName(value) :
              filter.key === 'LastModifiedByIds' ? getUserName(value) :
              filter.key === 'ConvertedByIds' ? getUserName(value) :
              filter.key === 'QualifiedByIds' ? getUserName(value) :
              filter.key === 'CreatedByIds' ? getUserName(value) :
              filter.key === 'DeletedByIds' ? getUserName(value) :
              filter.key === 'RestoredByIds' ? getUserName(value) :
              filter.key === 'SourceIds' ? sourceIdMap?.[value] :
              filter.key === 'EnquiryTypes' ? getEnquiryTypeName(value) :
              filter.key === 'StatusIds' ? statusIdMap?.[value] :
              filter.key === 'PropertyType' ? getPropertyTypeName(value):
              filter.key === 'PropertySubType' ? getPropertySubTypeName(value):
              filter.key === 'SourcingManagers' ? getUserName(value) :
              filter.key === 'ClosingManagers' ? getUserName(value) :
              filter.key === 'CarpetArea' ? getArea(value,filtersPayload?.CarpetAreaUnitId) :
              filter.key === 'BuiltUpArea' ? getArea(value,filtersPayload?.BuiltUpAreaUnitId) :
              filter.key === 'SaleableArea' ? getArea(value,filtersPayload?.SaleableAreaUnitId) :
              filter.key === 'PropertyArea' ? getArea(value,filtersPayload?.PropertyAreaUnitId) :
              filter.key === 'NetArea' ? getArea(value,filtersPayload?.NetAreaUnitId) :
              filter.key === 'Profession' ? getProfession(value):
              filter.key === 'Minbudget' ? value?.split(" ")?.[1] :
              filter.key === 'Maxbudget' ? value?.split(" ")?.[1] :
              filter.key === 'Currency' ? value?.split(" ")?.[1] :
              filter.key === 'PossesionType' ? getPossessionTypeDisplay(value) :
              filter.key === 'FromPossesionDate' || filter.key === 'ToPossesionDate' ? getTimeZoneDate(value,
              userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear') :
              value }}
              <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                (click)="onRemoveFilter(filter.key, value)"></span>
            </div>
          </div>
        </drag-scroll>
        <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
          (click)="onSaveFilter(addFile)">Save Filter</div>
        <div (click)="openAdvFiltersModal()"
          class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
          *ngIf="isFilterSelected">Edit Filter</div>
        <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
          *ngIf="isFilterUpdated" (click)="onSaveFilter(addFile)">Update Filter</div>
        <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
          (click)="onClearAllFilters()">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}</div>
      </div>
    </div>
  </div>
  <ng-container *ngIf="(cardData?.length && isMobileView && showWebUI) else phLoader">
    <ng-container *ngFor="let data of cardData">
      <div class="bg-white br-4 p-10 mx-24 my-10 fw-semi-bold text-dark-gray" (click)="openPreview(data)">
        <div class="flex-between">
          <div class="align-center"><span *ngIf="data?.enquiry?.prospectSource?.imageURL"><img alt="source"
                [type]="'leadrat'" [appImage]="s3BucketUrl + data?.enquiry?.prospectSource?.imageURL" [height]="15"
                class="mr-4">
            </span><span class="text-xxs">{{data?.enquiry?.prospectSource?.displayName}}</span></div>
          <span class="status-label-badge" [style.color]="data?.status?.color || '#4B4B4B'"
            [style.backgroundColor]="hexToRgba(data?.status?.color || '#4B4B4B', 0.08)">
            <span class="dot dot-xs mr-6" [style.backgroundColor]="data?.status?.color || '#4B4B4B'"></span>
            {{ data?.status?.displayName }}</span>
        </div>
        <div class="mt-6"><i class="header-6 text-coal text-truncate-1 break-all">{{data?.name}}</i></div>
        <div class="d-flex mt-4">
          <div *ngIf="data?.contactNo"><span class="icon ic-Call ic-xxxs ic-gray mr-4"></span><span
              class="text-sm">{{data.contactNo}}</span></div>
          <div class="align-center" *ngIf="data?.email"><span
              class="icon ic-envelope ic-xxxs ic-gray mr-4 ml-8"></span><span class="text-sm">{{data.email}}</span>
          </div>
        </div>
        <div class="mt-6" *ngIf="data?.assignTo"><span class="text-xs">assign to:</span>
          <i class="text-accent-green text-xs"> {{getAssignedToDetails(data?.assignTo, allUsers, true) || '--'}}</i>
        </div>
        <div class="my-10 border-bottom"></div>
        <div class="align-center w-100">
          <div class="align-center w-25 text-xs"><span class="icon ic-buliding ic-x-xs ic-gray mr-4"></span><span
              class="text-truncate-1 break-all">{{ data?.projects?.length > 0 ? data?.projects[0]?.name : '--' }}</span>
            <span *ngIf="data.projects.length > 1">{{ (data.projects.length > 1 ? ' +' + (data.projects.length - 1) :
              '') }}</span>
          </div>
          <div class="border-right h-10 mx-6"></div>
          <div class="align-center w-25 text-xs"><span class="icon ic-cube ic-x-xs ic-gray mr-4"></span> <span
              class="text-truncate-1 break-all">{{ (data?.enquiry?.bhKs !== null) && (data?.enquiry?.bhKs !== 0 ) &&
              (data?.enquiry?.bhKs.length > 0)
              ? getBHKDisplayString(data?.enquiry?.bhKs)
              : '--'
              }}</span></div>
          <div class="border-right h-10 mx-6"></div>
          <div class="text-truncate-1 align-center w-25 text-xs"> <span
              class="icon ic-cash ic-x-xs ic-gray mr-4"></span><span class="text-nowrap"> {{ (data?.enquiry?.upperBudget
              !== null) && (data?.enquiry?.upperBudget !== 0)
              ? formatBudget(data?.enquiry?.upperBudget, data?.enquiry?.currency || defaultCurrency)
              : '--' }}
            </span></div>
          <div class="mx-6 border-right h-10"></div>
          <div class="align-center w-25 text-xs">
            <span class="icon ic-location-solid ic-x-xs ic-gray mr-4"></span>
            <span class="text-truncate-1 break-all">
              {{ data?.enquiry?.addresses?.length > 0
              ? data?.enquiry?.addresses[0]?.subLocality

              : '--'
              }}
            </span>
            <span *ngIf="data?.enquiry?.addresses?.length > 1"> {{ (data?.enquiry?.addresses?.length > 1 ? ' +' +
              (data?.enquiry?.addresses?.length - 1) : '') }}</span>
          </div>

        </div>
        <div class="my-10 border-bottom"></div>
        <data-actions [data]="data" [data]="data" [showCommunicationCount]="showCommunicationCount" ></data-actions>
      </div>
    </ng-container>
    <div *ngIf="!isGridDataLoading else dataLoader" class="h-20px" inView (inView)="onInView($event)"></div>
  </ng-container>
</div>

<ng-template #phLoader>
  <ng-container *ngIf="cardData?.length === 0 && !isGridDataLoading; else dataLoader">
    <div class="flex-center-col h-100-250">
      <img src="assets/images/layered-cards.svg" alt="No Data found">
      <div class="header-3 fw-600 text-center">No Data Found</div>
    </div>
  </ng-container>
</ng-template>

<div [ngClass]="showWebUI ? 'hidden-web' : 'hidden-phone'" *ngIf="!isMobileView || !showWebUI">
  <div class="pt-20 pb-10 px-24" *ngIf="(!isGridDataLoading) else gridLoader">
    <div class="align-center bg-white w-100 border-gray tb-align-center-unset tb-flex-col">
      <div class="align-center flex-grow-1 no-validation border-end tb-br-0">
        <div *ngIf="canSearch" class="position-relative flex-grow-1" id="search-dropdown">
          <div class="align-center w-100 px-10 py-12">
            <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"></span>
            <input placeholder="type to search" autocomplete="off" name="search" class="border-0 outline-0 w-100"
              [(ngModel)]="searchTerm"
              (keydown.enter)="onSearch();trackingService.trackFeature('Web.Data.DataEntry.Search.DataEntry')"
              (input)="isEmptyInput()" />
            <span class="icon ic-triangle ic-xxxs ic-dark cursor-pointer" (click)="toggleSearchDropdown()"></span>
          </div>

          <!-- Search Enhancement Dropdown -->
          <div [ngClass]="{'pe-none': isModuleWiseSearchPropertiesLoading}" *ngIf="showSearchDropdown"
            class="position-absolute w-100 bg-white brbr-20 brbl-20 left-0 top-40 border shadow-sm z-index-1001"
            (clickOutside)="showSearchDropdown = false">
            <div class="scrollbar scroll-hide max-h-100-360">
              <div class="d-flex flex-wrap p-10">
                <h6 *ngFor="let filter of getDisplayedFilters()"
                  class="px-10 fw-semi-bold br-6 py-6 m-4 cursor-pointer border"
                  [ngClass]="{'text-muted': selectedSearchFilters.length >= 5 && !isSelectedFilter(filter)}" [ngStyle]="{'pointer-events': selectedSearchFilters.length >= 5 && !isSelectedFilter(filter) ? 'none' : 'auto',
                             'opacity': selectedSearchFilters.length >= 5 && !isSelectedFilter(filter) ? '0.5' : '1'}"
                  (click)="toggleSearchFilter(filter);$event.stopPropagation()">
                  {{ filter?.displayName }}
                </h6>
              </div>
            </div>
            <div *ngIf="getSelectedFilters().length" class="p-10">
              <h5 class="fw-300 mb-6">{{recentSearches?.length ? 'Recent Searches' : 'Selected'}}</h5>
              <div class="d-flex flex-wrap">
                <div *ngFor="let filter of getSelectedFilters()"
                  class="px-10 py-6 m-4 d-flex bg-white-100 align-center cursor-pointer rounded-pill bg-slate">
                  <div (click)="toggleSearchFilter(filter)">
                    {{ filter?.displayName }}
                  </div>
                  <div class="ml-8 bg-black-900 align-center p-4 br-20"
                    (click)="toggleSearchFilter(filter); $event.stopPropagation();">
                    <span class="icon ic-cancel ic-xx-xs"></span>
                  </div>
                </div>
              </div>
            </div>
            <div class="p-10 d-flex align-center">
              <span *ngIf="selectedSearchFilters.length >= 5" class="text-danger">Users can select up to five (5)
                parameters at a time</span>
            </div>
          </div>
          <small class="text-muted text-nowrap ph-d-none mr-10 position-absolute right-0 bottom-0">
            ({{ 'LEADS.lead-search-prompt' | translate }})</small>
        </div>
        <div class="flex-end">
          <div *ngIf="canExport && globalSettingsData?.isExportDataEnabled && rowData?.length"
            class="bg-accent-green text-white px-20 py-12 h-100 align-center cursor-pointer border-start w-70px tb-br-top"
            (click)="exportDataReport();trackingService.trackFeature('Web.Data.Button.Export.Click')">{{
            'REPORTS.export' | translate }}</div>
        </div>
      </div>
      <div class="tb-br-top align-center ip-flex-col ip-align-center-unset">
        <div class="d-flex w-100">
          <!-- <div class="px-10 align-center cursor-pointer border-end tb-flex-grow-1 ph-w-40px ph-flex-grow-unset"
            (click)="openAdvFiltersModal()">
            <div class="icon ic-filter-solid ic-xxs ic-black mr-10"></div>
            <span class="fw-600 ph-d-none">{{'PROPERTY.advanced-filters' | translate}}</span>
          </div> -->
          <div class="filters-grid clear-padding border-end h-100 align-center">
            <div class="align-center h-100 ml-16 tb-ml-0">
              <div class="bg-white manage-select">
                <ng-select [virtualScroll]="true" placeholder="{{'GLOBAL.all'| translate}}" [searchable]="false"
                  ResizableDropdown class="lead-date ip-max-w-80px min-w-60" [(ngModel)]="dateType"
                  (change)="dateChange()">
                  <ng-option name="dateType" ngDefaultControl *ngFor="let dType of dateTypeList"
                    [value]="dType">{{dType}}</ng-option>
                </ng-select>
              </div>
              <div class="date-picker border-start-0 align-center ph-p-0">
                <span class="ic-appointment icon ic-xxs ic-black" [owlDateTimeTrigger]="dt1"></span>
                <input type="text" readonly [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1" [selectMode]="'range'"
                  class="pl-20 ph-pl-12 text-large ph-w-150px" placeholder="ex. 19-06-2025 - 29-06-2025"
                  (ngModelChange)="filterDate = $event; dateChange()" [ngModel]="filterDate" />
                <owl-date-time [pickerType]="'calendar'" #dt1
                  (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
              </div>
            </div>
            <div *ngIf="filtersPayload?.FromDate" (click)="onResetDateFilter()"
              class="bg-coal text-white px-10 py-12 w-50px ip-w-30px h-100 align-center">
              <span class="ip-d-none">{{ 'GLOBAL.reset' | translate }}</span> <span
                class="ic-convert d-none ip-d-block"></span>
            </div>
          </div>
        </div>
        <div class="d-flex ip-br-top">
          <div class="align-center position-relative cursor-pointer d-flex border-end">
            <span class="position-absolute left-15 z-index-2 fw-600 text-sm">
              {{ 'BUTTONS.manage-columns' | translate }}</span>
            <div class="show-hide-gray w-140">
              <ng-select [virtualScroll]="true" class="bg-white" [items]="columns" [multiple]="true" ResizableDropdown
                [searchable]="false" [closeOnSelect]="false" [ngModel]="defaultColumns"
                (change)="onColumnsSelected($event);trackingService.trackFeature('Web.Data.Options.ManageColumns.Click')">
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                  <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                      data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                      class="checkmark"></span>{{item.label}}</div>
                </ng-template>
              </ng-select>
            </div>
          </div>
          <div class="px-12 ip-w-30px  align-center cursor-pointer"
            (click)="onSetColumnDefault();trackingService.trackFeature('Web.Data.Button.Default.Click')">
            <span class="ic-swap icon ic-dark ic-xs"></span>
          </div>
          <div class="bg-coal text-white p-12 align-center cursor-pointer" (click)="openAdvFiltersModal()">
            <span class="icon ic-sliders ic-white ic-xxs mr-8"></span>
            <span>Filter</span>
          </div>
          <div class="flex-end bg-black-10 px-12 position-relative" id="saved-filter" (click)="openSavedFilter($event)">
            <span class="icon ic-triangle ic-white ic-x-xs mx-2"></span>
            <saved-filter (closeFilter)="closeSavedFilter()" [showFilters]="showFilters" [isMobileView]="isMobileView"
              (selectFilter)="handleSelectFilter($event)" (editFilter)="handleEditFilter($event)" [filters]="filters"
              *ngIf="isSavedFilterOpen"></saved-filter>
          </div>
          <div class="show-dropdown-white align-center  position-relative">
            <!-- <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">{{ 'GLOBAL.show' | translate
                }}</span> {{ 'GLOBAL.entries' |
              translate }}</span> -->
            <ng-select [virtualScroll]="true" [placeholder]="pageSize" bindValue="id" class="w-75px" ResizableDropdown
              [(ngModel)]="selectedPageSize"
              (change)="assignCount();trackingService.trackFeature('Web.Data.Button.ShowEntries.Visit')"
              [searchable]="false">
              <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                {{pageSize}}</ng-option>
            </ng-select>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-white px-4 py-12 tb-w-100-40" [ngClass]="showLeftNav ? 'w-100-190' : 'w-100-90'">
      <div class="bg-secondary flex-between" *ngIf="showFilters">
        <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
          <div class="d-flex" *ngFor="let filter of filtersPayload | keyvalue">
            <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
              *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
              {{
              ((dataFiltersKeyLabel[filter.key]
              == 'NoOfBHKs' || filter.key == 'NoOfBHKs') &&
              globalSettingsData?.isCustomLeadFormEnabled) ? 'BR' :
              dataFiltersKeyLabel[filter.key] || filter.key
              }}: {{ filter.key === 'NoOfBHKs' && !globalSettingsData?.isCustomLeadFormEnabled ?
              getBHKDisplayString(value) :
              filter.key === 'DateOfBirth' ? getTimeZoneDate(value, '00:00:00', 'dayMonthYear') :
              filter.key === 'GenderTypes' ? Gender[value] ?? '' :
              filter.key === 'MaritalStatuses' ? MaritalStatusType[value] ?? '' :
              filter.key === 'NoOfBHKs' && globalSettingsData?.isCustomLeadFormEnabled ? getBRDisplayString(value) :
              filter.key === 'Beds'? getBedsDisplay(value) :
              filter.key === 'AssignTo' ? getUserName(value) :
              filter.key === 'AssignedFromIds' ? getUserName(value) :
              filter.key === 'LastModifiedByIds' ? getUserName(value) :
              filter.key === 'ConvertedByIds' ? getUserName(value) :
              filter.key === 'QualifiedByIds' ? getUserName(value) :
              filter.key === 'CreatedByIds' ? getUserName(value) :
              filter.key === 'DeletedByIds' ? getUserName(value) :
              filter.key === 'RestoredByIds' ? getUserName(value) :
              filter.key === 'SourceIds' ? sourceIdMap?.[value] :
              filter.key === 'EnquiryTypes' ? getEnquiryTypeName(value) :
              filter.key === 'StatusIds' ? statusIdMap?.[value] :
              filter.key === 'PropertyType' ? getPropertyTypeName(value):
              filter.key === 'PropertySubType' ? getPropertySubTypeName(value):
              filter.key === 'SourcingManagers' ? getUserName(value) :
              filter.key === 'ClosingManagers' ? getUserName(value) :
              filter.key === 'MinCarpetArea' || filter.key === 'MaxCarpetArea' ?
              getArea(value,filtersPayload?.CarpetAreaUnitId) :
              filter.key === 'MinBuiltUpArea' || filter.key === 'MaxBuiltUpArea' ?
              getArea(value,filtersPayload?.BuiltUpAreaUnitId) :
              filter.key === 'MinSaleableArea' || filter.key === 'MaxSaleableArea' ?
              getArea(value,filtersPayload?.SaleableAreaUnitId) :
              filter.key === 'MinPropertyArea' || filter.key === 'MaxPropertyArea' ?
              getArea(value,filtersPayload?.PropertyAreaUnitId) :
              filter.key === 'MinNetArea' || filter.key === 'MaxNetArea' ? getArea(value,filtersPayload?.NetAreaUnitId)
              :
              filter.key === 'Profession' ? getProfession(value):
              filter.key === 'FromMinbudget' || filter.key === 'ToMinbudget' ? value?.split(" ")?.[1] :
              filter.key === 'FromMaxbudget' || filter.key === 'ToMaxbudget' ? value?.split(" ")?.[1] :
              filter.key === 'Currency' ? value?.split(" ")?.[1] :
              filter.key === 'PossesionType' ? getPossessionTypeDisplay(value) :
              filter.key === 'FromPossesionDate' || filter.key === 'ToPossesionDate' ? getTimeZoneDate(value,
              userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear') :
              value }}
              <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                (click)="onRemoveFilter(filter.key, value)"></span>
            </div>
          </div>
        </drag-scroll>
        <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
          (click)="onSaveFilter(addFile)" *ngIf="!selectedFilter">Save Filter</div>
        <div (click)="openAdvFiltersModal()"
          class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
          *ngIf="isFilterSelected">Edit Filter</div>
        <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
          *ngIf="isFilterUpdated" (click)="onSaveFilter(addFile)">Update Filter</div>
        <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
          (click)="onClearAllFilters()">{{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}</div>
      </div>
    </div>
    <ng-container *ngIf="rowData?.length else noData">
      <div class="manage-data pinned-grid checkbox-align-h-60">
        <ag-grid-angular class="ag-theme-alpine" [gridOptions]="gridOptions" (gridReady)="onGridReady($event)"
          [rowData]="rowData" [paginationPageSize]="pageSize" [pagination]="true" [suppressPaginationPanel]="true"
          [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true" (cellClicked)="onCellClicked($event)"
          [suppressMovableColumns]="false" (filterChanged)="onFilterChanged($event)">
        </ag-grid-angular>
      </div>
      <div class="my-20 flex-end">
        <div class="mr-10">{{ 'GLOBAL.showing' | translate }} {{currOffset*pageSize + 1}}
          {{ 'GLOBAL.to-small' | translate }} {{currOffset*pageSize + gridApi?.getDisplayedRowCount()}}
          {{ 'GLOBAL.of-small' | translate }} {{dataTotalCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
        <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]='getPages(dataTotalCount,pageSize)'
          (pageChange)="onPageChange($event)">
        </pagination>
      </div>
      <data-bulk-update [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}" [gridApi]="gridApi"
        [allUsers]="allUsers || []" [deactiveUsers]="deactiveUsers || []"></data-bulk-update>
    </ng-container>
  </div>
</div>
<ng-template #addFile>
  <h5 class="text-white fw-600 bg-black px-20 py-12">{{ isFilterUpdated ? 'Edit':'Add'}} Filter Name
  </h5>
  <form [formGroup]="FileForm" autocomplete="off" class="pb-20 px-30" (ngSubmit)="onSubmit()">
    <div class="field-label-req">{{ isFilterUpdated ? 'Edit':'Add'}} Filter Name</div>
    <form-errors-wrapper [control]="FileForm.controls['name']" label="filter name">
      <input type="text" formControlName="name" placeholder="enter filter name"
        (keydown.enter)="onSubmit(); $event.preventDefault()" tabindex="1">
      <div class="error-message" *ngIf="doesFileNameExist && FileForm.controls.name.status === 'VALID'">
        name already exists
      </div>
    </form-errors-wrapper>
    <div class="flex-end mt-30">
      <button type="button" class="btn-gray mr-20" id="addFileNameCancel" data-automate-id="addFileNameCancel"
        (click)="modalService.hide()">{{
        'BUTTONS.cancel' | translate }}</button>
      <button type="submit" #focusable class="btn-coal" id="addFileName" data-automate-id="addFileName">
        {{ 'Save' }}
      </button>
    </div>
  </form>
</ng-template>
<ng-template #noData>
  <div class="flex-col flex-center h-100-260 min-h-250">
    <img src="assets/images/layered-cards.svg" alt="No Data Found" width="160" height="140">
    <div class="fw-semi-bold text-xl text-mud">{{'PROFILE.no-data-found' | translate}}</div>
  </div>
</ng-template>
<ng-template #loader>
  (<div class="container px-4">
    <ng-container *ngFor="let dot of [1,2,3]">
      <div class="dot-falling"></div>
    </ng-container>
  </div>)
</ng-template>
<ng-template #gridLoader>
  <div class="flex-center h-100-130 min-h-250">
    <application-loader></application-loader>
  </div>
</ng-template>
<ng-template #dataLoader>
  <div class="flex-center h-100">
    <application-loader></application-loader>
  </div>
</ng-template>

<button class="ph-d-block d-none btn btn-accent-green h-32 position-absolute bottom-40 right-15 icon"
  (click)="toggleUI()">
  <span class="position-relative right-5 ph-d-block d-none"
    [ngClass]="{'ic-flower': !showWebUI, 'ic-lines': showWebUI}"></span>
</button>