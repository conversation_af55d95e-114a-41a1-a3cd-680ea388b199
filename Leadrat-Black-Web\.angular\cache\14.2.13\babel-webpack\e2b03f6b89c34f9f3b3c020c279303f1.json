{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.merge = void 0;\n\nvar mergeAll_1 = require(\"../operators/mergeAll\");\n\nvar innerFrom_1 = require(\"./innerFrom\");\n\nvar empty_1 = require(\"./empty\");\n\nvar args_1 = require(\"../util/args\");\n\nvar from_1 = require(\"./from\");\n\nfunction merge() {\n  var args = [];\n\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n\n  var scheduler = args_1.popScheduler(args);\n  var concurrent = args_1.popNumber(args, Infinity);\n  var sources = args;\n  return !sources.length ? empty_1.EMPTY : sources.length === 1 ? innerFrom_1.innerFrom(sources[0]) : mergeAll_1.mergeAll(concurrent)(from_1.from(sources, scheduler));\n}\n\nexports.merge = merge;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "merge", "mergeAll_1", "require", "innerFrom_1", "empty_1", "args_1", "from_1", "args", "_i", "arguments", "length", "scheduler", "popScheduler", "concurrent", "popNumber", "Infinity", "sources", "EMPTY", "innerFrom", "mergeAll", "from"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/rxjs/dist/cjs/internal/observable/merge.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.merge = void 0;\nvar mergeAll_1 = require(\"../operators/mergeAll\");\nvar innerFrom_1 = require(\"./innerFrom\");\nvar empty_1 = require(\"./empty\");\nvar args_1 = require(\"../util/args\");\nvar from_1 = require(\"./from\");\nfunction merge() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var scheduler = args_1.popScheduler(args);\n    var concurrent = args_1.popNumber(args, Infinity);\n    var sources = args;\n    return !sources.length\n        ?\n            empty_1.EMPTY\n        : sources.length === 1\n            ?\n                innerFrom_1.innerFrom(sources[0])\n            :\n                mergeAll_1.mergeAll(concurrent)(from_1.from(sources, scheduler));\n}\nexports.merge = merge;\n"], "mappings": "AAAA;;AACAA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;EAAEC,KAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACE,KAAR,GAAgB,KAAK,CAArB;;AACA,IAAIC,UAAU,GAAGC,OAAO,CAAC,uBAAD,CAAxB;;AACA,IAAIC,WAAW,GAAGD,OAAO,CAAC,aAAD,CAAzB;;AACA,IAAIE,OAAO,GAAGF,OAAO,CAAC,SAAD,CAArB;;AACA,IAAIG,MAAM,GAAGH,OAAO,CAAC,cAAD,CAApB;;AACA,IAAII,MAAM,GAAGJ,OAAO,CAAC,QAAD,CAApB;;AACA,SAASF,KAAT,GAAiB;EACb,IAAIO,IAAI,GAAG,EAAX;;EACA,KAAK,IAAIC,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAGC,SAAS,CAACC,MAAhC,EAAwCF,EAAE,EAA1C,EAA8C;IAC1CD,IAAI,CAACC,EAAD,CAAJ,GAAWC,SAAS,CAACD,EAAD,CAApB;EACH;;EACD,IAAIG,SAAS,GAAGN,MAAM,CAACO,YAAP,CAAoBL,IAApB,CAAhB;EACA,IAAIM,UAAU,GAAGR,MAAM,CAACS,SAAP,CAAiBP,IAAjB,EAAuBQ,QAAvB,CAAjB;EACA,IAAIC,OAAO,GAAGT,IAAd;EACA,OAAO,CAACS,OAAO,CAACN,MAAT,GAECN,OAAO,CAACa,KAFT,GAGDD,OAAO,CAACN,MAAR,KAAmB,CAAnB,GAEMP,WAAW,CAACe,SAAZ,CAAsBF,OAAO,CAAC,CAAD,CAA7B,CAFN,GAIMf,UAAU,CAACkB,QAAX,CAAoBN,UAApB,EAAgCP,MAAM,CAACc,IAAP,CAAYJ,OAAZ,EAAqBL,SAArB,CAAhC,CAPZ;AAQH;;AACDb,OAAO,CAACE,KAAR,GAAgBA,KAAhB", "ignoreList": []}, "metadata": {}, "sourceType": "script"}