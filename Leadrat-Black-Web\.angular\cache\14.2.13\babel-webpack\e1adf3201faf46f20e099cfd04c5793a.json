{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Inject, isDevMode, Injector, Optional, SkipSelf, inject, InjectFlags, ENVIRONMENT_INITIALIZER, NgModule } from '@angular/core';\nimport { BehaviorSubject, Observable, Subject, queueScheduler } from 'rxjs';\nimport { observeOn, withLatestFrom, scan, pluck, map, distinctUntilChanged } from 'rxjs/operators';\nconst REGISTERED_ACTION_TYPES = {};\n\nfunction resetRegisteredActionTypes() {\n  for (const key of Object.keys(REGISTERED_ACTION_TYPES)) {\n    delete REGISTERED_ACTION_TYPES[key];\n  }\n}\n/**\n * @description\n * Creates a configured `Creator` function that, when called, returns an object in the shape of the `Action` interface.\n *\n * Action creators reduce the explicitness of class-based action creators.\n *\n * @param type Describes the action that will be dispatched\n * @param config Additional metadata needed for the handling of the action.  See {@link createAction#usage-notes Usage Notes}.\n *\n * @usageNotes\n *\n * **Declaring an action creator**\n *\n * Without additional metadata:\n * ```ts\n * export const increment = createAction('[Counter] Increment');\n * ```\n * With additional metadata:\n * ```ts\n * export const loginSuccess = createAction(\n *   '[Auth/API] Login Success',\n *   props<{ user: User }>()\n * );\n * ```\n * With a function:\n * ```ts\n * export const loginSuccess = createAction(\n *   '[Auth/API] Login Success',\n *   (response: Response) => response.user\n * );\n * ```\n *\n * **Dispatching an action**\n *\n * Without additional metadata:\n * ```ts\n * store.dispatch(increment());\n * ```\n * With additional metadata:\n * ```ts\n * store.dispatch(loginSuccess({ user: newUser }));\n * ```\n *\n * **Referencing an action in a reducer**\n *\n * Using a switch statement:\n * ```ts\n * switch (action.type) {\n *   // ...\n *   case AuthApiActions.loginSuccess.type: {\n *     return {\n *       ...state,\n *       user: action.user\n *     };\n *   }\n * }\n * ```\n * Using a reducer creator:\n * ```ts\n * on(AuthApiActions.loginSuccess, (state, { user }) => ({ ...state, user }))\n * ```\n *\n *  **Referencing an action in an effect**\n * ```ts\n * effectName$ = createEffect(\n *   () => this.actions$.pipe(\n *     ofType(AuthApiActions.loginSuccess),\n *     // ...\n *   )\n * );\n * ```\n */\n\n\nfunction createAction(type, config) {\n  REGISTERED_ACTION_TYPES[type] = (REGISTERED_ACTION_TYPES[type] || 0) + 1;\n\n  if (typeof config === 'function') {\n    return defineType(type, (...args) => Object.assign(Object.assign({}, config(...args)), {\n      type\n    }));\n  }\n\n  const as = config ? config._as : 'empty';\n\n  switch (as) {\n    case 'empty':\n      return defineType(type, () => ({\n        type\n      }));\n\n    case 'props':\n      return defineType(type, props => Object.assign(Object.assign({}, props), {\n        type\n      }));\n\n    default:\n      throw new Error('Unexpected config.');\n  }\n}\n\nfunction props() {\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  return {\n    _as: 'props',\n    _p: undefined\n  };\n}\n\nfunction union(creators) {\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  return undefined;\n}\n\nfunction defineType(type, creator) {\n  return Object.defineProperty(creator, 'type', {\n    value: type,\n    writable: false\n  });\n}\n\nfunction capitalize(text) {\n  return text.charAt(0).toUpperCase() + text.substring(1);\n}\n/**\n * @description\n * A function that creates a group of action creators with the same source.\n *\n * @param config An object that contains a source and dictionary of events.\n * An event is a key-value pair of an event name and event props.\n * @returns A dictionary of action creators.\n * The name of each action creator is created by camel casing the event name.\n * The type of each action is created using the \"[Source] Event Name\" pattern.\n *\n * @usageNotes\n *\n * ```ts\n * const authApiActions = createActionGroup({\n *   source: 'Auth API',\n *   events: {\n *     // defining events with payload using the `props` function\n *     'Login Success': props<{ userId: number; token: string }>(),\n *     'Login Failure': props<{ error: string }>(),\n *\n *     // defining an event without payload using the `emptyProps` function\n *     'Logout Success': emptyProps(),\n *\n *     // defining an event with payload using the props factory\n *     'Logout Failure': (error: Error) => ({ error }),\n *   },\n * });\n *\n * // action type: \"[Auth API] Login Success\"\n * authApiActions.loginSuccess({ userId: 10, token: 'ngrx' });\n *\n * // action type: \"[Auth API] Login Failure\"\n * authApiActions.loginFailure({ error: 'Login Failure!' });\n *\n * // action type: \"[Auth API] Logout Success\"\n * authApiActions.logoutSuccess();\n *\n * // action type: \"[Auth API] Logout Failure\";\n * authApiActions.logoutFailure(new Error('Logout Failure!'));\n * ```\n */\n\n\nfunction createActionGroup(config) {\n  const {\n    source,\n    events\n  } = config;\n  return Object.keys(events).reduce((actionGroup, eventName) => Object.assign(Object.assign({}, actionGroup), {\n    [toActionName(eventName)]: createAction(toActionType(source, eventName), events[eventName])\n  }), {});\n}\n\nfunction emptyProps() {\n  return props();\n}\n\nfunction toActionName(eventName) {\n  return eventName.trim().toLowerCase().split(' ').map((word, i) => i === 0 ? word : capitalize(word)).join('');\n}\n\nfunction toActionType(source, eventName) {\n  return `[${source}] ${eventName}`;\n}\n\nconst INIT = '@ngrx/store/init';\n\nclass ActionsSubject extends BehaviorSubject {\n  constructor() {\n    super({\n      type: INIT\n    });\n  }\n\n  next(action) {\n    if (typeof action === 'function') {\n      throw new TypeError(`\n        Dispatch expected an object, instead it received a function.\n        If you're using the createAction function, make sure to invoke the function\n        before dispatching the action. For example, someAction should be someAction().`);\n    } else if (typeof action === 'undefined') {\n      throw new TypeError(`Actions must be objects`);\n    } else if (typeof action.type === 'undefined') {\n      throw new TypeError(`Actions must have a type property`);\n    }\n\n    super.next(action);\n  }\n\n  complete() {\n    /* noop */\n  }\n\n  ngOnDestroy() {\n    super.complete();\n  }\n\n}\n/** @nocollapse */\n\n\nActionsSubject.ɵfac = function ActionsSubject_Factory(t) {\n  return new (t || ActionsSubject)();\n};\n/** @nocollapse */\n\n\nActionsSubject.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ActionsSubject,\n  factory: ActionsSubject.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ActionsSubject, [{\n    type: Injectable\n  }], function () {\n    return [];\n  }, null);\n})();\n\nconst ACTIONS_SUBJECT_PROVIDERS = [ActionsSubject];\n\nconst _ROOT_STORE_GUARD = new InjectionToken('@ngrx/store Internal Root Guard');\n\nconst _INITIAL_STATE = new InjectionToken('@ngrx/store Internal Initial State');\n\nconst INITIAL_STATE = new InjectionToken('@ngrx/store Initial State');\nconst REDUCER_FACTORY = new InjectionToken('@ngrx/store Reducer Factory');\n\nconst _REDUCER_FACTORY = new InjectionToken('@ngrx/store Internal Reducer Factory Provider');\n\nconst INITIAL_REDUCERS = new InjectionToken('@ngrx/store Initial Reducers');\n\nconst _INITIAL_REDUCERS = new InjectionToken('@ngrx/store Internal Initial Reducers');\n\nconst STORE_FEATURES = new InjectionToken('@ngrx/store Store Features');\n\nconst _STORE_REDUCERS = new InjectionToken('@ngrx/store Internal Store Reducers');\n\nconst _FEATURE_REDUCERS = new InjectionToken('@ngrx/store Internal Feature Reducers');\n\nconst _FEATURE_CONFIGS = new InjectionToken('@ngrx/store Internal Feature Configs');\n\nconst _STORE_FEATURES = new InjectionToken('@ngrx/store Internal Store Features');\n\nconst _FEATURE_REDUCERS_TOKEN = new InjectionToken('@ngrx/store Internal Feature Reducers Token');\n\nconst FEATURE_REDUCERS = new InjectionToken('@ngrx/store Feature Reducers');\n/**\n * User-defined meta reducers from StoreModule.forRoot()\n */\n\nconst USER_PROVIDED_META_REDUCERS = new InjectionToken('@ngrx/store User Provided Meta Reducers');\n/**\n * Meta reducers defined either internally by @ngrx/store or by library authors\n */\n\nconst META_REDUCERS = new InjectionToken('@ngrx/store Meta Reducers');\n/**\n * Concats the user provided meta reducers and the meta reducers provided on the multi\n * injection token\n */\n\nconst _RESOLVED_META_REDUCERS = new InjectionToken('@ngrx/store Internal Resolved Meta Reducers');\n/**\n * Runtime checks defined by the user via an InjectionToken\n * Defaults to `_USER_RUNTIME_CHECKS`\n */\n\n\nconst USER_RUNTIME_CHECKS = new InjectionToken('@ngrx/store User Runtime Checks Config');\n/**\n * Runtime checks defined by the user via forRoot()\n */\n\nconst _USER_RUNTIME_CHECKS = new InjectionToken('@ngrx/store Internal User Runtime Checks Config');\n/**\n * Runtime checks currently in use\n */\n\n\nconst ACTIVE_RUNTIME_CHECKS = new InjectionToken('@ngrx/store Internal Runtime Checks');\n\nconst _ACTION_TYPE_UNIQUENESS_CHECK = new InjectionToken('@ngrx/store Check if Action types are unique');\n/**\n * InjectionToken that registers the global Store.\n * Mainly used to provide a hook that can be injected\n * to ensure the root state is loaded before something\n * that depends on it.\n */\n\n\nconst ROOT_STORE_PROVIDER = new InjectionToken('@ngrx/store Root Store Provider');\n/**\n * InjectionToken that registers feature states.\n * Mainly used to provide a hook that can be injected\n * to ensure feature state is loaded before something\n * that depends on it.\n */\n\nconst FEATURE_STATE_PROVIDER = new InjectionToken('@ngrx/store Feature State Provider');\n/**\n * @description\n * Combines reducers for individual features into a single reducer.\n *\n * You can use this function to delegate handling of state transitions to multiple reducers, each acting on their\n * own sub-state within the root state.\n *\n * @param reducers An object mapping keys of the root state to their corresponding feature reducer.\n * @param initialState Provides a state value if the current state is `undefined`, as it is initially.\n * @returns A reducer function.\n *\n * @usageNotes\n *\n * **Example combining two feature reducers into one \"root\" reducer**\n *\n * ```ts\n * export const reducer = combineReducers({\n *   featureA: featureAReducer,\n *   featureB: featureBReducer\n * });\n * ```\n *\n * You can also override the initial states of the sub-features:\n * ```ts\n * export const reducer = combineReducers({\n *   featureA: featureAReducer,\n *   featureB: featureBReducer\n * }, {\n *   featureA: { counterA: 13 },\n *   featureB: { counterB: 37 }\n * });\n * ```\n */\n\nfunction combineReducers(reducers, initialState = {}) {\n  const reducerKeys = Object.keys(reducers);\n  const finalReducers = {};\n\n  for (let i = 0; i < reducerKeys.length; i++) {\n    const key = reducerKeys[i];\n\n    if (typeof reducers[key] === 'function') {\n      finalReducers[key] = reducers[key];\n    }\n  }\n\n  const finalReducerKeys = Object.keys(finalReducers);\n  return function combination(state, action) {\n    state = state === undefined ? initialState : state;\n    let hasChanged = false;\n    const nextState = {};\n\n    for (let i = 0; i < finalReducerKeys.length; i++) {\n      const key = finalReducerKeys[i];\n      const reducer = finalReducers[key];\n      const previousStateForKey = state[key];\n      const nextStateForKey = reducer(previousStateForKey, action);\n      nextState[key] = nextStateForKey;\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n    }\n\n    return hasChanged ? nextState : state;\n  };\n}\n\nfunction omit(object, keyToRemove) {\n  return Object.keys(object).filter(key => key !== keyToRemove).reduce((result, key) => Object.assign(result, {\n    [key]: object[key]\n  }), {});\n}\n\nfunction compose(...functions) {\n  return function (arg) {\n    if (functions.length === 0) {\n      return arg;\n    }\n\n    const last = functions[functions.length - 1];\n    const rest = functions.slice(0, -1);\n    return rest.reduceRight((composed, fn) => fn(composed), last(arg));\n  };\n}\n\nfunction createReducerFactory(reducerFactory, metaReducers) {\n  if (Array.isArray(metaReducers) && metaReducers.length > 0) {\n    reducerFactory = compose.apply(null, [...metaReducers, reducerFactory]);\n  }\n\n  return (reducers, initialState) => {\n    const reducer = reducerFactory(reducers);\n    return (state, action) => {\n      state = state === undefined ? initialState : state;\n      return reducer(state, action);\n    };\n  };\n}\n\nfunction createFeatureReducerFactory(metaReducers) {\n  const reducerFactory = Array.isArray(metaReducers) && metaReducers.length > 0 ? compose(...metaReducers) : r => r;\n  return (reducer, initialState) => {\n    reducer = reducerFactory(reducer);\n    return (state, action) => {\n      state = state === undefined ? initialState : state;\n      return reducer(state, action);\n    };\n  };\n}\n\nclass ReducerObservable extends Observable {}\n\nclass ReducerManagerDispatcher extends ActionsSubject {}\n\nconst UPDATE = '@ngrx/store/update-reducers';\n\nclass ReducerManager extends BehaviorSubject {\n  constructor(dispatcher, initialState, reducers, reducerFactory) {\n    super(reducerFactory(reducers, initialState));\n    this.dispatcher = dispatcher;\n    this.initialState = initialState;\n    this.reducers = reducers;\n    this.reducerFactory = reducerFactory;\n  }\n\n  get currentReducers() {\n    return this.reducers;\n  }\n\n  addFeature(feature) {\n    this.addFeatures([feature]);\n  }\n\n  addFeatures(features) {\n    const reducers = features.reduce((reducerDict, {\n      reducers,\n      reducerFactory,\n      metaReducers,\n      initialState,\n      key\n    }) => {\n      const reducer = typeof reducers === 'function' ? createFeatureReducerFactory(metaReducers)(reducers, initialState) : createReducerFactory(reducerFactory, metaReducers)(reducers, initialState);\n      reducerDict[key] = reducer;\n      return reducerDict;\n    }, {});\n    this.addReducers(reducers);\n  }\n\n  removeFeature(feature) {\n    this.removeFeatures([feature]);\n  }\n\n  removeFeatures(features) {\n    this.removeReducers(features.map(p => p.key));\n  }\n\n  addReducer(key, reducer) {\n    this.addReducers({\n      [key]: reducer\n    });\n  }\n\n  addReducers(reducers) {\n    this.reducers = Object.assign(Object.assign({}, this.reducers), reducers);\n    this.updateReducers(Object.keys(reducers));\n  }\n\n  removeReducer(featureKey) {\n    this.removeReducers([featureKey]);\n  }\n\n  removeReducers(featureKeys) {\n    featureKeys.forEach(key => {\n      this.reducers = omit(this.reducers, key)\n      /*TODO(#823)*/\n      ;\n    });\n    this.updateReducers(featureKeys);\n  }\n\n  updateReducers(featureKeys) {\n    this.next(this.reducerFactory(this.reducers, this.initialState));\n    this.dispatcher.next({\n      type: UPDATE,\n      features: featureKeys\n    });\n  }\n\n  ngOnDestroy() {\n    this.complete();\n  }\n\n}\n/** @nocollapse */\n\n\nReducerManager.ɵfac = function ReducerManager_Factory(t) {\n  return new (t || ReducerManager)(i0.ɵɵinject(ReducerManagerDispatcher), i0.ɵɵinject(INITIAL_STATE), i0.ɵɵinject(INITIAL_REDUCERS), i0.ɵɵinject(REDUCER_FACTORY));\n};\n/** @nocollapse */\n\n\nReducerManager.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ReducerManager,\n  factory: ReducerManager.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ReducerManager, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: ReducerManagerDispatcher\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [INITIAL_STATE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [INITIAL_REDUCERS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [REDUCER_FACTORY]\n      }]\n    }];\n  }, null);\n})();\n\nconst REDUCER_MANAGER_PROVIDERS = [ReducerManager, {\n  provide: ReducerObservable,\n  useExisting: ReducerManager\n}, {\n  provide: ReducerManagerDispatcher,\n  useExisting: ActionsSubject\n}];\n\nclass ScannedActionsSubject extends Subject {\n  ngOnDestroy() {\n    this.complete();\n  }\n\n}\n/** @nocollapse */\n\n\nScannedActionsSubject.ɵfac = /* @__PURE__ */function () {\n  let ɵScannedActionsSubject_BaseFactory;\n  return function ScannedActionsSubject_Factory(t) {\n    return (ɵScannedActionsSubject_BaseFactory || (ɵScannedActionsSubject_BaseFactory = i0.ɵɵgetInheritedFactory(ScannedActionsSubject)))(t || ScannedActionsSubject);\n  };\n}();\n/** @nocollapse */\n\n\nScannedActionsSubject.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ScannedActionsSubject,\n  factory: ScannedActionsSubject.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScannedActionsSubject, [{\n    type: Injectable\n  }], null, null);\n})();\n\nconst SCANNED_ACTIONS_SUBJECT_PROVIDERS = [ScannedActionsSubject];\n\nclass StateObservable extends Observable {}\n\nclass State extends BehaviorSubject {\n  constructor(actions$, reducer$, scannedActions, initialState) {\n    super(initialState);\n    const actionsOnQueue$ = actions$.pipe(observeOn(queueScheduler));\n    const withLatestReducer$ = actionsOnQueue$.pipe(withLatestFrom(reducer$));\n    const seed = {\n      state: initialState\n    };\n    const stateAndAction$ = withLatestReducer$.pipe(scan(reduceState, seed));\n    this.stateSubscription = stateAndAction$.subscribe(({\n      state,\n      action\n    }) => {\n      this.next(state);\n      scannedActions.next(action);\n    });\n  }\n\n  ngOnDestroy() {\n    this.stateSubscription.unsubscribe();\n    this.complete();\n  }\n\n}\n\nState.INIT = INIT;\n/** @nocollapse */\n\nState.ɵfac = function State_Factory(t) {\n  return new (t || State)(i0.ɵɵinject(ActionsSubject), i0.ɵɵinject(ReducerObservable), i0.ɵɵinject(ScannedActionsSubject), i0.ɵɵinject(INITIAL_STATE));\n};\n/** @nocollapse */\n\n\nState.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: State,\n  factory: State.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(State, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: ActionsSubject\n    }, {\n      type: ReducerObservable\n    }, {\n      type: ScannedActionsSubject\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [INITIAL_STATE]\n      }]\n    }];\n  }, null);\n})();\n\nfunction reduceState(stateActionPair = {\n  state: undefined\n}, [action, reducer]) {\n  const {\n    state\n  } = stateActionPair;\n  return {\n    state: reducer(state, action),\n    action\n  };\n}\n\nconst STATE_PROVIDERS = [State, {\n  provide: StateObservable,\n  useExisting: State\n}]; // disabled because we have lowercase generics for `select`\n\nclass Store extends Observable {\n  constructor(state$, actionsObserver, reducerManager) {\n    super();\n    this.actionsObserver = actionsObserver;\n    this.reducerManager = reducerManager;\n    this.source = state$;\n  }\n\n  select(pathOrMapFn, ...paths) {\n    return select.call(null, pathOrMapFn, ...paths)(this);\n  }\n\n  lift(operator) {\n    const store = new Store(this, this.actionsObserver, this.reducerManager);\n    store.operator = operator;\n    return store;\n  }\n\n  dispatch(action) {\n    this.actionsObserver.next(action);\n  }\n\n  next(action) {\n    this.actionsObserver.next(action);\n  }\n\n  error(err) {\n    this.actionsObserver.error(err);\n  }\n\n  complete() {\n    this.actionsObserver.complete();\n  }\n\n  addReducer(key, reducer) {\n    this.reducerManager.addReducer(key, reducer);\n  }\n\n  removeReducer(key) {\n    this.reducerManager.removeReducer(key);\n  }\n\n}\n/** @nocollapse */\n\n\nStore.ɵfac = function Store_Factory(t) {\n  return new (t || Store)(i0.ɵɵinject(StateObservable), i0.ɵɵinject(ActionsSubject), i0.ɵɵinject(ReducerManager));\n};\n/** @nocollapse */\n\n\nStore.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Store,\n  factory: Store.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Store, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: StateObservable\n    }, {\n      type: ActionsSubject\n    }, {\n      type: ReducerManager\n    }];\n  }, null);\n})();\n\nconst STORE_PROVIDERS = [Store];\n\nfunction select(pathOrMapFn, propsOrPath, ...paths) {\n  return function selectOperator(source$) {\n    let mapped$;\n\n    if (typeof pathOrMapFn === 'string') {\n      const pathSlices = [propsOrPath, ...paths].filter(Boolean);\n      mapped$ = source$.pipe(pluck(pathOrMapFn, ...pathSlices));\n    } else if (typeof pathOrMapFn === 'function') {\n      mapped$ = source$.pipe(map(source => pathOrMapFn(source, propsOrPath)));\n    } else {\n      throw new TypeError(`Unexpected type '${typeof pathOrMapFn}' in select operator,` + ` expected 'string' or 'function'`);\n    }\n\n    return mapped$.pipe(distinctUntilChanged());\n  };\n}\n\nconst RUNTIME_CHECK_URL = 'https://ngrx.io/guide/store/configuration/runtime-checks';\n\nfunction isUndefined(target) {\n  return target === undefined;\n}\n\nfunction isNull(target) {\n  return target === null;\n}\n\nfunction isArray(target) {\n  return Array.isArray(target);\n}\n\nfunction isString(target) {\n  return typeof target === 'string';\n}\n\nfunction isBoolean(target) {\n  return typeof target === 'boolean';\n}\n\nfunction isNumber(target) {\n  return typeof target === 'number';\n}\n\nfunction isObjectLike(target) {\n  return typeof target === 'object' && target !== null;\n}\n\nfunction isObject(target) {\n  return isObjectLike(target) && !isArray(target);\n}\n\nfunction isPlainObject(target) {\n  if (!isObject(target)) {\n    return false;\n  }\n\n  const targetPrototype = Object.getPrototypeOf(target);\n  return targetPrototype === Object.prototype || targetPrototype === null;\n}\n\nfunction isFunction(target) {\n  return typeof target === 'function';\n}\n\nfunction isComponent(target) {\n  return isFunction(target) && target.hasOwnProperty('ɵcmp');\n}\n\nfunction hasOwnProperty(target, propertyName) {\n  return Object.prototype.hasOwnProperty.call(target, propertyName);\n}\n\nlet _ngrxMockEnvironment = false;\n\nfunction setNgrxMockEnvironment(value) {\n  _ngrxMockEnvironment = value;\n}\n\nfunction isNgrxMockEnvironment() {\n  return _ngrxMockEnvironment;\n}\n\nfunction isEqualCheck(a, b) {\n  return a === b;\n}\n\nfunction isArgumentsChanged(args, lastArguments, comparator) {\n  for (let i = 0; i < args.length; i++) {\n    if (!comparator(args[i], lastArguments[i])) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction resultMemoize(projectionFn, isResultEqual) {\n  return defaultMemoize(projectionFn, isEqualCheck, isResultEqual);\n}\n\nfunction defaultMemoize(projectionFn, isArgumentsEqual = isEqualCheck, isResultEqual = isEqualCheck) {\n  let lastArguments = null; // eslint-disable-next-line @typescript-eslint/no-explicit-any, , , , ,\n\n  let lastResult = null;\n  let overrideResult;\n\n  function reset() {\n    lastArguments = null;\n    lastResult = null;\n  }\n\n  function setResult(result = undefined) {\n    overrideResult = {\n      result\n    };\n  }\n\n  function clearResult() {\n    overrideResult = undefined;\n  }\n  /* eslint-disable prefer-rest-params, prefer-spread */\n  // disabled because of the use of `arguments`\n\n\n  function memoized() {\n    if (overrideResult !== undefined) {\n      return overrideResult.result;\n    }\n\n    if (!lastArguments) {\n      lastResult = projectionFn.apply(null, arguments);\n      lastArguments = arguments;\n      return lastResult;\n    }\n\n    if (!isArgumentsChanged(arguments, lastArguments, isArgumentsEqual)) {\n      return lastResult;\n    }\n\n    const newResult = projectionFn.apply(null, arguments);\n    lastArguments = arguments;\n\n    if (isResultEqual(lastResult, newResult)) {\n      return lastResult;\n    }\n\n    lastResult = newResult;\n    return newResult;\n  }\n\n  return {\n    memoized,\n    reset,\n    setResult,\n    clearResult\n  };\n}\n\nfunction createSelector(...input) {\n  return createSelectorFactory(defaultMemoize)(...input);\n}\n\nfunction defaultStateFn(state, selectors, props, memoizedProjector) {\n  if (props === undefined) {\n    const args = selectors.map(fn => fn(state));\n    return memoizedProjector.memoized.apply(null, args);\n  }\n\n  const args = selectors.map(fn => fn(state, props));\n  return memoizedProjector.memoized.apply(null, [...args, props]);\n}\n/**\n *\n * @param memoize The function used to memoize selectors\n * @param options Config Object that may include a `stateFn` function defining how to return the selector's value, given the entire `Store`'s state, parent `Selector`s, `Props`, and a `MemoizedProjection`\n *\n * @usageNotes\n *\n * **Creating a Selector Factory Where Array Order Does Not Matter**\n *\n * ```ts\n * function removeMatch(arr: string[], target: string): string[] {\n *   const matchIndex = arr.indexOf(target);\n *   return [...arr.slice(0, matchIndex), ...arr.slice(matchIndex + 1)];\n * }\n *\n * function orderDoesNotMatterComparer(a: any, b: any): boolean {\n *   if (!Array.isArray(a) || !Array.isArray(b)) {\n *     return a === b;\n *   }\n *   if (a.length !== b.length) {\n *     return false;\n *   }\n *   let tempB = [...b];\n *   function reduceToDetermineIfArraysContainSameContents(\n *     previousCallResult: boolean,\n *     arrayMember: any\n *   ): boolean {\n *     if (previousCallResult === false) {\n *       return false;\n *     }\n *     if (tempB.includes(arrayMember)) {\n *       tempB = removeMatch(tempB, arrayMember);\n *       return true;\n *     }\n *     return false;\n *   }\n *   return a.reduce(reduceToDetermineIfArraysContainSameContents, true);\n * }\n *\n * export const creactOrderDoesNotMatterSelector = createSelectorFactory(\n *   (projectionFun) => defaultMemoize(\n *     projectionFun,\n *     orderDoesNotMatterComparer,\n *     orderDoesNotMatterComparer\n *   )\n * );\n * ```\n *\n * **Creating an Alternative Memoization Strategy**\n *\n * ```ts\n * function serialize(x: any): string {\n *   return JSON.stringify(x);\n * }\n *\n * export const createFullHistorySelector = createSelectorFactory(\n *  (projectionFunction) => {\n *    const cache = {};\n *\n *    function memoized() {\n *      const serializedArguments = serialize(...arguments);\n *       if (cache[serializedArguments] != null) {\n *         cache[serializedArguments] = projectionFunction.apply(null, arguments);\n *       }\n *       return cache[serializedArguments];\n *     }\n *     return {\n *       memoized,\n *       reset: () => {},\n *       setResult: () => {},\n *       clearResult: () => {},\n *     };\n *   }\n * );\n * ```\n *\n *\n */\n\n\nfunction createSelectorFactory(memoize, options = {\n  stateFn: defaultStateFn\n}) {\n  return function (...input) {\n    let args = input;\n\n    if (Array.isArray(args[0])) {\n      const [head, ...tail] = args;\n      args = [...head, ...tail];\n    }\n\n    const selectors = args.slice(0, args.length - 1);\n    const projector = args[args.length - 1];\n    const memoizedSelectors = selectors.filter(selector => selector.release && typeof selector.release === 'function');\n    const memoizedProjector = memoize(function (...selectors) {\n      return projector.apply(null, selectors);\n    });\n    const memoizedState = defaultMemoize(function (state, props) {\n      return options.stateFn.apply(null, [state, selectors, props, memoizedProjector]);\n    });\n\n    function release() {\n      memoizedState.reset();\n      memoizedProjector.reset();\n      memoizedSelectors.forEach(selector => selector.release());\n    }\n\n    return Object.assign(memoizedState.memoized, {\n      release,\n      projector: memoizedProjector.memoized,\n      setResult: memoizedState.setResult,\n      clearResult: memoizedState.clearResult\n    });\n  };\n}\n\nfunction createFeatureSelector(featureName) {\n  return createSelector(state => {\n    const featureState = state[featureName];\n\n    if (!isNgrxMockEnvironment() && isDevMode() && !(featureName in state)) {\n      console.warn(`@ngrx/store: The feature name \"${featureName}\" does ` + 'not exist in the state, therefore createFeatureSelector ' + 'cannot access it.  Be sure it is imported in a loaded module ' + `using StoreModule.forRoot('${featureName}', ...) or ` + `StoreModule.forFeature('${featureName}', ...).  If the default ` + 'state is intended to be undefined, as is the case with router ' + 'state, this development-only warning message can be ignored.');\n    }\n\n    return featureState;\n  }, featureState => featureState);\n}\n/**\n * @description\n * A function that accepts a feature name and a feature reducer, and creates\n * a feature selector and a selector for each feature state property.\n *\n * @param featureConfig An object that contains a feature name and a feature reducer.\n * @returns An object that contains a feature name, a feature reducer,\n * a feature selector, and a selector for each feature state property.\n *\n * @usageNotes\n *\n * **With Application State**\n *\n * ```ts\n * interface AppState {\n *   products: ProductsState;\n * }\n *\n * interface ProductsState {\n *   products: Product[];\n *   selectedId: string | null;\n * }\n *\n * const initialState: ProductsState = {\n *   products: [],\n *   selectedId: null,\n * };\n *\n * // AppState is passed as a generic argument\n * const productsFeature = createFeature<AppState>({\n *   name: 'products',\n *   reducer: createReducer(\n *     initialState,\n *     on(ProductsApiActions.loadSuccess(state, { products }) => ({\n *       ...state,\n *       products,\n *     }),\n *   ),\n * });\n *\n * const {\n *   selectProductsState, // type: MemoizedSelector<AppState, ProductsState>\n *   selectProducts, // type: MemoizedSelector<AppState, Product[]>\n *   selectSelectedId, // type: MemoizedSelector<AppState, string | null>\n * } = productsFeature;\n * ```\n *\n * **Without Application State**\n *\n * ```ts\n * const productsFeature = createFeature({\n *   name: 'products',\n *   reducer: createReducer(initialState),\n * });\n *\n * const {\n *   selectProductsState, // type: MemoizedSelector<Record<string, any>, ProductsState>\n *   selectProducts, // type: MemoizedSelector<Record<string, any>, Product[]>\n *   selectSelectedId, // type: MemoizedSelector<Record<string, any, string | null>\n * } = productsFeature;\n * ```\n */\n\n\nfunction createFeature(featureConfig) {\n  const {\n    name,\n    reducer\n  } = featureConfig;\n  const featureSelector = createFeatureSelector(name);\n  const nestedSelectors = createNestedSelectors(featureSelector, reducer);\n  return Object.assign({\n    name,\n    reducer,\n    [`select${capitalize(name)}State`]: featureSelector\n  }, nestedSelectors);\n}\n\nfunction createNestedSelectors(featureSelector, reducer) {\n  const initialState = getInitialState(reducer);\n  const nestedKeys = isPlainObject(initialState) ? Object.keys(initialState) : [];\n  return nestedKeys.reduce((nestedSelectors, nestedKey) => Object.assign(Object.assign({}, nestedSelectors), {\n    [`select${capitalize(nestedKey)}`]: createSelector(featureSelector, parentState => parentState === null || parentState === void 0 ? void 0 : parentState[nestedKey])\n  }), {});\n}\n\nfunction getInitialState(reducer) {\n  return reducer(undefined, {\n    type: '@ngrx/feature/init'\n  });\n}\n\nfunction _createStoreReducers(injector, reducers) {\n  return reducers instanceof InjectionToken ? injector.get(reducers) : reducers;\n}\n\nfunction _createFeatureStore(injector, configs, featureStores) {\n  return featureStores.map((feat, index) => {\n    if (configs[index] instanceof InjectionToken) {\n      const conf = injector.get(configs[index]);\n      return {\n        key: feat.key,\n        reducerFactory: conf.reducerFactory ? conf.reducerFactory : combineReducers,\n        metaReducers: conf.metaReducers ? conf.metaReducers : [],\n        initialState: conf.initialState\n      };\n    }\n\n    return feat;\n  });\n}\n\nfunction _createFeatureReducers(injector, reducerCollection) {\n  const reducers = reducerCollection.map(reducer => {\n    return reducer instanceof InjectionToken ? injector.get(reducer) : reducer;\n  });\n  return reducers;\n}\n\nfunction _initialStateFactory(initialState) {\n  if (typeof initialState === 'function') {\n    return initialState();\n  }\n\n  return initialState;\n}\n\nfunction _concatMetaReducers(metaReducers, userProvidedMetaReducers) {\n  return metaReducers.concat(userProvidedMetaReducers);\n}\n\nfunction _provideForRootGuard(store) {\n  if (store) {\n    throw new TypeError(`The root Store has been provided more than once. Feature modules should provide feature states instead.`);\n  }\n\n  return 'guarded';\n}\n\nfunction immutabilityCheckMetaReducer(reducer, checks) {\n  return function (state, action) {\n    const act = checks.action(action) ? freeze(action) : action;\n    const nextState = reducer(state, act);\n    return checks.state() ? freeze(nextState) : nextState;\n  };\n}\n\nfunction freeze(target) {\n  Object.freeze(target);\n  const targetIsFunction = isFunction(target);\n  Object.getOwnPropertyNames(target).forEach(prop => {\n    // Ignore Ivy properties, ref: https://github.com/ngrx/platform/issues/2109#issuecomment-582689060\n    if (prop.startsWith('ɵ')) {\n      return;\n    }\n\n    if (hasOwnProperty(target, prop) && (targetIsFunction ? prop !== 'caller' && prop !== 'callee' && prop !== 'arguments' : true)) {\n      const propValue = target[prop];\n\n      if ((isObjectLike(propValue) || isFunction(propValue)) && !Object.isFrozen(propValue)) {\n        freeze(propValue);\n      }\n    }\n  });\n  return target;\n}\n\nfunction serializationCheckMetaReducer(reducer, checks) {\n  return function (state, action) {\n    if (checks.action(action)) {\n      const unserializableAction = getUnserializable(action);\n      throwIfUnserializable(unserializableAction, 'action');\n    }\n\n    const nextState = reducer(state, action);\n\n    if (checks.state()) {\n      const unserializableState = getUnserializable(nextState);\n      throwIfUnserializable(unserializableState, 'state');\n    }\n\n    return nextState;\n  };\n}\n\nfunction getUnserializable(target, path = []) {\n  // Guard against undefined and null, e.g. a reducer that returns undefined\n  if ((isUndefined(target) || isNull(target)) && path.length === 0) {\n    return {\n      path: ['root'],\n      value: target\n    };\n  }\n\n  const keys = Object.keys(target);\n  return keys.reduce((result, key) => {\n    if (result) {\n      return result;\n    }\n\n    const value = target[key]; // Ignore Ivy components\n\n    if (isComponent(value)) {\n      return result;\n    }\n\n    if (isUndefined(value) || isNull(value) || isNumber(value) || isBoolean(value) || isString(value) || isArray(value)) {\n      return false;\n    }\n\n    if (isPlainObject(value)) {\n      return getUnserializable(value, [...path, key]);\n    }\n\n    return {\n      path: [...path, key],\n      value\n    };\n  }, false);\n}\n\nfunction throwIfUnserializable(unserializable, context) {\n  if (unserializable === false) {\n    return;\n  }\n\n  const unserializablePath = unserializable.path.join('.');\n  const error = new Error(`Detected unserializable ${context} at \"${unserializablePath}\". ${RUNTIME_CHECK_URL}#strict${context}serializability`);\n  error.value = unserializable.value;\n  error.unserializablePath = unserializablePath;\n  throw error;\n}\n\nfunction inNgZoneAssertMetaReducer(reducer, checks) {\n  return function (state, action) {\n    if (checks.action(action) && !i0.NgZone.isInAngularZone()) {\n      throw new Error(`Action '${action.type}' running outside NgZone. ${RUNTIME_CHECK_URL}#strictactionwithinngzone`);\n    }\n\n    return reducer(state, action);\n  };\n}\n\nfunction createActiveRuntimeChecks(runtimeChecks) {\n  if (isDevMode()) {\n    return Object.assign({\n      strictStateSerializability: false,\n      strictActionSerializability: false,\n      strictStateImmutability: true,\n      strictActionImmutability: true,\n      strictActionWithinNgZone: false,\n      strictActionTypeUniqueness: false\n    }, runtimeChecks);\n  }\n\n  return {\n    strictStateSerializability: false,\n    strictActionSerializability: false,\n    strictStateImmutability: false,\n    strictActionImmutability: false,\n    strictActionWithinNgZone: false,\n    strictActionTypeUniqueness: false\n  };\n}\n\nfunction createSerializationCheckMetaReducer({\n  strictActionSerializability,\n  strictStateSerializability\n}) {\n  return reducer => strictActionSerializability || strictStateSerializability ? serializationCheckMetaReducer(reducer, {\n    action: action => strictActionSerializability && !ignoreNgrxAction(action),\n    state: () => strictStateSerializability\n  }) : reducer;\n}\n\nfunction createImmutabilityCheckMetaReducer({\n  strictActionImmutability,\n  strictStateImmutability\n}) {\n  return reducer => strictActionImmutability || strictStateImmutability ? immutabilityCheckMetaReducer(reducer, {\n    action: action => strictActionImmutability && !ignoreNgrxAction(action),\n    state: () => strictStateImmutability\n  }) : reducer;\n}\n\nfunction ignoreNgrxAction(action) {\n  return action.type.startsWith('@ngrx');\n}\n\nfunction createInNgZoneCheckMetaReducer({\n  strictActionWithinNgZone\n}) {\n  return reducer => strictActionWithinNgZone ? inNgZoneAssertMetaReducer(reducer, {\n    action: action => strictActionWithinNgZone && !ignoreNgrxAction(action)\n  }) : reducer;\n}\n\nfunction provideRuntimeChecks(runtimeChecks) {\n  return [{\n    provide: _USER_RUNTIME_CHECKS,\n    useValue: runtimeChecks\n  }, {\n    provide: USER_RUNTIME_CHECKS,\n    useFactory: _runtimeChecksFactory,\n    deps: [_USER_RUNTIME_CHECKS]\n  }, {\n    provide: ACTIVE_RUNTIME_CHECKS,\n    deps: [USER_RUNTIME_CHECKS],\n    useFactory: createActiveRuntimeChecks\n  }, {\n    provide: META_REDUCERS,\n    multi: true,\n    deps: [ACTIVE_RUNTIME_CHECKS],\n    useFactory: createImmutabilityCheckMetaReducer\n  }, {\n    provide: META_REDUCERS,\n    multi: true,\n    deps: [ACTIVE_RUNTIME_CHECKS],\n    useFactory: createSerializationCheckMetaReducer\n  }, {\n    provide: META_REDUCERS,\n    multi: true,\n    deps: [ACTIVE_RUNTIME_CHECKS],\n    useFactory: createInNgZoneCheckMetaReducer\n  }];\n}\n\nfunction checkForActionTypeUniqueness() {\n  return [{\n    provide: _ACTION_TYPE_UNIQUENESS_CHECK,\n    multi: true,\n    deps: [ACTIVE_RUNTIME_CHECKS],\n    useFactory: _actionTypeUniquenessCheck\n  }];\n}\n\nfunction _runtimeChecksFactory(runtimeChecks) {\n  return runtimeChecks;\n}\n\nfunction _actionTypeUniquenessCheck(config) {\n  if (!config.strictActionTypeUniqueness) {\n    return;\n  }\n\n  const duplicates = Object.entries(REGISTERED_ACTION_TYPES).filter(([, registrations]) => registrations > 1).map(([type]) => type);\n\n  if (duplicates.length) {\n    throw new Error(`Action types are registered more than once, ${duplicates.map(type => `\"${type}\"`).join(', ')}. ${RUNTIME_CHECK_URL}#strictactiontypeuniqueness`);\n  }\n}\n/**\n * Provides additional slices of state in the Store.\n * These providers cannot be used at the component level.\n *\n * @usageNotes\n *\n * ### Providing Store Features\n *\n * ```ts\n * const booksRoutes: Route[] = [\n *   {\n *     path: '',\n *     providers: [provideState('books', booksReducer)],\n *     children: [\n *       { path: '', component: BookListComponent },\n *       { path: ':id', component: BookDetailsComponent },\n *     ],\n *   },\n * ];\n * ```\n */\n\n\nfunction provideState(featureNameOrSlice, reducers, config = {}) {\n  return {\n    ɵproviders: [..._provideState(featureNameOrSlice, reducers, config), ENVIRONMENT_STATE_PROVIDER]\n  };\n}\n\nfunction _provideStore(reducers, config) {\n  return [{\n    provide: _ROOT_STORE_GUARD,\n    useFactory: _provideForRootGuard,\n    deps: [[Store, new Optional(), new SkipSelf()]]\n  }, {\n    provide: _INITIAL_STATE,\n    useValue: config.initialState\n  }, {\n    provide: INITIAL_STATE,\n    useFactory: _initialStateFactory,\n    deps: [_INITIAL_STATE]\n  }, {\n    provide: _INITIAL_REDUCERS,\n    useValue: reducers\n  }, {\n    provide: _STORE_REDUCERS,\n    useExisting: reducers instanceof InjectionToken ? reducers : _INITIAL_REDUCERS\n  }, {\n    provide: INITIAL_REDUCERS,\n    deps: [Injector, _INITIAL_REDUCERS, [new Inject(_STORE_REDUCERS)]],\n    useFactory: _createStoreReducers\n  }, {\n    provide: USER_PROVIDED_META_REDUCERS,\n    useValue: config.metaReducers ? config.metaReducers : []\n  }, {\n    provide: _RESOLVED_META_REDUCERS,\n    deps: [META_REDUCERS, USER_PROVIDED_META_REDUCERS],\n    useFactory: _concatMetaReducers\n  }, {\n    provide: _REDUCER_FACTORY,\n    useValue: config.reducerFactory ? config.reducerFactory : combineReducers\n  }, {\n    provide: REDUCER_FACTORY,\n    deps: [_REDUCER_FACTORY, _RESOLVED_META_REDUCERS],\n    useFactory: createReducerFactory\n  }, ACTIONS_SUBJECT_PROVIDERS, REDUCER_MANAGER_PROVIDERS, SCANNED_ACTIONS_SUBJECT_PROVIDERS, STATE_PROVIDERS, STORE_PROVIDERS, provideRuntimeChecks(config.runtimeChecks), checkForActionTypeUniqueness()];\n}\n\nfunction rootStoreProviderFactory() {\n  inject(ActionsSubject);\n  inject(ReducerObservable);\n  inject(ScannedActionsSubject);\n  inject(Store);\n  inject(_ROOT_STORE_GUARD, InjectFlags.Optional);\n  inject(_ACTION_TYPE_UNIQUENESS_CHECK, InjectFlags.Optional);\n}\n/**\n * Environment Initializer used in the root\n * providers to initialize the Store\n */\n\n\nconst ENVIRONMENT_STORE_PROVIDER = [{\n  provide: ROOT_STORE_PROVIDER,\n  useFactory: rootStoreProviderFactory\n}, {\n  provide: ENVIRONMENT_INITIALIZER,\n  multi: true,\n\n  useFactory() {\n    return () => inject(ROOT_STORE_PROVIDER);\n  }\n\n}];\n/**\n * Provides the global Store providers and initializes\n * the Store.\n * These providers cannot be used at the component level.\n *\n * @usageNotes\n *\n * ### Providing the Global Store\n *\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideStore()],\n * });\n * ```\n */\n\nfunction provideStore(reducers = {}, config = {}) {\n  return {\n    ɵproviders: [..._provideStore(reducers, config), ENVIRONMENT_STORE_PROVIDER]\n  };\n}\n\nfunction featureStateProviderFactory() {\n  inject(ROOT_STORE_PROVIDER);\n  const features = inject(_STORE_FEATURES);\n  const featureReducers = inject(FEATURE_REDUCERS);\n  const reducerManager = inject(ReducerManager);\n  inject(_ACTION_TYPE_UNIQUENESS_CHECK, InjectFlags.Optional);\n  const feats = features.map((feature, index) => {\n    const featureReducerCollection = featureReducers.shift(); // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n\n    const reducers = featureReducerCollection\n    /*TODO(#823)*/\n    [index];\n    return Object.assign(Object.assign({}, feature), {\n      reducers,\n      initialState: _initialStateFactory(feature.initialState)\n    });\n  });\n  reducerManager.addFeatures(feats);\n}\n/**\n * Environment Initializer used in the feature\n * providers to register state features\n */\n\n\nconst ENVIRONMENT_STATE_PROVIDER = [{\n  provide: FEATURE_STATE_PROVIDER,\n  useFactory: featureStateProviderFactory\n}, {\n  provide: ENVIRONMENT_INITIALIZER,\n  multi: true,\n  deps: [],\n\n  useFactory() {\n    return () => inject(FEATURE_STATE_PROVIDER);\n  }\n\n}];\n\nfunction _provideState(featureNameOrSlice, reducers, config = {}) {\n  return [{\n    provide: _FEATURE_CONFIGS,\n    multi: true,\n    useValue: featureNameOrSlice instanceof Object ? {} : config\n  }, {\n    provide: STORE_FEATURES,\n    multi: true,\n    useValue: {\n      key: featureNameOrSlice instanceof Object ? featureNameOrSlice.name : featureNameOrSlice,\n      reducerFactory: !(config instanceof InjectionToken) && config.reducerFactory ? config.reducerFactory : combineReducers,\n      metaReducers: !(config instanceof InjectionToken) && config.metaReducers ? config.metaReducers : [],\n      initialState: !(config instanceof InjectionToken) && config.initialState ? config.initialState : undefined\n    }\n  }, {\n    provide: _STORE_FEATURES,\n    deps: [Injector, _FEATURE_CONFIGS, STORE_FEATURES],\n    useFactory: _createFeatureStore\n  }, {\n    provide: _FEATURE_REDUCERS,\n    multi: true,\n    useValue: featureNameOrSlice instanceof Object ? featureNameOrSlice.reducer : reducers\n  }, {\n    provide: _FEATURE_REDUCERS_TOKEN,\n    multi: true,\n    useExisting: reducers instanceof InjectionToken ? reducers : _FEATURE_REDUCERS\n  }, {\n    provide: FEATURE_REDUCERS,\n    multi: true,\n    deps: [Injector, _FEATURE_REDUCERS, [new Inject(_FEATURE_REDUCERS_TOKEN)]],\n    useFactory: _createFeatureReducers\n  }, checkForActionTypeUniqueness()];\n}\n\nclass StoreRootModule {\n  constructor(actions$, reducer$, scannedActions$, store, guard, actionCheck) {}\n\n}\n/** @nocollapse */\n\n\nStoreRootModule.ɵfac = function StoreRootModule_Factory(t) {\n  return new (t || StoreRootModule)(i0.ɵɵinject(ActionsSubject), i0.ɵɵinject(ReducerObservable), i0.ɵɵinject(ScannedActionsSubject), i0.ɵɵinject(Store), i0.ɵɵinject(_ROOT_STORE_GUARD, 8), i0.ɵɵinject(_ACTION_TYPE_UNIQUENESS_CHECK, 8));\n};\n/** @nocollapse */\n\n\nStoreRootModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: StoreRootModule\n});\n/** @nocollapse */\n\nStoreRootModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StoreRootModule, [{\n    type: NgModule,\n    args: [{}]\n  }], function () {\n    return [{\n      type: ActionsSubject\n    }, {\n      type: ReducerObservable\n    }, {\n      type: ScannedActionsSubject\n    }, {\n      type: Store\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [_ROOT_STORE_GUARD]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [_ACTION_TYPE_UNIQUENESS_CHECK]\n      }]\n    }];\n  }, null);\n})();\n\nclass StoreFeatureModule {\n  constructor(features, featureReducers, reducerManager, root, actionCheck) {\n    this.features = features;\n    this.featureReducers = featureReducers;\n    this.reducerManager = reducerManager;\n    const feats = features.map((feature, index) => {\n      const featureReducerCollection = featureReducers.shift(); // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n\n      const reducers = featureReducerCollection\n      /*TODO(#823)*/\n      [index];\n      return Object.assign(Object.assign({}, feature), {\n        reducers,\n        initialState: _initialStateFactory(feature.initialState)\n      });\n    });\n    reducerManager.addFeatures(feats);\n  } // eslint-disable-next-line @angular-eslint/contextual-lifecycle\n\n\n  ngOnDestroy() {\n    this.reducerManager.removeFeatures(this.features);\n  }\n\n}\n/** @nocollapse */\n\n\nStoreFeatureModule.ɵfac = function StoreFeatureModule_Factory(t) {\n  return new (t || StoreFeatureModule)(i0.ɵɵinject(_STORE_FEATURES), i0.ɵɵinject(FEATURE_REDUCERS), i0.ɵɵinject(ReducerManager), i0.ɵɵinject(StoreRootModule), i0.ɵɵinject(_ACTION_TYPE_UNIQUENESS_CHECK, 8));\n};\n/** @nocollapse */\n\n\nStoreFeatureModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: StoreFeatureModule\n});\n/** @nocollapse */\n\nStoreFeatureModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StoreFeatureModule, [{\n    type: NgModule,\n    args: [{}]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [_STORE_FEATURES]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [FEATURE_REDUCERS]\n      }]\n    }, {\n      type: ReducerManager\n    }, {\n      type: StoreRootModule\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [_ACTION_TYPE_UNIQUENESS_CHECK]\n      }]\n    }];\n  }, null);\n})();\n\nclass StoreModule {\n  static forRoot(reducers, config = {}) {\n    return {\n      ngModule: StoreRootModule,\n      providers: [..._provideStore(reducers, config)]\n    };\n  }\n\n  static forFeature(featureNameOrSlice, reducers, config = {}) {\n    return {\n      ngModule: StoreFeatureModule,\n      providers: [..._provideState(featureNameOrSlice, reducers, config)]\n    };\n  }\n\n}\n/** @nocollapse */\n\n\nStoreModule.ɵfac = function StoreModule_Factory(t) {\n  return new (t || StoreModule)();\n};\n/** @nocollapse */\n\n\nStoreModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: StoreModule\n});\n/** @nocollapse */\n\nStoreModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StoreModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n/**\n * @description\n * Associates actions with a given state change function.\n * A state change function must be provided as the last parameter.\n *\n * @param args `ActionCreator`'s followed by a state change function.\n *\n * @returns an association of action types with a state change function.\n *\n * @usageNotes\n * ```ts\n * on(AuthApiActions.loginSuccess, (state, { user }) => ({ ...state, user }))\n * ```\n */\n\n\nfunction on(...args) {\n  const reducer = args.pop();\n  const types = args.map(creator => creator.type);\n  return {\n    reducer,\n    types\n  };\n}\n/**\n * @description\n * Creates a reducer function to handle state transitions.\n *\n * Reducer creators reduce the explicitness of reducer functions with switch statements.\n *\n * @param initialState Provides a state value if the current state is `undefined`, as it is initially.\n * @param ons Associations between actions and state changes.\n * @returns A reducer function.\n *\n * @usageNotes\n *\n * - Must be used with `ActionCreator`'s (returned by `createAction`). Cannot be used with class-based action creators.\n * - The returned `ActionReducer` should additionally be wrapped with another function, if you are using View Engine AOT.\n * In case you are using Ivy (or only JIT View Engine) the extra wrapper function is not required.\n *\n * **Declaring a reducer creator**\n *\n * ```ts\n * export const reducer = createReducer(\n *   initialState,\n *   on(\n *     featureActions.actionOne,\n *     featureActions.actionTwo,\n *     (state, { updatedValue }) => ({ ...state, prop: updatedValue })\n *   ),\n *   on(featureActions.actionThree, () => initialState);\n * );\n * ```\n *\n * **Declaring a reducer creator using a wrapper function (Only needed if using View Engine AOT)**\n *\n * ```ts\n * const featureReducer = createReducer(\n *   initialState,\n *   on(\n *     featureActions.actionOne,\n *     featureActions.actionTwo,\n *     (state, { updatedValue }) => ({ ...state, prop: updatedValue })\n *   ),\n *   on(featureActions.actionThree, () => initialState);\n * );\n *\n * export function reducer(state: State | undefined, action: Action) {\n *   return featureReducer(state, action);\n * }\n * ```\n */\n\n\nfunction createReducer(initialState, ...ons) {\n  const map = new Map();\n\n  for (const on of ons) {\n    for (const type of on.types) {\n      const existingReducer = map.get(type);\n\n      if (existingReducer) {\n        const newReducer = (state, action) => on.reducer(existingReducer(state, action), action);\n\n        map.set(type, newReducer);\n      } else {\n        map.set(type, on.reducer);\n      }\n    }\n  }\n\n  return function (state = initialState, action) {\n    const reducer = map.get(action.type);\n    return reducer ? reducer(state, action) : state;\n  };\n}\n/**\n * DO NOT EDIT\n *\n * This file is automatically generated at build\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ACTIVE_RUNTIME_CHECKS, ActionsSubject, FEATURE_REDUCERS, FEATURE_STATE_PROVIDER, INIT, INITIAL_REDUCERS, INITIAL_STATE, META_REDUCERS, REDUCER_FACTORY, ROOT_STORE_PROVIDER, ReducerManager, ReducerManagerDispatcher, ReducerObservable, STORE_FEATURES, ScannedActionsSubject, State, StateObservable, Store, StoreFeatureModule, StoreModule, StoreRootModule, UPDATE, USER_PROVIDED_META_REDUCERS, USER_RUNTIME_CHECKS, combineReducers, compose, createAction, createActionGroup, createFeature, createFeatureSelector, createReducer, createReducerFactory, createSelector, createSelectorFactory, defaultMemoize, defaultStateFn, emptyProps, isNgrxMockEnvironment, on, props, provideState, provideStore, reduceState, resultMemoize, select, setNgrxMockEnvironment, union };", "map": {"version": 3, "names": ["i0", "Injectable", "InjectionToken", "Inject", "isDevMode", "Injector", "Optional", "SkipSelf", "inject", "InjectFlags", "ENVIRONMENT_INITIALIZER", "NgModule", "BehaviorSubject", "Observable", "Subject", "queueScheduler", "observeOn", "withLatestFrom", "scan", "pluck", "map", "distinctUntilChanged", "REGISTERED_ACTION_TYPES", "resetRegisteredActionTypes", "key", "Object", "keys", "createAction", "type", "config", "defineType", "args", "assign", "as", "_as", "props", "Error", "_p", "undefined", "union", "creators", "creator", "defineProperty", "value", "writable", "capitalize", "text", "char<PERSON>t", "toUpperCase", "substring", "createActionGroup", "source", "events", "reduce", "actionGroup", "eventName", "toActionName", "toActionType", "emptyProps", "trim", "toLowerCase", "split", "word", "i", "join", "INIT", "ActionsSubject", "constructor", "next", "action", "TypeError", "complete", "ngOnDestroy", "ɵfac", "ɵprov", "ACTIONS_SUBJECT_PROVIDERS", "_ROOT_STORE_GUARD", "_INITIAL_STATE", "INITIAL_STATE", "REDUCER_FACTORY", "_REDUCER_FACTORY", "INITIAL_REDUCERS", "_INITIAL_REDUCERS", "STORE_FEATURES", "_STORE_REDUCERS", "_FEATURE_REDUCERS", "_FEATURE_CONFIGS", "_STORE_FEATURES", "_FEATURE_REDUCERS_TOKEN", "FEATURE_REDUCERS", "USER_PROVIDED_META_REDUCERS", "META_REDUCERS", "_RESOLVED_META_REDUCERS", "USER_RUNTIME_CHECKS", "_USER_RUNTIME_CHECKS", "ACTIVE_RUNTIME_CHECKS", "_ACTION_TYPE_UNIQUENESS_CHECK", "ROOT_STORE_PROVIDER", "FEATURE_STATE_PROVIDER", "combineReducers", "reducers", "initialState", "reducerKeys", "finalReducers", "length", "final<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "combination", "state", "has<PERSON><PERSON>ed", "nextState", "reducer", "previousStateForKey", "nextStateForKey", "omit", "object", "keyToRemove", "filter", "result", "compose", "functions", "arg", "last", "rest", "slice", "reduceRight", "composed", "fn", "createReducerFactory", "reducerFactory", "metaReducers", "Array", "isArray", "apply", "createFeatureReducerFactory", "r", "ReducerObservable", "ReducerMana<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "UPDATE", "ReducerManager", "dispatcher", "currentReducers", "addFeature", "feature", "addFeatures", "features", "reducerDict", "addReducers", "removeFeature", "removeFeatures", "removeReducers", "p", "addReducer", "updateReducers", "removeReducer", "<PERSON><PERSON><PERSON>", "featureKeys", "for<PERSON>ach", "decorators", "REDUCER_MANAGER_PROVIDERS", "provide", "useExisting", "ScannedActionsSubject", "SCANNED_ACTIONS_SUBJECT_PROVIDERS", "StateObservable", "State", "actions$", "reducer$", "scannedActions", "actionsOnQueue$", "pipe", "withLatestReducer$", "seed", "stateAndAction$", "reduceState", "stateSubscription", "subscribe", "unsubscribe", "stateActionPair", "STATE_PROVIDERS", "Store", "state$", "actionsObserver", "reducerManager", "select", "pathOrMapFn", "paths", "call", "lift", "operator", "store", "dispatch", "error", "err", "STORE_PROVIDERS", "props<PERSON><PERSON><PERSON><PERSON>", "selectOperator", "source$", "mapped$", "pathSlices", "Boolean", "RUNTIME_CHECK_URL", "isUndefined", "target", "isNull", "isString", "isBoolean", "isNumber", "isObjectLike", "isObject", "isPlainObject", "targetPrototype", "getPrototypeOf", "prototype", "isFunction", "isComponent", "hasOwnProperty", "propertyName", "_ngrxMockEnvironment", "setNgrxMockEnvironment", "isNgrxMockEnvironment", "isEqualCheck", "a", "b", "isArgumentsChanged", "lastArguments", "comparator", "resultMemoize", "projectionFn", "isResultEqual", "defaultMemoize", "isArgumentsEqual", "lastResult", "overrideResult", "reset", "setResult", "clearResult", "memoized", "arguments", "newResult", "createSelector", "input", "createSelectorFactory", "defaultStateFn", "selectors", "memoizedProjector", "memoize", "options", "stateFn", "head", "tail", "projector", "memoizedSelectors", "selector", "release", "memoizedState", "createFeatureSelector", "featureName", "featureState", "console", "warn", "createFeature", "featureConfig", "name", "featureSelector", "nestedSelectors", "createNestedSelectors", "getInitialState", "nestedKeys", "nested<PERSON><PERSON>", "parentState", "_createStoreReducers", "injector", "get", "_createFeatureStore", "configs", "featureStores", "feat", "index", "conf", "_createFeatureReducers", "reducerCollection", "_initialStateFactory", "_concatMetaReducers", "userProvidedMetaReducers", "concat", "_provideForRootGuard", "immutabilityCheckMetaReducer", "checks", "act", "freeze", "targetIsFunction", "getOwnPropertyNames", "prop", "startsWith", "propValue", "isFrozen", "serializationCheckMetaReducer", "unserializableAction", "getUnserializable", "throwIfUnserializable", "unserializableState", "path", "unserializable", "context", "unserializablePath", "inNgZoneAssertMetaReducer", "NgZone", "isInAngularZone", "createActiveRuntimeChecks", "runtimeChecks", "strictStateSerializability", "strictActionSerializability", "strictStateImmutability", "strictActionImmutability", "strictActionWithinNgZone", "strictActionTypeUniqueness", "createSerializationCheckMetaReducer", "ignoreNgrxAction", "createImmutabilityCheckMetaReducer", "createInNgZoneCheckMetaReducer", "provideRuntimeChecks", "useValue", "useFactory", "_runtimeChecksFactory", "deps", "multi", "checkForActionTypeUniqueness", "_actionTypeUniquenessCheck", "duplicates", "entries", "registrations", "provideState", "featureNameOrSlice", "ɵproviders", "_provideState", "ENVIRONMENT_STATE_PROVIDER", "_provideStore", "rootStoreProviderFactory", "ENVIRONMENT_STORE_PROVIDER", "provideStore", "featureStateProviderFactory", "featureReducers", "feats", "featureReducerCollection", "shift", "StoreRootModule", "scannedActions$", "guard", "actionCheck", "ɵmod", "ɵinj", "StoreFeatureModule", "root", "StoreModule", "forRoot", "ngModule", "providers", "forFeature", "on", "pop", "types", "createReducer", "ons", "Map", "existingReducer", "newReducer", "set"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/@ngrx/store/fesm2015/ngrx-store.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Inject, isDevMode, Injector, Optional, SkipSelf, inject, InjectFlags, ENVIRONMENT_INITIALIZER, NgModule } from '@angular/core';\nimport { BehaviorSubject, Observable, Subject, queueScheduler } from 'rxjs';\nimport { observeOn, withLatestFrom, scan, pluck, map, distinctUntilChanged } from 'rxjs/operators';\n\nconst REGISTERED_ACTION_TYPES = {};\nfunction resetRegisteredActionTypes() {\n    for (const key of Object.keys(REGISTERED_ACTION_TYPES)) {\n        delete REGISTERED_ACTION_TYPES[key];\n    }\n}\n\n/**\n * @description\n * Creates a configured `Creator` function that, when called, returns an object in the shape of the `Action` interface.\n *\n * Action creators reduce the explicitness of class-based action creators.\n *\n * @param type Describes the action that will be dispatched\n * @param config Additional metadata needed for the handling of the action.  See {@link createAction#usage-notes Usage Notes}.\n *\n * @usageNotes\n *\n * **Declaring an action creator**\n *\n * Without additional metadata:\n * ```ts\n * export const increment = createAction('[Counter] Increment');\n * ```\n * With additional metadata:\n * ```ts\n * export const loginSuccess = createAction(\n *   '[Auth/API] Login Success',\n *   props<{ user: User }>()\n * );\n * ```\n * With a function:\n * ```ts\n * export const loginSuccess = createAction(\n *   '[Auth/API] Login Success',\n *   (response: Response) => response.user\n * );\n * ```\n *\n * **Dispatching an action**\n *\n * Without additional metadata:\n * ```ts\n * store.dispatch(increment());\n * ```\n * With additional metadata:\n * ```ts\n * store.dispatch(loginSuccess({ user: newUser }));\n * ```\n *\n * **Referencing an action in a reducer**\n *\n * Using a switch statement:\n * ```ts\n * switch (action.type) {\n *   // ...\n *   case AuthApiActions.loginSuccess.type: {\n *     return {\n *       ...state,\n *       user: action.user\n *     };\n *   }\n * }\n * ```\n * Using a reducer creator:\n * ```ts\n * on(AuthApiActions.loginSuccess, (state, { user }) => ({ ...state, user }))\n * ```\n *\n *  **Referencing an action in an effect**\n * ```ts\n * effectName$ = createEffect(\n *   () => this.actions$.pipe(\n *     ofType(AuthApiActions.loginSuccess),\n *     // ...\n *   )\n * );\n * ```\n */\nfunction createAction(type, config) {\n    REGISTERED_ACTION_TYPES[type] = (REGISTERED_ACTION_TYPES[type] || 0) + 1;\n    if (typeof config === 'function') {\n        return defineType(type, (...args) => (Object.assign(Object.assign({}, config(...args)), { type })));\n    }\n    const as = config ? config._as : 'empty';\n    switch (as) {\n        case 'empty':\n            return defineType(type, () => ({ type }));\n        case 'props':\n            return defineType(type, (props) => (Object.assign(Object.assign({}, props), { type })));\n        default:\n            throw new Error('Unexpected config.');\n    }\n}\nfunction props() {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return { _as: 'props', _p: undefined };\n}\nfunction union(creators) {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return undefined;\n}\nfunction defineType(type, creator) {\n    return Object.defineProperty(creator, 'type', {\n        value: type,\n        writable: false,\n    });\n}\n\nfunction capitalize(text) {\n    return (text.charAt(0).toUpperCase() + text.substring(1));\n}\n\n/**\n * @description\n * A function that creates a group of action creators with the same source.\n *\n * @param config An object that contains a source and dictionary of events.\n * An event is a key-value pair of an event name and event props.\n * @returns A dictionary of action creators.\n * The name of each action creator is created by camel casing the event name.\n * The type of each action is created using the \"[Source] Event Name\" pattern.\n *\n * @usageNotes\n *\n * ```ts\n * const authApiActions = createActionGroup({\n *   source: 'Auth API',\n *   events: {\n *     // defining events with payload using the `props` function\n *     'Login Success': props<{ userId: number; token: string }>(),\n *     'Login Failure': props<{ error: string }>(),\n *\n *     // defining an event without payload using the `emptyProps` function\n *     'Logout Success': emptyProps(),\n *\n *     // defining an event with payload using the props factory\n *     'Logout Failure': (error: Error) => ({ error }),\n *   },\n * });\n *\n * // action type: \"[Auth API] Login Success\"\n * authApiActions.loginSuccess({ userId: 10, token: 'ngrx' });\n *\n * // action type: \"[Auth API] Login Failure\"\n * authApiActions.loginFailure({ error: 'Login Failure!' });\n *\n * // action type: \"[Auth API] Logout Success\"\n * authApiActions.logoutSuccess();\n *\n * // action type: \"[Auth API] Logout Failure\";\n * authApiActions.logoutFailure(new Error('Logout Failure!'));\n * ```\n */\nfunction createActionGroup(config) {\n    const { source, events } = config;\n    return Object.keys(events).reduce((actionGroup, eventName) => (Object.assign(Object.assign({}, actionGroup), { [toActionName(eventName)]: createAction(toActionType(source, eventName), events[eventName]) })), {});\n}\nfunction emptyProps() {\n    return props();\n}\nfunction toActionName(eventName) {\n    return eventName\n        .trim()\n        .toLowerCase()\n        .split(' ')\n        .map((word, i) => (i === 0 ? word : capitalize(word)))\n        .join('');\n}\nfunction toActionType(source, eventName) {\n    return `[${source}] ${eventName}`;\n}\n\nconst INIT = '@ngrx/store/init';\nclass ActionsSubject extends BehaviorSubject {\n    constructor() {\n        super({ type: INIT });\n    }\n    next(action) {\n        if (typeof action === 'function') {\n            throw new TypeError(`\n        Dispatch expected an object, instead it received a function.\n        If you're using the createAction function, make sure to invoke the function\n        before dispatching the action. For example, someAction should be someAction().`);\n        }\n        else if (typeof action === 'undefined') {\n            throw new TypeError(`Actions must be objects`);\n        }\n        else if (typeof action.type === 'undefined') {\n            throw new TypeError(`Actions must have a type property`);\n        }\n        super.next(action);\n    }\n    complete() {\n        /* noop */\n    }\n    ngOnDestroy() {\n        super.complete();\n    }\n}\n/** @nocollapse */ ActionsSubject.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: ActionsSubject, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ ActionsSubject.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: ActionsSubject });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: ActionsSubject, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return []; } });\nconst ACTIONS_SUBJECT_PROVIDERS = [ActionsSubject];\n\nconst _ROOT_STORE_GUARD = new InjectionToken('@ngrx/store Internal Root Guard');\nconst _INITIAL_STATE = new InjectionToken('@ngrx/store Internal Initial State');\nconst INITIAL_STATE = new InjectionToken('@ngrx/store Initial State');\nconst REDUCER_FACTORY = new InjectionToken('@ngrx/store Reducer Factory');\nconst _REDUCER_FACTORY = new InjectionToken('@ngrx/store Internal Reducer Factory Provider');\nconst INITIAL_REDUCERS = new InjectionToken('@ngrx/store Initial Reducers');\nconst _INITIAL_REDUCERS = new InjectionToken('@ngrx/store Internal Initial Reducers');\nconst STORE_FEATURES = new InjectionToken('@ngrx/store Store Features');\nconst _STORE_REDUCERS = new InjectionToken('@ngrx/store Internal Store Reducers');\nconst _FEATURE_REDUCERS = new InjectionToken('@ngrx/store Internal Feature Reducers');\nconst _FEATURE_CONFIGS = new InjectionToken('@ngrx/store Internal Feature Configs');\nconst _STORE_FEATURES = new InjectionToken('@ngrx/store Internal Store Features');\nconst _FEATURE_REDUCERS_TOKEN = new InjectionToken('@ngrx/store Internal Feature Reducers Token');\nconst FEATURE_REDUCERS = new InjectionToken('@ngrx/store Feature Reducers');\n/**\n * User-defined meta reducers from StoreModule.forRoot()\n */\nconst USER_PROVIDED_META_REDUCERS = new InjectionToken('@ngrx/store User Provided Meta Reducers');\n/**\n * Meta reducers defined either internally by @ngrx/store or by library authors\n */\nconst META_REDUCERS = new InjectionToken('@ngrx/store Meta Reducers');\n/**\n * Concats the user provided meta reducers and the meta reducers provided on the multi\n * injection token\n */\nconst _RESOLVED_META_REDUCERS = new InjectionToken('@ngrx/store Internal Resolved Meta Reducers');\n/**\n * Runtime checks defined by the user via an InjectionToken\n * Defaults to `_USER_RUNTIME_CHECKS`\n */\nconst USER_RUNTIME_CHECKS = new InjectionToken('@ngrx/store User Runtime Checks Config');\n/**\n * Runtime checks defined by the user via forRoot()\n */\nconst _USER_RUNTIME_CHECKS = new InjectionToken('@ngrx/store Internal User Runtime Checks Config');\n/**\n * Runtime checks currently in use\n */\nconst ACTIVE_RUNTIME_CHECKS = new InjectionToken('@ngrx/store Internal Runtime Checks');\nconst _ACTION_TYPE_UNIQUENESS_CHECK = new InjectionToken('@ngrx/store Check if Action types are unique');\n/**\n * InjectionToken that registers the global Store.\n * Mainly used to provide a hook that can be injected\n * to ensure the root state is loaded before something\n * that depends on it.\n */\nconst ROOT_STORE_PROVIDER = new InjectionToken('@ngrx/store Root Store Provider');\n/**\n * InjectionToken that registers feature states.\n * Mainly used to provide a hook that can be injected\n * to ensure feature state is loaded before something\n * that depends on it.\n */\nconst FEATURE_STATE_PROVIDER = new InjectionToken('@ngrx/store Feature State Provider');\n\n/**\n * @description\n * Combines reducers for individual features into a single reducer.\n *\n * You can use this function to delegate handling of state transitions to multiple reducers, each acting on their\n * own sub-state within the root state.\n *\n * @param reducers An object mapping keys of the root state to their corresponding feature reducer.\n * @param initialState Provides a state value if the current state is `undefined`, as it is initially.\n * @returns A reducer function.\n *\n * @usageNotes\n *\n * **Example combining two feature reducers into one \"root\" reducer**\n *\n * ```ts\n * export const reducer = combineReducers({\n *   featureA: featureAReducer,\n *   featureB: featureBReducer\n * });\n * ```\n *\n * You can also override the initial states of the sub-features:\n * ```ts\n * export const reducer = combineReducers({\n *   featureA: featureAReducer,\n *   featureB: featureBReducer\n * }, {\n *   featureA: { counterA: 13 },\n *   featureB: { counterB: 37 }\n * });\n * ```\n */\nfunction combineReducers(reducers, initialState = {}) {\n    const reducerKeys = Object.keys(reducers);\n    const finalReducers = {};\n    for (let i = 0; i < reducerKeys.length; i++) {\n        const key = reducerKeys[i];\n        if (typeof reducers[key] === 'function') {\n            finalReducers[key] = reducers[key];\n        }\n    }\n    const finalReducerKeys = Object.keys(finalReducers);\n    return function combination(state, action) {\n        state = state === undefined ? initialState : state;\n        let hasChanged = false;\n        const nextState = {};\n        for (let i = 0; i < finalReducerKeys.length; i++) {\n            const key = finalReducerKeys[i];\n            const reducer = finalReducers[key];\n            const previousStateForKey = state[key];\n            const nextStateForKey = reducer(previousStateForKey, action);\n            nextState[key] = nextStateForKey;\n            hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n        }\n        return hasChanged ? nextState : state;\n    };\n}\nfunction omit(object, keyToRemove) {\n    return Object.keys(object)\n        .filter((key) => key !== keyToRemove)\n        .reduce((result, key) => Object.assign(result, { [key]: object[key] }), {});\n}\nfunction compose(...functions) {\n    return function (arg) {\n        if (functions.length === 0) {\n            return arg;\n        }\n        const last = functions[functions.length - 1];\n        const rest = functions.slice(0, -1);\n        return rest.reduceRight((composed, fn) => fn(composed), last(arg));\n    };\n}\nfunction createReducerFactory(reducerFactory, metaReducers) {\n    if (Array.isArray(metaReducers) && metaReducers.length > 0) {\n        reducerFactory = compose.apply(null, [\n            ...metaReducers,\n            reducerFactory,\n        ]);\n    }\n    return (reducers, initialState) => {\n        const reducer = reducerFactory(reducers);\n        return (state, action) => {\n            state = state === undefined ? initialState : state;\n            return reducer(state, action);\n        };\n    };\n}\nfunction createFeatureReducerFactory(metaReducers) {\n    const reducerFactory = Array.isArray(metaReducers) && metaReducers.length > 0\n        ? compose(...metaReducers)\n        : (r) => r;\n    return (reducer, initialState) => {\n        reducer = reducerFactory(reducer);\n        return (state, action) => {\n            state = state === undefined ? initialState : state;\n            return reducer(state, action);\n        };\n    };\n}\n\nclass ReducerObservable extends Observable {\n}\nclass ReducerManagerDispatcher extends ActionsSubject {\n}\nconst UPDATE = '@ngrx/store/update-reducers';\nclass ReducerManager extends BehaviorSubject {\n    constructor(dispatcher, initialState, reducers, reducerFactory) {\n        super(reducerFactory(reducers, initialState));\n        this.dispatcher = dispatcher;\n        this.initialState = initialState;\n        this.reducers = reducers;\n        this.reducerFactory = reducerFactory;\n    }\n    get currentReducers() {\n        return this.reducers;\n    }\n    addFeature(feature) {\n        this.addFeatures([feature]);\n    }\n    addFeatures(features) {\n        const reducers = features.reduce((reducerDict, { reducers, reducerFactory, metaReducers, initialState, key }) => {\n            const reducer = typeof reducers === 'function'\n                ? createFeatureReducerFactory(metaReducers)(reducers, initialState)\n                : createReducerFactory(reducerFactory, metaReducers)(reducers, initialState);\n            reducerDict[key] = reducer;\n            return reducerDict;\n        }, {});\n        this.addReducers(reducers);\n    }\n    removeFeature(feature) {\n        this.removeFeatures([feature]);\n    }\n    removeFeatures(features) {\n        this.removeReducers(features.map((p) => p.key));\n    }\n    addReducer(key, reducer) {\n        this.addReducers({ [key]: reducer });\n    }\n    addReducers(reducers) {\n        this.reducers = Object.assign(Object.assign({}, this.reducers), reducers);\n        this.updateReducers(Object.keys(reducers));\n    }\n    removeReducer(featureKey) {\n        this.removeReducers([featureKey]);\n    }\n    removeReducers(featureKeys) {\n        featureKeys.forEach((key) => {\n            this.reducers = omit(this.reducers, key) /*TODO(#823)*/;\n        });\n        this.updateReducers(featureKeys);\n    }\n    updateReducers(featureKeys) {\n        this.next(this.reducerFactory(this.reducers, this.initialState));\n        this.dispatcher.next({\n            type: UPDATE,\n            features: featureKeys,\n        });\n    }\n    ngOnDestroy() {\n        this.complete();\n    }\n}\n/** @nocollapse */ ReducerManager.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: ReducerManager, deps: [{ token: ReducerManagerDispatcher }, { token: INITIAL_STATE }, { token: INITIAL_REDUCERS }, { token: REDUCER_FACTORY }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ ReducerManager.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: ReducerManager });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: ReducerManager, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () {\n        return [{ type: ReducerManagerDispatcher }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [INITIAL_STATE]\n                    }] }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [INITIAL_REDUCERS]\n                    }] }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [REDUCER_FACTORY]\n                    }] }];\n    } });\nconst REDUCER_MANAGER_PROVIDERS = [\n    ReducerManager,\n    { provide: ReducerObservable, useExisting: ReducerManager },\n    { provide: ReducerManagerDispatcher, useExisting: ActionsSubject },\n];\n\nclass ScannedActionsSubject extends Subject {\n    ngOnDestroy() {\n        this.complete();\n    }\n}\n/** @nocollapse */ ScannedActionsSubject.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: ScannedActionsSubject, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ ScannedActionsSubject.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: ScannedActionsSubject });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: ScannedActionsSubject, decorators: [{\n            type: Injectable\n        }] });\nconst SCANNED_ACTIONS_SUBJECT_PROVIDERS = [\n    ScannedActionsSubject,\n];\n\nclass StateObservable extends Observable {\n}\nclass State extends BehaviorSubject {\n    constructor(actions$, reducer$, scannedActions, initialState) {\n        super(initialState);\n        const actionsOnQueue$ = actions$.pipe(observeOn(queueScheduler));\n        const withLatestReducer$ = actionsOnQueue$.pipe(withLatestFrom(reducer$));\n        const seed = { state: initialState };\n        const stateAndAction$ = withLatestReducer$.pipe(scan(reduceState, seed));\n        this.stateSubscription = stateAndAction$.subscribe(({ state, action }) => {\n            this.next(state);\n            scannedActions.next(action);\n        });\n    }\n    ngOnDestroy() {\n        this.stateSubscription.unsubscribe();\n        this.complete();\n    }\n}\nState.INIT = INIT;\n/** @nocollapse */ State.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: State, deps: [{ token: ActionsSubject }, { token: ReducerObservable }, { token: ScannedActionsSubject }, { token: INITIAL_STATE }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ State.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: State });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: State, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () {\n        return [{ type: ActionsSubject }, { type: ReducerObservable }, { type: ScannedActionsSubject }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [INITIAL_STATE]\n                    }] }];\n    } });\nfunction reduceState(stateActionPair = { state: undefined }, [action, reducer]) {\n    const { state } = stateActionPair;\n    return { state: reducer(state, action), action };\n}\nconst STATE_PROVIDERS = [\n    State,\n    { provide: StateObservable, useExisting: State },\n];\n\n// disabled because we have lowercase generics for `select`\nclass Store extends Observable {\n    constructor(state$, actionsObserver, reducerManager) {\n        super();\n        this.actionsObserver = actionsObserver;\n        this.reducerManager = reducerManager;\n        this.source = state$;\n    }\n    select(pathOrMapFn, ...paths) {\n        return select.call(null, pathOrMapFn, ...paths)(this);\n    }\n    lift(operator) {\n        const store = new Store(this, this.actionsObserver, this.reducerManager);\n        store.operator = operator;\n        return store;\n    }\n    dispatch(action) {\n        this.actionsObserver.next(action);\n    }\n    next(action) {\n        this.actionsObserver.next(action);\n    }\n    error(err) {\n        this.actionsObserver.error(err);\n    }\n    complete() {\n        this.actionsObserver.complete();\n    }\n    addReducer(key, reducer) {\n        this.reducerManager.addReducer(key, reducer);\n    }\n    removeReducer(key) {\n        this.reducerManager.removeReducer(key);\n    }\n}\n/** @nocollapse */ Store.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: Store, deps: [{ token: StateObservable }, { token: ActionsSubject }, { token: ReducerManager }], target: i0.ɵɵFactoryTarget.Injectable });\n/** @nocollapse */ Store.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: Store });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: Store, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: StateObservable }, { type: ActionsSubject }, { type: ReducerManager }]; } });\nconst STORE_PROVIDERS = [Store];\nfunction select(pathOrMapFn, propsOrPath, ...paths) {\n    return function selectOperator(source$) {\n        let mapped$;\n        if (typeof pathOrMapFn === 'string') {\n            const pathSlices = [propsOrPath, ...paths].filter(Boolean);\n            mapped$ = source$.pipe(pluck(pathOrMapFn, ...pathSlices));\n        }\n        else if (typeof pathOrMapFn === 'function') {\n            mapped$ = source$.pipe(map((source) => pathOrMapFn(source, propsOrPath)));\n        }\n        else {\n            throw new TypeError(`Unexpected type '${typeof pathOrMapFn}' in select operator,` +\n                ` expected 'string' or 'function'`);\n        }\n        return mapped$.pipe(distinctUntilChanged());\n    };\n}\n\nconst RUNTIME_CHECK_URL = 'https://ngrx.io/guide/store/configuration/runtime-checks';\nfunction isUndefined(target) {\n    return target === undefined;\n}\nfunction isNull(target) {\n    return target === null;\n}\nfunction isArray(target) {\n    return Array.isArray(target);\n}\nfunction isString(target) {\n    return typeof target === 'string';\n}\nfunction isBoolean(target) {\n    return typeof target === 'boolean';\n}\nfunction isNumber(target) {\n    return typeof target === 'number';\n}\nfunction isObjectLike(target) {\n    return typeof target === 'object' && target !== null;\n}\nfunction isObject(target) {\n    return isObjectLike(target) && !isArray(target);\n}\nfunction isPlainObject(target) {\n    if (!isObject(target)) {\n        return false;\n    }\n    const targetPrototype = Object.getPrototypeOf(target);\n    return targetPrototype === Object.prototype || targetPrototype === null;\n}\nfunction isFunction(target) {\n    return typeof target === 'function';\n}\nfunction isComponent(target) {\n    return isFunction(target) && target.hasOwnProperty('ɵcmp');\n}\nfunction hasOwnProperty(target, propertyName) {\n    return Object.prototype.hasOwnProperty.call(target, propertyName);\n}\n\nlet _ngrxMockEnvironment = false;\nfunction setNgrxMockEnvironment(value) {\n    _ngrxMockEnvironment = value;\n}\nfunction isNgrxMockEnvironment() {\n    return _ngrxMockEnvironment;\n}\n\nfunction isEqualCheck(a, b) {\n    return a === b;\n}\nfunction isArgumentsChanged(args, lastArguments, comparator) {\n    for (let i = 0; i < args.length; i++) {\n        if (!comparator(args[i], lastArguments[i])) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction resultMemoize(projectionFn, isResultEqual) {\n    return defaultMemoize(projectionFn, isEqualCheck, isResultEqual);\n}\nfunction defaultMemoize(projectionFn, isArgumentsEqual = isEqualCheck, isResultEqual = isEqualCheck) {\n    let lastArguments = null;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any, , , , ,\n    let lastResult = null;\n    let overrideResult;\n    function reset() {\n        lastArguments = null;\n        lastResult = null;\n    }\n    function setResult(result = undefined) {\n        overrideResult = { result };\n    }\n    function clearResult() {\n        overrideResult = undefined;\n    }\n    /* eslint-disable prefer-rest-params, prefer-spread */\n    // disabled because of the use of `arguments`\n    function memoized() {\n        if (overrideResult !== undefined) {\n            return overrideResult.result;\n        }\n        if (!lastArguments) {\n            lastResult = projectionFn.apply(null, arguments);\n            lastArguments = arguments;\n            return lastResult;\n        }\n        if (!isArgumentsChanged(arguments, lastArguments, isArgumentsEqual)) {\n            return lastResult;\n        }\n        const newResult = projectionFn.apply(null, arguments);\n        lastArguments = arguments;\n        if (isResultEqual(lastResult, newResult)) {\n            return lastResult;\n        }\n        lastResult = newResult;\n        return newResult;\n    }\n    return { memoized, reset, setResult, clearResult };\n}\nfunction createSelector(...input) {\n    return createSelectorFactory(defaultMemoize)(...input);\n}\nfunction defaultStateFn(state, selectors, props, memoizedProjector) {\n    if (props === undefined) {\n        const args = selectors.map((fn) => fn(state));\n        return memoizedProjector.memoized.apply(null, args);\n    }\n    const args = selectors.map((fn) => fn(state, props));\n    return memoizedProjector.memoized.apply(null, [...args, props]);\n}\n/**\n *\n * @param memoize The function used to memoize selectors\n * @param options Config Object that may include a `stateFn` function defining how to return the selector's value, given the entire `Store`'s state, parent `Selector`s, `Props`, and a `MemoizedProjection`\n *\n * @usageNotes\n *\n * **Creating a Selector Factory Where Array Order Does Not Matter**\n *\n * ```ts\n * function removeMatch(arr: string[], target: string): string[] {\n *   const matchIndex = arr.indexOf(target);\n *   return [...arr.slice(0, matchIndex), ...arr.slice(matchIndex + 1)];\n * }\n *\n * function orderDoesNotMatterComparer(a: any, b: any): boolean {\n *   if (!Array.isArray(a) || !Array.isArray(b)) {\n *     return a === b;\n *   }\n *   if (a.length !== b.length) {\n *     return false;\n *   }\n *   let tempB = [...b];\n *   function reduceToDetermineIfArraysContainSameContents(\n *     previousCallResult: boolean,\n *     arrayMember: any\n *   ): boolean {\n *     if (previousCallResult === false) {\n *       return false;\n *     }\n *     if (tempB.includes(arrayMember)) {\n *       tempB = removeMatch(tempB, arrayMember);\n *       return true;\n *     }\n *     return false;\n *   }\n *   return a.reduce(reduceToDetermineIfArraysContainSameContents, true);\n * }\n *\n * export const creactOrderDoesNotMatterSelector = createSelectorFactory(\n *   (projectionFun) => defaultMemoize(\n *     projectionFun,\n *     orderDoesNotMatterComparer,\n *     orderDoesNotMatterComparer\n *   )\n * );\n * ```\n *\n * **Creating an Alternative Memoization Strategy**\n *\n * ```ts\n * function serialize(x: any): string {\n *   return JSON.stringify(x);\n * }\n *\n * export const createFullHistorySelector = createSelectorFactory(\n *  (projectionFunction) => {\n *    const cache = {};\n *\n *    function memoized() {\n *      const serializedArguments = serialize(...arguments);\n *       if (cache[serializedArguments] != null) {\n *         cache[serializedArguments] = projectionFunction.apply(null, arguments);\n *       }\n *       return cache[serializedArguments];\n *     }\n *     return {\n *       memoized,\n *       reset: () => {},\n *       setResult: () => {},\n *       clearResult: () => {},\n *     };\n *   }\n * );\n * ```\n *\n *\n */\nfunction createSelectorFactory(memoize, options = {\n    stateFn: defaultStateFn,\n}) {\n    return function (...input) {\n        let args = input;\n        if (Array.isArray(args[0])) {\n            const [head, ...tail] = args;\n            args = [...head, ...tail];\n        }\n        const selectors = args.slice(0, args.length - 1);\n        const projector = args[args.length - 1];\n        const memoizedSelectors = selectors.filter((selector) => selector.release && typeof selector.release === 'function');\n        const memoizedProjector = memoize(function (...selectors) {\n            return projector.apply(null, selectors);\n        });\n        const memoizedState = defaultMemoize(function (state, props) {\n            return options.stateFn.apply(null, [\n                state,\n                selectors,\n                props,\n                memoizedProjector,\n            ]);\n        });\n        function release() {\n            memoizedState.reset();\n            memoizedProjector.reset();\n            memoizedSelectors.forEach((selector) => selector.release());\n        }\n        return Object.assign(memoizedState.memoized, {\n            release,\n            projector: memoizedProjector.memoized,\n            setResult: memoizedState.setResult,\n            clearResult: memoizedState.clearResult,\n        });\n    };\n}\nfunction createFeatureSelector(featureName) {\n    return createSelector((state) => {\n        const featureState = state[featureName];\n        if (!isNgrxMockEnvironment() && isDevMode() && !(featureName in state)) {\n            console.warn(`@ngrx/store: The feature name \"${featureName}\" does ` +\n                'not exist in the state, therefore createFeatureSelector ' +\n                'cannot access it.  Be sure it is imported in a loaded module ' +\n                `using StoreModule.forRoot('${featureName}', ...) or ` +\n                `StoreModule.forFeature('${featureName}', ...).  If the default ` +\n                'state is intended to be undefined, as is the case with router ' +\n                'state, this development-only warning message can be ignored.');\n        }\n        return featureState;\n    }, (featureState) => featureState);\n}\n\n/**\n * @description\n * A function that accepts a feature name and a feature reducer, and creates\n * a feature selector and a selector for each feature state property.\n *\n * @param featureConfig An object that contains a feature name and a feature reducer.\n * @returns An object that contains a feature name, a feature reducer,\n * a feature selector, and a selector for each feature state property.\n *\n * @usageNotes\n *\n * **With Application State**\n *\n * ```ts\n * interface AppState {\n *   products: ProductsState;\n * }\n *\n * interface ProductsState {\n *   products: Product[];\n *   selectedId: string | null;\n * }\n *\n * const initialState: ProductsState = {\n *   products: [],\n *   selectedId: null,\n * };\n *\n * // AppState is passed as a generic argument\n * const productsFeature = createFeature<AppState>({\n *   name: 'products',\n *   reducer: createReducer(\n *     initialState,\n *     on(ProductsApiActions.loadSuccess(state, { products }) => ({\n *       ...state,\n *       products,\n *     }),\n *   ),\n * });\n *\n * const {\n *   selectProductsState, // type: MemoizedSelector<AppState, ProductsState>\n *   selectProducts, // type: MemoizedSelector<AppState, Product[]>\n *   selectSelectedId, // type: MemoizedSelector<AppState, string | null>\n * } = productsFeature;\n * ```\n *\n * **Without Application State**\n *\n * ```ts\n * const productsFeature = createFeature({\n *   name: 'products',\n *   reducer: createReducer(initialState),\n * });\n *\n * const {\n *   selectProductsState, // type: MemoizedSelector<Record<string, any>, ProductsState>\n *   selectProducts, // type: MemoizedSelector<Record<string, any>, Product[]>\n *   selectSelectedId, // type: MemoizedSelector<Record<string, any, string | null>\n * } = productsFeature;\n * ```\n */\nfunction createFeature(featureConfig) {\n    const { name, reducer } = featureConfig;\n    const featureSelector = createFeatureSelector(name);\n    const nestedSelectors = createNestedSelectors(featureSelector, reducer);\n    return Object.assign({ name,\n        reducer, [`select${capitalize(name)}State`]: featureSelector }, nestedSelectors);\n}\nfunction createNestedSelectors(featureSelector, reducer) {\n    const initialState = getInitialState(reducer);\n    const nestedKeys = (isPlainObject(initialState) ? Object.keys(initialState) : []);\n    return nestedKeys.reduce((nestedSelectors, nestedKey) => (Object.assign(Object.assign({}, nestedSelectors), { [`select${capitalize(nestedKey)}`]: createSelector(featureSelector, (parentState) => parentState === null || parentState === void 0 ? void 0 : parentState[nestedKey]) })), {});\n}\nfunction getInitialState(reducer) {\n    return reducer(undefined, { type: '@ngrx/feature/init' });\n}\n\nfunction _createStoreReducers(injector, reducers) {\n    return reducers instanceof InjectionToken ? injector.get(reducers) : reducers;\n}\nfunction _createFeatureStore(injector, configs, featureStores) {\n    return featureStores.map((feat, index) => {\n        if (configs[index] instanceof InjectionToken) {\n            const conf = injector.get(configs[index]);\n            return {\n                key: feat.key,\n                reducerFactory: conf.reducerFactory\n                    ? conf.reducerFactory\n                    : combineReducers,\n                metaReducers: conf.metaReducers ? conf.metaReducers : [],\n                initialState: conf.initialState,\n            };\n        }\n        return feat;\n    });\n}\nfunction _createFeatureReducers(injector, reducerCollection) {\n    const reducers = reducerCollection.map((reducer) => {\n        return reducer instanceof InjectionToken ? injector.get(reducer) : reducer;\n    });\n    return reducers;\n}\nfunction _initialStateFactory(initialState) {\n    if (typeof initialState === 'function') {\n        return initialState();\n    }\n    return initialState;\n}\nfunction _concatMetaReducers(metaReducers, userProvidedMetaReducers) {\n    return metaReducers.concat(userProvidedMetaReducers);\n}\nfunction _provideForRootGuard(store) {\n    if (store) {\n        throw new TypeError(`The root Store has been provided more than once. Feature modules should provide feature states instead.`);\n    }\n    return 'guarded';\n}\n\nfunction immutabilityCheckMetaReducer(reducer, checks) {\n    return function (state, action) {\n        const act = checks.action(action) ? freeze(action) : action;\n        const nextState = reducer(state, act);\n        return checks.state() ? freeze(nextState) : nextState;\n    };\n}\nfunction freeze(target) {\n    Object.freeze(target);\n    const targetIsFunction = isFunction(target);\n    Object.getOwnPropertyNames(target).forEach((prop) => {\n        // Ignore Ivy properties, ref: https://github.com/ngrx/platform/issues/2109#issuecomment-582689060\n        if (prop.startsWith('ɵ')) {\n            return;\n        }\n        if (hasOwnProperty(target, prop) &&\n            (targetIsFunction\n                ? prop !== 'caller' && prop !== 'callee' && prop !== 'arguments'\n                : true)) {\n            const propValue = target[prop];\n            if ((isObjectLike(propValue) || isFunction(propValue)) &&\n                !Object.isFrozen(propValue)) {\n                freeze(propValue);\n            }\n        }\n    });\n    return target;\n}\n\nfunction serializationCheckMetaReducer(reducer, checks) {\n    return function (state, action) {\n        if (checks.action(action)) {\n            const unserializableAction = getUnserializable(action);\n            throwIfUnserializable(unserializableAction, 'action');\n        }\n        const nextState = reducer(state, action);\n        if (checks.state()) {\n            const unserializableState = getUnserializable(nextState);\n            throwIfUnserializable(unserializableState, 'state');\n        }\n        return nextState;\n    };\n}\nfunction getUnserializable(target, path = []) {\n    // Guard against undefined and null, e.g. a reducer that returns undefined\n    if ((isUndefined(target) || isNull(target)) && path.length === 0) {\n        return {\n            path: ['root'],\n            value: target,\n        };\n    }\n    const keys = Object.keys(target);\n    return keys.reduce((result, key) => {\n        if (result) {\n            return result;\n        }\n        const value = target[key];\n        // Ignore Ivy components\n        if (isComponent(value)) {\n            return result;\n        }\n        if (isUndefined(value) ||\n            isNull(value) ||\n            isNumber(value) ||\n            isBoolean(value) ||\n            isString(value) ||\n            isArray(value)) {\n            return false;\n        }\n        if (isPlainObject(value)) {\n            return getUnserializable(value, [...path, key]);\n        }\n        return {\n            path: [...path, key],\n            value,\n        };\n    }, false);\n}\nfunction throwIfUnserializable(unserializable, context) {\n    if (unserializable === false) {\n        return;\n    }\n    const unserializablePath = unserializable.path.join('.');\n    const error = new Error(`Detected unserializable ${context} at \"${unserializablePath}\". ${RUNTIME_CHECK_URL}#strict${context}serializability`);\n    error.value = unserializable.value;\n    error.unserializablePath = unserializablePath;\n    throw error;\n}\n\nfunction inNgZoneAssertMetaReducer(reducer, checks) {\n    return function (state, action) {\n        if (checks.action(action) && !i0.NgZone.isInAngularZone()) {\n            throw new Error(`Action '${action.type}' running outside NgZone. ${RUNTIME_CHECK_URL}#strictactionwithinngzone`);\n        }\n        return reducer(state, action);\n    };\n}\n\nfunction createActiveRuntimeChecks(runtimeChecks) {\n    if (isDevMode()) {\n        return Object.assign({ strictStateSerializability: false, strictActionSerializability: false, strictStateImmutability: true, strictActionImmutability: true, strictActionWithinNgZone: false, strictActionTypeUniqueness: false }, runtimeChecks);\n    }\n    return {\n        strictStateSerializability: false,\n        strictActionSerializability: false,\n        strictStateImmutability: false,\n        strictActionImmutability: false,\n        strictActionWithinNgZone: false,\n        strictActionTypeUniqueness: false,\n    };\n}\nfunction createSerializationCheckMetaReducer({ strictActionSerializability, strictStateSerializability, }) {\n    return (reducer) => strictActionSerializability || strictStateSerializability\n        ? serializationCheckMetaReducer(reducer, {\n            action: (action) => strictActionSerializability && !ignoreNgrxAction(action),\n            state: () => strictStateSerializability,\n        })\n        : reducer;\n}\nfunction createImmutabilityCheckMetaReducer({ strictActionImmutability, strictStateImmutability, }) {\n    return (reducer) => strictActionImmutability || strictStateImmutability\n        ? immutabilityCheckMetaReducer(reducer, {\n            action: (action) => strictActionImmutability && !ignoreNgrxAction(action),\n            state: () => strictStateImmutability,\n        })\n        : reducer;\n}\nfunction ignoreNgrxAction(action) {\n    return action.type.startsWith('@ngrx');\n}\nfunction createInNgZoneCheckMetaReducer({ strictActionWithinNgZone, }) {\n    return (reducer) => strictActionWithinNgZone\n        ? inNgZoneAssertMetaReducer(reducer, {\n            action: (action) => strictActionWithinNgZone && !ignoreNgrxAction(action),\n        })\n        : reducer;\n}\nfunction provideRuntimeChecks(runtimeChecks) {\n    return [\n        {\n            provide: _USER_RUNTIME_CHECKS,\n            useValue: runtimeChecks,\n        },\n        {\n            provide: USER_RUNTIME_CHECKS,\n            useFactory: _runtimeChecksFactory,\n            deps: [_USER_RUNTIME_CHECKS],\n        },\n        {\n            provide: ACTIVE_RUNTIME_CHECKS,\n            deps: [USER_RUNTIME_CHECKS],\n            useFactory: createActiveRuntimeChecks,\n        },\n        {\n            provide: META_REDUCERS,\n            multi: true,\n            deps: [ACTIVE_RUNTIME_CHECKS],\n            useFactory: createImmutabilityCheckMetaReducer,\n        },\n        {\n            provide: META_REDUCERS,\n            multi: true,\n            deps: [ACTIVE_RUNTIME_CHECKS],\n            useFactory: createSerializationCheckMetaReducer,\n        },\n        {\n            provide: META_REDUCERS,\n            multi: true,\n            deps: [ACTIVE_RUNTIME_CHECKS],\n            useFactory: createInNgZoneCheckMetaReducer,\n        },\n    ];\n}\nfunction checkForActionTypeUniqueness() {\n    return [\n        {\n            provide: _ACTION_TYPE_UNIQUENESS_CHECK,\n            multi: true,\n            deps: [ACTIVE_RUNTIME_CHECKS],\n            useFactory: _actionTypeUniquenessCheck,\n        },\n    ];\n}\nfunction _runtimeChecksFactory(runtimeChecks) {\n    return runtimeChecks;\n}\nfunction _actionTypeUniquenessCheck(config) {\n    if (!config.strictActionTypeUniqueness) {\n        return;\n    }\n    const duplicates = Object.entries(REGISTERED_ACTION_TYPES)\n        .filter(([, registrations]) => registrations > 1)\n        .map(([type]) => type);\n    if (duplicates.length) {\n        throw new Error(`Action types are registered more than once, ${duplicates\n            .map((type) => `\"${type}\"`)\n            .join(', ')}. ${RUNTIME_CHECK_URL}#strictactiontypeuniqueness`);\n    }\n}\n\n/**\n * Provides additional slices of state in the Store.\n * These providers cannot be used at the component level.\n *\n * @usageNotes\n *\n * ### Providing Store Features\n *\n * ```ts\n * const booksRoutes: Route[] = [\n *   {\n *     path: '',\n *     providers: [provideState('books', booksReducer)],\n *     children: [\n *       { path: '', component: BookListComponent },\n *       { path: ':id', component: BookDetailsComponent },\n *     ],\n *   },\n * ];\n * ```\n */\nfunction provideState(featureNameOrSlice, reducers, config = {}) {\n    return {\n        ɵproviders: [\n            ..._provideState(featureNameOrSlice, reducers, config),\n            ENVIRONMENT_STATE_PROVIDER,\n        ],\n    };\n}\nfunction _provideStore(reducers, config) {\n    return [\n        {\n            provide: _ROOT_STORE_GUARD,\n            useFactory: _provideForRootGuard,\n            deps: [[Store, new Optional(), new SkipSelf()]],\n        },\n        { provide: _INITIAL_STATE, useValue: config.initialState },\n        {\n            provide: INITIAL_STATE,\n            useFactory: _initialStateFactory,\n            deps: [_INITIAL_STATE],\n        },\n        { provide: _INITIAL_REDUCERS, useValue: reducers },\n        {\n            provide: _STORE_REDUCERS,\n            useExisting: reducers instanceof InjectionToken ? reducers : _INITIAL_REDUCERS,\n        },\n        {\n            provide: INITIAL_REDUCERS,\n            deps: [Injector, _INITIAL_REDUCERS, [new Inject(_STORE_REDUCERS)]],\n            useFactory: _createStoreReducers,\n        },\n        {\n            provide: USER_PROVIDED_META_REDUCERS,\n            useValue: config.metaReducers ? config.metaReducers : [],\n        },\n        {\n            provide: _RESOLVED_META_REDUCERS,\n            deps: [META_REDUCERS, USER_PROVIDED_META_REDUCERS],\n            useFactory: _concatMetaReducers,\n        },\n        {\n            provide: _REDUCER_FACTORY,\n            useValue: config.reducerFactory ? config.reducerFactory : combineReducers,\n        },\n        {\n            provide: REDUCER_FACTORY,\n            deps: [_REDUCER_FACTORY, _RESOLVED_META_REDUCERS],\n            useFactory: createReducerFactory,\n        },\n        ACTIONS_SUBJECT_PROVIDERS,\n        REDUCER_MANAGER_PROVIDERS,\n        SCANNED_ACTIONS_SUBJECT_PROVIDERS,\n        STATE_PROVIDERS,\n        STORE_PROVIDERS,\n        provideRuntimeChecks(config.runtimeChecks),\n        checkForActionTypeUniqueness(),\n    ];\n}\nfunction rootStoreProviderFactory() {\n    inject(ActionsSubject);\n    inject(ReducerObservable);\n    inject(ScannedActionsSubject);\n    inject(Store);\n    inject(_ROOT_STORE_GUARD, InjectFlags.Optional);\n    inject(_ACTION_TYPE_UNIQUENESS_CHECK, InjectFlags.Optional);\n}\n/**\n * Environment Initializer used in the root\n * providers to initialize the Store\n */\nconst ENVIRONMENT_STORE_PROVIDER = [\n    { provide: ROOT_STORE_PROVIDER, useFactory: rootStoreProviderFactory },\n    {\n        provide: ENVIRONMENT_INITIALIZER,\n        multi: true,\n        useFactory() {\n            return () => inject(ROOT_STORE_PROVIDER);\n        },\n    },\n];\n/**\n * Provides the global Store providers and initializes\n * the Store.\n * These providers cannot be used at the component level.\n *\n * @usageNotes\n *\n * ### Providing the Global Store\n *\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideStore()],\n * });\n * ```\n */\nfunction provideStore(reducers = {}, config = {}) {\n    return {\n        ɵproviders: [\n            ..._provideStore(reducers, config),\n            ENVIRONMENT_STORE_PROVIDER,\n        ],\n    };\n}\nfunction featureStateProviderFactory() {\n    inject(ROOT_STORE_PROVIDER);\n    const features = inject(_STORE_FEATURES);\n    const featureReducers = inject(FEATURE_REDUCERS);\n    const reducerManager = inject(ReducerManager);\n    inject(_ACTION_TYPE_UNIQUENESS_CHECK, InjectFlags.Optional);\n    const feats = features.map((feature, index) => {\n        const featureReducerCollection = featureReducers.shift();\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const reducers = featureReducerCollection /*TODO(#823)*/[index];\n        return Object.assign(Object.assign({}, feature), { reducers, initialState: _initialStateFactory(feature.initialState) });\n    });\n    reducerManager.addFeatures(feats);\n}\n/**\n * Environment Initializer used in the feature\n * providers to register state features\n */\nconst ENVIRONMENT_STATE_PROVIDER = [\n    {\n        provide: FEATURE_STATE_PROVIDER,\n        useFactory: featureStateProviderFactory,\n    },\n    {\n        provide: ENVIRONMENT_INITIALIZER,\n        multi: true,\n        deps: [],\n        useFactory() {\n            return () => inject(FEATURE_STATE_PROVIDER);\n        },\n    },\n];\nfunction _provideState(featureNameOrSlice, reducers, config = {}) {\n    return [\n        {\n            provide: _FEATURE_CONFIGS,\n            multi: true,\n            useValue: featureNameOrSlice instanceof Object ? {} : config,\n        },\n        {\n            provide: STORE_FEATURES,\n            multi: true,\n            useValue: {\n                key: featureNameOrSlice instanceof Object\n                    ? featureNameOrSlice.name\n                    : featureNameOrSlice,\n                reducerFactory: !(config instanceof InjectionToken) && config.reducerFactory\n                    ? config.reducerFactory\n                    : combineReducers,\n                metaReducers: !(config instanceof InjectionToken) && config.metaReducers\n                    ? config.metaReducers\n                    : [],\n                initialState: !(config instanceof InjectionToken) && config.initialState\n                    ? config.initialState\n                    : undefined,\n            },\n        },\n        {\n            provide: _STORE_FEATURES,\n            deps: [Injector, _FEATURE_CONFIGS, STORE_FEATURES],\n            useFactory: _createFeatureStore,\n        },\n        {\n            provide: _FEATURE_REDUCERS,\n            multi: true,\n            useValue: featureNameOrSlice instanceof Object\n                ? featureNameOrSlice.reducer\n                : reducers,\n        },\n        {\n            provide: _FEATURE_REDUCERS_TOKEN,\n            multi: true,\n            useExisting: reducers instanceof InjectionToken ? reducers : _FEATURE_REDUCERS,\n        },\n        {\n            provide: FEATURE_REDUCERS,\n            multi: true,\n            deps: [\n                Injector,\n                _FEATURE_REDUCERS,\n                [new Inject(_FEATURE_REDUCERS_TOKEN)],\n            ],\n            useFactory: _createFeatureReducers,\n        },\n        checkForActionTypeUniqueness(),\n    ];\n}\n\nclass StoreRootModule {\n    constructor(actions$, reducer$, scannedActions$, store, guard, actionCheck) { }\n}\n/** @nocollapse */ StoreRootModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreRootModule, deps: [{ token: ActionsSubject }, { token: ReducerObservable }, { token: ScannedActionsSubject }, { token: Store }, { token: _ROOT_STORE_GUARD, optional: true }, { token: _ACTION_TYPE_UNIQUENESS_CHECK, optional: true }], target: i0.ɵɵFactoryTarget.NgModule });\n/** @nocollapse */ StoreRootModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreRootModule });\n/** @nocollapse */ StoreRootModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreRootModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreRootModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }], ctorParameters: function () {\n        return [{ type: ActionsSubject }, { type: ReducerObservable }, { type: ScannedActionsSubject }, { type: Store }, { type: undefined, decorators: [{\n                        type: Optional\n                    }, {\n                        type: Inject,\n                        args: [_ROOT_STORE_GUARD]\n                    }] }, { type: undefined, decorators: [{\n                        type: Optional\n                    }, {\n                        type: Inject,\n                        args: [_ACTION_TYPE_UNIQUENESS_CHECK]\n                    }] }];\n    } });\nclass StoreFeatureModule {\n    constructor(features, featureReducers, reducerManager, root, actionCheck) {\n        this.features = features;\n        this.featureReducers = featureReducers;\n        this.reducerManager = reducerManager;\n        const feats = features.map((feature, index) => {\n            const featureReducerCollection = featureReducers.shift();\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            const reducers = featureReducerCollection /*TODO(#823)*/[index];\n            return Object.assign(Object.assign({}, feature), { reducers, initialState: _initialStateFactory(feature.initialState) });\n        });\n        reducerManager.addFeatures(feats);\n    }\n    // eslint-disable-next-line @angular-eslint/contextual-lifecycle\n    ngOnDestroy() {\n        this.reducerManager.removeFeatures(this.features);\n    }\n}\n/** @nocollapse */ StoreFeatureModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreFeatureModule, deps: [{ token: _STORE_FEATURES }, { token: FEATURE_REDUCERS }, { token: ReducerManager }, { token: StoreRootModule }, { token: _ACTION_TYPE_UNIQUENESS_CHECK, optional: true }], target: i0.ɵɵFactoryTarget.NgModule });\n/** @nocollapse */ StoreFeatureModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreFeatureModule });\n/** @nocollapse */ StoreFeatureModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreFeatureModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreFeatureModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }], ctorParameters: function () {\n        return [{ type: undefined, decorators: [{\n                        type: Inject,\n                        args: [_STORE_FEATURES]\n                    }] }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [FEATURE_REDUCERS]\n                    }] }, { type: ReducerManager }, { type: StoreRootModule }, { type: undefined, decorators: [{\n                        type: Optional\n                    }, {\n                        type: Inject,\n                        args: [_ACTION_TYPE_UNIQUENESS_CHECK]\n                    }] }];\n    } });\nclass StoreModule {\n    static forRoot(reducers, config = {}) {\n        return {\n            ngModule: StoreRootModule,\n            providers: [..._provideStore(reducers, config)],\n        };\n    }\n    static forFeature(featureNameOrSlice, reducers, config = {}) {\n        return {\n            ngModule: StoreFeatureModule,\n            providers: [..._provideState(featureNameOrSlice, reducers, config)],\n        };\n    }\n}\n/** @nocollapse */ StoreModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n/** @nocollapse */ StoreModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreModule });\n/** @nocollapse */ StoreModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.2.2\", ngImport: i0, type: StoreModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\n/**\n * @description\n * Associates actions with a given state change function.\n * A state change function must be provided as the last parameter.\n *\n * @param args `ActionCreator`'s followed by a state change function.\n *\n * @returns an association of action types with a state change function.\n *\n * @usageNotes\n * ```ts\n * on(AuthApiActions.loginSuccess, (state, { user }) => ({ ...state, user }))\n * ```\n */\nfunction on(...args) {\n    const reducer = args.pop();\n    const types = args.map((creator) => creator.type);\n    return { reducer, types };\n}\n/**\n * @description\n * Creates a reducer function to handle state transitions.\n *\n * Reducer creators reduce the explicitness of reducer functions with switch statements.\n *\n * @param initialState Provides a state value if the current state is `undefined`, as it is initially.\n * @param ons Associations between actions and state changes.\n * @returns A reducer function.\n *\n * @usageNotes\n *\n * - Must be used with `ActionCreator`'s (returned by `createAction`). Cannot be used with class-based action creators.\n * - The returned `ActionReducer` should additionally be wrapped with another function, if you are using View Engine AOT.\n * In case you are using Ivy (or only JIT View Engine) the extra wrapper function is not required.\n *\n * **Declaring a reducer creator**\n *\n * ```ts\n * export const reducer = createReducer(\n *   initialState,\n *   on(\n *     featureActions.actionOne,\n *     featureActions.actionTwo,\n *     (state, { updatedValue }) => ({ ...state, prop: updatedValue })\n *   ),\n *   on(featureActions.actionThree, () => initialState);\n * );\n * ```\n *\n * **Declaring a reducer creator using a wrapper function (Only needed if using View Engine AOT)**\n *\n * ```ts\n * const featureReducer = createReducer(\n *   initialState,\n *   on(\n *     featureActions.actionOne,\n *     featureActions.actionTwo,\n *     (state, { updatedValue }) => ({ ...state, prop: updatedValue })\n *   ),\n *   on(featureActions.actionThree, () => initialState);\n * );\n *\n * export function reducer(state: State | undefined, action: Action) {\n *   return featureReducer(state, action);\n * }\n * ```\n */\nfunction createReducer(initialState, ...ons) {\n    const map = new Map();\n    for (const on of ons) {\n        for (const type of on.types) {\n            const existingReducer = map.get(type);\n            if (existingReducer) {\n                const newReducer = (state, action) => on.reducer(existingReducer(state, action), action);\n                map.set(type, newReducer);\n            }\n            else {\n                map.set(type, on.reducer);\n            }\n        }\n    }\n    return function (state = initialState, action) {\n        const reducer = map.get(action.type);\n        return reducer ? reducer(state, action) : state;\n    };\n}\n\n/**\n * DO NOT EDIT\n *\n * This file is automatically generated at build\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ACTIVE_RUNTIME_CHECKS, ActionsSubject, FEATURE_REDUCERS, FEATURE_STATE_PROVIDER, INIT, INITIAL_REDUCERS, INITIAL_STATE, META_REDUCERS, REDUCER_FACTORY, ROOT_STORE_PROVIDER, ReducerManager, ReducerManagerDispatcher, ReducerObservable, STORE_FEATURES, ScannedActionsSubject, State, StateObservable, Store, StoreFeatureModule, StoreModule, StoreRootModule, UPDATE, USER_PROVIDED_META_REDUCERS, USER_RUNTIME_CHECKS, combineReducers, compose, createAction, createActionGroup, createFeature, createFeatureSelector, createReducer, createReducerFactory, createSelector, createSelectorFactory, defaultMemoize, defaultStateFn, emptyProps, isNgrxMockEnvironment, on, props, provideState, provideStore, reduceState, resultMemoize, select, setNgrxMockEnvironment, union };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,cAArB,EAAqCC,MAArC,EAA6CC,SAA7C,EAAwDC,QAAxD,EAAkEC,QAAlE,EAA4EC,QAA5E,EAAsFC,MAAtF,EAA8FC,WAA9F,EAA2GC,uBAA3G,EAAoIC,QAApI,QAAoJ,eAApJ;AACA,SAASC,eAAT,EAA0BC,UAA1B,EAAsCC,OAAtC,EAA+CC,cAA/C,QAAqE,MAArE;AACA,SAASC,SAAT,EAAoBC,cAApB,EAAoCC,IAApC,EAA0CC,KAA1C,EAAiDC,GAAjD,EAAsDC,oBAAtD,QAAkF,gBAAlF;AAEA,MAAMC,uBAAuB,GAAG,EAAhC;;AACA,SAASC,0BAAT,GAAsC;EAClC,KAAK,MAAMC,GAAX,IAAkBC,MAAM,CAACC,IAAP,CAAYJ,uBAAZ,CAAlB,EAAwD;IACpD,OAAOA,uBAAuB,CAACE,GAAD,CAA9B;EACH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,YAAT,CAAsBC,IAAtB,EAA4BC,MAA5B,EAAoC;EAChCP,uBAAuB,CAACM,IAAD,CAAvB,GAAgC,CAACN,uBAAuB,CAACM,IAAD,CAAvB,IAAiC,CAAlC,IAAuC,CAAvE;;EACA,IAAI,OAAOC,MAAP,KAAkB,UAAtB,EAAkC;IAC9B,OAAOC,UAAU,CAACF,IAAD,EAAO,CAAC,GAAGG,IAAJ,KAAcN,MAAM,CAACO,MAAP,CAAcP,MAAM,CAACO,MAAP,CAAc,EAAd,EAAkBH,MAAM,CAAC,GAAGE,IAAJ,CAAxB,CAAd,EAAkD;MAAEH;IAAF,CAAlD,CAArB,CAAjB;EACH;;EACD,MAAMK,EAAE,GAAGJ,MAAM,GAAGA,MAAM,CAACK,GAAV,GAAgB,OAAjC;;EACA,QAAQD,EAAR;IACI,KAAK,OAAL;MACI,OAAOH,UAAU,CAACF,IAAD,EAAO,OAAO;QAAEA;MAAF,CAAP,CAAP,CAAjB;;IACJ,KAAK,OAAL;MACI,OAAOE,UAAU,CAACF,IAAD,EAAQO,KAAD,IAAYV,MAAM,CAACO,MAAP,CAAcP,MAAM,CAACO,MAAP,CAAc,EAAd,EAAkBG,KAAlB,CAAd,EAAwC;QAAEP;MAAF,CAAxC,CAAnB,CAAjB;;IACJ;MACI,MAAM,IAAIQ,KAAJ,CAAU,oBAAV,CAAN;EANR;AAQH;;AACD,SAASD,KAAT,GAAiB;EACb;EACA,OAAO;IAAED,GAAG,EAAE,OAAP;IAAgBG,EAAE,EAAEC;EAApB,CAAP;AACH;;AACD,SAASC,KAAT,CAAeC,QAAf,EAAyB;EACrB;EACA,OAAOF,SAAP;AACH;;AACD,SAASR,UAAT,CAAoBF,IAApB,EAA0Ba,OAA1B,EAAmC;EAC/B,OAAOhB,MAAM,CAACiB,cAAP,CAAsBD,OAAtB,EAA+B,MAA/B,EAAuC;IAC1CE,KAAK,EAAEf,IADmC;IAE1CgB,QAAQ,EAAE;EAFgC,CAAvC,CAAP;AAIH;;AAED,SAASC,UAAT,CAAoBC,IAApB,EAA0B;EACtB,OAAQA,IAAI,CAACC,MAAL,CAAY,CAAZ,EAAeC,WAAf,KAA+BF,IAAI,CAACG,SAAL,CAAe,CAAf,CAAvC;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,iBAAT,CAA2BrB,MAA3B,EAAmC;EAC/B,MAAM;IAAEsB,MAAF;IAAUC;EAAV,IAAqBvB,MAA3B;EACA,OAAOJ,MAAM,CAACC,IAAP,CAAY0B,MAAZ,EAAoBC,MAApB,CAA2B,CAACC,WAAD,EAAcC,SAAd,KAA6B9B,MAAM,CAACO,MAAP,CAAcP,MAAM,CAACO,MAAP,CAAc,EAAd,EAAkBsB,WAAlB,CAAd,EAA8C;IAAE,CAACE,YAAY,CAACD,SAAD,CAAb,GAA2B5B,YAAY,CAAC8B,YAAY,CAACN,MAAD,EAASI,SAAT,CAAb,EAAkCH,MAAM,CAACG,SAAD,CAAxC;EAAzC,CAA9C,CAAxD,EAAyM,EAAzM,CAAP;AACH;;AACD,SAASG,UAAT,GAAsB;EAClB,OAAOvB,KAAK,EAAZ;AACH;;AACD,SAASqB,YAAT,CAAsBD,SAAtB,EAAiC;EAC7B,OAAOA,SAAS,CACXI,IADE,GAEFC,WAFE,GAGFC,KAHE,CAGI,GAHJ,EAIFzC,GAJE,CAIE,CAAC0C,IAAD,EAAOC,CAAP,KAAcA,CAAC,KAAK,CAAN,GAAUD,IAAV,GAAiBjB,UAAU,CAACiB,IAAD,CAJ3C,EAKFE,IALE,CAKG,EALH,CAAP;AAMH;;AACD,SAASP,YAAT,CAAsBN,MAAtB,EAA8BI,SAA9B,EAAyC;EACrC,OAAQ,IAAGJ,MAAO,KAAII,SAAU,EAAhC;AACH;;AAED,MAAMU,IAAI,GAAG,kBAAb;;AACA,MAAMC,cAAN,SAA6BtD,eAA7B,CAA6C;EACzCuD,WAAW,GAAG;IACV,MAAM;MAAEvC,IAAI,EAAEqC;IAAR,CAAN;EACH;;EACDG,IAAI,CAACC,MAAD,EAAS;IACT,IAAI,OAAOA,MAAP,KAAkB,UAAtB,EAAkC;MAC9B,MAAM,IAAIC,SAAJ,CAAe;AACjC;AACA;AACA,uFAHkB,CAAN;IAIH,CALD,MAMK,IAAI,OAAOD,MAAP,KAAkB,WAAtB,EAAmC;MACpC,MAAM,IAAIC,SAAJ,CAAe,yBAAf,CAAN;IACH,CAFI,MAGA,IAAI,OAAOD,MAAM,CAACzC,IAAd,KAAuB,WAA3B,EAAwC;MACzC,MAAM,IAAI0C,SAAJ,CAAe,mCAAf,CAAN;IACH;;IACD,MAAMF,IAAN,CAAWC,MAAX;EACH;;EACDE,QAAQ,GAAG;IACP;EACH;;EACDC,WAAW,GAAG;IACV,MAAMD,QAAN;EACH;;AAxBwC;AA0B7C;;;AAAmBL,cAAc,CAACO,IAAf;EAAA,iBAA2GP,cAA3G;AAAA;AACnB;;;AAAmBA,cAAc,CAACQ,KAAf,kBADiG1E,EACjG;EAAA,OAA+GkE,cAA/G;EAAA,SAA+GA,cAA/G;AAAA;;AACnB;EAAA,mDAFoHlE,EAEpH,mBAA2FkE,cAA3F,EAAuH,CAAC;IAC5GtC,IAAI,EAAE3B;EADsG,CAAD,CAAvH,EAE4B,YAAY;IAAE,OAAO,EAAP;EAAY,CAFtD;AAAA;;AAGA,MAAM0E,yBAAyB,GAAG,CAACT,cAAD,CAAlC;;AAEA,MAAMU,iBAAiB,GAAG,IAAI1E,cAAJ,CAAmB,iCAAnB,CAA1B;;AACA,MAAM2E,cAAc,GAAG,IAAI3E,cAAJ,CAAmB,oCAAnB,CAAvB;;AACA,MAAM4E,aAAa,GAAG,IAAI5E,cAAJ,CAAmB,2BAAnB,CAAtB;AACA,MAAM6E,eAAe,GAAG,IAAI7E,cAAJ,CAAmB,6BAAnB,CAAxB;;AACA,MAAM8E,gBAAgB,GAAG,IAAI9E,cAAJ,CAAmB,+CAAnB,CAAzB;;AACA,MAAM+E,gBAAgB,GAAG,IAAI/E,cAAJ,CAAmB,8BAAnB,CAAzB;;AACA,MAAMgF,iBAAiB,GAAG,IAAIhF,cAAJ,CAAmB,uCAAnB,CAA1B;;AACA,MAAMiF,cAAc,GAAG,IAAIjF,cAAJ,CAAmB,4BAAnB,CAAvB;;AACA,MAAMkF,eAAe,GAAG,IAAIlF,cAAJ,CAAmB,qCAAnB,CAAxB;;AACA,MAAMmF,iBAAiB,GAAG,IAAInF,cAAJ,CAAmB,uCAAnB,CAA1B;;AACA,MAAMoF,gBAAgB,GAAG,IAAIpF,cAAJ,CAAmB,sCAAnB,CAAzB;;AACA,MAAMqF,eAAe,GAAG,IAAIrF,cAAJ,CAAmB,qCAAnB,CAAxB;;AACA,MAAMsF,uBAAuB,GAAG,IAAItF,cAAJ,CAAmB,6CAAnB,CAAhC;;AACA,MAAMuF,gBAAgB,GAAG,IAAIvF,cAAJ,CAAmB,8BAAnB,CAAzB;AACA;AACA;AACA;;AACA,MAAMwF,2BAA2B,GAAG,IAAIxF,cAAJ,CAAmB,yCAAnB,CAApC;AACA;AACA;AACA;;AACA,MAAMyF,aAAa,GAAG,IAAIzF,cAAJ,CAAmB,2BAAnB,CAAtB;AACA;AACA;AACA;AACA;;AACA,MAAM0F,uBAAuB,GAAG,IAAI1F,cAAJ,CAAmB,6CAAnB,CAAhC;AACA;AACA;AACA;AACA;;;AACA,MAAM2F,mBAAmB,GAAG,IAAI3F,cAAJ,CAAmB,wCAAnB,CAA5B;AACA;AACA;AACA;;AACA,MAAM4F,oBAAoB,GAAG,IAAI5F,cAAJ,CAAmB,iDAAnB,CAA7B;AACA;AACA;AACA;;;AACA,MAAM6F,qBAAqB,GAAG,IAAI7F,cAAJ,CAAmB,qCAAnB,CAA9B;;AACA,MAAM8F,6BAA6B,GAAG,IAAI9F,cAAJ,CAAmB,8CAAnB,CAAtC;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM+F,mBAAmB,GAAG,IAAI/F,cAAJ,CAAmB,iCAAnB,CAA5B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMgG,sBAAsB,GAAG,IAAIhG,cAAJ,CAAmB,oCAAnB,CAA/B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASiG,eAAT,CAAyBC,QAAzB,EAAmCC,YAAY,GAAG,EAAlD,EAAsD;EAClD,MAAMC,WAAW,GAAG7E,MAAM,CAACC,IAAP,CAAY0E,QAAZ,CAApB;EACA,MAAMG,aAAa,GAAG,EAAtB;;EACA,KAAK,IAAIxC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuC,WAAW,CAACE,MAAhC,EAAwCzC,CAAC,EAAzC,EAA6C;IACzC,MAAMvC,GAAG,GAAG8E,WAAW,CAACvC,CAAD,CAAvB;;IACA,IAAI,OAAOqC,QAAQ,CAAC5E,GAAD,CAAf,KAAyB,UAA7B,EAAyC;MACrC+E,aAAa,CAAC/E,GAAD,CAAb,GAAqB4E,QAAQ,CAAC5E,GAAD,CAA7B;IACH;EACJ;;EACD,MAAMiF,gBAAgB,GAAGhF,MAAM,CAACC,IAAP,CAAY6E,aAAZ,CAAzB;EACA,OAAO,SAASG,WAAT,CAAqBC,KAArB,EAA4BtC,MAA5B,EAAoC;IACvCsC,KAAK,GAAGA,KAAK,KAAKrE,SAAV,GAAsB+D,YAAtB,GAAqCM,KAA7C;IACA,IAAIC,UAAU,GAAG,KAAjB;IACA,MAAMC,SAAS,GAAG,EAAlB;;IACA,KAAK,IAAI9C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0C,gBAAgB,CAACD,MAArC,EAA6CzC,CAAC,EAA9C,EAAkD;MAC9C,MAAMvC,GAAG,GAAGiF,gBAAgB,CAAC1C,CAAD,CAA5B;MACA,MAAM+C,OAAO,GAAGP,aAAa,CAAC/E,GAAD,CAA7B;MACA,MAAMuF,mBAAmB,GAAGJ,KAAK,CAACnF,GAAD,CAAjC;MACA,MAAMwF,eAAe,GAAGF,OAAO,CAACC,mBAAD,EAAsB1C,MAAtB,CAA/B;MACAwC,SAAS,CAACrF,GAAD,CAAT,GAAiBwF,eAAjB;MACAJ,UAAU,GAAGA,UAAU,IAAII,eAAe,KAAKD,mBAA/C;IACH;;IACD,OAAOH,UAAU,GAAGC,SAAH,GAAeF,KAAhC;EACH,CAbD;AAcH;;AACD,SAASM,IAAT,CAAcC,MAAd,EAAsBC,WAAtB,EAAmC;EAC/B,OAAO1F,MAAM,CAACC,IAAP,CAAYwF,MAAZ,EACFE,MADE,CACM5F,GAAD,IAASA,GAAG,KAAK2F,WADtB,EAEF9D,MAFE,CAEK,CAACgE,MAAD,EAAS7F,GAAT,KAAiBC,MAAM,CAACO,MAAP,CAAcqF,MAAd,EAAsB;IAAE,CAAC7F,GAAD,GAAO0F,MAAM,CAAC1F,GAAD;EAAf,CAAtB,CAFtB,EAEqE,EAFrE,CAAP;AAGH;;AACD,SAAS8F,OAAT,CAAiB,GAAGC,SAApB,EAA+B;EAC3B,OAAO,UAAUC,GAAV,EAAe;IAClB,IAAID,SAAS,CAACf,MAAV,KAAqB,CAAzB,EAA4B;MACxB,OAAOgB,GAAP;IACH;;IACD,MAAMC,IAAI,GAAGF,SAAS,CAACA,SAAS,CAACf,MAAV,GAAmB,CAApB,CAAtB;IACA,MAAMkB,IAAI,GAAGH,SAAS,CAACI,KAAV,CAAgB,CAAhB,EAAmB,CAAC,CAApB,CAAb;IACA,OAAOD,IAAI,CAACE,WAAL,CAAiB,CAACC,QAAD,EAAWC,EAAX,KAAkBA,EAAE,CAACD,QAAD,CAArC,EAAiDJ,IAAI,CAACD,GAAD,CAArD,CAAP;EACH,CAPD;AAQH;;AACD,SAASO,oBAAT,CAA8BC,cAA9B,EAA8CC,YAA9C,EAA4D;EACxD,IAAIC,KAAK,CAACC,OAAN,CAAcF,YAAd,KAA+BA,YAAY,CAACzB,MAAb,GAAsB,CAAzD,EAA4D;IACxDwB,cAAc,GAAGV,OAAO,CAACc,KAAR,CAAc,IAAd,EAAoB,CACjC,GAAGH,YAD8B,EAEjCD,cAFiC,CAApB,CAAjB;EAIH;;EACD,OAAO,CAAC5B,QAAD,EAAWC,YAAX,KAA4B;IAC/B,MAAMS,OAAO,GAAGkB,cAAc,CAAC5B,QAAD,CAA9B;IACA,OAAO,CAACO,KAAD,EAAQtC,MAAR,KAAmB;MACtBsC,KAAK,GAAGA,KAAK,KAAKrE,SAAV,GAAsB+D,YAAtB,GAAqCM,KAA7C;MACA,OAAOG,OAAO,CAACH,KAAD,EAAQtC,MAAR,CAAd;IACH,CAHD;EAIH,CAND;AAOH;;AACD,SAASgE,2BAAT,CAAqCJ,YAArC,EAAmD;EAC/C,MAAMD,cAAc,GAAGE,KAAK,CAACC,OAAN,CAAcF,YAAd,KAA+BA,YAAY,CAACzB,MAAb,GAAsB,CAArD,GACjBc,OAAO,CAAC,GAAGW,YAAJ,CADU,GAEhBK,CAAD,IAAOA,CAFb;EAGA,OAAO,CAACxB,OAAD,EAAUT,YAAV,KAA2B;IAC9BS,OAAO,GAAGkB,cAAc,CAAClB,OAAD,CAAxB;IACA,OAAO,CAACH,KAAD,EAAQtC,MAAR,KAAmB;MACtBsC,KAAK,GAAGA,KAAK,KAAKrE,SAAV,GAAsB+D,YAAtB,GAAqCM,KAA7C;MACA,OAAOG,OAAO,CAACH,KAAD,EAAQtC,MAAR,CAAd;IACH,CAHD;EAIH,CAND;AAOH;;AAED,MAAMkE,iBAAN,SAAgC1H,UAAhC,CAA2C;;AAE3C,MAAM2H,wBAAN,SAAuCtE,cAAvC,CAAsD;;AAEtD,MAAMuE,MAAM,GAAG,6BAAf;;AACA,MAAMC,cAAN,SAA6B9H,eAA7B,CAA6C;EACzCuD,WAAW,CAACwE,UAAD,EAAatC,YAAb,EAA2BD,QAA3B,EAAqC4B,cAArC,EAAqD;IAC5D,MAAMA,cAAc,CAAC5B,QAAD,EAAWC,YAAX,CAApB;IACA,KAAKsC,UAAL,GAAkBA,UAAlB;IACA,KAAKtC,YAAL,GAAoBA,YAApB;IACA,KAAKD,QAAL,GAAgBA,QAAhB;IACA,KAAK4B,cAAL,GAAsBA,cAAtB;EACH;;EACkB,IAAfY,eAAe,GAAG;IAClB,OAAO,KAAKxC,QAAZ;EACH;;EACDyC,UAAU,CAACC,OAAD,EAAU;IAChB,KAAKC,WAAL,CAAiB,CAACD,OAAD,CAAjB;EACH;;EACDC,WAAW,CAACC,QAAD,EAAW;IAClB,MAAM5C,QAAQ,GAAG4C,QAAQ,CAAC3F,MAAT,CAAgB,CAAC4F,WAAD,EAAc;MAAE7C,QAAF;MAAY4B,cAAZ;MAA4BC,YAA5B;MAA0C5B,YAA1C;MAAwD7E;IAAxD,CAAd,KAAgF;MAC7G,MAAMsF,OAAO,GAAG,OAAOV,QAAP,KAAoB,UAApB,GACViC,2BAA2B,CAACJ,YAAD,CAA3B,CAA0C7B,QAA1C,EAAoDC,YAApD,CADU,GAEV0B,oBAAoB,CAACC,cAAD,EAAiBC,YAAjB,CAApB,CAAmD7B,QAAnD,EAA6DC,YAA7D,CAFN;MAGA4C,WAAW,CAACzH,GAAD,CAAX,GAAmBsF,OAAnB;MACA,OAAOmC,WAAP;IACH,CANgB,EAMd,EANc,CAAjB;IAOA,KAAKC,WAAL,CAAiB9C,QAAjB;EACH;;EACD+C,aAAa,CAACL,OAAD,EAAU;IACnB,KAAKM,cAAL,CAAoB,CAACN,OAAD,CAApB;EACH;;EACDM,cAAc,CAACJ,QAAD,EAAW;IACrB,KAAKK,cAAL,CAAoBL,QAAQ,CAAC5H,GAAT,CAAckI,CAAD,IAAOA,CAAC,CAAC9H,GAAtB,CAApB;EACH;;EACD+H,UAAU,CAAC/H,GAAD,EAAMsF,OAAN,EAAe;IACrB,KAAKoC,WAAL,CAAiB;MAAE,CAAC1H,GAAD,GAAOsF;IAAT,CAAjB;EACH;;EACDoC,WAAW,CAAC9C,QAAD,EAAW;IAClB,KAAKA,QAAL,GAAgB3E,MAAM,CAACO,MAAP,CAAcP,MAAM,CAACO,MAAP,CAAc,EAAd,EAAkB,KAAKoE,QAAvB,CAAd,EAAgDA,QAAhD,CAAhB;IACA,KAAKoD,cAAL,CAAoB/H,MAAM,CAACC,IAAP,CAAY0E,QAAZ,CAApB;EACH;;EACDqD,aAAa,CAACC,UAAD,EAAa;IACtB,KAAKL,cAAL,CAAoB,CAACK,UAAD,CAApB;EACH;;EACDL,cAAc,CAACM,WAAD,EAAc;IACxBA,WAAW,CAACC,OAAZ,CAAqBpI,GAAD,IAAS;MACzB,KAAK4E,QAAL,GAAgBa,IAAI,CAAC,KAAKb,QAAN,EAAgB5E,GAAhB;MAAqB;MAAzC;IACH,CAFD;IAGA,KAAKgI,cAAL,CAAoBG,WAApB;EACH;;EACDH,cAAc,CAACG,WAAD,EAAc;IACxB,KAAKvF,IAAL,CAAU,KAAK4D,cAAL,CAAoB,KAAK5B,QAAzB,EAAmC,KAAKC,YAAxC,CAAV;IACA,KAAKsC,UAAL,CAAgBvE,IAAhB,CAAqB;MACjBxC,IAAI,EAAE6G,MADW;MAEjBO,QAAQ,EAAEW;IAFO,CAArB;EAIH;;EACDnF,WAAW,GAAG;IACV,KAAKD,QAAL;EACH;;AAvDwC;AAyD7C;;;AAAmBmE,cAAc,CAACjE,IAAf;EAAA,iBAA2GiE,cAA3G,EAlOiG1I,EAkOjG,UAA2IwI,wBAA3I,GAlOiGxI,EAkOjG,UAAgL8E,aAAhL,GAlOiG9E,EAkOjG,UAA0MiF,gBAA1M,GAlOiGjF,EAkOjG,UAAuO+E,eAAvO;AAAA;AACnB;;;AAAmB2D,cAAc,CAAChE,KAAf,kBAnOiG1E,EAmOjG;EAAA,OAA+G0I,cAA/G;EAAA,SAA+GA,cAA/G;AAAA;;AACnB;EAAA,mDApOoH1I,EAoOpH,mBAA2F0I,cAA3F,EAAuH,CAAC;IAC5G9G,IAAI,EAAE3B;EADsG,CAAD,CAAvH,EAE4B,YAAY;IAChC,OAAO,CAAC;MAAE2B,IAAI,EAAE4G;IAAR,CAAD,EAAqC;MAAE5G,IAAI,EAAEU,SAAR;MAAmBuH,UAAU,EAAE,CAAC;QAC5DjI,IAAI,EAAEzB,MADsD;QAE5D4B,IAAI,EAAE,CAAC+C,aAAD;MAFsD,CAAD;IAA/B,CAArC,EAGW;MAAElD,IAAI,EAAEU,SAAR;MAAmBuH,UAAU,EAAE,CAAC;QAClCjI,IAAI,EAAEzB,MAD4B;QAElC4B,IAAI,EAAE,CAACkD,gBAAD;MAF4B,CAAD;IAA/B,CAHX,EAMW;MAAErD,IAAI,EAAEU,SAAR;MAAmBuH,UAAU,EAAE,CAAC;QAClCjI,IAAI,EAAEzB,MAD4B;QAElC4B,IAAI,EAAE,CAACgD,eAAD;MAF4B,CAAD;IAA/B,CANX,CAAP;EAUH,CAbL;AAAA;;AAcA,MAAM+E,yBAAyB,GAAG,CAC9BpB,cAD8B,EAE9B;EAAEqB,OAAO,EAAExB,iBAAX;EAA8ByB,WAAW,EAAEtB;AAA3C,CAF8B,EAG9B;EAAEqB,OAAO,EAAEvB,wBAAX;EAAqCwB,WAAW,EAAE9F;AAAlD,CAH8B,CAAlC;;AAMA,MAAM+F,qBAAN,SAAoCnJ,OAApC,CAA4C;EACxC0D,WAAW,GAAG;IACV,KAAKD,QAAL;EACH;;AAHuC;AAK5C;;;AAAmB0F,qBAAqB,CAACxF,IAAtB;EAAA;EAAA;IAAA,oFA7PiGzE,EA6PjG,uBAAkHiK,qBAAlH,SAAkHA,qBAAlH;EAAA;AAAA;AACnB;;;AAAmBA,qBAAqB,CAACvF,KAAtB,kBA9PiG1E,EA8PjG;EAAA,OAAsHiK,qBAAtH;EAAA,SAAsHA,qBAAtH;AAAA;;AACnB;EAAA,mDA/PoHjK,EA+PpH,mBAA2FiK,qBAA3F,EAA8H,CAAC;IACnHrI,IAAI,EAAE3B;EAD6G,CAAD,CAA9H;AAAA;;AAGA,MAAMiK,iCAAiC,GAAG,CACtCD,qBADsC,CAA1C;;AAIA,MAAME,eAAN,SAA8BtJ,UAA9B,CAAyC;;AAEzC,MAAMuJ,KAAN,SAAoBxJ,eAApB,CAAoC;EAChCuD,WAAW,CAACkG,QAAD,EAAWC,QAAX,EAAqBC,cAArB,EAAqClE,YAArC,EAAmD;IAC1D,MAAMA,YAAN;IACA,MAAMmE,eAAe,GAAGH,QAAQ,CAACI,IAAT,CAAczJ,SAAS,CAACD,cAAD,CAAvB,CAAxB;IACA,MAAM2J,kBAAkB,GAAGF,eAAe,CAACC,IAAhB,CAAqBxJ,cAAc,CAACqJ,QAAD,CAAnC,CAA3B;IACA,MAAMK,IAAI,GAAG;MAAEhE,KAAK,EAAEN;IAAT,CAAb;IACA,MAAMuE,eAAe,GAAGF,kBAAkB,CAACD,IAAnB,CAAwBvJ,IAAI,CAAC2J,WAAD,EAAcF,IAAd,CAA5B,CAAxB;IACA,KAAKG,iBAAL,GAAyBF,eAAe,CAACG,SAAhB,CAA0B,CAAC;MAAEpE,KAAF;MAAStC;IAAT,CAAD,KAAuB;MACtE,KAAKD,IAAL,CAAUuC,KAAV;MACA4D,cAAc,CAACnG,IAAf,CAAoBC,MAApB;IACH,CAHwB,CAAzB;EAIH;;EACDG,WAAW,GAAG;IACV,KAAKsG,iBAAL,CAAuBE,WAAvB;IACA,KAAKzG,QAAL;EACH;;AAf+B;;AAiBpC6F,KAAK,CAACnG,IAAN,GAAaA,IAAb;AACA;;AAAmBmG,KAAK,CAAC3F,IAAN;EAAA,iBAAkG2F,KAAlG,EA1RiGpK,EA0RjG,UAAyHkE,cAAzH,GA1RiGlE,EA0RjG,UAAoJuI,iBAApJ,GA1RiGvI,EA0RjG,UAAkLiK,qBAAlL,GA1RiGjK,EA0RjG,UAAoN8E,aAApN;AAAA;AACnB;;;AAAmBsF,KAAK,CAAC1F,KAAN,kBA3RiG1E,EA2RjG;EAAA,OAAsGoK,KAAtG;EAAA,SAAsGA,KAAtG;AAAA;;AACnB;EAAA,mDA5RoHpK,EA4RpH,mBAA2FoK,KAA3F,EAA8G,CAAC;IACnGxI,IAAI,EAAE3B;EAD6F,CAAD,CAA9G,EAE4B,YAAY;IAChC,OAAO,CAAC;MAAE2B,IAAI,EAAEsC;IAAR,CAAD,EAA2B;MAAEtC,IAAI,EAAE2G;IAAR,CAA3B,EAAwD;MAAE3G,IAAI,EAAEqI;IAAR,CAAxD,EAAyF;MAAErI,IAAI,EAAEU,SAAR;MAAmBuH,UAAU,EAAE,CAAC;QAChHjI,IAAI,EAAEzB,MAD0G;QAEhH4B,IAAI,EAAE,CAAC+C,aAAD;MAF0G,CAAD;IAA/B,CAAzF,CAAP;EAIH,CAPL;AAAA;;AAQA,SAAS+F,WAAT,CAAqBI,eAAe,GAAG;EAAEtE,KAAK,EAAErE;AAAT,CAAvC,EAA6D,CAAC+B,MAAD,EAASyC,OAAT,CAA7D,EAAgF;EAC5E,MAAM;IAAEH;EAAF,IAAYsE,eAAlB;EACA,OAAO;IAAEtE,KAAK,EAAEG,OAAO,CAACH,KAAD,EAAQtC,MAAR,CAAhB;IAAiCA;EAAjC,CAAP;AACH;;AACD,MAAM6G,eAAe,GAAG,CACpBd,KADoB,EAEpB;EAAEL,OAAO,EAAEI,eAAX;EAA4BH,WAAW,EAAEI;AAAzC,CAFoB,CAAxB,C,CAKA;;AACA,MAAMe,KAAN,SAAoBtK,UAApB,CAA+B;EAC3BsD,WAAW,CAACiH,MAAD,EAASC,eAAT,EAA0BC,cAA1B,EAA0C;IACjD;IACA,KAAKD,eAAL,GAAuBA,eAAvB;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA,KAAKnI,MAAL,GAAciI,MAAd;EACH;;EACDG,MAAM,CAACC,WAAD,EAAc,GAAGC,KAAjB,EAAwB;IAC1B,OAAOF,MAAM,CAACG,IAAP,CAAY,IAAZ,EAAkBF,WAAlB,EAA+B,GAAGC,KAAlC,EAAyC,IAAzC,CAAP;EACH;;EACDE,IAAI,CAACC,QAAD,EAAW;IACX,MAAMC,KAAK,GAAG,IAAIV,KAAJ,CAAU,IAAV,EAAgB,KAAKE,eAArB,EAAsC,KAAKC,cAA3C,CAAd;IACAO,KAAK,CAACD,QAAN,GAAiBA,QAAjB;IACA,OAAOC,KAAP;EACH;;EACDC,QAAQ,CAACzH,MAAD,EAAS;IACb,KAAKgH,eAAL,CAAqBjH,IAArB,CAA0BC,MAA1B;EACH;;EACDD,IAAI,CAACC,MAAD,EAAS;IACT,KAAKgH,eAAL,CAAqBjH,IAArB,CAA0BC,MAA1B;EACH;;EACD0H,KAAK,CAACC,GAAD,EAAM;IACP,KAAKX,eAAL,CAAqBU,KAArB,CAA2BC,GAA3B;EACH;;EACDzH,QAAQ,GAAG;IACP,KAAK8G,eAAL,CAAqB9G,QAArB;EACH;;EACDgF,UAAU,CAAC/H,GAAD,EAAMsF,OAAN,EAAe;IACrB,KAAKwE,cAAL,CAAoB/B,UAApB,CAA+B/H,GAA/B,EAAoCsF,OAApC;EACH;;EACD2C,aAAa,CAACjI,GAAD,EAAM;IACf,KAAK8J,cAAL,CAAoB7B,aAApB,CAAkCjI,GAAlC;EACH;;AAhC0B;AAkC/B;;;AAAmB2J,KAAK,CAAC1G,IAAN;EAAA,iBAAkG0G,KAAlG,EAhViGnL,EAgVjG,UAAyHmK,eAAzH,GAhViGnK,EAgVjG,UAAqJkE,cAArJ,GAhViGlE,EAgVjG,UAAgL0I,cAAhL;AAAA;AACnB;;;AAAmByC,KAAK,CAACzG,KAAN,kBAjViG1E,EAiVjG;EAAA,OAAsGmL,KAAtG;EAAA,SAAsGA,KAAtG;AAAA;;AACnB;EAAA,mDAlVoHnL,EAkVpH,mBAA2FmL,KAA3F,EAA8G,CAAC;IACnGvJ,IAAI,EAAE3B;EAD6F,CAAD,CAA9G,EAE4B,YAAY;IAAE,OAAO,CAAC;MAAE2B,IAAI,EAAEuI;IAAR,CAAD,EAA4B;MAAEvI,IAAI,EAAEsC;IAAR,CAA5B,EAAsD;MAAEtC,IAAI,EAAE8G;IAAR,CAAtD,CAAP;EAAyF,CAFnI;AAAA;;AAGA,MAAMuD,eAAe,GAAG,CAACd,KAAD,CAAxB;;AACA,SAASI,MAAT,CAAgBC,WAAhB,EAA6BU,WAA7B,EAA0C,GAAGT,KAA7C,EAAoD;EAChD,OAAO,SAASU,cAAT,CAAwBC,OAAxB,EAAiC;IACpC,IAAIC,OAAJ;;IACA,IAAI,OAAOb,WAAP,KAAuB,QAA3B,EAAqC;MACjC,MAAMc,UAAU,GAAG,CAACJ,WAAD,EAAc,GAAGT,KAAjB,EAAwBrE,MAAxB,CAA+BmF,OAA/B,CAAnB;MACAF,OAAO,GAAGD,OAAO,CAAC3B,IAAR,CAAatJ,KAAK,CAACqK,WAAD,EAAc,GAAGc,UAAjB,CAAlB,CAAV;IACH,CAHD,MAIK,IAAI,OAAOd,WAAP,KAAuB,UAA3B,EAAuC;MACxCa,OAAO,GAAGD,OAAO,CAAC3B,IAAR,CAAarJ,GAAG,CAAE+B,MAAD,IAAYqI,WAAW,CAACrI,MAAD,EAAS+I,WAAT,CAAxB,CAAhB,CAAV;IACH,CAFI,MAGA;MACD,MAAM,IAAI5H,SAAJ,CAAe,oBAAmB,OAAOkH,WAAY,uBAAvC,GACf,kCADC,CAAN;IAEH;;IACD,OAAOa,OAAO,CAAC5B,IAAR,CAAapJ,oBAAoB,EAAjC,CAAP;EACH,CAdD;AAeH;;AAED,MAAMmL,iBAAiB,GAAG,0DAA1B;;AACA,SAASC,WAAT,CAAqBC,MAArB,EAA6B;EACzB,OAAOA,MAAM,KAAKpK,SAAlB;AACH;;AACD,SAASqK,MAAT,CAAgBD,MAAhB,EAAwB;EACpB,OAAOA,MAAM,KAAK,IAAlB;AACH;;AACD,SAASvE,OAAT,CAAiBuE,MAAjB,EAAyB;EACrB,OAAOxE,KAAK,CAACC,OAAN,CAAcuE,MAAd,CAAP;AACH;;AACD,SAASE,QAAT,CAAkBF,MAAlB,EAA0B;EACtB,OAAO,OAAOA,MAAP,KAAkB,QAAzB;AACH;;AACD,SAASG,SAAT,CAAmBH,MAAnB,EAA2B;EACvB,OAAO,OAAOA,MAAP,KAAkB,SAAzB;AACH;;AACD,SAASI,QAAT,CAAkBJ,MAAlB,EAA0B;EACtB,OAAO,OAAOA,MAAP,KAAkB,QAAzB;AACH;;AACD,SAASK,YAAT,CAAsBL,MAAtB,EAA8B;EAC1B,OAAO,OAAOA,MAAP,KAAkB,QAAlB,IAA8BA,MAAM,KAAK,IAAhD;AACH;;AACD,SAASM,QAAT,CAAkBN,MAAlB,EAA0B;EACtB,OAAOK,YAAY,CAACL,MAAD,CAAZ,IAAwB,CAACvE,OAAO,CAACuE,MAAD,CAAvC;AACH;;AACD,SAASO,aAAT,CAAuBP,MAAvB,EAA+B;EAC3B,IAAI,CAACM,QAAQ,CAACN,MAAD,CAAb,EAAuB;IACnB,OAAO,KAAP;EACH;;EACD,MAAMQ,eAAe,GAAGzL,MAAM,CAAC0L,cAAP,CAAsBT,MAAtB,CAAxB;EACA,OAAOQ,eAAe,KAAKzL,MAAM,CAAC2L,SAA3B,IAAwCF,eAAe,KAAK,IAAnE;AACH;;AACD,SAASG,UAAT,CAAoBX,MAApB,EAA4B;EACxB,OAAO,OAAOA,MAAP,KAAkB,UAAzB;AACH;;AACD,SAASY,WAAT,CAAqBZ,MAArB,EAA6B;EACzB,OAAOW,UAAU,CAACX,MAAD,CAAV,IAAsBA,MAAM,CAACa,cAAP,CAAsB,MAAtB,CAA7B;AACH;;AACD,SAASA,cAAT,CAAwBb,MAAxB,EAAgCc,YAAhC,EAA8C;EAC1C,OAAO/L,MAAM,CAAC2L,SAAP,CAAiBG,cAAjB,CAAgC7B,IAAhC,CAAqCgB,MAArC,EAA6Cc,YAA7C,CAAP;AACH;;AAED,IAAIC,oBAAoB,GAAG,KAA3B;;AACA,SAASC,sBAAT,CAAgC/K,KAAhC,EAAuC;EACnC8K,oBAAoB,GAAG9K,KAAvB;AACH;;AACD,SAASgL,qBAAT,GAAiC;EAC7B,OAAOF,oBAAP;AACH;;AAED,SAASG,YAAT,CAAsBC,CAAtB,EAAyBC,CAAzB,EAA4B;EACxB,OAAOD,CAAC,KAAKC,CAAb;AACH;;AACD,SAASC,kBAAT,CAA4BhM,IAA5B,EAAkCiM,aAAlC,EAAiDC,UAAjD,EAA6D;EACzD,KAAK,IAAIlK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGhC,IAAI,CAACyE,MAAzB,EAAiCzC,CAAC,EAAlC,EAAsC;IAClC,IAAI,CAACkK,UAAU,CAAClM,IAAI,CAACgC,CAAD,CAAL,EAAUiK,aAAa,CAACjK,CAAD,CAAvB,CAAf,EAA4C;MACxC,OAAO,IAAP;IACH;EACJ;;EACD,OAAO,KAAP;AACH;;AACD,SAASmK,aAAT,CAAuBC,YAAvB,EAAqCC,aAArC,EAAoD;EAChD,OAAOC,cAAc,CAACF,YAAD,EAAeP,YAAf,EAA6BQ,aAA7B,CAArB;AACH;;AACD,SAASC,cAAT,CAAwBF,YAAxB,EAAsCG,gBAAgB,GAAGV,YAAzD,EAAuEQ,aAAa,GAAGR,YAAvF,EAAqG;EACjG,IAAII,aAAa,GAAG,IAApB,CADiG,CAEjG;;EACA,IAAIO,UAAU,GAAG,IAAjB;EACA,IAAIC,cAAJ;;EACA,SAASC,KAAT,GAAiB;IACbT,aAAa,GAAG,IAAhB;IACAO,UAAU,GAAG,IAAb;EACH;;EACD,SAASG,SAAT,CAAmBrH,MAAM,GAAG/E,SAA5B,EAAuC;IACnCkM,cAAc,GAAG;MAAEnH;IAAF,CAAjB;EACH;;EACD,SAASsH,WAAT,GAAuB;IACnBH,cAAc,GAAGlM,SAAjB;EACH;EACD;EACA;;;EACA,SAASsM,QAAT,GAAoB;IAChB,IAAIJ,cAAc,KAAKlM,SAAvB,EAAkC;MAC9B,OAAOkM,cAAc,CAACnH,MAAtB;IACH;;IACD,IAAI,CAAC2G,aAAL,EAAoB;MAChBO,UAAU,GAAGJ,YAAY,CAAC/F,KAAb,CAAmB,IAAnB,EAAyByG,SAAzB,CAAb;MACAb,aAAa,GAAGa,SAAhB;MACA,OAAON,UAAP;IACH;;IACD,IAAI,CAACR,kBAAkB,CAACc,SAAD,EAAYb,aAAZ,EAA2BM,gBAA3B,CAAvB,EAAqE;MACjE,OAAOC,UAAP;IACH;;IACD,MAAMO,SAAS,GAAGX,YAAY,CAAC/F,KAAb,CAAmB,IAAnB,EAAyByG,SAAzB,CAAlB;IACAb,aAAa,GAAGa,SAAhB;;IACA,IAAIT,aAAa,CAACG,UAAD,EAAaO,SAAb,CAAjB,EAA0C;MACtC,OAAOP,UAAP;IACH;;IACDA,UAAU,GAAGO,SAAb;IACA,OAAOA,SAAP;EACH;;EACD,OAAO;IAAEF,QAAF;IAAYH,KAAZ;IAAmBC,SAAnB;IAA8BC;EAA9B,CAAP;AACH;;AACD,SAASI,cAAT,CAAwB,GAAGC,KAA3B,EAAkC;EAC9B,OAAOC,qBAAqB,CAACZ,cAAD,CAArB,CAAsC,GAAGW,KAAzC,CAAP;AACH;;AACD,SAASE,cAAT,CAAwBvI,KAAxB,EAA+BwI,SAA/B,EAA0ChN,KAA1C,EAAiDiN,iBAAjD,EAAoE;EAChE,IAAIjN,KAAK,KAAKG,SAAd,EAAyB;IACrB,MAAMP,IAAI,GAAGoN,SAAS,CAAC/N,GAAV,CAAe0G,EAAD,IAAQA,EAAE,CAACnB,KAAD,CAAxB,CAAb;IACA,OAAOyI,iBAAiB,CAACR,QAAlB,CAA2BxG,KAA3B,CAAiC,IAAjC,EAAuCrG,IAAvC,CAAP;EACH;;EACD,MAAMA,IAAI,GAAGoN,SAAS,CAAC/N,GAAV,CAAe0G,EAAD,IAAQA,EAAE,CAACnB,KAAD,EAAQxE,KAAR,CAAxB,CAAb;EACA,OAAOiN,iBAAiB,CAACR,QAAlB,CAA2BxG,KAA3B,CAAiC,IAAjC,EAAuC,CAAC,GAAGrG,IAAJ,EAAUI,KAAV,CAAvC,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS8M,qBAAT,CAA+BI,OAA/B,EAAwCC,OAAO,GAAG;EAC9CC,OAAO,EAAEL;AADqC,CAAlD,EAEG;EACC,OAAO,UAAU,GAAGF,KAAb,EAAoB;IACvB,IAAIjN,IAAI,GAAGiN,KAAX;;IACA,IAAI9G,KAAK,CAACC,OAAN,CAAcpG,IAAI,CAAC,CAAD,CAAlB,CAAJ,EAA4B;MACxB,MAAM,CAACyN,IAAD,EAAO,GAAGC,IAAV,IAAkB1N,IAAxB;MACAA,IAAI,GAAG,CAAC,GAAGyN,IAAJ,EAAU,GAAGC,IAAb,CAAP;IACH;;IACD,MAAMN,SAAS,GAAGpN,IAAI,CAAC4F,KAAL,CAAW,CAAX,EAAc5F,IAAI,CAACyE,MAAL,GAAc,CAA5B,CAAlB;IACA,MAAMkJ,SAAS,GAAG3N,IAAI,CAACA,IAAI,CAACyE,MAAL,GAAc,CAAf,CAAtB;IACA,MAAMmJ,iBAAiB,GAAGR,SAAS,CAAC/H,MAAV,CAAkBwI,QAAD,IAAcA,QAAQ,CAACC,OAAT,IAAoB,OAAOD,QAAQ,CAACC,OAAhB,KAA4B,UAA/E,CAA1B;IACA,MAAMT,iBAAiB,GAAGC,OAAO,CAAC,UAAU,GAAGF,SAAb,EAAwB;MACtD,OAAOO,SAAS,CAACtH,KAAV,CAAgB,IAAhB,EAAsB+G,SAAtB,CAAP;IACH,CAFgC,CAAjC;IAGA,MAAMW,aAAa,GAAGzB,cAAc,CAAC,UAAU1H,KAAV,EAAiBxE,KAAjB,EAAwB;MACzD,OAAOmN,OAAO,CAACC,OAAR,CAAgBnH,KAAhB,CAAsB,IAAtB,EAA4B,CAC/BzB,KAD+B,EAE/BwI,SAF+B,EAG/BhN,KAH+B,EAI/BiN,iBAJ+B,CAA5B,CAAP;IAMH,CAPmC,CAApC;;IAQA,SAASS,OAAT,GAAmB;MACfC,aAAa,CAACrB,KAAd;MACAW,iBAAiB,CAACX,KAAlB;MACAkB,iBAAiB,CAAC/F,OAAlB,CAA2BgG,QAAD,IAAcA,QAAQ,CAACC,OAAT,EAAxC;IACH;;IACD,OAAOpO,MAAM,CAACO,MAAP,CAAc8N,aAAa,CAAClB,QAA5B,EAAsC;MACzCiB,OADyC;MAEzCH,SAAS,EAAEN,iBAAiB,CAACR,QAFY;MAGzCF,SAAS,EAAEoB,aAAa,CAACpB,SAHgB;MAIzCC,WAAW,EAAEmB,aAAa,CAACnB;IAJc,CAAtC,CAAP;EAMH,CA/BD;AAgCH;;AACD,SAASoB,qBAAT,CAA+BC,WAA/B,EAA4C;EACxC,OAAOjB,cAAc,CAAEpI,KAAD,IAAW;IAC7B,MAAMsJ,YAAY,GAAGtJ,KAAK,CAACqJ,WAAD,CAA1B;;IACA,IAAI,CAACrC,qBAAqB,EAAtB,IAA4BvN,SAAS,EAArC,IAA2C,EAAE4P,WAAW,IAAIrJ,KAAjB,CAA/C,EAAwE;MACpEuJ,OAAO,CAACC,IAAR,CAAc,kCAAiCH,WAAY,SAA9C,GACT,0DADS,GAET,+DAFS,GAGR,8BAA6BA,WAAY,aAHjC,GAIR,2BAA0BA,WAAY,2BAJ9B,GAKT,gEALS,GAMT,8DANJ;IAOH;;IACD,OAAOC,YAAP;EACH,CAZoB,EAYjBA,YAAD,IAAkBA,YAZA,CAArB;AAaH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,aAAT,CAAuBC,aAAvB,EAAsC;EAClC,MAAM;IAAEC,IAAF;IAAQxJ;EAAR,IAAoBuJ,aAA1B;EACA,MAAME,eAAe,GAAGR,qBAAqB,CAACO,IAAD,CAA7C;EACA,MAAME,eAAe,GAAGC,qBAAqB,CAACF,eAAD,EAAkBzJ,OAAlB,CAA7C;EACA,OAAOrF,MAAM,CAACO,MAAP,CAAc;IAAEsO,IAAF;IACjBxJ,OADiB;IACR,CAAE,SAAQjE,UAAU,CAACyN,IAAD,CAAO,OAA3B,GAAoCC;EAD5B,CAAd,EAC6DC,eAD7D,CAAP;AAEH;;AACD,SAASC,qBAAT,CAA+BF,eAA/B,EAAgDzJ,OAAhD,EAAyD;EACrD,MAAMT,YAAY,GAAGqK,eAAe,CAAC5J,OAAD,CAApC;EACA,MAAM6J,UAAU,GAAI1D,aAAa,CAAC5G,YAAD,CAAb,GAA8B5E,MAAM,CAACC,IAAP,CAAY2E,YAAZ,CAA9B,GAA0D,EAA9E;EACA,OAAOsK,UAAU,CAACtN,MAAX,CAAkB,CAACmN,eAAD,EAAkBI,SAAlB,KAAiCnP,MAAM,CAACO,MAAP,CAAcP,MAAM,CAACO,MAAP,CAAc,EAAd,EAAkBwO,eAAlB,CAAd,EAAkD;IAAE,CAAE,SAAQ3N,UAAU,CAAC+N,SAAD,CAAY,EAAhC,GAAoC7B,cAAc,CAACwB,eAAD,EAAmBM,WAAD,IAAiBA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACD,SAAD,CAAxG;EAApD,CAAlD,CAAnD,EAAmR,EAAnR,CAAP;AACH;;AACD,SAASF,eAAT,CAAyB5J,OAAzB,EAAkC;EAC9B,OAAOA,OAAO,CAACxE,SAAD,EAAY;IAAEV,IAAI,EAAE;EAAR,CAAZ,CAAd;AACH;;AAED,SAASkP,oBAAT,CAA8BC,QAA9B,EAAwC3K,QAAxC,EAAkD;EAC9C,OAAOA,QAAQ,YAAYlG,cAApB,GAAqC6Q,QAAQ,CAACC,GAAT,CAAa5K,QAAb,CAArC,GAA8DA,QAArE;AACH;;AACD,SAAS6K,mBAAT,CAA6BF,QAA7B,EAAuCG,OAAvC,EAAgDC,aAAhD,EAA+D;EAC3D,OAAOA,aAAa,CAAC/P,GAAd,CAAkB,CAACgQ,IAAD,EAAOC,KAAP,KAAiB;IACtC,IAAIH,OAAO,CAACG,KAAD,CAAP,YAA0BnR,cAA9B,EAA8C;MAC1C,MAAMoR,IAAI,GAAGP,QAAQ,CAACC,GAAT,CAAaE,OAAO,CAACG,KAAD,CAApB,CAAb;MACA,OAAO;QACH7P,GAAG,EAAE4P,IAAI,CAAC5P,GADP;QAEHwG,cAAc,EAAEsJ,IAAI,CAACtJ,cAAL,GACVsJ,IAAI,CAACtJ,cADK,GAEV7B,eAJH;QAKH8B,YAAY,EAAEqJ,IAAI,CAACrJ,YAAL,GAAoBqJ,IAAI,CAACrJ,YAAzB,GAAwC,EALnD;QAMH5B,YAAY,EAAEiL,IAAI,CAACjL;MANhB,CAAP;IAQH;;IACD,OAAO+K,IAAP;EACH,CAbM,CAAP;AAcH;;AACD,SAASG,sBAAT,CAAgCR,QAAhC,EAA0CS,iBAA1C,EAA6D;EACzD,MAAMpL,QAAQ,GAAGoL,iBAAiB,CAACpQ,GAAlB,CAAuB0F,OAAD,IAAa;IAChD,OAAOA,OAAO,YAAY5G,cAAnB,GAAoC6Q,QAAQ,CAACC,GAAT,CAAalK,OAAb,CAApC,GAA4DA,OAAnE;EACH,CAFgB,CAAjB;EAGA,OAAOV,QAAP;AACH;;AACD,SAASqL,oBAAT,CAA8BpL,YAA9B,EAA4C;EACxC,IAAI,OAAOA,YAAP,KAAwB,UAA5B,EAAwC;IACpC,OAAOA,YAAY,EAAnB;EACH;;EACD,OAAOA,YAAP;AACH;;AACD,SAASqL,mBAAT,CAA6BzJ,YAA7B,EAA2C0J,wBAA3C,EAAqE;EACjE,OAAO1J,YAAY,CAAC2J,MAAb,CAAoBD,wBAApB,CAAP;AACH;;AACD,SAASE,oBAAT,CAA8BhG,KAA9B,EAAqC;EACjC,IAAIA,KAAJ,EAAW;IACP,MAAM,IAAIvH,SAAJ,CAAe,yGAAf,CAAN;EACH;;EACD,OAAO,SAAP;AACH;;AAED,SAASwN,4BAAT,CAAsChL,OAAtC,EAA+CiL,MAA/C,EAAuD;EACnD,OAAO,UAAUpL,KAAV,EAAiBtC,MAAjB,EAAyB;IAC5B,MAAM2N,GAAG,GAAGD,MAAM,CAAC1N,MAAP,CAAcA,MAAd,IAAwB4N,MAAM,CAAC5N,MAAD,CAA9B,GAAyCA,MAArD;IACA,MAAMwC,SAAS,GAAGC,OAAO,CAACH,KAAD,EAAQqL,GAAR,CAAzB;IACA,OAAOD,MAAM,CAACpL,KAAP,KAAiBsL,MAAM,CAACpL,SAAD,CAAvB,GAAqCA,SAA5C;EACH,CAJD;AAKH;;AACD,SAASoL,MAAT,CAAgBvF,MAAhB,EAAwB;EACpBjL,MAAM,CAACwQ,MAAP,CAAcvF,MAAd;EACA,MAAMwF,gBAAgB,GAAG7E,UAAU,CAACX,MAAD,CAAnC;EACAjL,MAAM,CAAC0Q,mBAAP,CAA2BzF,MAA3B,EAAmC9C,OAAnC,CAA4CwI,IAAD,IAAU;IACjD;IACA,IAAIA,IAAI,CAACC,UAAL,CAAgB,GAAhB,CAAJ,EAA0B;MACtB;IACH;;IACD,IAAI9E,cAAc,CAACb,MAAD,EAAS0F,IAAT,CAAd,KACCF,gBAAgB,GACXE,IAAI,KAAK,QAAT,IAAqBA,IAAI,KAAK,QAA9B,IAA0CA,IAAI,KAAK,WADxC,GAEX,IAHN,CAAJ,EAGiB;MACb,MAAME,SAAS,GAAG5F,MAAM,CAAC0F,IAAD,CAAxB;;MACA,IAAI,CAACrF,YAAY,CAACuF,SAAD,CAAZ,IAA2BjF,UAAU,CAACiF,SAAD,CAAtC,KACA,CAAC7Q,MAAM,CAAC8Q,QAAP,CAAgBD,SAAhB,CADL,EACiC;QAC7BL,MAAM,CAACK,SAAD,CAAN;MACH;IACJ;EACJ,CAfD;EAgBA,OAAO5F,MAAP;AACH;;AAED,SAAS8F,6BAAT,CAAuC1L,OAAvC,EAAgDiL,MAAhD,EAAwD;EACpD,OAAO,UAAUpL,KAAV,EAAiBtC,MAAjB,EAAyB;IAC5B,IAAI0N,MAAM,CAAC1N,MAAP,CAAcA,MAAd,CAAJ,EAA2B;MACvB,MAAMoO,oBAAoB,GAAGC,iBAAiB,CAACrO,MAAD,CAA9C;MACAsO,qBAAqB,CAACF,oBAAD,EAAuB,QAAvB,CAArB;IACH;;IACD,MAAM5L,SAAS,GAAGC,OAAO,CAACH,KAAD,EAAQtC,MAAR,CAAzB;;IACA,IAAI0N,MAAM,CAACpL,KAAP,EAAJ,EAAoB;MAChB,MAAMiM,mBAAmB,GAAGF,iBAAiB,CAAC7L,SAAD,CAA7C;MACA8L,qBAAqB,CAACC,mBAAD,EAAsB,OAAtB,CAArB;IACH;;IACD,OAAO/L,SAAP;EACH,CAXD;AAYH;;AACD,SAAS6L,iBAAT,CAA2BhG,MAA3B,EAAmCmG,IAAI,GAAG,EAA1C,EAA8C;EAC1C;EACA,IAAI,CAACpG,WAAW,CAACC,MAAD,CAAX,IAAuBC,MAAM,CAACD,MAAD,CAA9B,KAA2CmG,IAAI,CAACrM,MAAL,KAAgB,CAA/D,EAAkE;IAC9D,OAAO;MACHqM,IAAI,EAAE,CAAC,MAAD,CADH;MAEHlQ,KAAK,EAAE+J;IAFJ,CAAP;EAIH;;EACD,MAAMhL,IAAI,GAAGD,MAAM,CAACC,IAAP,CAAYgL,MAAZ,CAAb;EACA,OAAOhL,IAAI,CAAC2B,MAAL,CAAY,CAACgE,MAAD,EAAS7F,GAAT,KAAiB;IAChC,IAAI6F,MAAJ,EAAY;MACR,OAAOA,MAAP;IACH;;IACD,MAAM1E,KAAK,GAAG+J,MAAM,CAAClL,GAAD,CAApB,CAJgC,CAKhC;;IACA,IAAI8L,WAAW,CAAC3K,KAAD,CAAf,EAAwB;MACpB,OAAO0E,MAAP;IACH;;IACD,IAAIoF,WAAW,CAAC9J,KAAD,CAAX,IACAgK,MAAM,CAAChK,KAAD,CADN,IAEAmK,QAAQ,CAACnK,KAAD,CAFR,IAGAkK,SAAS,CAAClK,KAAD,CAHT,IAIAiK,QAAQ,CAACjK,KAAD,CAJR,IAKAwF,OAAO,CAACxF,KAAD,CALX,EAKoB;MAChB,OAAO,KAAP;IACH;;IACD,IAAIsK,aAAa,CAACtK,KAAD,CAAjB,EAA0B;MACtB,OAAO+P,iBAAiB,CAAC/P,KAAD,EAAQ,CAAC,GAAGkQ,IAAJ,EAAUrR,GAAV,CAAR,CAAxB;IACH;;IACD,OAAO;MACHqR,IAAI,EAAE,CAAC,GAAGA,IAAJ,EAAUrR,GAAV,CADH;MAEHmB;IAFG,CAAP;EAIH,CAxBM,EAwBJ,KAxBI,CAAP;AAyBH;;AACD,SAASgQ,qBAAT,CAA+BG,cAA/B,EAA+CC,OAA/C,EAAwD;EACpD,IAAID,cAAc,KAAK,KAAvB,EAA8B;IAC1B;EACH;;EACD,MAAME,kBAAkB,GAAGF,cAAc,CAACD,IAAf,CAAoB7O,IAApB,CAAyB,GAAzB,CAA3B;EACA,MAAM+H,KAAK,GAAG,IAAI3J,KAAJ,CAAW,2BAA0B2Q,OAAQ,QAAOC,kBAAmB,MAAKxG,iBAAkB,UAASuG,OAAQ,iBAA/G,CAAd;EACAhH,KAAK,CAACpJ,KAAN,GAAcmQ,cAAc,CAACnQ,KAA7B;EACAoJ,KAAK,CAACiH,kBAAN,GAA2BA,kBAA3B;EACA,MAAMjH,KAAN;AACH;;AAED,SAASkH,yBAAT,CAAmCnM,OAAnC,EAA4CiL,MAA5C,EAAoD;EAChD,OAAO,UAAUpL,KAAV,EAAiBtC,MAAjB,EAAyB;IAC5B,IAAI0N,MAAM,CAAC1N,MAAP,CAAcA,MAAd,KAAyB,CAACrE,EAAE,CAACkT,MAAH,CAAUC,eAAV,EAA9B,EAA2D;MACvD,MAAM,IAAI/Q,KAAJ,CAAW,WAAUiC,MAAM,CAACzC,IAAK,6BAA4B4K,iBAAkB,2BAA/E,CAAN;IACH;;IACD,OAAO1F,OAAO,CAACH,KAAD,EAAQtC,MAAR,CAAd;EACH,CALD;AAMH;;AAED,SAAS+O,yBAAT,CAAmCC,aAAnC,EAAkD;EAC9C,IAAIjT,SAAS,EAAb,EAAiB;IACb,OAAOqB,MAAM,CAACO,MAAP,CAAc;MAAEsR,0BAA0B,EAAE,KAA9B;MAAqCC,2BAA2B,EAAE,KAAlE;MAAyEC,uBAAuB,EAAE,IAAlG;MAAwGC,wBAAwB,EAAE,IAAlI;MAAwIC,wBAAwB,EAAE,KAAlK;MAAyKC,0BAA0B,EAAE;IAArM,CAAd,EAA4NN,aAA5N,CAAP;EACH;;EACD,OAAO;IACHC,0BAA0B,EAAE,KADzB;IAEHC,2BAA2B,EAAE,KAF1B;IAGHC,uBAAuB,EAAE,KAHtB;IAIHC,wBAAwB,EAAE,KAJvB;IAKHC,wBAAwB,EAAE,KALvB;IAMHC,0BAA0B,EAAE;EANzB,CAAP;AAQH;;AACD,SAASC,mCAAT,CAA6C;EAAEL,2BAAF;EAA+BD;AAA/B,CAA7C,EAA2G;EACvG,OAAQxM,OAAD,IAAayM,2BAA2B,IAAID,0BAA/B,GACdd,6BAA6B,CAAC1L,OAAD,EAAU;IACrCzC,MAAM,EAAGA,MAAD,IAAYkP,2BAA2B,IAAI,CAACM,gBAAgB,CAACxP,MAAD,CAD/B;IAErCsC,KAAK,EAAE,MAAM2M;EAFwB,CAAV,CADf,GAKdxM,OALN;AAMH;;AACD,SAASgN,kCAAT,CAA4C;EAAEL,wBAAF;EAA4BD;AAA5B,CAA5C,EAAoG;EAChG,OAAQ1M,OAAD,IAAa2M,wBAAwB,IAAID,uBAA5B,GACd1B,4BAA4B,CAAChL,OAAD,EAAU;IACpCzC,MAAM,EAAGA,MAAD,IAAYoP,wBAAwB,IAAI,CAACI,gBAAgB,CAACxP,MAAD,CAD7B;IAEpCsC,KAAK,EAAE,MAAM6M;EAFuB,CAAV,CADd,GAKd1M,OALN;AAMH;;AACD,SAAS+M,gBAAT,CAA0BxP,MAA1B,EAAkC;EAC9B,OAAOA,MAAM,CAACzC,IAAP,CAAYyQ,UAAZ,CAAuB,OAAvB,CAAP;AACH;;AACD,SAAS0B,8BAAT,CAAwC;EAAEL;AAAF,CAAxC,EAAuE;EACnE,OAAQ5M,OAAD,IAAa4M,wBAAwB,GACtCT,yBAAyB,CAACnM,OAAD,EAAU;IACjCzC,MAAM,EAAGA,MAAD,IAAYqP,wBAAwB,IAAI,CAACG,gBAAgB,CAACxP,MAAD;EADhC,CAAV,CADa,GAItCyC,OAJN;AAKH;;AACD,SAASkN,oBAAT,CAA8BX,aAA9B,EAA6C;EACzC,OAAO,CACH;IACItJ,OAAO,EAAEjE,oBADb;IAEImO,QAAQ,EAAEZ;EAFd,CADG,EAKH;IACItJ,OAAO,EAAElE,mBADb;IAEIqO,UAAU,EAAEC,qBAFhB;IAGIC,IAAI,EAAE,CAACtO,oBAAD;EAHV,CALG,EAUH;IACIiE,OAAO,EAAEhE,qBADb;IAEIqO,IAAI,EAAE,CAACvO,mBAAD,CAFV;IAGIqO,UAAU,EAAEd;EAHhB,CAVG,EAeH;IACIrJ,OAAO,EAAEpE,aADb;IAEI0O,KAAK,EAAE,IAFX;IAGID,IAAI,EAAE,CAACrO,qBAAD,CAHV;IAIImO,UAAU,EAAEJ;EAJhB,CAfG,EAqBH;IACI/J,OAAO,EAAEpE,aADb;IAEI0O,KAAK,EAAE,IAFX;IAGID,IAAI,EAAE,CAACrO,qBAAD,CAHV;IAIImO,UAAU,EAAEN;EAJhB,CArBG,EA2BH;IACI7J,OAAO,EAAEpE,aADb;IAEI0O,KAAK,EAAE,IAFX;IAGID,IAAI,EAAE,CAACrO,qBAAD,CAHV;IAIImO,UAAU,EAAEH;EAJhB,CA3BG,CAAP;AAkCH;;AACD,SAASO,4BAAT,GAAwC;EACpC,OAAO,CACH;IACIvK,OAAO,EAAE/D,6BADb;IAEIqO,KAAK,EAAE,IAFX;IAGID,IAAI,EAAE,CAACrO,qBAAD,CAHV;IAIImO,UAAU,EAAEK;EAJhB,CADG,CAAP;AAQH;;AACD,SAASJ,qBAAT,CAA+Bd,aAA/B,EAA8C;EAC1C,OAAOA,aAAP;AACH;;AACD,SAASkB,0BAAT,CAAoC1S,MAApC,EAA4C;EACxC,IAAI,CAACA,MAAM,CAAC8R,0BAAZ,EAAwC;IACpC;EACH;;EACD,MAAMa,UAAU,GAAG/S,MAAM,CAACgT,OAAP,CAAenT,uBAAf,EACd8F,MADc,CACP,CAAC,GAAGsN,aAAH,CAAD,KAAuBA,aAAa,GAAG,CADhC,EAEdtT,GAFc,CAEV,CAAC,CAACQ,IAAD,CAAD,KAAYA,IAFF,CAAnB;;EAGA,IAAI4S,UAAU,CAAChO,MAAf,EAAuB;IACnB,MAAM,IAAIpE,KAAJ,CAAW,+CAA8CoS,UAAU,CACpEpT,GAD0D,CACrDQ,IAAD,IAAW,IAAGA,IAAK,GADmC,EAE1DoC,IAF0D,CAErD,IAFqD,CAE/C,KAAIwI,iBAAkB,6BAFhC,CAAN;EAGH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASmI,YAAT,CAAsBC,kBAAtB,EAA0CxO,QAA1C,EAAoDvE,MAAM,GAAG,EAA7D,EAAiE;EAC7D,OAAO;IACHgT,UAAU,EAAE,CACR,GAAGC,aAAa,CAACF,kBAAD,EAAqBxO,QAArB,EAA+BvE,MAA/B,CADR,EAERkT,0BAFQ;EADT,CAAP;AAMH;;AACD,SAASC,aAAT,CAAuB5O,QAAvB,EAAiCvE,MAAjC,EAAyC;EACrC,OAAO,CACH;IACIkI,OAAO,EAAEnF,iBADb;IAEIsP,UAAU,EAAErC,oBAFhB;IAGIuC,IAAI,EAAE,CAAC,CAACjJ,KAAD,EAAQ,IAAI7K,QAAJ,EAAR,EAAwB,IAAIC,QAAJ,EAAxB,CAAD;EAHV,CADG,EAMH;IAAEwJ,OAAO,EAAElF,cAAX;IAA2BoP,QAAQ,EAAEpS,MAAM,CAACwE;EAA5C,CANG,EAOH;IACI0D,OAAO,EAAEjF,aADb;IAEIoP,UAAU,EAAEzC,oBAFhB;IAGI2C,IAAI,EAAE,CAACvP,cAAD;EAHV,CAPG,EAYH;IAAEkF,OAAO,EAAE7E,iBAAX;IAA8B+O,QAAQ,EAAE7N;EAAxC,CAZG,EAaH;IACI2D,OAAO,EAAE3E,eADb;IAEI4E,WAAW,EAAE5D,QAAQ,YAAYlG,cAApB,GAAqCkG,QAArC,GAAgDlB;EAFjE,CAbG,EAiBH;IACI6E,OAAO,EAAE9E,gBADb;IAEImP,IAAI,EAAE,CAAC/T,QAAD,EAAW6E,iBAAX,EAA8B,CAAC,IAAI/E,MAAJ,CAAWiF,eAAX,CAAD,CAA9B,CAFV;IAGI8O,UAAU,EAAEpD;EAHhB,CAjBG,EAsBH;IACI/G,OAAO,EAAErE,2BADb;IAEIuO,QAAQ,EAAEpS,MAAM,CAACoG,YAAP,GAAsBpG,MAAM,CAACoG,YAA7B,GAA4C;EAF1D,CAtBG,EA0BH;IACI8B,OAAO,EAAEnE,uBADb;IAEIwO,IAAI,EAAE,CAACzO,aAAD,EAAgBD,2BAAhB,CAFV;IAGIwO,UAAU,EAAExC;EAHhB,CA1BG,EA+BH;IACI3H,OAAO,EAAE/E,gBADb;IAEIiP,QAAQ,EAAEpS,MAAM,CAACmG,cAAP,GAAwBnG,MAAM,CAACmG,cAA/B,GAAgD7B;EAF9D,CA/BG,EAmCH;IACI4D,OAAO,EAAEhF,eADb;IAEIqP,IAAI,EAAE,CAACpP,gBAAD,EAAmBY,uBAAnB,CAFV;IAGIsO,UAAU,EAAEnM;EAHhB,CAnCG,EAwCHpD,yBAxCG,EAyCHmF,yBAzCG,EA0CHI,iCA1CG,EA2CHgB,eA3CG,EA4CHe,eA5CG,EA6CH+H,oBAAoB,CAACnS,MAAM,CAACwR,aAAR,CA7CjB,EA8CHiB,4BAA4B,EA9CzB,CAAP;AAgDH;;AACD,SAASW,wBAAT,GAAoC;EAChCzU,MAAM,CAAC0D,cAAD,CAAN;EACA1D,MAAM,CAAC+H,iBAAD,CAAN;EACA/H,MAAM,CAACyJ,qBAAD,CAAN;EACAzJ,MAAM,CAAC2K,KAAD,CAAN;EACA3K,MAAM,CAACoE,iBAAD,EAAoBnE,WAAW,CAACH,QAAhC,CAAN;EACAE,MAAM,CAACwF,6BAAD,EAAgCvF,WAAW,CAACH,QAA5C,CAAN;AACH;AACD;AACA;AACA;AACA;;;AACA,MAAM4U,0BAA0B,GAAG,CAC/B;EAAEnL,OAAO,EAAE9D,mBAAX;EAAgCiO,UAAU,EAAEe;AAA5C,CAD+B,EAE/B;EACIlL,OAAO,EAAErJ,uBADb;EAEI2T,KAAK,EAAE,IAFX;;EAGIH,UAAU,GAAG;IACT,OAAO,MAAM1T,MAAM,CAACyF,mBAAD,CAAnB;EACH;;AALL,CAF+B,CAAnC;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASkP,YAAT,CAAsB/O,QAAQ,GAAG,EAAjC,EAAqCvE,MAAM,GAAG,EAA9C,EAAkD;EAC9C,OAAO;IACHgT,UAAU,EAAE,CACR,GAAGG,aAAa,CAAC5O,QAAD,EAAWvE,MAAX,CADR,EAERqT,0BAFQ;EADT,CAAP;AAMH;;AACD,SAASE,2BAAT,GAAuC;EACnC5U,MAAM,CAACyF,mBAAD,CAAN;EACA,MAAM+C,QAAQ,GAAGxI,MAAM,CAAC+E,eAAD,CAAvB;EACA,MAAM8P,eAAe,GAAG7U,MAAM,CAACiF,gBAAD,CAA9B;EACA,MAAM6F,cAAc,GAAG9K,MAAM,CAACkI,cAAD,CAA7B;EACAlI,MAAM,CAACwF,6BAAD,EAAgCvF,WAAW,CAACH,QAA5C,CAAN;EACA,MAAMgV,KAAK,GAAGtM,QAAQ,CAAC5H,GAAT,CAAa,CAAC0H,OAAD,EAAUuI,KAAV,KAAoB;IAC3C,MAAMkE,wBAAwB,GAAGF,eAAe,CAACG,KAAhB,EAAjC,CAD2C,CAE3C;;IACA,MAAMpP,QAAQ,GAAGmP;IAAyB;IAAD,CAAgBlE,KAAhB,CAAzC;IACA,OAAO5P,MAAM,CAACO,MAAP,CAAcP,MAAM,CAACO,MAAP,CAAc,EAAd,EAAkB8G,OAAlB,CAAd,EAA0C;MAAE1C,QAAF;MAAYC,YAAY,EAAEoL,oBAAoB,CAAC3I,OAAO,CAACzC,YAAT;IAA9C,CAA1C,CAAP;EACH,CALa,CAAd;EAMAiF,cAAc,CAACvC,WAAf,CAA2BuM,KAA3B;AACH;AACD;AACA;AACA;AACA;;;AACA,MAAMP,0BAA0B,GAAG,CAC/B;EACIhL,OAAO,EAAE7D,sBADb;EAEIgO,UAAU,EAAEkB;AAFhB,CAD+B,EAK/B;EACIrL,OAAO,EAAErJ,uBADb;EAEI2T,KAAK,EAAE,IAFX;EAGID,IAAI,EAAE,EAHV;;EAIIF,UAAU,GAAG;IACT,OAAO,MAAM1T,MAAM,CAAC0F,sBAAD,CAAnB;EACH;;AANL,CAL+B,CAAnC;;AAcA,SAAS4O,aAAT,CAAuBF,kBAAvB,EAA2CxO,QAA3C,EAAqDvE,MAAM,GAAG,EAA9D,EAAkE;EAC9D,OAAO,CACH;IACIkI,OAAO,EAAEzE,gBADb;IAEI+O,KAAK,EAAE,IAFX;IAGIJ,QAAQ,EAAEW,kBAAkB,YAAYnT,MAA9B,GAAuC,EAAvC,GAA4CI;EAH1D,CADG,EAMH;IACIkI,OAAO,EAAE5E,cADb;IAEIkP,KAAK,EAAE,IAFX;IAGIJ,QAAQ,EAAE;MACNzS,GAAG,EAAEoT,kBAAkB,YAAYnT,MAA9B,GACCmT,kBAAkB,CAACtE,IADpB,GAECsE,kBAHA;MAIN5M,cAAc,EAAE,EAAEnG,MAAM,YAAY3B,cAApB,KAAuC2B,MAAM,CAACmG,cAA9C,GACVnG,MAAM,CAACmG,cADG,GAEV7B,eANA;MAON8B,YAAY,EAAE,EAAEpG,MAAM,YAAY3B,cAApB,KAAuC2B,MAAM,CAACoG,YAA9C,GACRpG,MAAM,CAACoG,YADC,GAER,EATA;MAUN5B,YAAY,EAAE,EAAExE,MAAM,YAAY3B,cAApB,KAAuC2B,MAAM,CAACwE,YAA9C,GACRxE,MAAM,CAACwE,YADC,GAER/D;IAZA;EAHd,CANG,EAwBH;IACIyH,OAAO,EAAExE,eADb;IAEI6O,IAAI,EAAE,CAAC/T,QAAD,EAAWiF,gBAAX,EAA6BH,cAA7B,CAFV;IAGI+O,UAAU,EAAEjD;EAHhB,CAxBG,EA6BH;IACIlH,OAAO,EAAE1E,iBADb;IAEIgP,KAAK,EAAE,IAFX;IAGIJ,QAAQ,EAAEW,kBAAkB,YAAYnT,MAA9B,GACJmT,kBAAkB,CAAC9N,OADf,GAEJV;EALV,CA7BG,EAoCH;IACI2D,OAAO,EAAEvE,uBADb;IAEI6O,KAAK,EAAE,IAFX;IAGIrK,WAAW,EAAE5D,QAAQ,YAAYlG,cAApB,GAAqCkG,QAArC,GAAgDf;EAHjE,CApCG,EAyCH;IACI0E,OAAO,EAAEtE,gBADb;IAEI4O,KAAK,EAAE,IAFX;IAGID,IAAI,EAAE,CACF/T,QADE,EAEFgF,iBAFE,EAGF,CAAC,IAAIlF,MAAJ,CAAWqF,uBAAX,CAAD,CAHE,CAHV;IAQI0O,UAAU,EAAE3C;EARhB,CAzCG,EAmDH+C,4BAA4B,EAnDzB,CAAP;AAqDH;;AAED,MAAMmB,eAAN,CAAsB;EAClBtR,WAAW,CAACkG,QAAD,EAAWC,QAAX,EAAqBoL,eAArB,EAAsC7J,KAAtC,EAA6C8J,KAA7C,EAAoDC,WAApD,EAAiE,CAAG;;AAD7D;AAGtB;;;AAAmBH,eAAe,CAAChR,IAAhB;EAAA,iBAA4GgR,eAA5G,EAlnCiGzV,EAknCjG,UAA6IkE,cAA7I,GAlnCiGlE,EAknCjG,UAAwKuI,iBAAxK,GAlnCiGvI,EAknCjG,UAAsMiK,qBAAtM,GAlnCiGjK,EAknCjG,UAAwOmL,KAAxO,GAlnCiGnL,EAknCjG,UAA0P4E,iBAA1P,MAlnCiG5E,EAknCjG,UAAwSgG,6BAAxS;AAAA;AACnB;;;AAAmByP,eAAe,CAACI,IAAhB,kBAnnCiG7V,EAmnCjG;EAAA,MAA6GyV;AAA7G;AACnB;;AAAmBA,eAAe,CAACK,IAAhB,kBApnCiG9V,EAonCjG;;AACnB;EAAA,mDArnCoHA,EAqnCpH,mBAA2FyV,eAA3F,EAAwH,CAAC;IAC7G7T,IAAI,EAAEjB,QADuG;IAE7GoB,IAAI,EAAE,CAAC,EAAD;EAFuG,CAAD,CAAxH,EAG4B,YAAY;IAChC,OAAO,CAAC;MAAEH,IAAI,EAAEsC;IAAR,CAAD,EAA2B;MAAEtC,IAAI,EAAE2G;IAAR,CAA3B,EAAwD;MAAE3G,IAAI,EAAEqI;IAAR,CAAxD,EAAyF;MAAErI,IAAI,EAAEuJ;IAAR,CAAzF,EAA0G;MAAEvJ,IAAI,EAAEU,SAAR;MAAmBuH,UAAU,EAAE,CAAC;QACjIjI,IAAI,EAAEtB;MAD2H,CAAD,EAEjI;QACCsB,IAAI,EAAEzB,MADP;QAEC4B,IAAI,EAAE,CAAC6C,iBAAD;MAFP,CAFiI;IAA/B,CAA1G,EAKW;MAAEhD,IAAI,EAAEU,SAAR;MAAmBuH,UAAU,EAAE,CAAC;QAClCjI,IAAI,EAAEtB;MAD4B,CAAD,EAElC;QACCsB,IAAI,EAAEzB,MADP;QAEC4B,IAAI,EAAE,CAACiE,6BAAD;MAFP,CAFkC;IAA/B,CALX,CAAP;EAWH,CAfL;AAAA;;AAgBA,MAAM+P,kBAAN,CAAyB;EACrB5R,WAAW,CAAC6E,QAAD,EAAWqM,eAAX,EAA4B/J,cAA5B,EAA4C0K,IAA5C,EAAkDJ,WAAlD,EAA+D;IACtE,KAAK5M,QAAL,GAAgBA,QAAhB;IACA,KAAKqM,eAAL,GAAuBA,eAAvB;IACA,KAAK/J,cAAL,GAAsBA,cAAtB;IACA,MAAMgK,KAAK,GAAGtM,QAAQ,CAAC5H,GAAT,CAAa,CAAC0H,OAAD,EAAUuI,KAAV,KAAoB;MAC3C,MAAMkE,wBAAwB,GAAGF,eAAe,CAACG,KAAhB,EAAjC,CAD2C,CAE3C;;MACA,MAAMpP,QAAQ,GAAGmP;MAAyB;MAAD,CAAgBlE,KAAhB,CAAzC;MACA,OAAO5P,MAAM,CAACO,MAAP,CAAcP,MAAM,CAACO,MAAP,CAAc,EAAd,EAAkB8G,OAAlB,CAAd,EAA0C;QAAE1C,QAAF;QAAYC,YAAY,EAAEoL,oBAAoB,CAAC3I,OAAO,CAACzC,YAAT;MAA9C,CAA1C,CAAP;IACH,CALa,CAAd;IAMAiF,cAAc,CAACvC,WAAf,CAA2BuM,KAA3B;EACH,CAZoB,CAarB;;;EACA9Q,WAAW,GAAG;IACV,KAAK8G,cAAL,CAAoBlC,cAApB,CAAmC,KAAKJ,QAAxC;EACH;;AAhBoB;AAkBzB;;;AAAmB+M,kBAAkB,CAACtR,IAAnB;EAAA,iBAA+GsR,kBAA/G,EAvpCiG/V,EAupCjG,UAAmJuF,eAAnJ,GAvpCiGvF,EAupCjG,UAA+KyF,gBAA/K,GAvpCiGzF,EAupCjG,UAA4M0I,cAA5M,GAvpCiG1I,EAupCjG,UAAuOyV,eAAvO,GAvpCiGzV,EAupCjG,UAAmQgG,6BAAnQ;AAAA;AACnB;;;AAAmB+P,kBAAkB,CAACF,IAAnB,kBAxpCiG7V,EAwpCjG;EAAA,MAAgH+V;AAAhH;AACnB;;AAAmBA,kBAAkB,CAACD,IAAnB,kBAzpCiG9V,EAypCjG;;AACnB;EAAA,mDA1pCoHA,EA0pCpH,mBAA2F+V,kBAA3F,EAA2H,CAAC;IAChHnU,IAAI,EAAEjB,QAD0G;IAEhHoB,IAAI,EAAE,CAAC,EAAD;EAF0G,CAAD,CAA3H,EAG4B,YAAY;IAChC,OAAO,CAAC;MAAEH,IAAI,EAAEU,SAAR;MAAmBuH,UAAU,EAAE,CAAC;QACxBjI,IAAI,EAAEzB,MADkB;QAExB4B,IAAI,EAAE,CAACwD,eAAD;MAFkB,CAAD;IAA/B,CAAD,EAGW;MAAE3D,IAAI,EAAEU,SAAR;MAAmBuH,UAAU,EAAE,CAAC;QAClCjI,IAAI,EAAEzB,MAD4B;QAElC4B,IAAI,EAAE,CAAC0D,gBAAD;MAF4B,CAAD;IAA/B,CAHX,EAMW;MAAE7D,IAAI,EAAE8G;IAAR,CANX,EAMqC;MAAE9G,IAAI,EAAE6T;IAAR,CANrC,EAMgE;MAAE7T,IAAI,EAAEU,SAAR;MAAmBuH,UAAU,EAAE,CAAC;QACvFjI,IAAI,EAAEtB;MADiF,CAAD,EAEvF;QACCsB,IAAI,EAAEzB,MADP;QAEC4B,IAAI,EAAE,CAACiE,6BAAD;MAFP,CAFuF;IAA/B,CANhE,CAAP;EAYH,CAhBL;AAAA;;AAiBA,MAAMiQ,WAAN,CAAkB;EACA,OAAPC,OAAO,CAAC9P,QAAD,EAAWvE,MAAM,GAAG,EAApB,EAAwB;IAClC,OAAO;MACHsU,QAAQ,EAAEV,eADP;MAEHW,SAAS,EAAE,CAAC,GAAGpB,aAAa,CAAC5O,QAAD,EAAWvE,MAAX,CAAjB;IAFR,CAAP;EAIH;;EACgB,OAAVwU,UAAU,CAACzB,kBAAD,EAAqBxO,QAArB,EAA+BvE,MAAM,GAAG,EAAxC,EAA4C;IACzD,OAAO;MACHsU,QAAQ,EAAEJ,kBADP;MAEHK,SAAS,EAAE,CAAC,GAAGtB,aAAa,CAACF,kBAAD,EAAqBxO,QAArB,EAA+BvE,MAA/B,CAAjB;IAFR,CAAP;EAIH;;AAZa;AAclB;;;AAAmBoU,WAAW,CAACxR,IAAZ;EAAA,iBAAwGwR,WAAxG;AAAA;AACnB;;;AAAmBA,WAAW,CAACJ,IAAZ,kBA1rCiG7V,EA0rCjG;EAAA,MAAyGiW;AAAzG;AACnB;;AAAmBA,WAAW,CAACH,IAAZ,kBA3rCiG9V,EA2rCjG;;AACnB;EAAA,mDA5rCoHA,EA4rCpH,mBAA2FiW,WAA3F,EAAoH,CAAC;IACzGrU,IAAI,EAAEjB,QADmG;IAEzGoB,IAAI,EAAE,CAAC,EAAD;EAFmG,CAAD,CAApH;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASuU,EAAT,CAAY,GAAGvU,IAAf,EAAqB;EACjB,MAAM+E,OAAO,GAAG/E,IAAI,CAACwU,GAAL,EAAhB;EACA,MAAMC,KAAK,GAAGzU,IAAI,CAACX,GAAL,CAAUqB,OAAD,IAAaA,OAAO,CAACb,IAA9B,CAAd;EACA,OAAO;IAAEkF,OAAF;IAAW0P;EAAX,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,aAAT,CAAuBpQ,YAAvB,EAAqC,GAAGqQ,GAAxC,EAA6C;EACzC,MAAMtV,GAAG,GAAG,IAAIuV,GAAJ,EAAZ;;EACA,KAAK,MAAML,EAAX,IAAiBI,GAAjB,EAAsB;IAClB,KAAK,MAAM9U,IAAX,IAAmB0U,EAAE,CAACE,KAAtB,EAA6B;MACzB,MAAMI,eAAe,GAAGxV,GAAG,CAAC4P,GAAJ,CAAQpP,IAAR,CAAxB;;MACA,IAAIgV,eAAJ,EAAqB;QACjB,MAAMC,UAAU,GAAG,CAAClQ,KAAD,EAAQtC,MAAR,KAAmBiS,EAAE,CAACxP,OAAH,CAAW8P,eAAe,CAACjQ,KAAD,EAAQtC,MAAR,CAA1B,EAA2CA,MAA3C,CAAtC;;QACAjD,GAAG,CAAC0V,GAAJ,CAAQlV,IAAR,EAAciV,UAAd;MACH,CAHD,MAIK;QACDzV,GAAG,CAAC0V,GAAJ,CAAQlV,IAAR,EAAc0U,EAAE,CAACxP,OAAjB;MACH;IACJ;EACJ;;EACD,OAAO,UAAUH,KAAK,GAAGN,YAAlB,EAAgChC,MAAhC,EAAwC;IAC3C,MAAMyC,OAAO,GAAG1F,GAAG,CAAC4P,GAAJ,CAAQ3M,MAAM,CAACzC,IAAf,CAAhB;IACA,OAAOkF,OAAO,GAAGA,OAAO,CAACH,KAAD,EAAQtC,MAAR,CAAV,GAA4BsC,KAA1C;EACH,CAHD;AAIH;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASZ,qBAAT,EAAgC7B,cAAhC,EAAgDuB,gBAAhD,EAAkES,sBAAlE,EAA0FjC,IAA1F,EAAgGgB,gBAAhG,EAAkHH,aAAlH,EAAiIa,aAAjI,EAAgJZ,eAAhJ,EAAiKkB,mBAAjK,EAAsLyC,cAAtL,EAAsMF,wBAAtM,EAAgOD,iBAAhO,EAAmPpD,cAAnP,EAAmQ8E,qBAAnQ,EAA0RG,KAA1R,EAAiSD,eAAjS,EAAkTgB,KAAlT,EAAyT4K,kBAAzT,EAA6UE,WAA7U,EAA0VR,eAA1V,EAA2WhN,MAA3W,EAAmX/C,2BAAnX,EAAgZG,mBAAhZ,EAAqaM,eAAra,EAAsbmB,OAAtb,EAA+b3F,YAA/b,EAA6cuB,iBAA7c,EAAgekN,aAAhe,EAA+eL,qBAA/e,EAAsgB0G,aAAtgB,EAAqhB1O,oBAArhB,EAA2iBgH,cAA3iB,EAA2jBE,qBAA3jB,EAAklBZ,cAAllB,EAAkmBa,cAAlmB,EAAknBxL,UAAlnB,EAA8nBiK,qBAA9nB,EAAqpB2I,EAArpB,EAAypBnU,KAAzpB,EAAgqBwS,YAAhqB,EAA8qBQ,YAA9qB,EAA4rBtK,WAA5rB,EAAysBqD,aAAzsB,EAAwtB3C,MAAxtB,EAAguBmC,sBAAhuB,EAAwvBnL,KAAxvB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}