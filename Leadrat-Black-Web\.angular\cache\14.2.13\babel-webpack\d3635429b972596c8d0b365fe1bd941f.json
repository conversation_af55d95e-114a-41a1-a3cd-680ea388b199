{"ast": null, "code": "import baseFill from './_baseFill.js';\nimport isIterateeCall from './_isIterateeCall.js';\n/**\n * Fills elements of `array` with `value` from `start` up to, but not\n * including, `end`.\n *\n * **Note:** This method mutates `array`.\n *\n * @static\n * @memberOf _\n * @since 3.2.0\n * @category Array\n * @param {Array} array The array to fill.\n * @param {*} value The value to fill `array` with.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns `array`.\n * @example\n *\n * var array = [1, 2, 3];\n *\n * _.fill(array, 'a');\n * console.log(array);\n * // => ['a', 'a', 'a']\n *\n * _.fill(Array(3), 2);\n * // => [2, 2, 2]\n *\n * _.fill([4, 6, 8, 10], '*', 1, 3);\n * // => [4, '*', '*', 10]\n */\n\nfunction fill(array, value, start, end) {\n  var length = array == null ? 0 : array.length;\n\n  if (!length) {\n    return [];\n  }\n\n  if (start && typeof start != 'number' && isIterateeCall(array, value, start)) {\n    start = 0;\n    end = length;\n  }\n\n  return baseFill(array, value, start, end);\n}\n\nexport default fill;", "map": {"version": 3, "names": ["baseFill", "isIterateeCall", "fill", "array", "value", "start", "end", "length"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/lodash-es/fill.js"], "sourcesContent": ["import baseFill from './_baseFill.js';\nimport isIterateeCall from './_isIterateeCall.js';\n\n/**\n * Fills elements of `array` with `value` from `start` up to, but not\n * including, `end`.\n *\n * **Note:** This method mutates `array`.\n *\n * @static\n * @memberOf _\n * @since 3.2.0\n * @category Array\n * @param {Array} array The array to fill.\n * @param {*} value The value to fill `array` with.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns `array`.\n * @example\n *\n * var array = [1, 2, 3];\n *\n * _.fill(array, 'a');\n * console.log(array);\n * // => ['a', 'a', 'a']\n *\n * _.fill(Array(3), 2);\n * // => [2, 2, 2]\n *\n * _.fill([4, 6, 8, 10], '*', 1, 3);\n * // => [4, '*', '*', 10]\n */\nfunction fill(array, value, start, end) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  if (start && typeof start != 'number' && isIterateeCall(array, value, start)) {\n    start = 0;\n    end = length;\n  }\n  return baseFill(array, value, start, end);\n}\n\nexport default fill;\n"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,gBAArB;AACA,OAAOC,cAAP,MAA2B,sBAA3B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,IAAT,CAAcC,KAAd,EAAqBC,KAArB,EAA4BC,KAA5B,EAAmCC,GAAnC,EAAwC;EACtC,IAAIC,MAAM,GAAGJ,KAAK,IAAI,IAAT,GAAgB,CAAhB,GAAoBA,KAAK,CAACI,MAAvC;;EACA,IAAI,CAACA,MAAL,EAAa;IACX,OAAO,EAAP;EACD;;EACD,IAAIF,KAAK,IAAI,OAAOA,KAAP,IAAgB,QAAzB,IAAqCJ,cAAc,CAACE,KAAD,EAAQC,KAAR,EAAeC,KAAf,CAAvD,EAA8E;IAC5EA,KAAK,GAAG,CAAR;IACAC,GAAG,GAAGC,MAAN;EACD;;EACD,OAAOP,QAAQ,CAACG,KAAD,EAAQC,KAAR,EAAeC,KAAf,EAAsBC,GAAtB,CAAf;AACD;;AAED,eAAeJ,IAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}