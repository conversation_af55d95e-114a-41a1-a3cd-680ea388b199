{"ast": null, "code": "import arrayMap from './_arrayMap.js';\n/**\n * The base implementation of `_.values` and `_.valuesIn` which creates an\n * array of `object` property values corresponding to the property names\n * of `props`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} props The property names to get values for.\n * @returns {Object} Returns the array of property values.\n */\n\nfunction baseValues(object, props) {\n  return arrayMap(props, function (key) {\n    return object[key];\n  });\n}\n\nexport default baseValues;", "map": {"version": 3, "names": ["arrayMap", "baseValues", "object", "props", "key"], "sources": ["C:/Users/<USER>/Desktop/Translate/Leadrat-Black-Web/node_modules/lodash-es/_baseValues.js"], "sourcesContent": ["import arrayMap from './_arrayMap.js';\n\n/**\n * The base implementation of `_.values` and `_.valuesIn` which creates an\n * array of `object` property values corresponding to the property names\n * of `props`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} props The property names to get values for.\n * @returns {Object} Returns the array of property values.\n */\nfunction baseValues(object, props) {\n  return arrayMap(props, function(key) {\n    return object[key];\n  });\n}\n\nexport default baseValues;\n"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,gBAArB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,UAAT,CAAoBC,MAApB,EAA4BC,KAA5B,EAAmC;EACjC,OAAOH,QAAQ,CAACG,KAAD,EAAQ,UAASC,GAAT,EAAc;IACnC,OAAOF,MAAM,CAACE,GAAD,CAAb;EACD,CAFc,CAAf;AAGD;;AAED,eAAeH,UAAf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}